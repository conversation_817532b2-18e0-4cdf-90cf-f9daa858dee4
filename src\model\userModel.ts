declare namespace UserApi {
  /**
   * @description License详情信息
   * @interface LicenseDetail
   */
  interface LicenseDetail {
    endTime: string
    instanceLimit: number
    level: number
    licenseMD5Id: string
    metaTableLimit: number
    modules: string
    startTime: string
  }

  /**
   * @description 用户登陆
   * @export
   * @param {string} account - 账号
   * @param {string} password - 密码
   * @interface ILogin
   */
  interface ILogin {
    loginName: string
    password: string
    rememberMe: boolean
  }

  //用户登陆返回数据
  interface LoginData {
    jwtToken: string
  }

  /**
   * @description 新增用户
   * @export
   * @param {string} userName - 账号别名
   * @param {string} email - 邮箱
   * @param {string} loginName - 账号名
   * @param {string} password - 密码
   * @param {string} phone - 手机
   * @param {Array<stringOrNumber>} roleList - 角色IDs
   * @param {number} sex - 用户性别 0男 1女
   * @param {string} description - 描述
   * @param {string} position - 职位
   * @interface IAddUser
   */
  interface IAddUser {
    userName: string
    email: string
    loginName: string
    password: string
    phone: string
    roleList: number[]
    sex?: number
    description?: string
    position?: string
  }

  /**
   * @description 根据ID删除用户
   * @export
   * @param {Array<stringOrNumber>} userIds - 用户id
   * @interface IDeteleUserById
   */
  interface IDeteleUserById {
    userIds: any[]
  }

  /**
   * @description 根据ID获取用户详细信息
   * @
   * @param {stringOrNumber} id - 用户id
   * @interface IGetUserDetail
   */
  interface IGetUserDetails {
    id: stringOrNumber
  }

  //用户详细信息返回数据
  interface UserDetailData {
    aliasName: string
    componentList: Array<string>
    description: string | null
    email: string
    loginName: string
    phone: string
    position: string | null
    roleIds: Array<number>
    roleNames: Array<string>
    sex: number
    tenantId: string
    tenantName: string
    type: string
    userId: string
    userName: string
    version: string | null
    businessAuthority: Record<string, stringOrNumber[]>
  }

  /**
   * @description 获取用户列表
   * @export
   * @param {string} condition - 搜索条件-账号/手机号/姓名
   * @param {number} pageNo - 当前页
   * @param {number} pageSize - 页大小
   * @param {stringOrNumber} roleId - 搜索条件-用户角色
   * @interface IGetUserList
   */
  interface IGetUserList extends PageArgument {
    condition?: string
    roleId?: number
    tenantId?: number
  }
  //用户列表返回数据
  interface UserListData {
    current: number
    records: Array<UserDetailData>
    size: number
    startRowNo: number
    total: number
    totalPage: number
  }
  /**
   * @description 根据ID重置用户密码
   * @export
   * @param {stringOrNumber} id - 用户id
   * @interface IResetPassword
   */
  interface IResetPassword {
    id: stringOrNumber
  }

  /**
   * @description 修改某个用户的信息
   * @export
   * @param {string} email - 邮箱
   * @param {string} id - 用户id
   * @param {string} position - 职位
   * @param {Array<stringOrNumber>} roleIds - 用户角色列表
   * @param {number} sex - 用户性别 0男 1女
   * @param {string} phone - 手机号码
   * @param {string} loginName - 账号
   * @param {string} userName - 用户名称
   * @interface IUpdateUser
   */
  interface IUpdateUser {
    userName: string
    email: string
    id: number
    loginName: string
    phone: string
    position: string
    roleIds: any[]
    sex: number
  }

  /**
   * @description 根据ID冻结/解冻用户
   * @export
   * @param {string} id - 用户id
   * @interface IUpdateStatus
   */
  interface IUpdateUserStatus {
    id: stringOrNumber
  }

  /**
   * @description 修改当前用户的邮箱或手机号
   * @export
   * @param {string} email - 邮箱
   * @param {string} phone - 手机号码
   * @interface IUpdateCurrentUserInfo
   */
  interface IUpdateCurrentUserInfo {
    email: string
    phone: string
  }
  /**
   * @description 当前用户修改密码
   * @export
   * @param {string} oldPassword - 原密码
   * @param {string} newPassword - 新密码
   * @interface IUpdatePassword
   */
  interface IUpdatePassword {
    oldPassword: string
    newPassword: string
  }
  /**
   * @description 修改用户角色信息
   * @export
   * @param {stringOrNumber} id - 用户id
   * @param {Array<stringOrNumber>} roleIds - 用户角色ID列表
   * @interface IUpdateUserRole
   */
  interface IUpdateUserRole {
    id: stringOrNumber
    roleIds: Array<stringOrNumber>
  }

  /**
   * @description 获取当前用户信息
   * @export
   * @param {stringOrNumber} url - 上一次进入的地址
   * @param {stringOrNumber} namespaceId - 集群id
   * @param {string} namespaceTitle - 集群名称
   * @interface SaveRecords
   */
  interface SaveRecords {
    url: string
    namespaceTitle: any
    namespaceId: any
    deployMode?: stringOrNumber
  }

  type roleInfoList = {
    roleId: number
    tenantId: number
    type: number
    name: string
  }
  // 当前角色列表
  interface RoleInfoListData {
    roleInfoList: Array<roleInfoList>
  }

  /**
   * @description 获取当前租户列表
   * @export
   * @param {string} name - 租户名称
   * @param {number} pageNo - 页
   * @param {number} pageSize - 每页条数
   * @param {any[]} product - 筛序不同系统的数据CE为2
   * @param {number} state - 状态
   * @interface SaveRecords
   */
  interface GetTenantList {
    name: string
    pageNo: number
    pageSize: number
    product: any[]
    state: number
  }
  /**
   * @description 添加租户
   * @export
   * @param {string} director - 负责人
   * @param {string} email - 邮箱
   * @param {string} phone - 手机号
   * @param {any[]} productAndVersionReqList -
   * @param {string} loginName - 登录账号
   * @param {string} password - 密码
   * @param {string} tenantName - 租户名称
   * @interface AddTenant
   */
  interface AddTenant {
    director: string
    email: string
    phone: string
    productAndVersionReqList: any[]
    loginName: string
    password: string
    tenantName: string
  }
  /**
   * @description 编辑租户
   * @export
   * @param {string} director - 负责人
   * @param {string} email - 邮箱
   * @param {string} phone - 手机号
   * @param {any[]} productAndVersionReqList -
   * @param {string} loginName - 登录账号
   * @param {string} password - 密码
   * @param {stringOrNumber} tenantId - 租户id
   * @param {string} tenantName - 租户名称
   * @interface UpdateTenant
   */
  interface UpdateTenant {
    director: string
    email: string
    phone: string
    productAndVersionReqList: any[]
    loginName: string
    password?: string
    tenantId: stringOrNumber
    tenantName: string
  }
  /**
   * @description 变更租户状态
   * @export
   * @param {number} state - 0正常，1停用
   * @param {stringOrNumber} tenantId - 租户id
   * @interface UpdateTenantState
   */
  interface UpdateTenantState {
    state: number
    tenantId: stringOrNumber
  }

  /**
   * @description 重置租户密码
   * @export
   * @param {stringOrNumber} loginName - 登录账号
   * @interface ResetTenantPassword
   */
  interface ResetTenantPassword {
    loginName: stringOrNumber
  }
  /**
   * @description 变更租户状态
   * @export
   * @param {stringOrNumber} tenantId - 租户id
   * @interface DeleteTenant
   */
  interface DeleteTenant {
    tenantId: stringOrNumber
  }
  interface GetTenatPermission {
    roleName: string
    tenantId: stringOrNumber
  }
  interface UpdateTenatPermission {
    k8sCompetence: 0 | 1
    k8sIds: stringOrNumber[]
    tenantId: stringOrNumber
  }
}
