<template>
  <div class="inspection">
    <div class="flex justify-between" style="height: 100%">
      <TableBlock />
      <MessageBlock />
    </div>
  </div>
</template>
<script lang="ts" setup>
import MessageBlock from './components/MessageBlock.vue'
import TableBlock from './components/TableBlock.vue'
const { setBreadList } = useBasicTool()
onMounted(() => {
  setBreadList([
    {
      name: t('replenish.platformInspection')
    }
  ])
})
</script>
<style lang="scss" scoped>
.inspection {
  overflow: hidden;
  height: 100%;
}
</style>
