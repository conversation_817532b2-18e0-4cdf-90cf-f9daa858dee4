{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/login/src/index.vue"], "sourcesContent": ["<template>\n  <div class=\"cyber-login\">\n    <div v-if=\"productKey === 'UserCenter'\" ref=\"imgBox\" class=\"login-img\">\n      <el-carousel trigger=\"click\" arrow=\"never\" :interval=\"5000\">\n        <el-carousel-item v-for=\"el in carouselList\" :key=\"el.title\">\n          <div class=\"login-img\">\n            <el-image :src=\"bgImg[el.title]\" />\n            <div class=\"content\">\n              <div class=\"title\">{{ el.title }}</div>\n              <div class=\"desc\">{{ el.desc }}</div>\n            </div>\n          </div>\n        </el-carousel-item>\n      </el-carousel>\n    </div>\n    <div v-else ref=\"imgBox\" class=\"login-img\">\n      <el-image :src=\"bgImg[productKey]\" />\n      <div class=\"content\">\n        <div class=\"title\">\n          {{ title || productKey }}\n        </div>\n        <div class=\"desc\">\n          {{ productDesc || config?.[locale]?.[productKey]?.desc }}\n        </div>\n      </div>\n    </div>\n    <div class=\"login-content\">\n      <div class=\"login-content-logo\">\n        <el-image\n          v-show=\"showLogo\"\n          style=\"width: auto; height: 48px\"\n          :src=\"logo || logoImg\"\n        />\n        <div v-show=\"showLogo\" class=\"login-content-logo-desc\">\n          {{ logoDesc }}\n        </div>\n      </div>\n      <div class=\"login-content-form\">\n        <div v-if=\"isRegister\" class=\"form-title\">\n          <div>{{ config[locale].register }}</div>\n        </div>\n        <div v-else class=\"form-title\">\n          <div>Hello!</div>\n          <div>{{ config[locale].welcome }}</div>\n        </div>\n        <el-form\n          v-if=\"!autoLogin\"\n          ref=\"formRef\"\n          :model=\"loginForm\"\n          :rules=\"rules\"\n        >\n          <el-form-item prop=\"name\">\n            <el-input\n              v-model=\"loginForm.name\"\n              link\n              autocomplete=\"off\"\n              :disabled=\"loading\"\n              :placeholder=\"config[locale].usernameP\"\n              @keyup.enter=\"Login()\"\n            >\n              <template #prepend>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n                  t=\"1701420180107\"\n                  class=\"icon\"\n                  viewBox=\"0 0 1024 1024\"\n                  version=\"1.1\"\n                  p-id=\"11971\"\n                  width=\"16\"\n                  height=\"16\"\n                >\n                  <path\n                    d=\"M444.48 563.264h135.04A316.48 316.48 0 0 1 896 879.744a63.36 63.36 0 0 1-63.36 63.296H191.36a63.36 63.36 0 0 1-63.36-63.296 316.48 316.48 0 0 1 316.48-316.48zM512 499.968c-123.712 0-224-99.2-224-221.568C288 156.16 388.288 56.96 512 56.96s224 99.2 224 221.504c0 122.368-100.288 221.568-224 221.568z\"\n                    p-id=\"11972\"\n                    fill=\"#545C71\"\n                  />\n                </svg>\n              </template>\n            </el-input>\n          </el-form-item>\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"loginForm.password\"\n              :type=\"showPwd ? 'text' : 'password'\"\n              autocomplete=\"off\"\n              :placeholder=\"config[locale].passwordP\"\n              :disabled=\"loading\"\n              @keyup.enter=\"Login()\"\n            >\n              <template #prepend>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n                  t=\"1701420415373\"\n                  class=\"icon\"\n                  viewBox=\"0 0 1024 1024\"\n                  version=\"1.1\"\n                  p-id=\"12358\"\n                  width=\"16\"\n                  height=\"16\"\n                >\n                  <path\n                    d=\"M832 960H192c-35.328 0-64-26.688-64-59.776v-448c0-33.024 28.672-59.712 64-59.712h640c35.328 0 64 26.688 64 59.712v448c0 33.088-28.672 59.776-64 59.776M512 541.888c-52.992 0-96 40.128-96 89.6 0 33.088 19.392 61.696 48 77.184v85.312c0 25.728 21.504 46.72 48 46.72a47.36 47.36 0 0 0 48-46.72v-85.312c28.608-15.488 48-44.096 48-77.184 0-49.472-43.008-89.6-96-89.6M512 153.6c-105.984 0-192 80.192-192 179.2H224C224 184.32 352.896 64 512 64s288 120.32 288 268.8H704c0-99.008-86.016-179.2-192-179.2\"\n                    p-id=\"12359\"\n                    fill=\"#545C71\"\n                  />\n                </svg>\n              </template>\n              <template #suffix>\n                <svg\n                  v-if=\"showPwd\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n                  t=\"1701420495142\"\n                  class=\"icon\"\n                  viewBox=\"0 0 1024 1024\"\n                  version=\"1.1\"\n                  p-id=\"12713\"\n                  width=\"16\"\n                  height=\"16\"\n                  @click=\"() => (showPwd = false)\"\n                >\n                  <path\n                    d=\"M512 147.236571c197.485714 0 358.4 104.228571 482.816 312.685715l11.995429 20.48a64 64 0 0 1 0 63.122285C880.859429 765.805714 715.922286 876.836571 512 876.836571c-197.485714 0-358.4-104.301714-482.816-312.758857l-11.995429-20.48a64 64 0 0 1 0-63.122285C143.140571 258.194286 308.077714 147.236571 512 147.236571z m0 95.963429c-155.501714 0-283.209143 78.994286-388.900571 247.222857L109.933714 512l1.682286 2.925714c104.301714 174.811429 230.034286 259.949714 383.780571 265.508572l16.603429 0.365714c155.501714 0 283.209143-78.994286 388.900571-247.222857l13.165715-21.650286-1.682286-2.779428c-104.301714-174.811429-230.034286-260.022857-383.780571-265.654858L512 243.2zM351.963429 512a160.036571 160.036571 0 1 0 320.073142 0 160.036571 160.036571 0 0 0-320.073142 0z\"\n                    p-id=\"12714\"\n                    fill=\"rgba(41, 52, 78, 0.4)\"\n                  />\n                </svg>\n                <svg\n                  v-else\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n                  t=\"1701420602146\"\n                  class=\"icon\"\n                  viewBox=\"0 0 1024 1024\"\n                  version=\"1.1\"\n                  p-id=\"13068\"\n                  width=\"16\"\n                  height=\"16\"\n                  @click=\"() => (showPwd = true)\"\n                >\n                  <path\n                    d=\"M161.92 104.704a25.6 25.6 0 0 1 18.112 7.488l257.92 257.92a160 160 0 0 1 215.872 215.936l257.984 257.92a25.6 25.6 0 0 1 0 36.224l-31.616 31.616a25.6 25.6 0 0 1-36.224 0L112.192 180.032a25.6 25.6 0 0 1 0-36.224l31.616-31.616a25.6 25.6 0 0 1 18.112-7.488z m-10.624 182.208l67.84 67.84c-32 32.64-62.08 71.744-90.24 117.312l-13.888 23.296L105.6 512l9.408 16.64c97.088 168.32 217.664 255.68 364.032 266.88l16.384 0.96 16.576 0.32c45.12 0 87.872-6.848 128.384-20.672l73.984 73.984A483.2 483.2 0 0 1 512 892.8c-211.584 0-380.544-122.048-503.424-358.656a48 48 0 0 1 0-44.288c42.24-81.28 89.856-148.992 142.72-202.88v-0.064zM512 131.2c211.584 0 380.544 122.048 503.424 358.656a48 48 0 0 1 0 44.288c-42.24 81.216-89.792 148.928-142.72 202.88l-67.84-67.84c32-32.64 62.08-71.68 90.24-117.248l13.888-23.296 9.344-16.64-9.344-16.64c-97.088-168.32-217.664-255.68-364.032-266.88l-16.384-0.96L512 227.2c-45.12 0-87.872 6.848-128.384 20.672L309.632 173.888A483.2 483.2 0 0 1 512 131.2z\"\n                    p-id=\"13069\"\n                    fill=\"rgba(41, 52, 78, 0.4)\"\n                  />\n                </svg>\n              </template>\n            </el-input>\n          </el-form-item>\n          <template v-if=\"isRegister\">\n            <el-form-item prop=\"phone\">\n              <el-input\n                v-model=\"loginForm.phone\"\n                link\n                autocomplete=\"off\"\n                :disabled=\"loading\"\n                :placeholder=\"config[locale].phoneP\"\n                @keyup.enter=\"Login()\"\n              >\n                <template #prepend>\n                  <svg\n                    t=\"1745217311571\"\n                    class=\"icon\"\n                    viewBox=\"0 0 1024 1024\"\n                    version=\"1.1\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    p-id=\"18991\"\n                    width=\"48\"\n                    height=\"48\"\n                  >\n                    <path\n                      d=\"M432.394737 591.587288C331.530931 490.723482 234.497396 373.261835 281.099028 327.298582c63.837852-63.837852 123.845433-107.247591 6.383785-255.351408S92.138987 40.028248 27.662756 104.504479C-46.389152 178.556387 23.832485 454.335907 296.420113 727.561913s549.005526 342.809264 623.057434 268.757356c63.837852-63.837852 176.192471-142.35841 30.642169-259.820057s-187.044906-60.007581-255.351408 6.383785c-46.601632 45.963253-163.424901-50.431903-264.288706-151.295709z m0 0\"\n                      fill=\"#545C71\"\n                      p-id=\"18992\"\n                    />\n                  </svg>\n                </template>\n              </el-input>\n            </el-form-item>\n            <el-form-item prop=\"email\">\n              <el-input\n                v-model=\"loginForm.email\"\n                link\n                autocomplete=\"off\"\n                :disabled=\"loading\"\n                :placeholder=\"config[locale].emailP\"\n                @keyup.enter=\"Login()\"\n              >\n                <template #prepend>\n                  <svg\n                    t=\"1745217477962\"\n                    class=\"icon\"\n                    viewBox=\"0 0 1024 1024\"\n                    version=\"1.1\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    p-id=\"20617\"\n                    width=\"48\"\n                    height=\"48\"\n                  >\n                    <path\n                      d=\"M1022.08 716.928V300.48l-217.28 217.344 217.28 199.04M655.36 667.2a188.16 188.16 0 0 1-133.888 55.488c-50.56 0-98.176-19.712-133.952-55.488L270.976 550.592 20.864 779.84v38.4c0 50.24 40.832 91.072 91.008 91.072h819.2c50.176 0 91.008-40.832 91.008-91.008v-39.68l-249.536-228.608-117.12 117.12\"\n                      fill=\"#545C71\"\n                      p-id=\"20618\"\n                    />\n                    <path\n                      d=\"M932.928 128h-819.2C63.616 128 22.848 168.832 22.848 219.008v19.008l398.784 398.912c27.2 27.136 63.36 42.112 101.76 42.112 38.464 0 74.624-14.976 101.76-42.112L1024 238.016v-19.008C1024 168.832 983.04 128 932.928 128M20.864 300.48v417.6L238.72 518.4 20.864 300.48\"\n                      fill=\"#545C71\"\n                      p-id=\"20619\"\n                    />\n                  </svg>\n                </template>\n              </el-input>\n            </el-form-item>\n          </template>\n          <el-form-item\n            v-if=\"!isRegister && useCode\"\n            prop=\"code\"\n            class=\"code-form-item\"\n          >\n            <el-input\n              v-model=\"loginForm.code\"\n              link\n              autocomplete=\"off\"\n              :disabled=\"loading\"\n              :placeholder=\"config[locale].codeP\"\n              @keyup.enter=\"Login()\"\n            >\n              <template #prepend>\n                <svg\n                  width=\"16px\"\n                  height=\"16px\"\n                  viewBox=\"0 0 16 16\"\n                  version=\"1.1\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n                >\n                  <g\n                    id=\"页面-1\"\n                    stroke=\"none\"\n                    stroke-width=\"1\"\n                    fill=\"none\"\n                    fill-rule=\"evenodd\"\n                  >\n                    <g id=\"验证码\">\n                      <rect\n                        id=\"矩形\"\n                        opacity=\"0\"\n                        x=\"0\"\n                        y=\"0\"\n                        width=\"16\"\n                        height=\"16\"\n                      />\n                      <path\n                        id=\"形状\"\n                        d=\"M7.46916366,0.998385906 C6.78171341,1.60346587 6.04199219,2.10400391 5.25,2.5 C4.44042969,2.90478516 3.52162536,3.14571929 2.49358702,3.2228024 C2.07311192,3.25228729 1.7478524,3.60315613 1.75,4.0246582 L1.75,8 L1.75,8 C1.75,10.8278809 3.60113661,13.1374435 7.30340983,14.9286878 L7.30336224,14.9287862 C7.74340515,15.1416123 8.25659485,15.1416123 8.69663776,14.9287862 C12.3988634,13.1374435 14.25,10.8278809 14.25,8 L14.25,4.0246582 C14.2521476,3.60315613 13.9268881,3.25228729 13.506413,3.2228024 C12.4783746,3.14571929 11.5595703,2.90478516 10.75,2.5 C9.95605469,2.10302734 9.21463847,1.60099649 8.52575135,0.993907452 L8.52475913,0.995038829 C8.22215091,0.729651247 7.76949016,0.730410711 7.46777416,0.996812215 Z M11.94,6.68 L7.64,10.74 C7.44,10.92 7.16,10.96 6.92,10.82 L6.8,10.72 L4.42,8.24 C4.2,8 4.22,7.62 4.46,7.4 C4.7,7.18 5.08,7.2 5.3,7.44 L7.26,9.5 L11.14,5.82 C11.38,5.6 11.76,5.62 11.98,5.86 C12.2,6.1 12.18,6.48 11.94,6.7 L11.94,6.68 L11.94,6.68 Z\"\n                        fill=\"#545C71\"\n                      />\n                    </g>\n                  </g>\n                </svg>\n              </template>\n            </el-input>\n            <div class=\"code-img\" @click=\"getLoginCode\">\n              <el-icon v-if=\"codeLoading\" class=\"el-icon is-loading\"\n                ><Loading\n              /></el-icon>\n\n              <el-image v-else :src=\"codeImg\" />\n            </div>\n          </el-form-item>\n          <el-form-item>\n            <el-button\n              v-if=\"isRegister\"\n              type=\"primary\"\n              :loading=\"loading\"\n              @click=\"Login()\"\n            >\n              {{ config[locale].register }}\n            </el-button>\n            <el-button\n              v-else\n              type=\"primary\"\n              :loading=\"loading\"\n              @click=\"Login()\"\n            >\n              {{ config[locale].login }}\n            </el-button>\n          </el-form-item>\n        </el-form>\n        <div v-else class=\"flex\">\n          <div v-loading=\"autoLoginLoading\" style=\"height: 40px; width: 40px\" />\n          <div class=\"mll\">自动登录中</div>\n        </div>\n        <div\n          v-if=\"supportRegister\"\n          class=\"support-register\"\n          @click=\"goRegister\"\n        >\n          <div v-if=\"isRegister\" class=\"w100 text-center\">\n            {{ config[locale].loginText }}\n          </div>\n          <div v-else class=\"w100 text-center\">\n            {{ config[locale].registerText }}\n          </div>\n        </div>\n      </div>\n      <div class=\"login-content-bottom\">{{ bottomDesc }}</div>\n    </div>\n    <div class=\"login-switch-language\">\n      <div v-if=\"useLocale\" class=\"switch-language\">\n        <el-tooltip\n          :hide-after=\"200\"\n          effect=\"light\"\n          :content=\"'Switch to English'\"\n          placement=\"bottom\"\n          popper-class=\"login-popper lang-tolltip\"\n        >\n          <svg\n            t=\"1729158173316\"\n            class=\"icon\"\n            viewBox=\"0 0 1024 1024\"\n            version=\"1.1\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            p-id=\"13545\"\n            xmlns:xlink=\"http://www.w3.org/1999/xlink\"\n            width=\"24\"\n            height=\"24\"\n            @click=\"updateLanguage\"\n          >\n            <path\n              d=\"M0 512c0 282.752 229.248 512 512 512s512-229.248 512-512S794.752 0 512 0 0 229.248 0 512z\"\n              fill=\"#FFFFFF\"\n              p-id=\"13546\"\n            />\n            <path\n              d=\"M512 213.333333a298.666667 298.666667 0 1 1 0 597.333334 298.666667 298.666667 0 0 1 0-597.333334z m27.264 44.117334l3.84 3.328c9.557333 8.96 14.336 17.322667 14.336 25.258666 0 27.136-27.136 18.090667-27.136 27.136 0 9.045333 0 45.226667-18.090667 54.272-12.458667 6.186667-29.44 6.186667-33.962666 13.952-4.522667 7.722667 4.522667 16.597333 15.488 19.882667 10.965333 3.328 27.52 11.349333 36.565333-6.741333 9.045333-18.090667 45.226667-9.045333 36.181333 9.045333-9.045333 18.090667-36.181333 45.226667-36.181333 63.317333 0 4.266667 1.024 9.514667 2.176 15.061334l1.792 8.490666c3.157333 15.573333 4.565333 30.72-13.013333 30.72-27.136 0-45.226667-45.226667-63.317334-45.226666-18.090667 0-36.181333 0-36.181333 27.136 0 14.378667-5.973333 59.818667-24.490667 66.56-18.517333 6.826667-26.069333-19.029333-48.725333-28.16-19.2-7.68-20.778667-41.386667-20.778667-68.522667 0-27.136-11.093333-29.866667-26.666666-24.448-12.288 4.266667-23.765333 18.901333-41.386667 20.437333a256 256 0 0 0 484.906667 150.101334c-23.936 21.76-51.797333 21.973333-51.797334 4.949333 0-27.989333-12.202667-37.333333-30.890666-74.666667-18.645333-37.333333-1.408-77.568 15.786666-93.568 21.632-20.053333 28.586667-15.658667 33.024-23.978666 4.48-8.277333-5.632-25.173333 8.405334-34.688a23.722667 23.722667 0 0 1 19.541333-4.181334 256.170667 256.170667 0 0 0-199.424-135.466666z\"\n              fill=\"#545C71\"\n              p-id=\"13547\"\n            />\n            <path\n              d=\"M397.226667 585.301333c4.181333-23.210667 47.530667-30.250667 74.453333-25.173333 26.922667 5.12 31.914667 31.402667 56.746667 39.893333 24.874667 8.533333 24.874667 39.936 32.64 39.936 7.722667 0 33.706667-9.898667 41.642666 11.392 7.936 21.290667-19.882667 26.88-33.621333 47.829334-13.781333 21.034667-8.021333 58.069333-32.170667 62.293333-24.106667 4.181333-26.666667-20.650667-37.717333-35.84-11.093333-15.104-57.258667-20.096-67.584-59.52s7.082667-46.293333-3.242667-57.6c-10.325333-11.221333-35.242667 0-31.146666-23.210667z\"\n              fill=\"#545C71\"\n              opacity=\".5\"\n              p-id=\"13548\"\n            />\n          </svg>\n          <template #content>\n            <div\n              v-for=\"item in showLangs\"\n              :key=\"item.value\"\n              class=\"lang-item\"\n              :class=\"{ active: locale === item.value }\"\n              @click=\"updateLanguage(item.value)\"\n            >\n              {{ item.label }}\n            </div>\n          </template>\n        </el-tooltip>\n      </div>\n    </div>\n    <el-dialog\n      v-model=\"dialogVisible\"\n      :title=\"config[locale].dialogTitle\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div class=\"content\">\n        <div\n          v-for=\"el in enterList\"\n          :key=\"el.title\"\n          class=\"item\"\n          :class=\"{ disabled: !roleList.includes(el.role) }\"\n          @click=\"goDetail(el)\"\n        >\n          <div v-if=\"useProductKey === el.title\" class=\"is-use\">最近使用</div>\n          <el-image :src=\"bgImg[el.img]\" />\n          <div class=\"title\">{{ el.title }}</div>\n          <div v-if=\"recentlyUsed === el.title\" class=\"recently-used\">\n            {{ config[locale].recentlyUsed }}\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, nextTick, onMounted, reactive, ref, watch } from \"vue\";\nimport {\n  ElButton,\n  ElCarousel,\n  ElCarouselItem,\n  ElDialog,\n  ElForm,\n  ElFormItem,\n  ElIcon,\n  ElImage,\n  ElInput,\n  ElMessage,\n  ElTooltip,\n} from \"element-plus\";\nimport { Loading } from \"@element-plus/icons-vue\";\nimport config from \"./constants\";\nimport { decrypt, encrypt, getQueryParam } from \"./encryption\";\nimport CyberAI from \"./images/CyberAI.jpg\";\nimport CyberAIEnter from \"./images/CyberAIEnter.png\";\nimport CyberData from \"./images/CyberData.jpg\";\nimport CyberDataEnter from \"./images/CyberDataEnter.png\";\nimport CyberEngine from \"./images/CyberEngine.jpg\";\nimport CyberEngineEnter from \"./images/CyberEngineEnter.png\";\nimport logoImg from \"./images/logo.png\";\nimport type { PropType } from \"vue\";\nimport type { DcLocaleType } from \"../../types\";\nimport type { DcLoginProductKey } from \"./types\";\n\nconst bgImg = {\n  CyberData,\n  CyberEngine,\n  CyberAI,\n  CyberDataEnter,\n  CyberEngineEnter,\n  CyberAIEnter,\n};\n\ndefineOptions({\n  name: \"DcLogin\",\n});\n\nconst props = defineProps({\n  productKey: {\n    type: String as PropType<DcLoginProductKey>,\n    default: \"UserCenter\",\n  },\n  useLocale: {\n    type: Boolean,\n    default: true,\n  },\n  baseUrl: {\n    type: String,\n    default: \"\",\n  },\n  locale: {\n    type: String as PropType<DcLocaleType>,\n    default: \"zh-CN\",\n  },\n  title: {\n    type: String,\n    default: \"\",\n  },\n  logo: {\n    type: String,\n    default: \"\",\n  },\n  logoDesc: {\n    type: String,\n    default: \"\",\n  },\n  bottomDesc: {\n    type: String,\n    default: \"\",\n  },\n  showLogo: {\n    type: Boolean,\n    default: true,\n  },\n  autoLogin: {\n    type: Boolean,\n    default: false,\n  },\n  autoLoginLoading: {\n    type: Boolean,\n    default: false,\n  },\n  productDesc: {\n    type: String,\n    default: \"\",\n  },\n  supportLanguages: {\n    type: Array as PropType<DcLocaleType[]>,\n    default: () => [\"zh-CN\", \"en-US\", \"ja-JP\"],\n  },\n  // 多点登录\n  multipointLogin: {\n    type: Boolean,\n    default: false,\n  },\n});\n\nconst langs: {\n  label: string;\n  value: DcLocaleType;\n}[] = [\n  {\n    label: \"中文\",\n    value: \"zh-CN\",\n  },\n  {\n    label: \"English\",\n    value: \"en-US\",\n  },\n  {\n    label: \"日本語\",\n    value: \"ja-JP\",\n  },\n];\n\nconst showLangs = computed(() =>\n  langs.filter((el) => props.supportLanguages.includes(el.value))\n);\n\nconst formRef = ref();\n\nconst loginForm = reactive({\n  name: \"\",\n  password: \"\",\n  code: \"\",\n  uuid: \"\",\n  phone: \"\",\n  email: \"\",\n});\n\nconst loading = ref(false);\n\nconst showPwd = ref(false);\n\nconst dialogVisible = ref(false);\n\nconst recentlyUsed = localStorage.getItem(\"recentlyUsed\") || \"\";\n\nconst roleList = ref([\"1\", \"2\", \"3\", \"4\"]);\n\nconst requestUrl = computed(\n  () => props.baseUrl || decrypt(\"eBzuUdcdYxzfOLTq5UqbLHy5QHwlhli67obxiHGdOYA=\")\n);\n\nconst cdUrlObj = {\n  dev: \"eBzuUdcdYxzfOLTq5UqbLAiOzyyWdZ+iE5rmaWy0BrBObVlg/6nTL/LEijtwH9nb\",\n  test: \"eBzuUdcdYxzfOLTq5UqbLC/Q854Xs7wfzMBpNp2pNMoVK+6WxBP0Fet9slOOfhy4\",\n  prod: \"dZPIhU9h4zdgwsGaJYmnoW3UbBbXkSdhsLs0x+mmD+NweqN2wRaAcarRZvVUyqNm\",\n  demo: \"dZPIhU9h4zdgwsGaJYmnobzvcsCnAhTEpm35xhCkHPtIl/7CKzpbfmk+uIqHv34V\",\n};\n\nconst ceUrlObj = {\n  dev: \"rqSPgr+4lfpsb05cvi5KSFPDL2tOe2VImhlCmVVpL3K8vyXqSemE/hP+p5yBLfx9\",\n  test: \"eBzuUdcdYxzfOLTq5UqbLNYeC7g0VLatoxfLKJZ63/TyIP5XISvp/WyTrZcQ4c/z\",\n  prod: \"Qg3klVfA4KId6+KSbylvvWpuhVXWabZsVQ+GSTathKf84tHWDfNKNI91IaP4Efmz\",\n  demo: \"Qg3klVfA4KId6+KSbylvvQ54CSTgmaXTXXrcTfqfBnNHt3nSD4QrLThb1dUE0rja\",\n};\n\nconst ciUrlObj = {\n  dev: \"eBzuUdcdYxzfOLTq5UqbLNOzwL6vS+EKrG11bRujh82rvcL6xrmUInR0QMIZ5NGm\",\n  test: \"eBzuUdcdYxzfOLTq5UqbLNOzwL6vS+EKrG11bRujh82rvcL6xrmUInR0QMIZ5NGm\",\n  prod: \"dO8hWz7j+ph+K7FrYE7BsLXMGLcmD5er9RTOEcE6qnBDkyrkElwTabLOKKsojh8+\",\n  demo: \"dO8hWz7j+ph+K7FrYE7BsCnLRfSX6l5VtbjbhydzYL6lgEIWkHdmIqAk/r3VIW41\",\n};\n\nconst rules = computed(() => {\n  const rule = {\n    code: [\n      {\n        required: true,\n        message: config[props.locale].codeR,\n        trigger: \"blur\",\n      },\n    ],\n    phone: [\n      {\n        required: true,\n        trigger: \"blur\",\n        message: config[props.locale].phoneP,\n      },\n      {\n        pattern:\n          /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$/,\n        message: config[props.locale].phoneR,\n      },\n    ],\n    email: [\n      {\n        required: true,\n        trigger: \"blur\",\n        message: config[props.locale].emailP,\n      },\n      {\n        pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\\.[a-zA-Z0-9_-])+/,\n        message: config[props.locale].emailR,\n      },\n    ],\n  };\n  if (isRegister.value) {\n    return {\n      ...rule,\n      name: [\n        {\n          required: true,\n          message: config[props.locale].usernameR,\n          trigger: \"blur\",\n        },\n        {\n          pattern: /^[0-9a-zA-Z-_]{6,16}$/,\n          message: config[props.locale].usernameRule,\n        },\n      ],\n      password: [\n        {\n          required: true,\n          message: config[props.locale].passwordR,\n          trigger: \"blur\",\n        },\n        {\n          pattern: /^[0-9a-zA-Z-_]{6,16}$/,\n          message: config[props.locale].usernameRule,\n        },\n      ],\n    };\n  } else {\n    return {\n      ...rule,\n      name: [\n        {\n          required: true,\n          message: config[props.locale].usernameR,\n          trigger: \"blur\",\n        },\n      ],\n      password: [\n        {\n          required: true,\n          message: config[props.locale].passwordR,\n          trigger: \"blur\",\n        },\n      ],\n    };\n  }\n});\n\nconst carouselList: {\n  title: \"CyberEngine\" | \"CyberData\" | \"CyberAI\";\n  desc: string;\n}[] = [\n  {\n    title: \"CyberEngine\",\n    desc: config[props.locale].CyberEngine.desc,\n  },\n  {\n    title: \"CyberData\",\n    desc: config[props.locale].CyberData.desc,\n  },\n  {\n    title: \"CyberAI\",\n    desc: config[props.locale].CyberAI.desc,\n  },\n];\n\nconst enterList = ref<\n  {\n    img: \"CyberEngineEnter\" | \"CyberDataEnter\" | \"CyberAIEnter\";\n    title: \"CyberEngine\" | \"CyberData\" | \"CyberAI\";\n    role: \"1\" | \"2\" | \"3\" | \"4\";\n    url: string;\n  }[]\n>([\n  {\n    img: \"CyberEngineEnter\",\n    title: \"CyberEngine\",\n    role: \"2\",\n    url: \"\",\n  },\n  {\n    img: \"CyberDataEnter\",\n    title: \"CyberData\",\n    role: \"3\",\n    url: \"\",\n  },\n  {\n    img: \"CyberAIEnter\",\n    title: \"CyberAI\",\n    role: \"4\",\n    url: \"\",\n  },\n]);\n\nconst useProductKey = sessionStorage.getItem(\"productKey\");\n\nconst emit = defineEmits([\"updateLanguage\", \"loginSuccess\"]);\n\nconst imgBox = ref();\n\nconst codeImg = ref();\n\nconst codeLoading = ref(false);\n\nconst useCode = ref(false);\n\nconst isRegister = ref(false);\n\nconst gcpToken = getQueryParam(\"gcpToken\");\n\nconst supportRegister = computed(() => !!gcpToken);\n\nwatch(\n  () => props.locale,\n  () => {\n    window.location.reload();\n  }\n);\n\nwatch(\n  () => props.autoLoginLoading,\n  (newVal) => {\n    console.log(\"autoLoginLoading\", newVal);\n    setTimeout(() => {\n      loading.value = newVal;\n    });\n  }\n);\n\nconst updateLanguage = (l: DcLocaleType) => {\n  if (l === props.locale) return;\n  emit(\"updateLanguage\", l);\n};\n\nconst goRegister = () => {\n  isRegister.value = !isRegister.value;\n  nextTick(() => {\n    setTimeout(() => {\n      formRef.value.clearValidate();\n    });\n  });\n};\n\nconst setHeader = (useToken = false) => {\n  const headers = new Headers();\n  headers.append(\"Content-Type\", \"application/json\");\n  if (useToken) {\n    headers.append(\"JwtToken\", sessionStorage.getItem(\"jwtToken\") || \"\");\n  }\n  headers.append(\"lang\", props.locale);\n  if (props.productKey === \"UserCenter\") {\n    headers.append(\"ProductKey\", \"CyberData,CyberEngine,CyberAI\");\n  } else {\n    headers.append(\"ProductKey\", props.productKey);\n  }\n  return (\n    headers || {\n      \"Content-Type\": \"application/json\",\n      ProductKey:\n        props.productKey === \"UserCenter\"\n          ? [\"CyberData\", \"CyberAI\", \"CyberEngine\"]\n          : props.productKey,\n      ...(useToken ? { JwtToken: sessionStorage.getItem(\"jwtToken\") } : {}),\n    }\n  );\n};\n\nconst getLoginCode = () => {\n  if (codeLoading.value) return;\n  codeLoading.value = true;\n  fetch(`${requestUrl.value}/captcha`, {\n    method: \"GET\",\n    headers: setHeader(),\n  })\n    .then((res) => {\n      return res.json();\n    })\n    .then((res) => {\n      console.log(res);\n      if (res.code === \"200\") {\n        codeImg.value = res.data.base64Img;\n        loginForm.uuid = res.data.captchaKey;\n      } else {\n        ElMessage.error(res.msg);\n      }\n    })\n    .catch((err) => {})\n    .finally(() => (codeLoading.value = false));\n};\n\nconst getCaptchaEnabled = () => {\n  fetch(`${requestUrl.value}/auth/captchaEnabled`, {\n    method: \"GET\",\n    headers: setHeader(),\n  })\n    .then((res) => {\n      return res.json();\n    })\n    .then((res) => {\n      console.log(res);\n      if (res.code === \"200\") {\n        useCode.value = res.data;\n        if (useCode.value) {\n          getLoginCode();\n        }\n      }\n    })\n    .catch((err) => {});\n};\n\nconst getUserInfo = () => {\n  fetch(`${requestUrl.value}/user/getUserInfo`, {\n    method: \"POST\",\n    headers: setHeader(true),\n  })\n    .then((res) => {\n      return res.json();\n    })\n    .then((res) => {\n      console.log(res);\n      if (res.code === \"200\") {\n        roleList.value = res.data.version;\n        if (roleList.value.length === 1) {\n          const url = `${\n            enterList.value[Number(roleList.value[0]) - 2].url\n          }?jwtToken=${sessionStorage.getItem(\"jwtToken\")}`;\n          sessionStorage.setItem(\n            \"productKey\",\n            enterList.value[Number(roleList.value[0]) - 2].title\n          );\n          window.open(url, \"_self\");\n        } else {\n          dialogVisible.value = true;\n        }\n      } else {\n        ElMessage.error(res.msg);\n      }\n    })\n    .catch((err) => {});\n};\n\nconst registerRequest = () => {\n  loading.value = true;\n  fetch(`${requestUrl.value}/marketplace/register`, {\n    method: \"POST\",\n    headers: setHeader(),\n    body: JSON.stringify({\n      loginName: loginForm.name,\n      aliasName: loginForm.name,\n      tenantName: loginForm.name,\n      password: encrypt(loginForm.password),\n      phone: loginForm.phone,\n      email: loginForm.email,\n      jwtToken: gcpToken,\n    }),\n  })\n    .then((res) => {\n      return res.json();\n    })\n    .then((res) => {\n      console.log(res);\n      if (res.code === \"200\") {\n        ElMessage.success(config[props.locale].registerSu);\n        setTimeout(() => {\n          isRegister.value = false;\n          formRef.value.resetFields();\n        }, 100);\n      } else {\n        ElMessage.error(res.msg);\n        if (useCode.value) {\n          getLoginCode();\n        }\n      }\n    })\n    .catch((err) => {})\n    .finally(() => (loading.value = false));\n};\n\nconst loginRequest = () => {\n  loading.value = true;\n  const url = useCode.value ? \"/auth/loginWithCaptcha\" : \"/auth/login\";\n  fetch(`${requestUrl.value}${url}`, {\n    method: \"POST\",\n    headers: setHeader(),\n    body: useCode.value\n      ? JSON.stringify({\n          loginName: loginForm.name,\n          password: encrypt(loginForm.password),\n          code: loginForm.code,\n          uuid: loginForm.uuid,\n          multipointLogin: props.multipointLogin,\n        })\n      : JSON.stringify({\n          loginName: loginForm.name,\n          password: encrypt(loginForm.password),\n          multipointLogin: props.multipointLogin,\n        }),\n  })\n    .then((res) => {\n      return res.json();\n    })\n    .then((res) => {\n      console.log(res);\n      if (res.code === \"200\") {\n        const jwtToken = res?.data?.jwtToken;\n        sessionStorage.setItem(\"jwtToken\", jwtToken);\n        if (props.productKey !== \"UserCenter\") {\n          emit(\"loginSuccess\", {\n            title: props.productKey,\n            jwtToken,\n            loginName: loginForm.name,\n            password: loginForm.password,\n          });\n        } else {\n          getUserInfo();\n        }\n      } else {\n        ElMessage.error(res.msg);\n        if (useCode.value) {\n          getLoginCode();\n        }\n      }\n    })\n    .catch((err) => {})\n    .finally(() => (loading.value = false));\n};\n\nconst Login = () => {\n  formRef.value.validate((f: boolean) => {\n    if (!f) return;\n    if (isRegister.value) {\n      registerRequest();\n    } else {\n      loginRequest();\n    }\n  });\n};\n\nconst goDetail = (el: {\n  img: \"CyberEngineEnter\" | \"CyberDataEnter\" | \"CyberAIEnter\";\n  title: \"CyberEngine\" | \"CyberData\" | \"CyberAI\";\n  role: \"1\" | \"2\" | \"3\" | \"4\";\n  url: string;\n}) => {\n  if (!roleList.value.includes(el.role)) return;\n  sessionStorage.setItem(\"productKey\", el.title);\n  const url = `${el.url}?jwtToken=${sessionStorage.getItem(\"jwtToken\")}`;\n  window.open(url, \"_self\");\n};\n\nonMounted(() => {\n  const h = document.documentElement.clientHeight;\n  const bi = 1200 / h;\n  imgBox.value.style.width = `${800 / bi}px`;\n  const port = window.location.port;\n  getCaptchaEnabled();\n  nextTick(() => {\n    imgBox.value.click();\n  });\n  switch (port) {\n    case \"30011\":\n      enterList.value[0].url = decrypt(ceUrlObj.dev);\n      enterList.value[1].url = decrypt(cdUrlObj.dev);\n      enterList.value[2].url = decrypt(ciUrlObj.dev);\n      break;\n    case \"30012\":\n      enterList.value[0].url = decrypt(ceUrlObj.test);\n      enterList.value[1].url = decrypt(cdUrlObj.test);\n      enterList.value[2].url = decrypt(ciUrlObj.test);\n      break;\n    case \"30013\":\n      enterList.value[0].url = decrypt(ceUrlObj.prod);\n      enterList.value[1].url = decrypt(cdUrlObj.prod);\n      enterList.value[2].url = decrypt(ciUrlObj.prod);\n      break;\n    default:\n      enterList.value[0].url = decrypt(ceUrlObj.demo);\n      enterList.value[1].url = decrypt(cdUrlObj.demo);\n      enterList.value[2].url = decrypt(ciUrlObj.demo);\n  }\n  if (window.location.hostname === \"localhost\") {\n    enterList.value[0].url = decrypt(ceUrlObj.dev);\n    enterList.value[1].url = decrypt(cdUrlObj.dev);\n    enterList.value[2].url = decrypt(ciUrlObj.dev);\n  }\n});\n</script>\n\n<style lang=\"scss\">\n:root {\n  --dc-component--color-blue-1: #eef2fe;\n  --dc-component--color-blue-2: #ccd7ff;\n  --dc-component--color-blue-3: #567aff;\n\n  --dc-component--color-red-1: #feefed;\n  --dc-component--color-red-2: #f9beb9;\n  --dc-component--color-red-3: #f05d4f;\n\n  --dc-component--color-yellow-1: #fff7e8;\n  --dc-component--color-yellow-2: #fddea2;\n  --dc-component--color-yellow-3: #faad14;\n\n  --dc-component--color-green-1: #e5f9ed;\n  --dc-component--color-green-2: #99eab9;\n  --dc-component--color-green-3: #3bbb6e;\n\n  --dc-component--color-text-1: #29344e;\n  --dc-component--color-text-2: rgba(41, 52, 78, 0.85);\n  --dc-component--color-text-3: rgba(41, 52, 78, 0.65);\n  --dc-component--color-text-5: rgba(41, 52, 78, 0.3);\n  --dc-component--color-text-6: rgba(41, 52, 78, 0.4);\n  --dc-component--color-text-7: #545c71;\n\n  --dc-component--color-gray-1: rgba(41, 52, 78, 0.15);\n  --dc-component--color-gray-6: rgba(236, 241, 246, 0.55);\n  --dc-component--color-gray-13: #eff1f5;\n  --dc-component--color-gray-8: #f7f9fc;\n\n  --dc-component--color-title: var(--dc-component--color-text-1);\n  --dc-component--color-text: var(--dc-component--color-text-2);\n  --dc-component--color-icon: var(--dc-component--color-text-7);\n  --dc-component--color-placeholder: var(--dc-component--color-text-6);\n  --dc-component--color-disabled: var(--dc-component--color-text-6);\n  --dc-component--color-border: var(--dc-component--color-gray-1);\n  --dc-component--color-shadow: var(--dc-component--color-gray-1);\n  --dc-component--color-dialog-header-bg: var(--dc-component--color-gray-6);\n\n  --dc-component--color-primary: var(--dc-component--color-blue-3);\n  --dc-component--color-primary-border: var(--dc-component--color-blue-2);\n  --dc-component--color-primary-shadow: var(--dc-component--color-blue-2);\n  --dc-component--color-primary-hover: #708fff;\n  --dc-component--color-primary-active: #4d6ee6;\n}\n\n.cyber-login {\n  height: 100%;\n  display: flex;\n  min-height: 580px;\n  .el-input {\n    .el-input__inner {\n      font-size: 14px;\n    }\n\n    input:-webkit-autofill,\n    textarea:-webkit-autofill,\n    select:-webkit-autofill {\n      background-color: transparent;\n      transition: background-color 50000s ease-in-out 0s;\n    }\n\n    input::-webkit-input-placeholder {\n      color: var(--dc-component--color-placeholder);\n    }\n\n    input:-moz-placeholder {\n      /* Mozilla Firefox 4 to 18 */\n      color: var(--dc-component--color-placeholder);\n    }\n\n    input::-moz-placeholder {\n      /* Mozilla Firefox 19+ */\n      color: var(--dc-component--color-placeholder);\n    }\n  }\n  .mll {\n    margin-left: 16px;\n  }\n  .flex {\n    display: flex;\n    align-items: center;\n  }\n  .login-img {\n    width: 37.5%;\n    height: 100%;\n    background: no-repeat;\n    background-size: 100% 100%;\n    position: relative;\n    min-width: 620px;\n    .el-image {\n      width: 100%;\n      height: 100%;\n    }\n    .el-carousel {\n      height: 100%;\n      .el-carousel__container {\n        height: 100%;\n        .login-img {\n          width: 100%;\n        }\n      }\n      .el-carousel__indicators {\n        bottom: 60px;\n        .el-carousel__indicator {\n          .el-carousel__button {\n            width: 10px;\n            height: 10px;\n            background: var(--dc-component--color-primary);\n            opacity: 0.3;\n            border-radius: 5px;\n          }\n          &.is-active {\n            .el-carousel__button {\n              opacity: 1;\n              width: 40px;\n            }\n          }\n        }\n      }\n    }\n\n    .content {\n      position: absolute;\n      left: 50px;\n      top: 12%;\n      width: calc(100% - 100px);\n      .title {\n        font-size: 32px;\n        font-weight: 600;\n        color: var(--dc-component--color-title);\n        margin-bottom: 12px;\n      }\n      .desc {\n        font-size: 20px;\n        font-weight: 400;\n        color: var(--dc-component--color-text-3);\n      }\n    }\n  }\n\n  .login-content {\n    flex: 1;\n    background: #ffffff;\n    display: flex;\n    overflow: hidden;\n    flex-direction: column;\n    position: relative;\n\n    .login-content-logo {\n      padding: 0 30px;\n      font-size: 24px;\n      font-weight: 600;\n      color: var(--dc-component--color-primary);\n      line-height: 33px;\n      height: 93px;\n      display: flex;\n      align-items: center;\n      .login-content-logo-desc {\n        font-size: 16px;\n        font-weight: 400;\n        color: var(--dc-component--color-text-3);\n        margin-left: 16px;\n      }\n    }\n\n    .login-content-form {\n      width: 340px;\n      margin: auto;\n      transform: translateY(-42px);\n\n      .form-title {\n        margin-bottom: 30px;\n        display: flex;\n        color: var(--dc-component--color-title);\n        div:first-child {\n          font-size: 24px;\n          margin-right: 8px;\n        }\n        div:last-child {\n          font-size: 16px;\n          vertical-align: bottom;\n          padding-top: 7px;\n        }\n      }\n      .support-register {\n        width: 100%;\n        margin-top: -50px;\n        .w100 {\n          width: 100%;\n        }\n        .text-center {\n          text-align: center;\n          cursor: pointer;\n        }\n      }\n\n      .el-form {\n        .el-form-item {\n          margin: 30px 0;\n          &.code-form-item {\n            .el-form-item__content {\n              display: flex;\n              position: relative;\n              &::before {\n                content: \"\";\n                height: 50px;\n                width: 14px;\n                position: absolute;\n                left: 220px;\n                top: -1px;\n                background: #fff;\n                z-index: 100;\n              }\n              .el-input {\n                width: 220px;\n              }\n              .code-img {\n                flex: 1;\n                height: 48px;\n                .el-image {\n                  width: 100%;\n                  height: 48px;\n                  cursor: pointer;\n                  .el-image__error {\n                    padding-left: 14px;\n                    width: calc(100% - 14px);\n                  }\n                }\n                .el-icon {\n                  width: 100%;\n                  height: 100%;\n                }\n              }\n            }\n          }\n          &:last-child {\n            margin-bottom: 100px;\n          }\n          .el-input > .el-input__wrapper:hover {\n            border: 1px solid var(--dc-component--color-primary);\n            padding-left: 47px;\n            padding-right: 10px;\n          }\n          .el-input > .el-input__wrapper.is-focus {\n            border: 2px solid var(--dc-component--color-primary-border);\n            box-shadow: 0 0 0 1px var(--dc-component--color-primary) inset;\n            padding-left: 46px;\n            padding-right: 9px;\n          }\n          &.is-error {\n            .el-input > .el-input__wrapper {\n              border: 1px solid var(--dc-component--color-red-3);\n              padding-left: 47px;\n              padding-right: 10px;\n              &.is-focus {\n                box-shadow: 0 0 0 1px var(--dc-component--color-red-3) inset !important;\n                border: 2px solid var(--dc-component--color-red-2) !important;\n                padding-left: 46px;\n                padding-right: 9px;\n              }\n            }\n          }\n        }\n\n        .el-input {\n          background: #f2f4f9;\n          width: 360px;\n          max-width: none;\n          border-radius: 2px;\n          box-sizing: border-box;\n          height: 48px;\n          overflow: hidden;\n          .el-input-group__prepend {\n            background: transparent;\n            padding-left: 16px;\n            svg {\n              display: inline-flex;\n              align-items: center;\n              width: 16px;\n              height: 16px;\n              fill: currentColor;\n              overflow: hidden;\n              outline: none;\n              flex-shrink: 0;\n              font-size: 16px;\n              color: var(--dc-component--color-icon);\n            }\n          }\n          .el-input__suffix {\n            background: #f2f4f9;\n            svg {\n              display: inline-flex;\n              align-items: center;\n              width: 14px;\n              height: 14px;\n              fill: currentColor;\n              overflow: hidden;\n              outline: none;\n              cursor: pointer;\n              flex-shrink: 0;\n              font-size: 16px;\n              color: var(--dc-component--color-icon);\n            }\n          }\n        }\n\n        .el-input__wrapper {\n          background: transparent;\n          border: none;\n          box-shadow: none;\n          padding-left: 48px;\n          border-radius: 2px;\n        }\n\n        .el-form-item__error {\n          padding-top: 3px;\n        }\n\n        .el-input-group__prepend {\n          padding: 18px 0 18px 19px;\n          height: 44px;\n          box-sizing: border-box;\n          border: none;\n          box-shadow: none;\n          position: absolute;\n          img {\n            width: 14px;\n          }\n        }\n\n        .el-form-item__content .el-input .el-input__inner {\n          height: 44px;\n          line-height: 44px;\n          border: none;\n        }\n\n        .el-form-item__content .el-button {\n          width: 100%;\n          height: 48px;\n          line-height: 48px;\n          border-radius: 2px;\n          border: none;\n          margin-top: 20px;\n          letter-spacing: 2px;\n          background: var(--dc-component--color-primary);\n          font-size: 16px;\n          box-shadow: 0px 4px 10px 0px var(--dc-component--color-primary-shadow);\n          &:hover,\n          &:focus {\n            background: var(--dc-component--color-primary-hover);\n          }\n          &:active {\n            background: var(--dc-component--color-primary-active);\n          }\n        }\n\n        .el-form-item__content .el-button .el-button__text--expand {\n          letter-spacing: 2px;\n        }\n      }\n    }\n    .login-content-bottom {\n      position: absolute;\n      font-size: 16px;\n      font-weight: 400;\n      bottom: 40px;\n      left: 0;\n      color: var(--dc-component--color-title);\n      text-align: center;\n      width: 100%;\n    }\n  }\n  .login-switch-language {\n    position: absolute;\n    right: 30px;\n    top: 25px;\n    svg {\n      box-shadow: 0px 4px 10px 0px var(--dc-component--color-shadow);\n      border-radius: 12px;\n      cursor: pointer;\n    }\n  }\n  .el-overlay {\n    .el-overlay-dialog {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n    .el-dialog {\n      margin: 0;\n      .el-dialog__header {\n        height: 45px;\n        background: var(--dc-component--color-dialog-header-bg);\n        border-radius: 2px 2px 0px 0px;\n        padding: 0;\n        margin: 0;\n        border-bottom: 1px solid var(--dc-component--color-border);\n        .el-dialog__title {\n          font-size: 14px;\n          line-height: 45px;\n          padding-left: 15px;\n          font-weight: 600;\n          color: var(--dc-component--color-title);\n        }\n        .el-dialog__headerbtn {\n          top: 0;\n          height: 45px;\n          width: 45px;\n          &:hover {\n            .el-dialog__close {\n              color: var(--dc-component--color-primary);\n            }\n          }\n        }\n      }\n      .el-dialog__body {\n        padding: 35px 44px 60px 44px;\n        .content {\n          display: flex;\n          justify-content: space-between;\n          .item {\n            width: 160px;\n            height: 160px;\n            background: #ffffff;\n            border-radius: 2px;\n            border: 1px solid var(--dc-component--color-border);\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding-top: 22px;\n            box-sizing: border-box;\n            position: relative;\n            cursor: pointer;\n            .el-image {\n              img {\n                height: 80px;\n                width: 80px;\n              }\n            }\n            &:hover {\n              border-color: var(--dc-component--color-primary);\n            }\n            .is-use {\n              position: absolute;\n              left: 0;\n              top: 0;\n              width: 48px;\n              height: 20px;\n              font-size: 12px;\n              font-family: PingFangSC, PingFang SC;\n              font-weight: 400;\n              color: var(--dc-component--color-primary);\n              line-height: 20px;\n              text-align: center;\n              border-right: 1px solid var(--dc-component--color-border);\n              border-bottom: 1px solid var(--dc-component--color-border);\n              border-radius: 2px;\n              padding: 0 8px;\n              background: #eef2fe;\n            }\n            &.disabled {\n              background: #f7f9fc;\n              cursor: not-allowed;\n              &:hover {\n                border-color: #d6dde7;\n              }\n              .title {\n                opacity: 0.6;\n              }\n              .base-image {\n                opacity: 0.6;\n              }\n              .recently-used {\n                opacity: 0.6;\n              }\n            }\n            .title {\n              margin-top: 18px;\n              font-size: 16px;\n              font-weight: 500;\n              color: var(--dc-component--color-title);\n            }\n            .recently-used {\n              position: absolute;\n              height: 20px;\n              background: #eef2fe;\n              border-radius: 2px;\n              border: 1px solid var(--dc-component--color-primary-border);\n              padding: 0 8px;\n              position: absolute;\n              left: 0;\n              top: 0;\n              font-size: 12px;\n              font-weight: 400;\n              color: var(--dc-component--color-primary);\n              line-height: 20px;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n\n<style lang=\"scss\">\nhtml {\n  .el-popper.is-light.login-popper {\n    background: #ffffff;\n    box-shadow: 0px 4px 20px 0px var(--dc-component--color-shadow);\n    border: none;\n    .el-popper__arrow::before {\n      box-shadow: 0px 4px 20px 0px var(--dc-component--color-shadow);\n      border: none;\n    }\n  }\n  .lang-tolltip {\n    padding: 5px 0;\n    .lang-item {\n      cursor: pointer;\n      padding: 4px 11px;\n      min-width: 60px;\n      text-align: center;\n      &:hover {\n        background: rgba(41, 52, 78, 0.06);\n      }\n      &.active {\n        color: var(--dc-component--color-primary-active);\n      }\n    }\n  }\n}\n</style>\n"], "names": ["CyberData", "CyberEngine", "CyberAI", "CyberDataEnter", "CyberEngineEnter", "CyberAIEnter", "computed", "ref", "reactive", "decrypt", "config", "getQueryParam", "watch", "nextTick", "ElMessage", "encrypt", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA0ac,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXA,IAAA,MAAM,KAAQ,GAAA;AAAA,iBACZA,oBAAA;AAAA,mBACAC,sBAAA;AAAA,eACAC,kBAAA;AAAA,sBACAC,yBAAA;AAAA,wBACAC,2BAAA;AAAA,oBACAC,uBAAA;AAAA,KACF,CAAA;AAIE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AA4Dd,IAAA,MAAM,KAGA,GAAA;AAAA,MACJ;AAAA,QACE,KAAO,EAAA,cAAA;AAAA,QACP,KAAO,EAAA,OAAA;AAAA,OACT;AAAA,MACA;AAAA,QACE,KAAO,EAAA,SAAA;AAAA,QACP,KAAO,EAAA,OAAA;AAAA,OACT;AAAA,MACA;AAAA,QACE,KAAO,EAAA,oBAAA;AAAA,QACP,KAAO,EAAA,OAAA;AAAA,OACT;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,SAAY,GAAAC,YAAA,CAAS,MACzB,KAAA,CAAM,MAAO,CAAA,CAAC,EAAO,KAAA,KAAA,CAAM,gBAAiB,CAAA,QAAA,CAAS,EAAG,CAAA,KAAK,CAAC,CAChE,CAAA,CAAA;AAEA,IAAA,MAAM,UAAUC,OAAI,EAAA,CAAA;AAEpB,IAAA,MAAM,YAAYC,YAAS,CAAA;AAAA,MACzB,IAAM,EAAA,EAAA;AAAA,MACN,QAAU,EAAA,EAAA;AAAA,MACV,IAAM,EAAA,EAAA;AAAA,MACN,IAAM,EAAA,EAAA;AAAA,MACN,KAAO,EAAA,EAAA;AAAA,MACP,KAAO,EAAA,EAAA;AAAA,KACR,CAAA,CAAA;AAEK,IAAA,MAAA,OAAA,GAAUD,QAAI,KAAK,CAAA,CAAA;AAEnB,IAAA,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AAEnB,IAAA,MAAA,aAAA,GAAgBA,QAAI,KAAK,CAAA,CAAA;AAE/B,IAAA,MAAM,YAAe,GAAA,YAAA,CAAa,OAAQ,CAAA,cAAc,CAAK,IAAA,EAAA,CAAA;AAE7D,IAAA,MAAM,WAAWA,OAAI,CAAA,CAAC,KAAK,GAAK,EAAA,GAAA,EAAK,GAAG,CAAC,CAAA,CAAA;AAEzC,IAAA,MAAM,aAAaD,YACjB,CAAA,MAAM,MAAM,OAAW,IAAAG,kBAAA,CAAQ,8CAA8C,CAC/E,CAAA,CAAA;AAEA,IAAA,MAAM,QAAW,GAAA;AAAA,MACf,GAAK,EAAA,kEAAA;AAAA,MACL,IAAM,EAAA,kEAAA;AAAA,MACN,IAAM,EAAA,kEAAA;AAAA,MACN,IAAM,EAAA,kEAAA;AAAA,KACR,CAAA;AAEA,IAAA,MAAM,QAAW,GAAA;AAAA,MACf,GAAK,EAAA,kEAAA;AAAA,MACL,IAAM,EAAA,kEAAA;AAAA,MACN,IAAM,EAAA,kEAAA;AAAA,MACN,IAAM,EAAA,kEAAA;AAAA,KACR,CAAA;AAEA,IAAA,MAAM,QAAW,GAAA;AAAA,MACf,GAAK,EAAA,kEAAA;AAAA,MACL,IAAM,EAAA,kEAAA;AAAA,MACN,IAAM,EAAA,kEAAA;AAAA,MACN,IAAM,EAAA,kEAAA;AAAA,KACR,CAAA;AAEM,IAAA,MAAA,KAAA,GAAQH,aAAS,MAAM;AAC3B,MAAA,MAAM,IAAO,GAAA;AAAA,QACX,IAAM,EAAA;AAAA,UACJ;AAAA,YACE,QAAU,EAAA,IAAA;AAAA,YACV,OAAA,EAASI,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,KAAA;AAAA,YAC9B,OAAS,EAAA,MAAA;AAAA,WACX;AAAA,SACF;AAAA,QACA,KAAO,EAAA;AAAA,UACL;AAAA,YACE,QAAU,EAAA,IAAA;AAAA,YACV,OAAS,EAAA,MAAA;AAAA,YACT,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,MAAA;AAAA,WAChC;AAAA,UACA;AAAA,YACE,OACE,EAAA,wEAAA;AAAA,YACF,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,MAAA;AAAA,WAChC;AAAA,SACF;AAAA,QACA,KAAO,EAAA;AAAA,UACL;AAAA,YACE,QAAU,EAAA,IAAA;AAAA,YACV,OAAS,EAAA,MAAA;AAAA,YACT,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,MAAA;AAAA,WAChC;AAAA,UACA;AAAA,YACE,OAAS,EAAA,sDAAA;AAAA,YACT,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,MAAA;AAAA,WAChC;AAAA,SACF;AAAA,OACF,CAAA;AACA,MAAA,IAAI,WAAW,KAAO,EAAA;AACb,QAAA,OAAA;AAAA,UACL,GAAG,IAAA;AAAA,UACH,IAAM,EAAA;AAAA,YACJ;AAAA,cACE,QAAU,EAAA,IAAA;AAAA,cACV,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,SAAA;AAAA,cAC9B,OAAS,EAAA,MAAA;AAAA,aACX;AAAA,YACA;AAAA,cACE,OAAS,EAAA,uBAAA;AAAA,cACT,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,YAAA;AAAA,aAChC;AAAA,WACF;AAAA,UACA,QAAU,EAAA;AAAA,YACR;AAAA,cACE,QAAU,EAAA,IAAA;AAAA,cACV,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,SAAA;AAAA,cAC9B,OAAS,EAAA,MAAA;AAAA,aACX;AAAA,YACA;AAAA,cACE,OAAS,EAAA,uBAAA;AAAA,cACT,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,YAAA;AAAA,aAChC;AAAA,WACF;AAAA,SACF,CAAA;AAAA,OACK,MAAA;AACE,QAAA,OAAA;AAAA,UACL,GAAG,IAAA;AAAA,UACH,IAAM,EAAA;AAAA,YACJ;AAAA,cACE,QAAU,EAAA,IAAA;AAAA,cACV,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,SAAA;AAAA,cAC9B,OAAS,EAAA,MAAA;AAAA,aACX;AAAA,WACF;AAAA,UACA,QAAU,EAAA;AAAA,YACR;AAAA,cACE,QAAU,EAAA,IAAA;AAAA,cACV,OAAA,EAASA,oBAAO,CAAA,KAAA,CAAM,MAAQ,CAAA,CAAA,SAAA;AAAA,cAC9B,OAAS,EAAA,MAAA;AAAA,aACX;AAAA,WACF;AAAA,SACF,CAAA;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,YAGA,GAAA;AAAA,MACJ;AAAA,QACE,KAAO,EAAA,aAAA;AAAA,QACP,IAAM,EAAAA,oBAAA,CAAO,KAAM,CAAA,MAAA,CAAA,CAAQ,WAAY,CAAA,IAAA;AAAA,OACzC;AAAA,MACA;AAAA,QACE,KAAO,EAAA,WAAA;AAAA,QACP,IAAM,EAAAA,oBAAA,CAAO,KAAM,CAAA,MAAA,CAAA,CAAQ,SAAU,CAAA,IAAA;AAAA,OACvC;AAAA,MACA;AAAA,QACE,KAAO,EAAA,SAAA;AAAA,QACP,IAAM,EAAAA,oBAAA,CAAO,KAAM,CAAA,MAAA,CAAA,CAAQ,OAAQ,CAAA,IAAA;AAAA,OACrC;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,YAAYH,OAOhB,CAAA;AAAA,MACA;AAAA,QACE,GAAK,EAAA,kBAAA;AAAA,QACL,KAAO,EAAA,aAAA;AAAA,QACP,IAAM,EAAA,GAAA;AAAA,QACN,GAAK,EAAA,EAAA;AAAA,OACP;AAAA,MACA;AAAA,QACE,GAAK,EAAA,gBAAA;AAAA,QACL,KAAO,EAAA,WAAA;AAAA,QACP,IAAM,EAAA,GAAA;AAAA,QACN,GAAK,EAAA,EAAA;AAAA,OACP;AAAA,MACA;AAAA,QACE,GAAK,EAAA,cAAA;AAAA,QACL,KAAO,EAAA,SAAA;AAAA,QACP,IAAM,EAAA,GAAA;AAAA,QACN,GAAK,EAAA,EAAA;AAAA,OACP;AAAA,KACD,CAAA,CAAA;AAEK,IAAA,MAAA,aAAA,GAAgB,cAAe,CAAA,OAAA,CAAQ,YAAY,CAAA,CAAA;AAEzD,IAAA,MAAM,IAAO,GAAA,MAAA,CAAA;AAEb,IAAA,MAAM,SAASA,OAAI,EAAA,CAAA;AAEnB,IAAA,MAAM,UAAUA,OAAI,EAAA,CAAA;AAEd,IAAA,MAAA,WAAA,GAAcA,QAAI,KAAK,CAAA,CAAA;AAEvB,IAAA,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AAEnB,IAAA,MAAA,UAAA,GAAaA,QAAI,KAAK,CAAA,CAAA;AAEtB,IAAA,MAAA,QAAA,GAAWI,yBAAc,UAAU,CAAA,CAAA;AAEzC,IAAA,MAAM,eAAkB,GAAAL,YAAA,CAAS,MAAM,CAAC,CAAC,QAAQ,CAAA,CAAA;AAG/C,IAAAM,SAAA,CAAA,MAAM,KAAM,CAAA,MAAA,EACZ,MAAM;AACJ,MAAA,MAAA,CAAO,SAAS,MAAO,EAAA,CAAA;AAAA,KAE3B,CAAA,CAAA;AAEA,IAAAA,SAAA,CACE,MAAM,KAAA,CAAM,gBACZ,EAAA,CAAC,MAAW,KAAA;AACF,MAAA,OAAA,CAAA,GAAA,CAAI,oBAAoB,MAAM,CAAA,CAAA;AACtC,MAAA,UAAA,CAAW,MAAM;AACf,QAAA,OAAA,CAAQ,KAAQ,GAAA,MAAA,CAAA;AAAA,OACjB,CAAA,CAAA;AAAA,KAEL,CAAA,CAAA;AAEM,IAAA,MAAA,cAAA,GAAiB,CAAC,CAAoB,KAAA;AAC1C,MAAA,IAAI,MAAM,KAAM,CAAA,MAAA;AAAQ,QAAA,OAAA;AACxB,MAAA,IAAA,CAAK,kBAAkB,CAAC,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAA,MAAM,aAAa,MAAM;AACZ,MAAA,UAAA,CAAA,KAAA,GAAQ,CAAC,UAAW,CAAA,KAAA,CAAA;AAC/B,MAAAC,YAAA,CAAS,MAAM;AACb,QAAA,UAAA,CAAW,MAAM;AACf,UAAA,OAAA,CAAQ,MAAM,aAAc,EAAA,CAAA;AAAA,SAC7B,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KACH,CAAA;AAEM,IAAA,MAAA,SAAA,GAAY,CAAC,QAAA,GAAW,KAAU,KAAA;AAChC,MAAA,MAAA,OAAA,GAAU,IAAI,OAAQ,EAAA,CAAA;AACpB,MAAA,OAAA,CAAA,MAAA,CAAO,gBAAgB,kBAAkB,CAAA,CAAA;AACjD,MAAA,IAAI,QAAU,EAAA;AACZ,QAAA,OAAA,CAAQ,OAAO,UAAY,EAAA,cAAA,CAAe,OAAQ,CAAA,UAAU,KAAK,EAAE,CAAA,CAAA;AAAA,OACrE;AACQ,MAAA,OAAA,CAAA,MAAA,CAAO,MAAQ,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAC/B,MAAA,IAAA,KAAA,CAAM,eAAe,YAAc,EAAA;AAC7B,QAAA,OAAA,CAAA,MAAA,CAAO,cAAc,+BAA+B,CAAA,CAAA;AAAA,OACvD,MAAA;AACG,QAAA,OAAA,CAAA,MAAA,CAAO,YAAc,EAAA,KAAA,CAAM,UAAU,CAAA,CAAA;AAAA,OAC/C;AACA,MAAA,OACE,OAAW,IAAA;AAAA,QACT,cAAgB,EAAA,kBAAA;AAAA,QAChB,UAAA,EACE,MAAM,UAAe,KAAA,YAAA,GACjB,CAAC,WAAa,EAAA,SAAA,EAAW,aAAa,CAAA,GACtC,KAAM,CAAA,UAAA;AAAA,QACZ,GAAI,WAAW,EAAE,QAAA,EAAU,eAAe,OAAQ,CAAA,UAAU,CAAE,EAAA,GAAI,EAAC;AAAA,OACrE,CAAA;AAAA,KAEJ,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,IAAI,WAAY,CAAA,KAAA;AAAO,QAAA,OAAA;AACvB,MAAA,WAAA,CAAY,KAAQ,GAAA,IAAA,CAAA;AACd,MAAA,KAAA,CAAA,CAAA,EAAG,WAAW,KAAiB,CAAA,QAAA,CAAA,EAAA;AAAA,QACnC,MAAQ,EAAA,KAAA;AAAA,QACR,SAAS,SAAU,EAAA;AAAA,OACpB,CAAA,CACE,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAO,IAAI,IAAK,EAAA,CAAA;AAAA,OACjB,CAAA,CACA,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAA,CAAQ,IAAI,GAAG,CAAA,CAAA;AACX,QAAA,IAAA,GAAA,CAAI,SAAS,KAAO,EAAA;AACd,UAAA,OAAA,CAAA,KAAA,GAAQ,IAAI,IAAK,CAAA,SAAA,CAAA;AACf,UAAA,SAAA,CAAA,IAAA,GAAO,IAAI,IAAK,CAAA,UAAA,CAAA;AAAA,SACrB,MAAA;AACK,UAAAC,qBAAA,CAAA,KAAA,CAAM,IAAI,GAAG,CAAA,CAAA;AAAA,SACzB;AAAA,OACD,CAAA,CACA,KAAM,CAAA,CAAC,GAAQ,KAAA;AAAA,OAAE,CACjB,CAAA,OAAA,CAAQ,MAAO,WAAA,CAAY,QAAQ,KAAM,CAAA,CAAA;AAAA,KAC9C,CAAA;AAEA,IAAA,MAAM,oBAAoB,MAAM;AACxB,MAAA,KAAA,CAAA,CAAA,EAAG,WAAW,KAA6B,CAAA,oBAAA,CAAA,EAAA;AAAA,QAC/C,MAAQ,EAAA,KAAA;AAAA,QACR,SAAS,SAAU,EAAA;AAAA,OACpB,CAAA,CACE,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAO,IAAI,IAAK,EAAA,CAAA;AAAA,OACjB,CAAA,CACA,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAA,CAAQ,IAAI,GAAG,CAAA,CAAA;AACX,QAAA,IAAA,GAAA,CAAI,SAAS,KAAO,EAAA;AACtB,UAAA,OAAA,CAAQ,QAAQ,GAAI,CAAA,IAAA,CAAA;AACpB,UAAA,IAAI,QAAQ,KAAO,EAAA;AACJ,YAAA,YAAA,EAAA,CAAA;AAAA,WACf;AAAA,SACF;AAAA,OACD,CAAA,CACA,KAAM,CAAA,CAAC,GAAQ,KAAA;AAAA,OAAE,CAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AAClB,MAAA,KAAA,CAAA,CAAA,EAAG,WAAW,KAA0B,CAAA,iBAAA,CAAA,EAAA;AAAA,QAC5C,MAAQ,EAAA,MAAA;AAAA,QACR,OAAA,EAAS,UAAU,IAAI,CAAA;AAAA,OACxB,CAAA,CACE,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAO,IAAI,IAAK,EAAA,CAAA;AAAA,OACjB,CAAA,CACA,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAA,CAAQ,IAAI,GAAG,CAAA,CAAA;AACX,QAAA,IAAA,GAAA,CAAI,SAAS,KAAO,EAAA;AACb,UAAA,QAAA,CAAA,KAAA,GAAQ,IAAI,IAAK,CAAA,OAAA,CAAA;AACtB,UAAA,IAAA,QAAA,CAAS,KAAM,CAAA,MAAA,KAAW,CAAG,EAAA;AAC/B,YAAA,MAAM,GAAM,GAAA,CAAA,EACV,SAAU,CAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,KAAM,CAAA,CAAA,CAAE,CAAI,GAAA,CAAA,CAAA,CAAG,GACpC,CAAA,UAAA,EAAA,cAAA,CAAe,QAAQ,UAAU,CAAA,CAAA,CAAA,CAAA;AAC/B,YAAA,cAAA,CAAA,OAAA,CACb,YACA,EAAA,SAAA,CAAU,KAAM,CAAA,MAAA,CAAO,SAAS,KAAM,CAAA,CAAA,CAAE,CAAI,GAAA,CAAA,CAAA,CAAG,KACjD,CAAA,CAAA;AACO,YAAA,MAAA,CAAA,IAAA,CAAK,KAAK,OAAO,CAAA,CAAA;AAAA,WACnB,MAAA;AACL,YAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AAAA,WACxB;AAAA,SACK,MAAA;AACK,UAAAA,qBAAA,CAAA,KAAA,CAAM,IAAI,GAAG,CAAA,CAAA;AAAA,SACzB;AAAA,OACD,CAAA,CACA,KAAM,CAAA,CAAC,GAAQ,KAAA;AAAA,OAAE,CAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AACV,MAAA,KAAA,CAAA,CAAA,EAAG,WAAW,KAA8B,CAAA,qBAAA,CAAA,EAAA;AAAA,QAChD,MAAQ,EAAA,MAAA;AAAA,QACR,SAAS,SAAU,EAAA;AAAA,QACnB,IAAA,EAAM,KAAK,SAAU,CAAA;AAAA,UACnB,WAAW,SAAU,CAAA,IAAA;AAAA,UACrB,WAAW,SAAU,CAAA,IAAA;AAAA,UACrB,YAAY,SAAU,CAAA,IAAA;AAAA,UACtB,QAAA,EAAUC,kBAAQ,CAAA,SAAA,CAAU,QAAQ,CAAA;AAAA,UACpC,OAAO,SAAU,CAAA,KAAA;AAAA,UACjB,OAAO,SAAU,CAAA,KAAA;AAAA,UACjB,QAAU,EAAA,QAAA;AAAA,SACX,CAAA;AAAA,OACF,CAAA,CACE,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAO,IAAI,IAAK,EAAA,CAAA;AAAA,OACjB,CAAA,CACA,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAA,CAAQ,IAAI,GAAG,CAAA,CAAA;AACX,QAAA,IAAA,GAAA,CAAI,SAAS,KAAO,EAAA;AACtB,UAAAD,qBAAA,CAAU,OAAQ,CAAAJ,oBAAA,CAAO,KAAM,CAAA,MAAA,CAAA,CAAQ,UAAU,CAAA,CAAA;AACjD,UAAA,UAAA,CAAW,MAAM;AACf,YAAA,UAAA,CAAW,KAAQ,GAAA,KAAA,CAAA;AACnB,YAAA,OAAA,CAAQ,MAAM,WAAY,EAAA,CAAA;AAAA,aACzB,GAAG,CAAA,CAAA;AAAA,SACD,MAAA;AACK,UAAAI,qBAAA,CAAA,KAAA,CAAM,IAAI,GAAG,CAAA,CAAA;AACvB,UAAA,IAAI,QAAQ,KAAO,EAAA;AACJ,YAAA,YAAA,EAAA,CAAA;AAAA,WACf;AAAA,SACF;AAAA,OACD,CAAA,CACA,KAAM,CAAA,CAAC,GAAQ,KAAA;AAAA,OAAE,CACjB,CAAA,OAAA,CAAQ,MAAO,OAAA,CAAQ,QAAQ,KAAM,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AACV,MAAA,MAAA,GAAA,GAAM,OAAQ,CAAA,KAAA,GAAQ,wBAA2B,GAAA,aAAA,CAAA;AACjD,MAAA,KAAA,CAAA,CAAA,EAAG,UAAW,CAAA,KAAA,CAAA,EAAQ,GAAO,CAAA,CAAA,EAAA;AAAA,QACjC,MAAQ,EAAA,MAAA;AAAA,QACR,SAAS,SAAU,EAAA;AAAA,QACnB,IAAM,EAAA,OAAA,CAAQ,KACV,GAAA,IAAA,CAAK,SAAU,CAAA;AAAA,UACb,WAAW,SAAU,CAAA,IAAA;AAAA,UACrB,QAAA,EAAUC,kBAAQ,CAAA,SAAA,CAAU,QAAQ,CAAA;AAAA,UACpC,MAAM,SAAU,CAAA,IAAA;AAAA,UAChB,MAAM,SAAU,CAAA,IAAA;AAAA,UAChB,iBAAiB,KAAM,CAAA,eAAA;AAAA,SACxB,CACD,GAAA,IAAA,CAAK,SAAU,CAAA;AAAA,UACb,WAAW,SAAU,CAAA,IAAA;AAAA,UACrB,QAAA,EAAUA,kBAAQ,CAAA,SAAA,CAAU,QAAQ,CAAA;AAAA,UACpC,iBAAiB,KAAM,CAAA,eAAA;AAAA,SACxB,CAAA;AAAA,OACN,CAAA,CACE,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAO,IAAI,IAAK,EAAA,CAAA;AAAA,OACjB,CAAA,CACA,IAAK,CAAA,CAAC,GAAQ,KAAA;AACb,QAAA,OAAA,CAAQ,IAAI,GAAG,CAAA,CAAA;AACX,QAAA,IAAA,GAAA,CAAI,SAAS,KAAO,EAAA;AAChB,UAAA,MAAA,QAAA,GAAW,KAAK,IAAM,EAAA,QAAA,CAAA;AACb,UAAA,cAAA,CAAA,OAAA,CAAQ,YAAY,QAAQ,CAAA,CAAA;AACvC,UAAA,IAAA,KAAA,CAAM,eAAe,YAAc,EAAA;AACrC,YAAA,IAAA,CAAK,cAAgB,EAAA;AAAA,cACnB,OAAO,KAAM,CAAA,UAAA;AAAA,cACb,QAAA;AAAA,cACA,WAAW,SAAU,CAAA,IAAA;AAAA,cACrB,UAAU,SAAU,CAAA,QAAA;AAAA,aACrB,CAAA,CAAA;AAAA,WACI,MAAA;AACO,YAAA,WAAA,EAAA,CAAA;AAAA,WACd;AAAA,SACK,MAAA;AACK,UAAAD,qBAAA,CAAA,KAAA,CAAM,IAAI,GAAG,CAAA,CAAA;AACvB,UAAA,IAAI,QAAQ,KAAO,EAAA;AACJ,YAAA,YAAA,EAAA,CAAA;AAAA,WACf;AAAA,SACF;AAAA,OACD,CAAA,CACA,KAAM,CAAA,CAAC,GAAQ,KAAA;AAAA,OAAE,CACjB,CAAA,OAAA,CAAQ,MAAO,OAAA,CAAQ,QAAQ,KAAM,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAA,MAAM,QAAQ,MAAM;AACV,MAAA,OAAA,CAAA,KAAA,CAAM,QAAS,CAAA,CAAC,CAAe,KAAA;AACrC,QAAA,IAAI,CAAC,CAAA;AAAG,UAAA,OAAA;AACR,QAAA,IAAI,WAAW,KAAO,EAAA;AACJ,UAAA,eAAA,EAAA,CAAA;AAAA,SACX,MAAA;AACQ,UAAA,YAAA,EAAA,CAAA;AAAA,SACf;AAAA,OACD,CAAA,CAAA;AAAA,KACH,CAAA;AAEM,IAAA,MAAA,QAAA,GAAW,CAAC,EAKZ,KAAA;AACJ,MAAA,IAAI,CAAC,QAAA,CAAS,KAAM,CAAA,QAAA,CAAS,GAAG,IAAI,CAAA;AAAG,QAAA,OAAA;AACxB,MAAA,cAAA,CAAA,OAAA,CAAQ,YAAc,EAAA,EAAA,CAAG,KAAK,CAAA,CAAA;AAC7C,MAAA,MAAM,MAAM,CAAG,EAAA,EAAA,CAAG,GAAgB,CAAA,UAAA,EAAA,cAAA,CAAe,QAAQ,UAAU,CAAA,CAAA,CAAA,CAAA;AAC5D,MAAA,MAAA,CAAA,IAAA,CAAK,KAAK,OAAO,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAAE,aAAA,CAAU,MAAM;AACR,MAAA,MAAA,CAAA,GAAI,SAAS,eAAgB,CAAA,YAAA,CAAA;AACnC,MAAA,MAAM,KAAK,IAAO,GAAA,CAAA,CAAA;AAClB,MAAA,MAAA,CAAO,KAAM,CAAA,KAAA,CAAM,KAAQ,GAAA,CAAA,EAAG,GAAM,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AAC9B,MAAA,MAAA,IAAA,GAAO,OAAO,QAAS,CAAA,IAAA,CAAA;AACX,MAAA,iBAAA,EAAA,CAAA;AAClB,MAAAH,YAAA,CAAS,MAAM;AACb,QAAA,MAAA,CAAO,MAAM,KAAM,EAAA,CAAA;AAAA,OACpB,CAAA,CAAA;AACO,MAAA,QAAA,IAAA;AAAA,QACD,KAAA,OAAA;AACH,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAJ,kBAAA,CAAQ,SAAS,GAAG,CAAA,CAAA;AAC7C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,GAAG,CAAA,CAAA;AAC7C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,GAAG,CAAA,CAAA;AAC7C,UAAA,MAAA;AAAA,QACG,KAAA,OAAA;AACH,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,MAAA;AAAA,QACG,KAAA,OAAA;AACH,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,MAAA;AAAA,QAAA;AAEA,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAC9C,UAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,IAAI,CAAA,CAAA;AAAA,OAAA;AAE9C,MAAA,IAAA,MAAA,CAAO,QAAS,CAAA,QAAA,KAAa,WAAa,EAAA;AAC5C,QAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,GAAG,CAAA,CAAA;AAC7C,QAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,GAAG,CAAA,CAAA;AAC7C,QAAA,SAAA,CAAU,KAAM,CAAA,CAAA,CAAA,CAAG,GAAM,GAAAA,kBAAA,CAAQ,SAAS,GAAG,CAAA,CAAA;AAAA,OAC/C;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}