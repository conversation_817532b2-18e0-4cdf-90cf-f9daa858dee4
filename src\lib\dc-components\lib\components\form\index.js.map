{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/form/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Form from \"./src/index.vue\";\r\n\r\nexport const DcForm = withInstall(Form);\r\n\r\nexport default DcForm;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "Form"], "mappings": ";;;;;;;;AAIa,MAAA,MAAA,GAASA,qBAAYC,gBAAI;;;;;;"}