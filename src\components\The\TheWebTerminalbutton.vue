<template>
  <BaseElTooltip v-if="$has('hosts-webTerminal') && terminalVisible" :content="$t('button.commandTerminalTip')" placement="top">
    <BaseButton v-bind="$attrs" link>
      <a :href="url" target="_blank">
        <img class="w-17px h-16px" src="@/assets/icons/icon-webTerminal.svg" alt="web" />
      </a>
    </BaseButton>
  </BaseElTooltip>
</template>
<script lang="ts" setup>
const { route, t, setBreadList, router, store, $has } = useBasicTool()
defineProps({
  url: {
    type: String,
    default: ''
  }
})
const terminalVisible = computed(() => {
  return store.state.user.terminalVisible
})
</script>
