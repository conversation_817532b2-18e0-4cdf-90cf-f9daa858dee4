import { defineComponent, computed, openBlock, createElement<PERSON><PERSON>, createVNode, unref, createSlots, withCtx, createBlock, renderSlot } from 'vue';
import { ElEmpty, ElImage } from 'element-plus';
import 'element-plus/es/components/empty/style/css';
import 'element-plus/es/components/image/style/css';
import { imgConfig, descConfig } from './constants.mjs';
import './index.vue_vue_type_style_index_0_lang.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const _hoisted_1 = { class: "dc-empty" };
const _hoisted_2 = { key: 1 };
const __default__ = defineComponent({
  name: "DcEmpty"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    locale: {
      type: String,
      default: "zh-CN"
    },
    type: {
      type: String,
      default: "list"
    },
    theme: {
      type: String,
      default: "light"
    },
    description: {
      type: String,
      default: ""
    },
    showImg: {
      type: Boolean,
      default: true
    },
    img: {
      type: String,
      default: ""
    }
  },
  setup(__props) {
    ;
    const props = __props;
    const image = computed(() => props.img || imgConfig[props.theme]?.[props.type]);
    const desc = computed(() => props.description || descConfig[props.locale]?.[props.type]);
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createVNode(unref(ElEmpty), { description: desc.value }, createSlots({
          image: withCtx(() => [
            __props.showImg ? (openBlock(), createBlock(unref(ElImage), {
              key: 0,
              src: image.value
            }, null, 8, ["src"])) : (openBlock(), createElementBlock("div", _hoisted_2))
          ]),
          default: withCtx(() => [
            renderSlot(_ctx.$slots, "default")
          ]),
          _: 2
        }, [
          _ctx.$slots.description ? {
            name: "description",
            fn: withCtx(() => [
              renderSlot(_ctx.$slots, "description")
            ]),
            key: "0"
          } : void 0
        ]), 1032, ["description"])
      ]);
    };
  }
});
var DcEmpty = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\empty\\src\\index.vue"]]);

export { DcEmpty as default };
//# sourceMappingURL=index.mjs.map
