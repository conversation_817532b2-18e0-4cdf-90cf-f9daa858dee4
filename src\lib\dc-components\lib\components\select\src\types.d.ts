import type { Ref } from "vue";
import type { RenderVNodeFn } from "../../render-vnode/src/types";
import type DcSelect from "./index.vue";
export interface DcSelectOpItem {
    label: string | number;
    value: string | number | boolean;
    disabled?: boolean;
    defaultSlot?: RenderVNodeFn;
}
export interface DcSelectGroupItem {
    label: string;
    disabled?: boolean;
    options: DcSelectOpItem[];
}
export declare type DcSelectOptions = DcSelectOpItem[] | Ref<DcSelectOpItem[]>;
export declare type DcSelectGroups = DcSelectGroupItem[] | Ref<DcSelectGroupItem[]>;
export declare type DcSelectInstance = InstanceType<typeof DcSelect>;
