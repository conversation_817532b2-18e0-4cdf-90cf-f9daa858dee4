<template>
  <BaseLayout :calc-minuend="141">
    <template #header>
      <BaseSearch ref="baseSearch" :searchItemData="[]" @on-submit="submitSearch" @reset-search="resetSearch">
        <template #after>
          <el-form-item :label="$t('form.timeSelection') + '：'">
            <el-date-picker
              v-model="timeInterval"
              type="datetimerange"
              range-separator="⇀"
              :start-placeholder="$t('form.startDate')"
              :end-placeholder="$t('form.endDate')"
              prefix-icon="el-icon-date"
              clearable
              :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </template>
      </BaseSearch>
    </template>
    <template #content>
      <BaseTable v-loading="tableLoading" :columns="tableData.columns" :data="tableData.data" />
    </template>
    <template #footer>
      <BasePagination v-model="pageInfo" @page-change="handlePageChange" />
    </template>
  </BaseLayout>
</template>
<script lang="ts" setup>
import type { DateModelType } from 'element-plus'
const { t, route, store, $has } = useBasicTool()
const tableLoading = ref(true)
const { pageInfo, resetPageInfo } = usePage()
const baseSearch = ref()
const userInfo = computed(() => store.state.user.userInfo)
const tenantId = computed(() => store.state.user.tenantId)
const timeInterval = ref<[DateModelType, DateModelType]>(['', ''])
// 表格数据
const tableData = reactive({
  columns: [
    { prop: 'fileName', label: t('replenish.fileName') },
    { prop: 'fileSize', label: t('replenish.fileSize') },
    { prop: 'modificationTime', label: t('replenish.lastModifiedTime') },
    { prop: 'filePath', label: t('replenish.directoryAddress') },
    { prop: 'subdirectoryNum', label: t('replenish.subdirectoryCount') },
    { prop: 'fileStatus', label: t('replenish.fileStatus') }
  ],
  data: []
})
onMounted(() => {
  getData()
})
watch(
  () => route.name,
  () => {
    resetSearch()
  }
)
function getData() {
  tableLoading.value = false
  let id = 0
  if (Number(userInfo.value.type) !== 2 && tenantId.value === '') id = userInfo.value.tenantId
  else id = tenantId.value
  const type = route.name === 'hotDataList' ? 1 : 2
  store
    .dispatch('migration/getMigrationList', { ...pageInfo, startTime: timeInterval.value[0], endTime: timeInterval.value[1], tenantId: id, type })
    .then((res) => {
      tableData.data = res.data.records
      pageInfo.total = res.data.total
    })
}
function handlePageChange() {
  getData()
}
function submitSearch(value: any) {
  pageInfo.pageNo = 1
  getData()
}
function resetSearch() {
  resetPageInfo()
  timeInterval.value = ['', '']
  getData()
}
</script>
