{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/button/src/index.vue"], "sourcesContent": ["<template>\r\n  <ElButton v-bind=\"$attrs\" @click=\"onClick\">\r\n    <slot />\r\n    <template v-if=\"$slots.loading\" #loading>\r\n      <slot name=\"loading\" />\r\n    </template>\r\n    <template v-if=\"$slots.icon\" #icon>\r\n      <slot name=\"icon\" />\r\n    </template>\r\n  </ElButton>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ElButton } from \"element-plus\";\r\nimport { debounce as debounceFn } from \"lodash\";\r\n\r\ndefineOptions({\r\n  name: \"DcButton\",\r\n});\r\n\r\nconst props = defineProps({\r\n  debounce: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  debounceInterval: {\r\n    type: Number,\r\n    default: 300,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"click\"]);\r\n\r\nconst handleClick = (e: MouseEvent) => {\r\n  emits(\"click\", e);\r\n};\r\n\r\nconst onClick = props.debounce\r\n  ? debounceFn(handleClick, props.debounceInterval)\r\n  : handleClick;\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["_defineComponent", "debounceFn"], "mappings": ";;;;;;;AAgBc,EAAA,IAAA,EAAA,UAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAWd,IAAA,MAAM,WAAQ,GAAA,CAAA,CAAA,KAAA;AAEd,MAAM,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAc;AAClB,KAAA,CAAA;AAAgB,IAClB,MAAA,OAAA,GAAA,KAAA,CAAA,QAAA,GAAAC,QAAA,CAAA,WAAA,EAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,WAAA,CAAA;AAEA,IAAA,oBAAsB,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}