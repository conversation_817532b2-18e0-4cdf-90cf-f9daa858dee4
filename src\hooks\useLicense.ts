import { computed } from 'vue'
import { useStore } from 'vuex'
import BaseMessage from '@/components/BaseMessage'
import { useBasicTool } from '@/hooks/useBasicTool'
import dayjs from 'dayjs'

export const useLicense = () => {
  const formatTime = (time: string | number | Date) => {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
  }
  const store = useStore()
  const { t } = useBasicTool()

  const licenseInfo = computed(() => store.state.user.licenseInfo)

  const setMessage = () => {
    const storage = window && window.localStorage
    storage.setItem('licenseEncryptStr', licenseInfo.value ? JSON.stringify(licenseInfo.value) : '')
    const startTime = new Date(licenseInfo.value.startTime)
    const expirationTime = new Date(licenseInfo.value.expirationTime)
    const currentTime = new Date()
    if (currentTime.getTime() < startTime.getTime()) {
      BaseMessage.error(t('replenish.licenseTip1').replace('$date', formatTime(startTime)))
    } else if (currentTime.getTime() > expirationTime.getTime()) {
      BaseMessage.error(t('replenish.licenseTip2').replace('$date', formatTime(expirationTime)))
    } else if (currentTime.getTime() > expirationTime.getTime() - 10 * 24 * 60 * 60 * 1000) {
      const licenseMarkObjStr = storage.getItem('licenseMarkObj')
      const licenseMarkObj = licenseMarkObjStr ? JSON.parse(licenseMarkObjStr) : {}
      if (licenseMarkObj.date !== formatTime(currentTime).split(' ')[0]) {
        const daysLeft = Math.ceil((expirationTime.getTime() - currentTime.getTime()) / (24 * 60 * 60 * 1000))

        BaseMessage.error({
          message: t('replenish.licenseTip3').replace('$date', daysLeft.toString()),
          duration: 0, // 设置为0，不自动关闭
          onClose: () => {
            storage.setItem(
              'licenseMarkObj',
              JSON.stringify({
                ...licenseInfo.value,
                date: formatTime(currentTime).split(' ')[0]
              })
            )
          }
        })
      }
    }
  }

  const getLicense = async () => {
    try {
      await store.dispatch('user/getLicenseInfo')
      setMessage()
      return licenseInfo.value
    } catch (error) {
      console.error('获取License信息失败:', error)
      return null
    }
  }

  return {
    licenseInfo,
    getLicense,
    formatTime
  }
}
