<template>
  <el-dialog :model-value="modelValue" :title="$t('title.tip')" width="600px" :before-close="handleClose">
    <div class="messge-batch-box">
      <div class="messge-container">
        <i class="iconfont icon-icon_jinggaoo" />
        <div>
          {{ params.message }}
        </div>
      </div>
      <div class="messge-description">
        {{ params.desc }}
      </div>
      <div class="batch-item-box">
        <div class="batch-item-box_left">
          {{ params.batchLable }}
        </div>
        <div class="batch-item-box_right">
          <div :class="{ 'batch-item-box_right-content': isExpand }">
            <span ref="batchItemRef">{{ params.itemList }}</span>
          </div>
          <div v-if="batchItemRightButton" style="margin-left: -16px">
            <slot name="button">
              <BaseButton type="primary" link @click="() => (isExpand = !isExpand)">
                {{ isExpand ? $t('button.expand') : $t('button.putAway') }}
              </BaseButton>
            </slot>
          </div>
        </div>
      </div>
      <div class="messge-remindarea">
        <div class="remind-title">
          <span>{{ params.preventMessage }}</span>
          <span class="hots-name" style="white-space: pre">{{ params.name }}</span>
        </div>
        <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="0px">
          <el-form-item prop="name">
            <el-input v-model="ruleForm.name" style="width: 280px" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span>
        <BaseButton type="primary" @click="submitForm(ruleFormRef)"> {{ $t('button.sure') }} </BaseButton>
        <BaseButton type="info" @click="resetForm(ruleFormRef)">{{ $t('button.cancel') }}</BaseButton>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
type Params = {
  message: string
  desc: string
  batchLable: string
  itemList: string
  preventMessage: string
  name: string
  inputErrorMessage: string //检验信息
}
interface Props {
  modelValue: boolean
  params: Params
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  params: () => ({
    message: '我是谁?',
    desc: '还有谁',
    batchLable: '已选主机(12):',
    itemList:
      '主机1（IP）；主机2（IP）；主机3（IP）；主机4（IP）；主机1（IP）；主机2（IP）；主机3（IP）；主机4（IP）；主机1（IP）；主机2（IP）；主机3（IP）；；主机2（IP）；主机3',
    preventMessage: '为防止意外操作，请输入该验证码到输入框：',
    name: 'ASDF',
    inputErrorMessage: t('replenish.enterVerification')
  })
})
const batchItemRef = ref()
const isExpand = ref(true)
const batchItemRightButton = computed(() => {
  if (batchItemRef.value?.offsetHeight > 57) {
    return true
  } else {
    return false
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])
const handleClose = () => {
  shutDown()
}
function shutDown() {
  emit('update:modelValue', false)
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  name: ''
})
const validatePass = (rule: any, value: any, callback: any) => {
  if (value !== props.params.name) {
    callback(new Error(props.params.inputErrorMessage))
  } else {
    callback()
  }
}
const rules = reactive<FormRules>({
  name: [
    { required: true, message: props.params.inputErrorMessage, trigger: 'blur' },
    { required: true, validator: validatePass, trigger: 'blur' }
  ]
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      emit('submit', ruleForm)
      resetForm(ruleFormRef.value)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  shutDown()
}
</script>
<style scoped>
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>

<style lang="scss" scoped>
.messge-batch-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  width: 585px;
  min-height: 140px;
  padding-bottom: 10px;
  padding-left: 35px;
  margin-top: -8px;
  margin-right: 0;
  margin-left: -16px;
  .iconfont {
    color: var(--ops-state-abnormal-color);
  }
}
.batch-item-box {
  display: flex;
  flex-flow: column;
  padding-top: 12px;
  padding-right: 30px;
  color: rgba(51, 57, 76, 1);
  .batch-item-box_right {
    color: var(--ops-primary-color);
    .batch-item-box_right-content {
      @include text-ellipsis(3);
    }
  }
}
.messge-container {
  display: flex;
  padding: 0px 20px 8px 0;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  // text-align: center;
  color: var(--ops-tips-text-color);
  .iconfont {
    margin-right: 10px;
  }
}
.messge-description {
  padding-right: 20px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  color: var(--ops-secondary-tips-text-color);
}
.messge-remindarea {
  position: relative;
  box-sizing: border-box;
  width: 520px;
  height: 110px;
  padding: 13px 15px 20px 16px;
  margin-top: 12px;
  font-size: 14px;
  line-height: 24px;
  color: var(--ops-text-color);
  background: rgb(200 204 218 / 30%);
  .remind-title {
    margin-bottom: 8px;
    font-weight: 400;
  }
  .hots-name {
    color: var(--ops-primary-color);
  }
  .copyIconfont {
    margin-left: 5px;
    color: var(--ops-primary-color) !important;
  }
}
</style>
