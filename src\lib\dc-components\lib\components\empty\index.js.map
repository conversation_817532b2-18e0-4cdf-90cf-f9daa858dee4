{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/empty/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Empty from \"./src/index.vue\";\r\n\r\nexport const DcEmpty = withInstall(Empty);\r\n\r\nexport default DcEmpty;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "Empty"], "mappings": ";;;;;;;;AAIa,MAAA,OAAA,GAAUA,qBAAYC,gBAAK;;;;;"}