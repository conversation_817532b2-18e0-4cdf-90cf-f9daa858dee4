import service from '@/utils/Http'

/**
 * @description yarn任务列表管理
 * @param {YarnApi.GetYarnTasks} data
 * @return {*}
 */

export const getYarnTasks = (data: YarnApi.GetYarnTasks) => {
  return service.post<ApiResponse<Array<any>>>(`/yarn/getYarnTasks`, data)
}

/**
 * @description yarn任务查杀
 * @param {YarnApi.KillYarnTask} data
 * @return {*}
 */

export const killYarnTask = (data: YarnApi.KillYarnTask) => {
  return service.post<ApiResponse<Array<any>>>(`/yarn/killYarnTask`, data)
}

/**
 * @description yarn定时查杀
 * @param {YarnApi.SetTimedKill} data
 * @return {*}
 */

export const setTimedKill = (data: YarnApi.SetTimedKill) => {
  return service.post<ApiResponse<Array<any>>>(`/yarn/setTimedKill`, data)
}

/**
 * @description 取消定时查杀
 * @param {YarnApi.SetTimedKill} data
 * @return {*}
 */

export const cancelTimedKill = (data: YarnApi.CancelTimedKill) => {
  return service.post<ApiResponse<Array<any>>>(`/yarn/cancelTimedKill`, data)
}

/**
 * @description 获取主机扳手告警
 * @param {YarnApi.SetTimedKill} data
 * @return {*}
 */

export const getListLatest = (namespaceId: Number) => {
  return service.get<ApiResponse<Array<any>>>(`/alert/list/latest?namespaceId=${namespaceId}`)
}
