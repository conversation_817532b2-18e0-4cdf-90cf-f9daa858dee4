<template>
  <el-config-provider :locale="getLocale()" :message="{ max: 1 }">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import useBrowserLabel from '@/hooks/useBrowserLabel'
import useLocale from '@/locales/useLocale'
import { browserTips } from '@/utils/BrowserVersion'
import defaultFavicon from '@/assets/img/favicon.ico'
import { useLicense } from '@/hooks/useLicense'

const { t, router, store } = useBasicTool()
const { setLocale, getLocale } = useLocale()
const { getLicense, licenseInfo } = useLicense()

const logoInfo = computed(() => store.state.user.logoInfo)

const favicon = computed(() => {
  if (logoInfo.value.logoInfo === 1 && logoInfo.value.browserTab) {
    return `data:image/png;base64,${logoInfo.value.browserTab}`
  } else {
    return defaultFavicon
  }
})

// 设置浏览器标签title
useBrowserLabel()
// 设置语言版本
setLocale()
// 浏览器版本提示
browserTips()
// 获取License信息
getLicense()

const preloadError = () => {
  window.location.reload()
}

onMounted(() => {
  window.addEventListener('vite:preloadError', preloadError)
})

onBeforeUnmount(() => {
  window.removeEventListener('vite:preloadError', preloadError)
})

watch(
  () => favicon.value,
  (val) => {
    let favicon: HTMLLinkElement | null = document.querySelector('link[rel="shortcut icon"]')
    if (favicon !== null) {
      favicon.href = val
    } else {
      favicon = document.createElement('link')
      favicon.rel = 'icon'
      favicon.href = val
      document.head.appendChild(favicon)
    }
  },
  {
    immediate: true
  }
)
</script>
