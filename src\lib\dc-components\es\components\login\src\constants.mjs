var config = {
  "zh-CN": {
    CyberEngine: {
      desc: "\u4E00\u7AD9\u5F0F\u4E91\u539F\u751F\u5927\u6570\u636E\u5E73\u53F0"
    },
    CyberData: {
      desc: "\u4E00\u7AD9\u5F0F\u591A\u4E91\u5927\u6570\u636E\u7814\u53D1\u6CBB\u7406\u5E73\u53F0"
    },
    CyberAI: {
      desc: "\u4E00\u7AD9\u5F0F\u673A\u5668\u5B66\u4E60\u751F\u6001\u670D\u52A1\u5E73\u53F0"
    },
    welcome: "\u6B22\u8FCE\u56DE\u6765",
    usernameP: "\u8BF7\u8F93\u5165\u7528\u6237\u540D",
    passwordP: "\u8BF7\u8F93\u5165\u5BC6\u7801",
    codeP: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801",
    usernameR: "\u8BF7\u8F93\u5165\u7528\u6237\u540D",
    passwordR: "\u8BF7\u8F93\u5165\u5BC6\u7801",
    codeR: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801",
    login: "\u767B\u5F55",
    dialogTitle: "\u8BF7\u9009\u62E9\u60A8\u8981\u8FDB\u5165\u7684\u5E73\u53F0",
    recentlyUsed: "\u6700\u8FD1\u4F7F\u7528",
    register: "\u6CE8\u518C",
    registerText: "\u6CA1\u6709\u8D26\u53F7\uFF1F\u7ACB\u5373\u6CE8\u518C",
    loginText: "\u5DF2\u6709\u8D26\u53F7\uFF0C\u7ACB\u5373\u767B\u5F55",
    phoneP: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7",
    phoneR: "\u8BF7\u8F93\u5165\u5408\u6CD5\u7684\u7535\u8BDD\u53F7\u7801",
    emailP: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740",
    emailR: "\u8BF7\u8F93\u5165\u5408\u6CD5\u7684\u90AE\u7BB1\u5730\u5740",
    registerSu: "\u6CE8\u518C\u6210\u529F\uFF0C\u5373\u5C06\u8DF3\u8F6C\u767B\u5F55",
    usernameRule: "\u957F\u5EA66~16\u4E2A\u5B57\u7B26\uFF0C\u4EC5\u652F\u6301\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001-\u6216_"
  },
  "en-US": {
    CyberEngine: {
      desc: "One-stop cloud-native big data platform"
    },
    CyberData: {
      desc: "One-stop multi-cloud big data R&D management platform"
    },
    CyberAI: {
      desc: "One-stop machine learning ecological service platform"
    },
    welcome: "Welcome back",
    usernameP: "User name",
    passwordP: "Password",
    codeP: "Verification code",
    usernameR: "Please enter user name",
    passwordR: "Please enter password",
    codeR: "Please enter the verification code",
    login: "Log in",
    dialogTitle: "Please select the platform you want to enter",
    recentlyUsed: "Recently Used",
    register: "Register",
    registerText: "Don't have an account? Register Now",
    loginText: "Already have an account, log in now",
    phoneP: "Please enter your phone number",
    phoneR: "Please enter a valid phone number",
    emailP: "Please enter your email address",
    emailR: "Please enter a valid email address",
    registerSu: "Registration successful, will be redirected to login soon",
    usernameRule: "6~16 characters, only letters, numbers, - and _ are allowed"
  },
  "ja-JP": {
    CyberEngine: {
      desc: "\u30EF\u30F3\u30B9\u30C8\u30C3\u30D7\u30AF\u30E9\u30A6\u30C9\u30CD\u30A4\u30C6\u30A3\u30D6\u30D3\u30C3\u30B0\u30C7\u30FC\u30BF\u30D7\u30E9\u30C3\u30C8\u30D5\u30A9\u30FC\u30E0"
    },
    CyberData: {
      desc: "\u30EF\u30F3\u30B9\u30C8\u30C3\u30D7\u578B\u30DE\u30EB\u30C1\u30AF\u30E9\u30A6\u30C9\u30D3\u30C3\u30B0\u30C7\u30FC\u30BF\u7814\u7A76\u958B\u767A\u7BA1\u7406\u30D7\u30E9\u30C3\u30C8\u30D5\u30A9\u30FC\u30E0"
    },
    CyberAI: {
      desc: "\u30EF\u30F3\u30B9\u30C8\u30C3\u30D7\u578B\u30DE\u30EB\u30C1\u30AF\u30E9\u30A6\u30C9\u30D3\u30C3\u30B0\u30C7\u30FC\u30BF\u7814\u7A76\u958B\u767A\u7BA1\u7406\u30D7\u30E9\u30C3\u30C8\u30D5\u30A9\u30FC\u30E0"
    },
    welcome: "\u304A\u5E30\u308A\u306A\u3055\u3044",
    usernameP: "\u30E6\u30FC\u30B6\u30FC\u540D",
    passwordP: "\u30D1\u30B9\u30EF\u30FC\u30C9",
    codeP: "\u8A8D\u8A3C\u30B3\u30FC\u30C9",
    usernameR: "\u30E6\u30FC\u30B6\u30FC\u540D\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    passwordR: "\u30D1\u30B9\u30EF\u30FC\u30C9\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    codeR: "\u8A8D\u8A3C\u30B3\u30FC\u30C9\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    login: "\u30ED\u30B0\u30A4\u30F3",
    dialogTitle: "\u30A2\u30AF\u30BB\u30B9\u3059\u308B\u30D7\u30E9\u30C3\u30C8\u30D5\u30A9\u30FC\u30E0\u3092\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044",
    recentlyUsed: "\u6700\u8FD1\u4F7F\u7528\u3057\u305F",
    register: "\u767B\u9332",
    registerText: "\u30A2\u30AB\u30A6\u30F3\u30C8\u306F\u3042\u308A\u307E\u305B\u3093\u304B\uFF1F\u4ECA\u3059\u3050\u767B\u9332",
    loginText: "\u65E2\u5B58\u306E\u30A2\u30AB\u30A6\u30F3\u30C8\u3001\u3059\u3050\u306B\u30ED\u30B0\u30A4\u30F3",
    phoneP: "\u643A\u5E2F\u756A\u53F7\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    phoneR: "\u5408\u6CD5\u7684\u306A\u96FB\u8A71\u756A\u53F7\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    emailP: "\u30E1\u30FC\u30EB\u30A2\u30C9\u30EC\u30B9\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    emailR: "\u9069\u5207\u306A\u30E1\u30FC\u30EB\u30A2\u30C9\u30EC\u30B9\u3092\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",
    registerSu: "\u767B\u9332\u306B\u6210\u529F\u3057\u307E\u3057\u305F\u3002\u30B8\u30E3\u30F3\u30D7\u30ED\u30B0\u30A4\u30F3\u3057\u3088\u3046\u3068\u3057\u3066\u3044\u307E\u3059",
    usernameRule: "\u6587\u5B57\u3001\u6570\u5B57\u3001-\u307E\u305F\u306F_"
  }
};

export { config as default };
//# sourceMappingURL=constants.mjs.map
