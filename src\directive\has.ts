import { ref, computed } from 'vue'
import store from '@/store'
import { points as PointsList } from '@/utils/PointsList'

// const { points } = store.state.user;
// const points = computed(() => store.state.user.points)

// if (import.meta.env.MODE === 'development') {
//     ((() => {
//         const arrFilter = points.value.filter((p: any) => !PointsList.includes(p))
//         if (arrFilter.length > 0) {
//             console.error('服务器返回无效的权限标识:\n', arrFilter);
//         }
//     })())
// }

interface Has {
  name: string
  mounted: (el: Element, binding: AnyObject) => void
}

const has: Has = {
  name: 'has',
  // ！！！注意Vue3中的改变：
  // 在Vue3中，自定义指令的生命周期钩子有所改变，将不再存在 inserted 生命周期，建议使用新的 mounted 生命周期钩子。
  // 参考：[Vue2] https://cn.vuejs.org/v2/guide/custom-directive.html#%E9%92%A9%E5%AD%90%E5%87%BD%E6%95%B0
  // 参考：[Vue3] https://v3.cn.vuejs.org/guide/custom-directive.html#%E9%92%A9%E5%AD%90%E5%87%BD%E6%95%B0
  mounted(el: Element, binding: AnyObject) {
    if (!binding.value) return
    if (import.meta.env.MODE === 'development') {
      const arrFilter = ((PointsList, ...values) => values.flat(Infinity).filter((v) => !PointsList.includes(v)))(PointsList, binding.value)
      if (arrFilter.length > 0) {
        console.error('无法识别的标识: ', arrFilter.join(','))
        return
      }
    }
    // const { points } = store.state.user;
    const points = computed(() => store.state.user.points)
    const flag = ((points, ...values) => values.flat(Infinity).filter((v) => points.value.includes(v)).length > 0)(points, binding.value)
    if (!flag) {
      el.parentNode?.removeChild(el)
    }
  }
}

export { has }
