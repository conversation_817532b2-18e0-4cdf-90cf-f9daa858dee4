{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/empty/src/constants.ts"], "sourcesContent": ["import {\r\n  api,\r\n  bloodDark,\r\n  bloodLight,\r\n  dark404,\r\n  dark405,\r\n  echarts,\r\n  feature,\r\n  file,\r\n  image,\r\n  imagePlaceholder,\r\n  light404,\r\n  light405,\r\n  listDark,\r\n  listLight,\r\n  message,\r\n  model,\r\n  networkDark,\r\n  networkLight,\r\n  powerDark,\r\n  powerLight,\r\n  unListDark,\r\n  unListLight,\r\n} from \"./images/index\";\r\n\r\nexport const imgConfig = {\r\n  light: {\r\n    list: listLight,\r\n    unList: unListLight,\r\n    echarts,\r\n    file,\r\n    power: powerLight,\r\n    feature,\r\n    blood: bloodLight,\r\n    model,\r\n    network: networkLight,\r\n    \"404\": light404,\r\n    \"405\": light405,\r\n    message,\r\n    image,\r\n    imagePlaceholder,\r\n    api,\r\n  },\r\n  dark: {\r\n    list: listDark,\r\n    unList: unListDark,\r\n    echarts,\r\n    file,\r\n    power: powerDark,\r\n    feature,\r\n    blood: bloodDark,\r\n    model,\r\n    network: networkDark,\r\n    \"404\": dark404,\r\n    \"405\": dark405,\r\n    message,\r\n    image,\r\n    imagePlaceholder,\r\n    api,\r\n  },\r\n};\r\n\r\nexport const descConfig = {\r\n  \"zh-CN\": {\r\n    list: \"暂无数据\",\r\n    unList: \"暂无内容\",\r\n    echarts: \"暂无图表\",\r\n    file: \"暂未添加文件\",\r\n    power: \"暂无权限\",\r\n    feature: \"暂无该功能\",\r\n    blood: \"暂无血缘信息\",\r\n    model: \"暂无模型，请先新建\",\r\n    network: \"网络中断，请刷新重试\",\r\n    \"404\": \"404\",\r\n    \"405\": \"405\",\r\n    message: \"暂无消息\",\r\n    image: \"暂无图片，请先添加\",\r\n    imagePlaceholder: \"图片占位\",\r\n    api: \"暂无API，请先新建\",\r\n  },\r\n  \"en-US\": {\r\n    list: \"No data\",\r\n    unList: \"No content\",\r\n    echarts: \"No chart\",\r\n    file: \"No files added\",\r\n    power: \"No permission\",\r\n    feature: \"This function is not available\",\r\n    blood: \"No bloodline information\",\r\n    model: \"There is no model, please create a new one first\",\r\n    network: \"The network is interrupted, please refresh and try again.\",\r\n    \"404\": \"404\",\r\n    \"405\": \"405\",\r\n    message: \"No news\",\r\n    image: \"There are no pictures, please add them first\",\r\n    imagePlaceholder: \"Picture placeholder\",\r\n    api: \"There is no API, please create a new one first\",\r\n  },\r\n};\r\n"], "names": ["listLight", "unListLight", "echarts", "file", "powerLight", "feature", "bloodLight", "model", "networkLight", "light404", "light405", "message", "image", "imagePlaceholder", "api", "listDark", "unListDark", "powerDark", "bloodDark", "networkDark", "dark404", "dark405"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBO,MAAM,SAAY,GAAA;AAAA,EACvB,KAAO,EAAA;AAAA,IACL,IAAM,EAAAA,oBAAA;AAAA,IACN,MAAQ,EAAAC,sBAAA;AAAA,aACRC,kBAAA;AAAA,UACAC,eAAA;AAAA,IACA,KAAO,EAAAC,qBAAA;AAAA,aACPC,kBAAA;AAAA,IACA,KAAO,EAAAC,qBAAA;AAAA,WACPC,gBAAA;AAAA,IACA,OAAS,EAAAC,uBAAA;AAAA,IACT,KAAO,EAAAC,oBAAA;AAAA,IACP,KAAO,EAAAC,oBAAA;AAAA,aACPC,kBAAA;AAAA,WACAC,gBAAA;AAAA,sBACAC,2BAAA;AAAA,SACAC,cAAA;AAAA,GACF;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,IAAM,EAAAC,mBAAA;AAAA,IACN,MAAQ,EAAAC,qBAAA;AAAA,aACRd,kBAAA;AAAA,UACAC,eAAA;AAAA,IACA,KAAO,EAAAc,oBAAA;AAAA,aACPZ,kBAAA;AAAA,IACA,KAAO,EAAAa,oBAAA;AAAA,WACPX,gBAAA;AAAA,IACA,OAAS,EAAAY,sBAAA;AAAA,IACT,KAAO,EAAAC,mBAAA;AAAA,IACP,KAAO,EAAAC,mBAAA;AAAA,aACPV,kBAAA;AAAA,WACAC,gBAAA;AAAA,sBACAC,2BAAA;AAAA,SACAC,cAAA;AAAA,GACF;AACF,EAAA;AAEO,MAAM,UAAa,GAAA;AAAA,EACxB,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,0BAAA;AAAA,IACN,MAAQ,EAAA,0BAAA;AAAA,IACR,OAAS,EAAA,0BAAA;AAAA,IACT,IAAM,EAAA,sCAAA;AAAA,IACN,KAAO,EAAA,0BAAA;AAAA,IACP,OAAS,EAAA,gCAAA;AAAA,IACT,KAAO,EAAA,sCAAA;AAAA,IACP,KAAO,EAAA,wDAAA;AAAA,IACP,OAAS,EAAA,8DAAA;AAAA,IACT,KAAO,EAAA,KAAA;AAAA,IACP,KAAO,EAAA,KAAA;AAAA,IACP,OAAS,EAAA,0BAAA;AAAA,IACT,KAAO,EAAA,wDAAA;AAAA,IACP,gBAAkB,EAAA,0BAAA;AAAA,IAClB,GAAK,EAAA,+CAAA;AAAA,GACP;AAAA,EACA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,SAAA;AAAA,IACN,MAAQ,EAAA,YAAA;AAAA,IACR,OAAS,EAAA,UAAA;AAAA,IACT,IAAM,EAAA,gBAAA;AAAA,IACN,KAAO,EAAA,eAAA;AAAA,IACP,OAAS,EAAA,gCAAA;AAAA,IACT,KAAO,EAAA,0BAAA;AAAA,IACP,KAAO,EAAA,kDAAA;AAAA,IACP,OAAS,EAAA,2DAAA;AAAA,IACT,KAAO,EAAA,KAAA;AAAA,IACP,KAAO,EAAA,KAAA;AAAA,IACP,OAAS,EAAA,SAAA;AAAA,IACT,KAAO,EAAA,8CAAA;AAAA,IACP,gBAAkB,EAAA,qBAAA;AAAA,IAClB,GAAK,EAAA,gDAAA;AAAA,GACP;AACF;;;;;"}