<template>
  <el-table
    ref="$refs"
    v-bind="$attrs"
    fit
    :tooltip-effect="effect"
    :header-cell-style="{
      'background-color': 'var(--ops-table-bg-heder-color)',
      'font-weight': 400,
      color: 'var(--ops-table-text-heder-color)'
    }"
    :cell-style="{
      color: 'var(--ops-text-color)'
    }"
    :class="`w-100% ${data.length ? '' : 'el-table-is-empty'}`"
    style="width: 100%; height: 100%"
    :data="data"
    :max-height="maxHeight"
    @filter-change="filterChange"
  >
    <slot name="before" />
    <template v-for="(col, index) in columns">
      <el-table-column v-if="col?.slot && vif(col?.vIf)" v-bind="col" :key="index">
        <template v-if="col?.slotHeader" #header="{ column, $index }">
          <slot :name="col.prop + 'Header'" :column="column" :$index="$index" />
        </template>
        <template #default="{ row, column, $index, store }">
          <slot :name="col.prop" :row="row" :column="column" :$index="$index" :store="store" />
        </template>
      </el-table-column>
      <el-table-column v-if="!col?.slot && vif(col?.vIf)" v-bind="col" :key="index" />
    </template>
    <template #empty>
      <slot name="empty">
        <BaseEmpty image="nodata.png" :image-size="120" />
      </slot>
    </template>
    <slot />
    <slot name="after" />
  </el-table>
</template>

<script lang="ts" setup>
import type { ElTable } from 'element-plus'
const { store: stores, t } = useBasicTool()
const effect = computed(() => stores.state.app.tooltipEffect)
interface Column {
  prop?: string
  label?: string
  slot?: boolean
  slotHeader?: boolean
  vIf?: boolean
}
interface Props {
  columns?: Array<Column>
  heightOff?: boolean
  data: any[]
  maxHeight?: number
}

const emit = defineEmits(['filter-change'])

withDefaults(defineProps<Props>(), {
  columns: () => [],
  data: () => []
})
function vif(val?: boolean) {
  return val ?? true
}

const filterChange = (filters: any) => {
  console.log('filterChange', filters, Object.values(filters)[0])
  emit('filter-change', filters)
}

// 表格的ref
const $refs = ref<InstanceType<typeof ElTable>>()
// 把$ref暴露出去方便父组件使用
defineExpose({ $refs })
</script>

<style lang="scss" scoped>
.el-table-is-empty {
  min-height: 300px;
  :deep(.el-scrollbar) {
    min-height: 300px;
  }
  :deep(.el-table__inner-wrapper) {
    min-height: 300px;
  }
}
</style>
