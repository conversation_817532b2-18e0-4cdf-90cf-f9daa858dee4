import cloneDeep from 'lodash-es/cloneDeep'
export function useGetListByPage(store: any, clusterServiceId: string, clusterServiceInstanceId: string) {
  const defaultData = ref()
  const viewData = ref()
  const { route } = useBasicTool()
  const isLoadList = ref(true)
  const { pageInfo, resetPageInfo } = usePage()
  const editRecords = ref<any[]>([])
  const customParametersData = ref<any[]>([])
  // 删除自定义对比数组
  const customParametersDataCompare = ref<any[]>([])
  const searchForm = reactive({
    searchKey: '',
    confName: '',
    roleName: '',
    labelType: ''
  })
  function onSearch() {
    handlePageChange()
  }
  function onReset() {
    searchForm.searchKey = ''
    searchForm.confName = ''
    searchForm.roleName = ''
    searchForm.labelType = ''
    getData()
  }
  function handlePageChange() {
    getData()
  }
  function getData() {
    isLoadList.value = true
    let url = 'clusterServiceConfig/getClusterConfigsPage'
    if (clusterServiceInstanceId) url = 'clusterServiceConfig/getInstanceConfigsPage'
    store
      .dispatch(url, {
        ...pageInfo,
        ...searchForm,
        clusterServiceId,
        clusterServiceInstanceId
      })
      .then((res: any) => {
        res.data.mergeConfigs.records.map((item) => {
          if (item.selectList) {
            const valIndex = item.selectList.findIndex((i) => String(i.id) === item.confDefaultValue)

            if (valIndex !== -1) {
              item.confDefaultValue = item.selectList[valIndex].confKey
              item.confValue = item.selectList[valIndex].confKey
              if (item.instanceConfigs) {
                item.instanceConfigs.map((i) => {
                  i.confValue = item.selectList[valIndex].confKey
                })
              }
            }
          }
        })
        defaultData.value = cloneDeep(res.data.mergeConfigs.records)
        viewData.value = cloneDeep(res.data.mergeConfigs.records)

        pageInfo.total = res.data.mergeConfigs.total
        editRecords.value = res.data?.editRecords?.filter((item: any) => item.configTemplateId) ?? []
        customParametersDataCompare.value = res.data?.editRecords?.filter((item: any) => !item.configTemplateId) ?? []
        customParametersData.value = res.data?.editRecords?.filter((item: any) => !item.configTemplateId) ?? []
      })
      .finally(() => {
        isLoadList.value = false
      })
  }
  return {
    defaultData,
    viewData,
    pageInfo,
    searchForm,
    handlePageChange,
    getData,
    resetPageInfo,
    onSearch,
    onReset,
    isLoadList,
    editRecords,
    customParametersData,
    customParametersDataCompare
  }
}
