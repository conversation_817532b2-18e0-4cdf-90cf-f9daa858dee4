export declare function useDrawer({ cancel, confirm, show, defaultData, }?: {
    cancel?: (val?: any, drawerData?: any) => void;
    confirm?: (val?: any, drawerData?: any) => void;
    show?: (val?: any) => void;
    defaultData?: any;
}): {
    showDrawer: (val?: any) => void;
    onCancel: (val?: any) => void;
    onConfirm: (val?: any) => void;
    visible: import("vue").Ref<boolean>;
    drawerData: any;
};
