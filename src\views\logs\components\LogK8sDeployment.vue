<template>
  <BaseLayout :calc-minuend="152" :is-footer="false">
    <template #header>
      <div class="search mt-20px">
        <div class="open-box">
          <el-form :inline="true" :model="searchModelObj" label-width="auto">
            <el-form-item :label="$t('form.keywordSearch') + '：'">
              <el-input v-model="searchModelObj.key" class="key-input" :placeholder="$t('form.pleaseEnterKeyWords')" clearable />
            </el-form-item>
            <el-form-item label-width="0">
              <BaseButton class="btn1" debounce type="primary" @click="searchTable">{{ $t('button.inquire') }}</BaseButton>
              <BaseButton type="info" debounce class="btn2" @click="resetTable"> {{ $t('button.reset') }} </BaseButton>
            </el-form-item>
          </el-form>
          <div>
            <BaseButton
              type="primary"
              class="es-button"
              v-if="$has('down-logs')"
              debounce
              :disabled="searchModelObj.index === ''"
              @click="
                () => {
                  download(getSearchParams())
                }
              "
            >
              <i class="iconfont icon-xiazai" />
              &nbsp;{{ $t('button.downloadLog') }}
            </BaseButton>
            <img
              v-if="isSearch"
              @click="
                () => {
                  switchSearch()
                  wacthActive(isSearch)
                }
              "
              class="inline-block ml-10px mb-16px cursor-pointer"
              src="@/assets/icons/icon_you.svg"
              alt="icon"
            />
            <img
              v-else
              @click="
                () => {
                  switchSearch()
                  wacthActive(isSearch)
                }
              "
              class="inline-block ml-10px mb-16px cursor-pointer"
              src="@/assets/icons/icon_mieyou.svg"
              alt="icon"
            />
          </div>
        </div>
        <div :class="{ 'search-box': true, 'search-box--off': !isSearch }">
          <el-form :inline="true" :model="searchModelObj" label-width="auto">
            <el-form-item>
              <template #label>
                <span class="asterisk">{{ $t('form.roleName') + '：' }}</span>
              </template>
              <el-select v-model="searchModelObj.index" class="input-width" :placeholder="$t('form.pleaseChoose')" clearable @change="roleNameChange">
                <el-option v-for="item in roleNameOptions" :key="item.roleName" :label="item.roleName" :value="item.roleName" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.nodeName') + '：'">
              <el-input v-model="searchModelObj.k8s_node_name" class="input-width" :placeholder="$t('form.pleaseEnter')" clearable />
            </el-form-item>
            <el-form-item :label="$t('form.logLevel') + '：'">
              <el-select v-model="searchModelObj.level" class="input-width" :placeholder="$t('form.pleaseChoose')">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.namespaceName') + '：'">
              <el-select v-model="searchModelObj.k8s_pod_namespace" class="input-width" clearable :placeholder="$t('form.pleaseChoose')">
                <el-option v-for="(item, key) in k8s_pod_namespaceOptions" :key="key" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item clearable :label="$t('form.podName') + '：'">
              <el-select v-model="searchModelObj.k8s_pod" :placeholder="$t('form.pleaseChoose')" class="input-width">
                <el-option v-for="(item, key) in k8s_podOptions" :key="key" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.timeout') + '：'">
              <el-input v-model="searchModelObj.timeOut" placeholder="≤60" class="input-width" clearable />
            </el-form-item>
            <el-form-item :label="$t('form.timeRange') + '：'">
              <el-select
                v-model="searchModelObj.lastTime"
                :placeholder="$t('form.pleaseChoose')"
                class="input-width"
                clearable
                @change="lastTimeChange"
              >
                <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.selectionPeriod') + '：'">
              <el-date-picker
                v-model="searchModelObj.searchTimes"
                type="datetimerange"
                range-separator="⇀"
                :start-placeholder="$t('form.startDate')"
                :end-placeholder="$t('form.endDate')"
                prefix-icon="el-icon-date"
                clearable
                :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="searchTimesChange"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
    <template #content>
      <LogContentTable :table-data="tableData" :loading="loading" class="k8sTable" />
    </template>
  </BaseLayout>
</template>

<script lang="ts" setup>
import { dayFormat, tableDayFormat } from '@/utils/Day'
import LogContentTable from './LogContentTable.vue'
import { useJournal } from './hooks/useJournal'
import { useSwitchSearchCon } from './hooks/useSwitchSearchCon'
const { route, t, store, $has } = useBasicTool()
const { switchSearch, isSearch } = useSwitchSearchCon()
const props = defineProps<{
  isLoading: boolean
  clusterId: string
}>()
const emits = defineEmits<{
  (e: 'update:isLoading', value: boolean): void
}>()
const { load, total, loading, timeOptions, levelOptions, wacthActive, onScroll, tableLevelFormatter, download } = useJournal()
const { roleSimpleName, msgSource, instanceName, deployWay } = route.query

// 搜索参数对象键值对数据类型，不定义遍历对象时会报错
interface SearchModelObj {
  [key: string]: string
}

// 搜索参数
const searchModelObj: SearchModelObj = reactive({
  index: '', // 角色名称(zknode*)
  level: 'INFO', // 日志级别
  timeOut: '', // 超时时间
  lastTime: '1800000', // 时间段
  key: '', // 关键字搜索
  k8s_node_name: '', // 节点名称
  k8s_pod_namespace: '', // 命名空间名称
  k8s_pod: '', // pod名称
  searchTimes: '' // 时间范围
})

const roleNameOptions = ref<any>()

// 表格数据
const tableData = reactive({
  columns: [
    {
      prop: 'k8s_node_name',
      label: computed(() => t('table.nodeName'))
    },
    {
      prop: 'k8s_pod_namespace',
      label: computed(() => t('table.namespaceName'))
    },
    {
      prop: 'k8s_pod',
      label: computed(() => t('table.podName'))
    },
    {
      prop: 'component_name',
      label: computed(() => t('table.componentName'))
    },
    {
      prop: 'level',
      label: computed(() => t('table.logLevel')),
      slot: true
    },
    {
      prop: '@timestamp',
      label: computed(() => t('table.time')),
      formatter: tableDayFormat,
      default: true
    },
    {
      prop: 'message',
      label: computed(() => t('table.information')),
      'min-width': 400,
      default: true
    }
  ],
  data: []
})

const k8s_pod_namespaceOptions = ref()
const k8s_podOptions = ref()

onMounted(() => {
  store.dispatch('serviceInstance/getServiceInstancelistroles', { type: 2, clusterId: props.clusterId }).then((res: any) => {
    roleNameOptions.value = res.data
    if (msgSource === 'kubernetes') {
      searchModelObj.index = String(roleSimpleName)
      roleNameChange()
      getAllEs()
    }
    if (deployWay == '2' || deployWay == '3') {
      searchModelObj.index = String(roleSimpleName)
      roleNameChange()
      searchModelObj.k8s_pod = String(instanceName)
      getAllEs()
    }
  })
})
function roleNameChange() {
  searchModelObj.k8s_pod = ''
  searchModelObj.k8s_pod_namespace = ''
  let data = roleNameOptions.value.find((item: any) => item.roleName === searchModelObj.index)?.containerNames
  k8s_podOptions.value = data ? data : []
  data = roleNameOptions.value.find((item: any) => item.roleName === searchModelObj.index)?.kubeNsNames
  k8s_pod_namespaceOptions.value = data ? data : []
}
nextTick(() => {
  onScroll(tableData, getAllEs, '.k8sTable')
})

// 选中时间段发生变化时
function lastTimeChange() {
  if (searchModelObj.lastTime !== '') {
    searchModelObj.searchTimes = ''
  }
}

// 选中时间范围发生变化时
function searchTimesChange() {
  if (searchModelObj.searchTimes !== '') {
    searchModelObj.lastTime = ''
  }
}

// 根据条件查询表格
function searchTable() {
  emits('update:isLoading', true)
  tableData.data = []
  getAllEs()
}

// 重置表格
function resetTable() {
  for (const item in searchModelObj) {
    searchModelObj[item] = ''
  }
  searchModelObj.lastTime = '1800000'
  searchModelObj.level = 'INFO'
  tableData.data = []
}
function getSearchParams() {
  const { index, level, timeOut, key, lastTime, k8s_node_name, k8s_pod_namespace, k8s_pod, searchTimes } = searchModelObj
  const tableLength = tableData.data.length
  const searchAfter = tableLength ? tableData.data[tableLength - 1]['sort'] : new Date().getTime()
  let start = ''
  let end = ''
  if (lastTime) {
    start = dayFormat(new Date().getTime() - Number(lastTime))
    end = dayFormat(new Date().getTime())
  } else if (searchTimes) {
    start = searchTimes[0]
    end = searchTimes[1]
  }
  return {
    searchAfter,
    index,
    level,
    timeOut,
    key,
    msgSource: 'kubernetes',
    where: {
      k8s_node_name,
      k8s_pod_namespace,
      k8s_pod,
      searchTimes: {
        start,
        end
      }
    },
    clusterId: props.clusterId
  }
}
// 根据条件获取列表数据
function getAllEs() {
  store
    .dispatch('es/getAllEs', getSearchParams())
    .then((res: any) => {
      const result = res.data.records
      tableData.data = tableData.data.concat(result)
      total.value = res.data.total
      load.value = true
    })
    .finally(() => {
      loading.value = false
      emits('update:isLoading', false)
    })
}
</script>

<style lang="scss" scoped>
@import '../scss/log';
</style>
