<template>
  <div class="h-48px lh-48px pl-60px border-solid-bottom bg-bgWhite relative">
    <span class="f-16 font-600">{{ props.title }}</span>
    <img src="@/assets/img/back.png" class="cursor-pointer back-icon" @click="handleBack" />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  backFun: {
    type: Function,
    default: null
  }
})

const { router } = useBasicTool()

const handleBack = () => {
  if (props.backFun) {
    props.backFun()
  } else {
    router.back()
  }
}
</script>

<style scoped lang="scss">
.back-icon {
  position: absolute;
  top: 12px;
  left: 20px;
  width: 24px;
  height: 24px;
}
</style>
