{"version": 3, "file": "component.js", "sources": ["../../../../packages/dc-components/component.ts"], "sourcesContent": ["import {\r\n  DcButton,\r\n  DcCheckbox,\r\n  DcDialog,\r\n  DcDrawer,\r\n  DcEditTable,\r\n  DcEditTableColumn,\r\n  DcEmpty,\r\n  DcForm,\r\n  DcLogin,\r\n  DcRadio,\r\n  DcSearchFilter,\r\n  DcSelect,\r\n  DcTable,\r\n  DcViewLayout,\r\n} from \"@dc-components/components\";\r\n\r\nimport type { Plugin } from \"vue\";\r\n\r\nexport default [\r\n  DcButton,\r\n  DcEditTable,\r\n  DcEditTableColumn,\r\n  DcSelect,\r\n  DcForm,\r\n  DcRadio,\r\n  DcCheckbox,\r\n  DcTable,\r\n  DcSearchFilter,\r\n  DcDialog,\r\n  DcDrawer,\r\n  DcEmpty,\r\n  DcLogin,\r\n  DcViewLayout,\r\n] as Plugin[];\r\n"], "names": ["DcButton", "DcEditTable", "DcEditTableColumn", "DcSelect", "DcForm", "DcRadio", "DcCheckbox", "DcTable", "DcSearchFilter", "DcDialog", "DcDrawer", "DcEmpty", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DcViewLayout"], "mappings": ";;;;;;AAmBA,iBAAe;AAAA,EACbA,iBAAA;AAAA,EACAC,oBAAA;AAAA,EACAC,0BAAA;AAAA,EACAC,iBAAA;AAAA,EACAC,eAAA;AAAA,EACAC,gBAAA;AAAA,EACAC,mBAAA;AAAA,EACAC,gBAAA;AAAA,EACAC,uBAAA;AAAA,EACAC,iBAAA;AAAA,EACAC,iBAAA;AAAA,EACAC,gBAAA;AAAA,EACAC,gBAAA;AAAA,EACAC,qBAAA;AACF,CAAA;;;;"}