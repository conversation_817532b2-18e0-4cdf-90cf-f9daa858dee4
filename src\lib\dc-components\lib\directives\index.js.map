{"version": 3, "file": "index.js", "sources": ["../../../../packages/directives/index.ts"], "sourcesContent": ["// import directives\r\nimport copy from \"./common/copy\";\r\nimport type { App } from \"vue\";\r\n\r\nconst directivesList: any = {\r\n  copy,\r\n};\r\n\r\nconst directives = {\r\n  install(app: App<Element>) {\r\n    Object.keys(directivesList).forEach((key) => {\r\n      app.directive(key, directivesList[key]);\r\n    });\r\n  },\r\n};\r\n\r\nexport default directives;\r\n"], "names": ["copy"], "mappings": ";;;;;;AAIA,MAAM,cAAsB,GAAA;AAAA,QAC1BA,eAAA;AACF,CAAA,CAAA;AAEA,MAAM,UAAa,GAAA;AAAA,EACjB,QAAQ,GAAmB,EAAA;AACzB,IAAA,MAAA,CAAO,IAAK,CAAA,cAAc,CAAE,CAAA,OAAA,CAAQ,CAAC,GAAQ,KAAA;AAC3C,MAAI,GAAA,CAAA,SAAA,CAAU,GAAK,EAAA,cAAA,CAAe,GAAI,CAAA,CAAA,CAAA;AAAA,KACvC,CAAA,CAAA;AAAA,GACH;AACF;;;;"}