import 'monaco-editor/esm/vs/basic-languages/ini/ini.contribution'
import 'monaco-editor/esm/vs/basic-languages/python/python.contribution'
import 'monaco-editor/esm/vs/basic-languages/shell/shell.contribution'
import 'monaco-editor/esm/vs/basic-languages/sql/sql.contribution'
import 'monaco-editor/esm/vs/basic-languages/xml/xml.contribution'
import 'monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution'
import 'monaco-editor/esm/vs/language/json/monaco.contribution'

import 'monaco-editor/esm/vs/editor/contrib/comment/browser/comment'
import 'monaco-editor/esm/vs/editor/contrib/contextmenu/browser/contextmenu'
import 'monaco-editor/esm/vs/editor/contrib/copyPaste/browser/copyPasteController'
import 'monaco-editor/esm/vs/editor/contrib/cursorUndo/browser/cursorUndo'
import 'monaco-editor/esm/vs/editor/contrib/editorState/browser/editorState'
import 'monaco-editor/esm/vs/editor/contrib/find/browser/findController'
import 'monaco-editor/esm/vs/editor/contrib/folding/browser/folding'
import 'monaco-editor/esm/vs/editor/contrib/format/browser/format'
import 'monaco-editor/esm/vs/editor/contrib/gotoError/browser/gotoError'
import 'monaco-editor/esm/vs/editor/contrib/hover/browser/hover'
import 'monaco-editor/esm/vs/editor/contrib/lineSelection/browser/lineSelection'
import 'monaco-editor/esm/vs/editor/contrib/message/browser/messageController'
import 'monaco-editor/esm/vs/editor/contrib/snippet/browser/snippetController2'
import 'monaco-editor/esm/vs/editor/contrib/suggest/browser/suggestController'
import 'monaco-editor/esm/vs/editor/contrib/tokenization/browser/tokenization'
import 'monaco-editor/esm/vs/editor/contrib/wordHighlighter/browser/wordHighlighter'

import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'

export function initEditor() {
  monaco.editor.defineTheme('lightTheme', {
    base: 'vs',
    inherit: true,
    rules: [],
    colors: {
      'editor.background': '#f5f6f9',
      'editorGutter.background': '#ffffff',
      'minimap.background': '#f7f9fc'
    }
  })
}
