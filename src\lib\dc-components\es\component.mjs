import { DcButton, DcEditTable, DcEditTableColumn, DcSelect, DcForm, DcRadio, DcCheckbox, DcTable, DcSearchFilter, DcDialog, DcDrawer, DcEmpty, DcLogin, DcViewLayout } from './components/index.mjs';

var Components = [
  DcButton,
  DcEditTable,
  DcEditTableColumn,
  DcSelect,
  DcForm,
  DcRadio,
  DcCheckbox,
  DcTable,
  DcSearchFilter,
  DcDialog,
  DcDrawer,
  DcEmpty,
  DcLogin,
  DcViewLayout
];

export { Components as default };
//# sourceMappingURL=component.mjs.map
