{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../../packages/components/form/src/components/Text/index.tsx"], "sourcesContent": ["import { defineComponent, PropType } from \"vue\";\r\nimport \"./index.scss\";\r\n\r\nexport default defineComponent({\r\n  name: \"DcFormText\",\r\n  props: {\r\n    modelValue: {\r\n      type: [String, Number, Boolean] as PropType<string | number | boolean>,\r\n    },\r\n  },\r\n\r\n  render() {\r\n    return <div class=\"dc-form-text\">{this.$props.modelValue}</div>;\r\n  },\r\n});\r\n"], "names": ["defineComponent", "name", "props", "modelValue", "type", "String", "Number", "Boolean", "render", "_createVNode", "$props"], "mappings": ";;;AAGA,iBAAeA,eAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,YAAY;AAClBC,EAAAA,KAAK,EAAE;AACLC,IAAAA,UAAU,EAAE;AACVC,MAAAA,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,OAAO,CAAA;AAChC,KAAA;GACD;AAEDC,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAAC,WAAA,CAAA,KAAA,EAAA;AAAA,MAAA,OAAA,EAAA,cAAA;AAAA,KAAA,EAAA,CAAkC,IAAI,CAACC,MAAM,CAACP,UAAU,CAAA,CAAA,CAAA;AAC1D,GAAA;AACF,CAAC,CAAC;;;;"}