<template>
  <div class="batch-config">
    <BaseLayout :is-header="false" :is-footer="false">
      <template #content>
        <BaseElCollapse v-model="activeNames">
          <el-collapse-item v-for="(el, i) in updateList" :name="i" :key="i">
            <template #title>
              <TheTitle class="ml-20px">
                <template #titleText> {{ el.confKey }} </template>
              </TheTitle>
            </template>
            <div class="h-80 w-full">
              <MonacoEditor v-model="el.confValue" :readOnly="false" @change="codeChange" />
            </div>
          </el-collapse-item>
        </BaseElCollapse>
      </template>
    </BaseLayout>
    <div class="batch-config-group">
      <BaseButton type="primary" :disabled="!isUpdate" @click="save">
        {{ $t('button.save') }}
      </BaseButton>
    </div>
    <UpdateInfo
      v-model="updateVisible"
      :cluster-service-id="clusterServiceId"
      :cluster-service-instance-id="clusterServiceInstanceId"
      :list="updateList"
      :config-request-from="configRequestFrom"
      :isBatch="true"
      @submit="submitUpdateInfo"
    />
  </div>
</template>

<script lang="ts" setup>
import { GetBatchConfiguration } from '@/api/clusterServiceConfigApi'
import UpdateInfo from './components/UpdateInfo.vue'

const { route, t, router, setBreadList, store, $has } = useBasicTool()

const props = defineProps({
  clusterServiceId: {
    type: String,
    default: ''
  },
  clusterServiceInstanceId: {
    type: String,
    default: ''
  }
})

const configRequestFrom = reactive<{
  configRequestDtos: any
  modifyReason: string
  applied: number
}>({
  configRequestDtos: [],
  modifyReason: '',
  applied: 0
})

const updateList = ref<Record<string, string>[]>([])
const activeNames = ref([])
const isUpdate = ref(false)

const updateVisible = ref(false)

const getConfig = () => {
  GetBatchConfiguration({
    clusterServiceId: props.clusterServiceId
  }).then((res) => {
    if (res.data.code === 0) {
      updateList.value = []
      res.data.data?.forEach((el: Record<string, string>) => {
        Object.keys(el).forEach((ele) => {
          updateList.value.push({
            confKey: ele,
            oldValue: el[ele],
            confValue: el[ele]
          })
        })
      })
      updateList.value.forEach((el, i) => {
        activeNames.value.push(i)
      })
      // nodeClassData.data = res.data.data.records || []
      // nodeClassPageInfo.total = res.data.data.total || 0
    }
  })
}

const codeChange = () => {
  isUpdate.value = true
}

const save = () => {
  updateVisible.value = true
}

const submitUpdateInfo = () => {
  isUpdate.value = false
  updateVisible.value = false
}

onMounted(() => {
  getConfig()
})
</script>

<style lang="scss" scoped>
.batch-config {
  box-sizing: border-box;
  box-sizing: border-box;
  height: 100%;
  padding: 20px 0px;
  // background-color: var(--ops-bg-white-color);
  :deep(.title-box) {
    margin-bottom: 0;
  }
  .batch-config-group {
    position: absolute;
    right: 16px;
    bottom: 0px;
    z-index: 2;
    box-sizing: border-box;
    width: calc(100% - 32px);
    height: 60px;
    padding: 0 20px 10px;
    background-color: var(--ops-bg-white-color);
    @include flex(flex-end, center);
    .change-reason-group__input {
      width: 480px;
    }
  }
}
</style>
