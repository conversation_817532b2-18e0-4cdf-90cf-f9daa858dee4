<template>
  <div class="h-56px flex items-center mr-20px">
    <el-select
      ref="selectRef"
      v-show="isShow"
      :model-value="tenantry"
      class="min-w-5px"
      clearable
      :placeholder="$t('replenish.chooseTenant')"
      @clear="clear"
      @change="select"
    >
      <template #prefix>
        <span class="c-text">
          {{ data?.find((item: any) => item.tenantId === tenantry)?.name || $t('replenish.chooseTenant') }}
        </span>
      </template>
      <el-option v-for="item in data" :key="item.tenantId" :label="item.name" :value="item.tenantId" />
    </el-select>
  </div>
</template>
<script lang="ts" setup>
const { store } = useBasicTool()
const tenantry = computed(() => store.state.user.tenantId)
onMounted(() => {
  getTenantAll()
})
const selectRef = ref()
const isShow = ref(false)

function select(value: string) {
  store.commit('user/setTenantId', value)
  location.reload()
}
function clear() {
  store.commit('user/setTenantId', '')
  location.reload()
}
const data = ref()
function getTenantAll() {
  store.dispatch('user/tenantAll').then((res) => {
    data.value = res.data
    isShow.value = true
  })
}
</script>
<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  padding: 0 !important;
  box-shadow: none !important;
}
:deep(.el-input__inner) {
  display: none;
  border: none !important;
  box-shadow: none !important;
}
:deep(.el-select) {
  --el-select-input-focus-border-color: transparent;
  --el-input-icon-color: transparent;
  .el-input__wrapper {
    background-color: transparent !important;
  }
}
:deep(.el-input__prefix) {
  position: relative;
  right: -10px;
}
</style>
