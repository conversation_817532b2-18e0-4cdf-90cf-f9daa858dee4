{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/form/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    ref=\"formRef\"\r\n    :inline=\"inline\"\r\n    :label-width=\"props.config.labelWidth\"\r\n    :label-position=\"props.config.labelPosition\"\r\n    :show-message=\"props.config.showMessage\"\r\n    :inline-message=\"props.config.inlineMessage\"\r\n    :disabled=\"props.config.disabled\"\r\n    :model=\"model\"\r\n    class=\"dc-form\"\r\n    @validate=\"handleValidate\"\r\n  >\r\n    <template v-for=\"item in props.config.children\" :key=\"item.model\">\r\n      <DcFormItem\r\n        v-model=\"model[item.model]\"\r\n        :config=\"item\"\r\n        :visible=\"visibleControl[item.model]\"\r\n        :is-inline=\"inline\"\r\n        :margin=\"{\r\n          ...(props.config.itemMargin || {}),\r\n          ...(item.margin || {}),\r\n        }\"\r\n        @triggerEffect=\"itemChange\"\r\n      />\r\n    </template>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, ref, watch } from \"vue\";\r\nimport { ElForm } from \"element-plus\";\r\nimport { cloneDeep, isObject } from \"lodash\";\r\nimport DcFormItem from \"./components/FormItem/index.vue\";\r\n\r\nimport type { FormItemProp, FormValidateCallback } from \"element-plus\";\r\nimport type { PropType } from \"vue\";\r\nimport type {\r\n  DcFormConfig,\r\n  DcFormItemEffect,\r\n  DcFormModel,\r\n  DcFormVisibleControl,\r\n} from \"./types\";\r\n\r\ndefineOptions({\r\n  name: \"DcForm\",\r\n});\r\n\r\nconst props = defineProps({\r\n  config: {\r\n    type: Object as PropType<DcFormConfig>,\r\n    default: () => ({\r\n      children: [],\r\n    }),\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"validate\"]);\r\n\r\nconst formRef = ref();\r\n\r\nconst model = ref<DcFormModel>({});\r\nconst visibleControl = ref<DcFormVisibleControl>({});\r\n\r\nconst inline = computed(() =>\r\n  typeof props.config.inline !== \"undefined\" ? props.config.inline : false\r\n);\r\n\r\nconst handleValidate = (\r\n  prop: FormItemProp,\r\n  isValid: boolean,\r\n  message: string\r\n) => {\r\n  emits(\"validate\", prop, isValid, message);\r\n};\r\n\r\nconst validate = (callback?: FormValidateCallback) => {\r\n  return formRef.value.validate(callback);\r\n};\r\n\r\nconst validateField = (\r\n  props?: FormItemProp | FormItemProp[] | undefined,\r\n  callback?: FormValidateCallback | undefined\r\n) => {\r\n  return formRef.value.validateField(props, callback);\r\n};\r\n\r\nconst resetFields = (props?: FormItemProp | FormItemProp[] | undefined) => {\r\n  formRef.value.resetFields(props);\r\n};\r\n\r\nconst scrollToField = (prop: FormItemProp) => {\r\n  formRef.value.scrollToField(prop);\r\n};\r\n\r\nconst clearValidate = (props?: FormItemProp | FormItemProp[] | undefined) => {\r\n  formRef.value.clearValidate(props);\r\n};\r\n\r\nconst initModel = (initVal: DcFormModel) => {\r\n  const modelVal = model.value;\r\n  model.value = {\r\n    ...modelVal,\r\n    ...cloneDeep(initVal),\r\n  };\r\n};\r\n\r\nconst getValues = () => {\r\n  return {\r\n    ...model.value,\r\n  };\r\n};\r\n\r\nconst setFieldsValue = (fn: (val: DcFormModel) => DcFormModel) => {\r\n  model.value = fn(model.value);\r\n};\r\n\r\nconst reset = (clearValidate = true) => {\r\n  const modelVal = { ...model.value };\r\n  props.config.children.map((item) => {\r\n    modelVal[item.model] = isObject(item.defaultValue)\r\n      ? cloneDeep(item.defaultValue)\r\n      : item.defaultValue;\r\n    return null;\r\n  });\r\n  model.value = modelVal;\r\n\r\n  clearValidate &&\r\n    setTimeout(() => {\r\n      formRef.value?.clearValidate();\r\n    }, 0);\r\n};\r\n\r\nconst itemChange = (effect?: DcFormItemEffect, rowIndex?: number) => {\r\n  if (effect && typeof effect === \"function\") {\r\n    effect(model.value, initModel, rowIndex);\r\n  }\r\n};\r\n\r\nconst asyncValidate = (errorInfo: any) => {\r\n  return new Promise((resolve, reject) => {\r\n    formRef.value?.validate((isValid: boolean) => {\r\n      if (isValid) {\r\n        resolve({\r\n          ...getValues(),\r\n        });\r\n      } else {\r\n        reject(errorInfo);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nwatch(\r\n  () => props.config.children.length,\r\n  () => {\r\n    const modelVal = { ...model.value };\r\n    props.config.children.map((item) => {\r\n      if (typeof modelVal[item.model] === \"undefined\") {\r\n        modelVal[item.model] = isObject(item.defaultValue)\r\n          ? cloneDeep(item.defaultValue)\r\n          : item.defaultValue;\r\n      }\r\n      if (typeof visibleControl.value[item.model] === \"undefined\") {\r\n        visibleControl.value[item.model] = item.visible\r\n          ? computed(() => {\r\n              const res = item.visible ? item.visible(model.value) : false;\r\n              return res;\r\n            })\r\n          : true;\r\n      }\r\n\r\n      return null;\r\n    });\r\n    model.value = modelVal;\r\n  },\r\n  { deep: true, immediate: true }\r\n);\r\n\r\ndefineExpose({\r\n  validate,\r\n  validateField,\r\n  resetFields,\r\n  scrollToField,\r\n  clearValidate,\r\n  getValues,\r\n  initModel,\r\n  reset,\r\n  setFieldsValue,\r\n  asyncValidate,\r\n  model,\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["DO_defineComponent", "ref", "computed", "cloneDeep", "isObject", "watch"], "mappings": ";;;;;;;;;;;AA4Cc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AASd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAA,MAAM,UAAUC,OAAI,EAAA,CAAA;AAEd,IAAA,MAAA,KAAA,GAAQA,OAAiB,CAAA,EAAE,CAAA,CAAA;AAC3B,IAAA,MAAA,cAAA,GAAiBA,OAA0B,CAAA,EAAE,CAAA,CAAA;AAE7C,IAAA,MAAA,MAAA,GAASC,YAAS,CAAA,MACtB,OAAO,KAAA,CAAM,MAAO,CAAA,MAAA,KAAW,WAAc,GAAA,KAAA,CAAM,MAAO,CAAA,MAAA,GAAS,KACrE,CAAA,CAAA;AAEA,IAAA,MAAM,cAAiB,GAAA,CACrB,IACA,EAAA,OAAA,EACA,OACG,KAAA;AACG,MAAA,KAAA,CAAA,UAAA,EAAY,IAAM,EAAA,OAAA,EAAS,OAAO,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEM,IAAA,MAAA,QAAA,GAAW,CAAC,QAAoC,KAAA;AAC7C,MAAA,OAAA,OAAA,CAAQ,KAAM,CAAA,QAAA,CAAS,QAAQ,CAAA,CAAA;AAAA,KACxC,CAAA;AAEM,IAAA,MAAA,aAAA,GAAgB,CACpB,MAAA,EACA,QACG,KAAA;AACH,MAAA,OAAO,OAAQ,CAAA,KAAA,CAAM,aAAc,CAAA,MAAA,EAAO,QAAQ,CAAA,CAAA;AAAA,KACpD,CAAA;AAEM,IAAA,MAAA,WAAA,GAAc,CAAC,MAAsD,KAAA;AACjE,MAAA,OAAA,CAAA,KAAA,CAAM,YAAY,MAAK,CAAA,CAAA;AAAA,KACjC,CAAA;AAEM,IAAA,MAAA,aAAA,GAAgB,CAAC,IAAuB,KAAA;AACpC,MAAA,OAAA,CAAA,KAAA,CAAM,cAAc,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA;AAEM,IAAA,MAAA,aAAA,GAAgB,CAAC,MAAsD,KAAA;AACnE,MAAA,OAAA,CAAA,KAAA,CAAM,cAAc,MAAK,CAAA,CAAA;AAAA,KACnC,CAAA;AAEM,IAAA,MAAA,SAAA,GAAY,CAAC,OAAyB,KAAA;AAC1C,MAAA,MAAM,WAAW,KAAM,CAAA,KAAA,CAAA;AACvB,MAAA,KAAA,CAAM,KAAQ,GAAA;AAAA,QACZ,GAAG,QAAA;AAAA,QACH,GAAGC,iBAAU,OAAO,CAAA;AAAA,OACtB,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,YAAY,MAAM;AACf,MAAA,OAAA;AAAA,QACL,GAAG,KAAM,CAAA,KAAA;AAAA,OACX,CAAA;AAAA,KACF,CAAA;AAEM,IAAA,MAAA,cAAA,GAAiB,CAAC,EAA0C,KAAA;AAC1D,MAAA,KAAA,CAAA,KAAA,GAAQ,EAAG,CAAA,KAAA,CAAM,KAAK,CAAA,CAAA;AAAA,KAC9B,CAAA;AAEM,IAAA,MAAA,KAAA,GAAQ,CAAC,cAAA,GAAgB,IAAS,KAAA;AACtC,MAAA,MAAM,QAAW,GAAA,EAAE,GAAG,KAAA,CAAM,KAAM,EAAA,CAAA;AAClC,MAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,GAAI,CAAA,CAAC,IAAS,KAAA;AACzB,QAAA,QAAA,CAAA,IAAA,CAAK,KAAS,CAAA,GAAAC,eAAA,CAAS,IAAK,CAAA,YAAY,IAC7CD,gBAAU,CAAA,IAAA,CAAK,YAAY,CAAA,GAC3B,IAAK,CAAA,YAAA,CAAA;AACF,QAAA,OAAA,IAAA,CAAA;AAAA,OACR,CAAA,CAAA;AACD,MAAA,KAAA,CAAM,KAAQ,GAAA,QAAA,CAAA;AAEd,MAAA,cAAA,IACE,WAAW,MAAM;AACf,QAAA,OAAA,CAAQ,OAAO,aAAc,EAAA,CAAA;AAAA,SAC5B,CAAC,CAAA,CAAA;AAAA,KACR,CAAA;AAEM,IAAA,MAAA,UAAA,GAAa,CAAC,MAAA,EAA2B,QAAsB,KAAA;AAC/D,MAAA,IAAA,MAAA,IAAU,OAAO,MAAA,KAAW,UAAY,EAAA;AACnC,QAAA,MAAA,CAAA,KAAA,CAAM,KAAO,EAAA,SAAA,EAAW,QAAQ,CAAA,CAAA;AAAA,OACzC;AAAA,KACF,CAAA;AAEM,IAAA,MAAA,aAAA,GAAgB,CAAC,SAAmB,KAAA;AACxC,MAAA,OAAO,IAAI,OAAA,CAAQ,CAAC,OAAA,EAAS,MAAW,KAAA;AAC9B,QAAA,OAAA,CAAA,KAAA,EAAO,QAAS,CAAA,CAAC,OAAqB,KAAA;AAC5C,UAAA,IAAI,OAAS,EAAA;AACH,YAAA,OAAA,CAAA;AAAA,cACN,GAAG,SAAU,EAAA;AAAA,aACd,CAAA,CAAA;AAAA,WACI,MAAA;AACL,YAAA,MAAA,CAAO,SAAS,CAAA,CAAA;AAAA,WAClB;AAAA,SACD,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAAE,SAAA,CACE,MAAM,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,QAC5B,MAAM;AACJ,MAAA,MAAM,QAAW,GAAA,EAAE,GAAG,KAAA,CAAM,KAAM,EAAA,CAAA;AAClC,MAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,GAAI,CAAA,CAAC,IAAS,KAAA;AAClC,QAAA,IAAI,OAAO,QAAA,CAAS,IAAK,CAAA,KAAA,CAAA,KAAW,WAAa,EAAA;AACtC,UAAA,QAAA,CAAA,IAAA,CAAK,KAAS,CAAA,GAAAD,eAAA,CAAS,IAAK,CAAA,YAAY,IAC7CD,gBAAU,CAAA,IAAA,CAAK,YAAY,CAAA,GAC3B,IAAK,CAAA,YAAA,CAAA;AAAA,SACX;AACA,QAAA,IAAI,OAAO,cAAA,CAAe,KAAM,CAAA,IAAA,CAAK,WAAW,WAAa,EAAA;AAC3D,UAAA,cAAA,CAAe,MAAM,IAAK,CAAA,KAAA,CAAA,GAAS,IAAK,CAAA,OAAA,GACpCD,aAAS,MAAM;AACb,YAAA,MAAM,MAAM,IAAK,CAAA,OAAA,GAAU,KAAK,OAAQ,CAAA,KAAA,CAAM,KAAK,CAAI,GAAA,KAAA,CAAA;AAChD,YAAA,OAAA,GAAA,CAAA;AAAA,WACR,CACD,GAAA,IAAA,CAAA;AAAA,SACN;AAEO,QAAA,OAAA,IAAA,CAAA;AAAA,OACR,CAAA,CAAA;AACD,MAAA,KAAA,CAAM,KAAQ,GAAA,QAAA,CAAA;AAAA,OAEhB,EAAE,IAAA,EAAM,IAAM,EAAA,SAAA,EAAW,MAC3B,CAAA,CAAA;AAEa,IAAA,QAAA,CAAA;AAAA,MACX,QAAA;AAAA,MACA,aAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,SAAA;AAAA,MACA,KAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,KAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}