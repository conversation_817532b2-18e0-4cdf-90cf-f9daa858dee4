'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/form-item/style/css');
require('element-plus/es/components/table-column/style/css');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcEditTableColumn"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    prop: {
      type: String
    },
    label: {
      type: String
    },
    width: {
      type: String
    },
    rules: {
      type: Array
    }
  },
  setup(__props) {
    ;
    const props = __props;
    const defaultEditActions = {};
    const editActions = vue.inject("editActions");
    const formModel = vue.inject("formModel");
    const formProps = vue.inject("formProps");
    vue.watchEffect(() => {
      if (props.prop) {
        formProps?.value?.add(props.prop);
      }
    });
    const getEditModel = (index) => {
      if (!formModel || !formModel.value?.model) {
        return {
          isEditing: false,
          isNew: false,
          formData: {},
          data: {}
        };
      }
      return formModel.value.model[index];
    };
    const getEditRow = (index) => getEditModel(index).formData;
    const isEditing = (index) => getEditModel(index).isEditing ?? false;
    const calculateColumnDefaultValue = (scope) => {
      if (props.prop)
        return scope.row?.[props.prop];
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), vue.mergeProps(_ctx.$attrs, {
        prop: __props.prop,
        label: __props.label,
        width: __props.width
      }), {
        default: vue.withCtx((scope) => [
          isEditing(scope.$index) ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElFormItem), {
            key: 0,
            prop: `model.${scope.$index}.formData.${__props.prop}`,
            rules: __props.rules
          }, {
            default: vue.withCtx(() => [
              vue.renderSlot(_ctx.$slots, "edit", {
                $index: scope.$index,
                row: getEditRow(scope.$index),
                column: scope.column,
                actions: vue.unref(editActions) ?? defaultEditActions
              }, () => [
                vue.createTextVNode(vue.toDisplayString(calculateColumnDefaultValue(scope)), 1)
              ])
            ]),
            _: 2
          }, 1032, ["prop", "rules"])) : vue.renderSlot(_ctx.$slots, "default", {
            key: 1,
            $index: scope.$index,
            row: scope.row,
            column: scope.column,
            actions: vue.unref(editActions) ?? defaultEditActions
          }, () => [
            vue.createTextVNode(vue.toDisplayString(calculateColumnDefaultValue(scope)), 1)
          ])
        ]),
        _: 3
      }, 16, ["prop", "label", "width"]);
    };
  }
});
var EditTableColumn = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\edit-table-column\\src\\index.vue"]]);

exports["default"] = EditTableColumn;
//# sourceMappingURL=index.js.map
