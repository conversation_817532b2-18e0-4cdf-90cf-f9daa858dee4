{"EngineManager": "EngineManager", "accountInfo": "Account Info", "addCluster": "Add Cluster", "addComponents": "Add Component", "addHost": "Add Host", "audit": "audit", "clusterHostList": "Cluster Host List", "clusterList": "Cluster List", "clusterMonitoring": "Monitor Cluster", "clusterOverview": "Cluster Overview", "componentConfig": "Component Configuration", "componentInstance": "Component Instance", "componentMonitoring": "Monitor Component", "componentsConfigurationRecord": "Component configuration history", "configurationRecord": "Configuration history", "diagnosticReport": "Diagnostic report", "expansionAndContraction": "Scale", "giveAlarm": "Alarm service", "hostDetails": "Host details", "hostList": "Host List", "hostMonitoring": "Monitor Host", "hostRoleList": "Host role list", "installComponents": "Install Components", "instanceConfiguration": "Instance Configuration", "log": "Logs", "login": "<PERSON><PERSON>", "monitor": "Monitoring", "oneClickDeployment": "One-click Deploy", "operationHistory": "Operation history", "productDocumentation": "Product documentation", "restartList": "Restart list", "serviceComponent": "Service Component", "shelf": "shelf management ", "taskManagement": "Task management", "userManagement": "User Management", "viewComponents": "View component", "webTerminal": "web-terminal", "yarnResource": "Yarn Resource pool configuration", "resourceManagement": "Resource Management", "kubernetesManagement": "Kubernetes Management", "hostManagement": "Host management"}