<template>
  <div class="container-code" :class="{ 'read-only': readOnly }" v-if="showCode" ref="containerRef">
    <div ref="editor" style="height: 100%"></div>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onBeforeUnmount, ref, watch } from 'vue'
import { debounce } from 'lodash'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'

const props = defineProps({
  language: {
    type: String,
    required: false,
    default: 'xml'
  },
  modelValue: {
    type: String,
    default: ''
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  showLine: {
    type: Boolean,
    default: true
  },
  codeFlag: {
    type: Boolean,
    default: true
  },
  miniMap: {
    type: Boolean,
    default: false
  },
  lineWrapping: {
    type: Boolean,
    default: false
  },
  isShow: {
    type: Boolean,
    default: true
  },
  selectionRun: {
    type: Boolean,
    default: false
  },
  runDisabled: {
    type: <PERSON>olean,
    default: false
  }
})

const emits = defineEmits<{
  (e: 'update:modelValue', val: string): void
  (e: 'change', val: string): void
}>()

const monacoTheme = ref(`lightTheme`)

const editor = ref()
const showCode = ref(false)
const changeEmit = ref(true)
const editorId = ref('')
const containerRef = ref<HTMLElement | null>(null)

let coder: monaco.editor.IStandaloneCodeEditor | undefined
let model: monaco.editor.ITextModel | undefined
const selectionRunInfos: {
  decorations: string[]
  overlayWidget: null | any
  overlayWidgetDom: null | HTMLDivElement
  overlayWidgetTop: number
  codeScrollTop: number
} = {
  decorations: [],
  overlayWidget: null,
  overlayWidgetDom: null,
  overlayWidgetTop: 0,
  codeScrollTop: 0
}
let codeObserver: ResizeObserver | null = null
let lastCodeSize = {
  width: '0',
  height: '0'
}

const init = () => {
  nextTick(() => {
    model = monaco.editor.createModel(props.modelValue, props.language)
    coder = monaco.editor.create(editor.value, {
      model,
      theme: monacoTheme.value,
      minimap: {
        enabled: props.miniMap // 是否启用预览图
      },
      lineNumbers: props.showLine ? 'on' : 'off',
      wordWrap: props.lineWrapping ? 'on' : 'off',
      readOnly: props.readOnly,
      tabSize: 2
    })
    codeObserver = new ResizeObserver(
      debounce(() => {
        if (!containerRef.value) {
          return
        }
        const width = getComputedStyle(containerRef?.value).getPropertyValue('width')
        const height = getComputedStyle(containerRef?.value).getPropertyValue('height')
        if (width === lastCodeSize.width && height === lastCodeSize.height) return
        lastCodeSize.height = height
        lastCodeSize.width = width
        resizeCoder()
      }, 300)
    )

    if (containerRef.value) {
      codeObserver.observe(containerRef.value)
    }

    editorId.value = `editor-${Date.now()}`
    addChangeEmit()
    // todo 数据开发有时候code会不渲染
    nextTick(() => {
      resizeCoder()
    })
  })
}

function resizeCoder() {
  coder?.layout()
}

const addChangeEmit = () => {
  if (model) {
    model.onDidChangeContent(() => {
      if (changeEmit.value && coder) {
        emits('update:modelValue', coder.getValue())
        emits('change', coder.getValue())
      }
    })
  }
}

watch(
  () => props.isShow,
  (isShow) => {
    if (!isShow || coder) return
    showCode.value = false
    nextTick(() => {
      showCode.value = true
      init()
    })
  },
  { immediate: true }
)

// watch(
//   () => props.content,
//   (content) => {
//     if (coder && props.readOnly) {
//       setValue(content)
//     }
//   }
// )

watch(
  () => props.language,
  (language) => {
    if (coder && model) {
      monaco.editor.setModelLanguage(model, language)
    }
  }
)

watch(
  () => props.readOnly,
  (readOnly) => {
    if (coder) {
      coder.updateOptions({
        readOnly: readOnly
      })
      if (readOnly) {
        if (containerRef.value) {
          containerRef.value.classList.add('read-only')
        }
      }
    }
  }
)

watch(
  () => props.runDisabled,
  (disabled) => {
    if (selectionRunInfos.overlayWidgetDom) {
      if (disabled) {
        selectionRunInfos.overlayWidgetDom.classList.add('disabled')
      } else {
        selectionRunInfos.overlayWidgetDom.classList.remove('disabled')
      }
    }
  }
)

/**
 * 对外提供赋值
 */
const setValue = (val: string) => {
  coder?.setValue(val)
}

const setValueNotTriggleChange = (val: string) => {
  changeEmit.value = false
  setValue(val)
  nextTick(() => {
    changeEmit.value = true
  })
}

/**
 * 对外提供取值
 */
const getValue = () => {
  return coder?.getValue()
}

/**
 * 获取选中值
 */
const getSelection = () => {
  const selection = coder?.getSelection()
  return selection && model ? model.getValueInRange(selection) : ''
}

onBeforeUnmount(() => {
  if (coder && model) {
    model.dispose()
    coder.dispose()
    coder = undefined
    codeObserver = null
    lastCodeSize = {
      width: '0',
      height: '0'
    }
  }
})

defineExpose({
  setValue,
  getValue,
  setValueNotTriggleChange,
  resizeCoder,
  getSelection
})
</script>
<style lang="scss" scoped>
.container-code {
  width: 100%;
  height: 100%;
}
</style>

<style lang="scss">
.container-code {
  .margin {
    background: #f7f9fc;
  }
  .monaco-editor-background {
    background: #ffffff;
  }
  .minimap-slider-horizontal {
    background-color: transparent;
  }
  .minimap-slider {
    display: block !important;
  }
  .minimap-slider:hover {
    .minimap-slider-horizontal {
      display: block;
      box-sizing: border-box;
      border: 1px solid rgba(86, 122, 255, 0.5);
      background: rgba(86, 122, 255, 0.1);
    }
  }
  &.read-only {
    .monaco-editor-background {
      background-color: #f7f9fc;
    }
  }
}
</style>
