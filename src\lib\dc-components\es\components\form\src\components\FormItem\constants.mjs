import { ElInput, ElAutocomplete, ElCascader, ElColorPicker, ElCascaderPanel, ElDatePicker, ElInputNumber, ElRate, ElSelectV2, ElSlider, ElSwitch, ElTimePicker, ElTimeSelect, ElTransfer, ElUpload } from 'element-plus';
import 'element-plus/es/components/autocomplete/style/css';
import 'element-plus/es/components/cascader/style/css';
import 'element-plus/es/components/cascader-panel/style/css';
import 'element-plus/es/components/color-picker/style/css';
import 'element-plus/es/components/date-picker/style/css';
import 'element-plus/es/components/input/style/css';
import 'element-plus/es/components/input-number/style/css';
import 'element-plus/es/components/rate/style/css';
import 'element-plus/es/components/select-v2/style/css';
import 'element-plus/es/components/slider/style/css';
import 'element-plus/es/components/switch/style/css';
import 'element-plus/es/components/time-picker/style/css';
import 'element-plus/es/components/time-select/style/css';
import 'element-plus/es/components/transfer/style/css';
import 'element-plus/es/components/upload/style/css';
import DcCheckbox from '../../../../checkbox/src/index.mjs';
import Radio from '../../../../radio/src/index.mjs';
import Select from '../../../../select/src/index.mjs';
import { DcFormComponentEnum } from '../../types.mjs';
import DcFormCustom from '../Custom/index.mjs';
import DcFormTable from '../Table/index.mjs';
import DcFormText from '../Text/index.mjs';

const Component = {
  [DcFormComponentEnum.INPUT]: ElInput,
  [DcFormComponentEnum.TEXT]: DcFormText,
  [DcFormComponentEnum.SELECT]: Select,
  [DcFormComponentEnum.RADIO]: Radio,
  [DcFormComponentEnum.AUTOCOMPLETE]: ElAutocomplete,
  [DcFormComponentEnum.CHECKBOX]: DcCheckbox,
  [DcFormComponentEnum.CASCADER]: ElCascader,
  [DcFormComponentEnum.COLORPICKER]: ElColorPicker,
  [DcFormComponentEnum.CASCADERPANEL]: ElCascaderPanel,
  [DcFormComponentEnum.DATEPICKER]: ElDatePicker,
  [DcFormComponentEnum.INPUTNUMBER]: ElInputNumber,
  [DcFormComponentEnum.RATE]: ElRate,
  [DcFormComponentEnum.SELECTV2]: ElSelectV2,
  [DcFormComponentEnum.SLIDER]: ElSlider,
  [DcFormComponentEnum.SWITCH]: ElSwitch,
  [DcFormComponentEnum.TIMEPICKER]: ElTimePicker,
  [DcFormComponentEnum.TIMESELECT]: ElTimeSelect,
  [DcFormComponentEnum.TRANSFER]: ElTransfer,
  [DcFormComponentEnum.UPLOAD]: ElUpload,
  [DcFormComponentEnum.CUSTOM]: DcFormCustom,
  [DcFormComponentEnum.TABLE]: DcFormTable
};

export { Component };
//# sourceMappingURL=constants.mjs.map
