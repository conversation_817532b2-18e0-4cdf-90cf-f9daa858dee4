<template>
  <div class="scaling">
    <TheTitle :content="$t('replenish.nodePoolConfiguration')">
      <template #right>
        <!-- <BaseAddButton :is-button="true">{{ $t('replenish.installCarpenter') }}</BaseAddButton> -->
        <BaseAddButton :is-button="true" @click="openAddNodePool" :disabled="disabledAddNodePool">{{ $t('replenish.createNodePool') }}</BaseAddButton>
      </template>
    </TheTitle>
    <div class="mb-4">
      <BaseTable v-loading="nodePoolData.loading" :columns="nodePoolData.columns" :data="nodePoolData.data">
        <template #maxCpu="{ row }"> {{ `${row.cpuCapacity}/${row.maxCpu}` }} </template>
        <template #maxMemory="{ row }"> {{ `${row.memoryCapacity}/${row.maxMemory}` }} </template>
        <template #after>
          <el-table-column :label="$t('table.operate')" :width="300" fixed="right">
            <template #default="scope">
              <BaseTableToolbar :button-data="nodePoolTableToolbar(scope).buttonData" />
            </template>
          </el-table-column>
        </template>
      </BaseTable>
    </div>
    <div class="flex-end mb-4">
      <BasePagination v-model="nodePoolPageInfo" @page-change="nodePoolPageChange" />
    </div>
    <TheTitle content="Nodeclass">
      <template #right>
        <BaseAddButton :is-button="true" @click="openAddNodeClass">{{ $t('replenish.createNodeClass') }}</BaseAddButton>
      </template>
    </TheTitle>
    <div class="mb-4">
      <BaseTable v-loading="nodeClassData.loading" :columns="nodeClassData.columns" :data="nodeClassData.data">
        <template #after>
          <el-table-column :label="$t('table.operate')" :width="300" fixed="right">
            <template #default="scope">
              <BaseTableToolbar :button-data="nodeClassTableToolbar(scope).buttonData" />
            </template>
          </el-table-column>
        </template>
      </BaseTable>
    </div>
    <div class="flex-end">
      <BasePagination v-model="nodeClassPageInfo" @page-change="nodeClassPageChange" />
    </div>
    <!-- <TheTitle :content="$t('replenish.scalableEvent')" />
    <div class="mb-4">
      <BaseTable v-loading="scalableEventData.loading" :columns="scalableEventData.columns" :data="scalableEventData.data"> </BaseTable>
    </div>
    <div class="flex-end">
      <BasePagination v-model="scalableEventPageInfo" @page-change="scalableEventPageChange" />
    </div> -->

    <AddNodeClass v-model="addNodeClassVisible" :k8s-id="k8sId" @submit="getNodeClassList" />
    <AddNodePool v-model="addNodePoolVisible" :k8s-id="k8sId" @submit="getNodePoolList" />
    <EditYaml v-model="editVisible" :k8s-id="k8sId" :id="editId" :type="editType" :yaml="editYaml" @submit="editSubmit" />
  </div>
</template>
<script lang="ts" setup>
import elConfim from '@/utils/elConfim'
import AddNodeClass from './components/AddNodeClass.vue'
import AddNodePool from './components/AddNodePool.vue'
import EditYaml from './components/EditYaml.vue'
import { deleteNodeClass, deleteNodePool, pageNodeClasses, pageNodePools, validK8sCluster } from '@/api/k8sApi'

const { t, store, $has, setBreadList, route, hasBusinessAuthority, router } = useBasicTool()

const k8sId = computed(() => store.state.k8s.k8sId || route.query.id)

const tenantId = computed(() => store.state.user.tenantId)

const nodePoolData = reactive({
  columns: [
    { label: t('replenish.nodePoolName'), prop: 'name', minWidth: 180 },
    { label: t('replenish.nodeclassName'), prop: 'nodeClass', minWidth: 150 },
    // { label: t('replenish.expansionStatus'), prop: 'createTime' },
    { label: t('replenish.nodeNum'), prop: 'nodeCount' },
    // { label: t('replenish.availableNodeNum'), prop: 'createTime' },
    { label: t('replenish.allocatedAndMaximumCpu'), prop: 'maxCpu', slot: true },
    { label: t('replenish.allocatedAndMaximumMemory'), prop: 'maxMemory', slot: true },
    { label: t('replenish.updateTime'), prop: 'updateTime' }
  ],
  data: [] as K8sApi.NodePoolRecord[],
  loading: false
})
const { pageInfo: nodePoolPageInfo, resetPageInfo: resetNodePoolPageInfo } = usePage()

const nodeClassData = reactive({
  columns: [
    { label: t('replenish.nodeclassName'), prop: 'name' },
    { label: t('replenish.clusterName'), prop: 'clusterName' },
    { label: 'iam role', prop: 'roleName' },
    { label: t('replenish.updateTime'), prop: 'updateTime' }
  ],
  data: [] as K8sApi.FindNodeClassRecord[],
  loading: false
})
const { pageInfo: nodeClassPageInfo, resetPageInfo: resetNodeClassPageInfo } = usePage()

const scalableEventData = reactive({
  columns: [
    { label: t('replenish.timeID'), prop: 'tenantName' },
    {
      label: t('replenish.nodePool'),
      prop: 'createTime',
      filters: [
        { text: '2016-05-01', value: '05-01' },
        { text: '2016-05-02', value: '05-02' },
        { text: '2016-05-03', value: '05-03' },
        { text: '2016-05-04', value: '05-04' }
      ]
    },
    { label: t('replenish.status'), prop: 'createTime' },
    { label: t('replenish.totalInstancesAfterChanges'), prop: 'updateTime' },
    { label: t('replenish.startTime'), prop: 'updateTime' },
    { label: t('replenish.endTime'), prop: 'updateTime' },
    { label: t('replenish.description'), prop: 'updateTime' },
    { label: t('replenish.type'), prop: 'updateTime' }
  ],
  data: [],
  loading: false
})
const { pageInfo: scalableEventPageInfo, resetPageInfo: resetScalableEventPageInfo } = usePage()

const addNodeClassVisible = ref(false)

const addNodePoolVisible = ref(false)

const editVisible = ref(false)
const editId = ref(0)
const editType = ref('Nodeclass')
const editYaml = ref('')

const disabledAddNodePool = ref(true)

const removeNodePool = (scope: any) => {
  elConfim
    .confim({
      isCancelButton: true,
      message: t('replenish.isDelete').replace('{value}', scope.row.name)
    })
    .then(() => {
      deleteNodePool({
        k8sClusterId: k8sId.value,
        nodePoolId: scope.row.id,
        tenantId: tenantId.value
      }).then((res) => {
        if (res.data.code === 0) {
          getNodePoolList()
        }
      })
    })
}

const removeNodeClass = (scope: any) => {
  elConfim
    .confim({
      isCancelButton: true,
      message: t('replenish.isDelete').replace('{value}', scope.row.name)
    })
    .then(() => {
      deleteNodeClass({
        k8sClusterId: k8sId.value,
        nodeClassId: scope.row.id,
        tenantId: tenantId.value
      }).then((res) => {
        if (res.data.code === 0) {
          getNodeClassList()
        }
      })
    })
}

function nodePoolTableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('button.edit'),
        disabled: false,
        click: () => {
          editType.value = 'Nodepool'
          editId.value = scope.row.id
          editYaml.value = scope.row.config
          editVisible.value = true
        },
        isButton: true
      },
      {
        buttonName: t('table.delete'),
        disabled: scope.row.createMode === 'template',
        click: () => {
          removeNodePool(scope)
        },
        isButton: scope.row.createMode === 'custom'
      }
    ]
  }
}

function nodeClassTableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('button.edit'),
        disabled: false,
        click: () => {
          editType.value = 'Nodeclass'
          editId.value = scope.row.id
          editYaml.value = scope.row.config
          editVisible.value = true
        },
        isButton: true
      },
      {
        buttonName: t('table.delete'),
        disabled: false,
        click: () => {
          removeNodeClass(scope)
        },
        isButton: true
      }
    ]
  }
}

const getCanAddNodePool = () => {
  validK8sCluster({
    k8sId: k8sId.value
  }).then((res) => {
    if (res.data.code === 0) {
      disabledAddNodePool.value = false
    }
  })
}

const getNodeClassList = () => {
  nodeClassData.loading = true
  pageNodeClasses({
    k8sClusterId: k8sId.value,
    pageNo: nodeClassPageInfo.pageNo,
    pageSize: nodeClassPageInfo.pageSize,
    tenantId: tenantId.value
  })
    .then((res) => {
      if (res.data.code === 0) {
        nodeClassData.data = res.data.data.records || []
        nodeClassPageInfo.total = res.data.data.total || 0
      }
    })
    .finally(() => {
      nodeClassData.loading = false
    })
}

const getNodePoolList = () => {
  nodePoolData.loading = true
  pageNodePools({
    k8sClusterId: k8sId.value,
    pageNo: nodePoolPageInfo.pageNo,
    pageSize: nodePoolPageInfo.pageSize,
    tenantId: tenantId.value
  })
    .then((res) => {
      if (res.data.code === 0) {
        nodePoolData.data = res.data.data.records || []
        nodePoolPageInfo.total = res.data.data.total || 0
      }
    })
    .finally(() => {
      nodePoolData.loading = false
    })
}

const nodeClassPageChange = () => {
  getNodeClassList()
}
const nodePoolPageChange = () => {
  getNodePoolList()
}
const scalableEventPageChange = () => {}

const openAddNodeClass = () => {
  addNodeClassVisible.value = true
}

const openAddNodePool = () => {
  addNodePoolVisible.value = true
}

const editSubmit = () => {
  if (editType.value === 'Nodeclass') {
    getNodeClassList()
  } else {
    getNodePoolList()
  }
}

onMounted(() => {
  getNodeClassList()
  getNodePoolList()
  getCanAddNodePool()
})
</script>
<style scoped lang="scss">
.scaling {
  overflow: auto;
  box-sizing: border-box;
  height: 100%;
  padding: 20px;
  .flex-end {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
