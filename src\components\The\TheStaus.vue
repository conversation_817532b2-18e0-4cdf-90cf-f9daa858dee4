<template>
  <div :class="canClick ? 'can-click' : ''" @click="onClick">
    <!-- is-loading 为element-plus 转动样式 -->
    <el-icon v-if="isLoadingArr.includes(state)" class="is-loading" style="position: relative; top: 2px; left: -3px">
      <Loading />
    </el-icon>
    <span v-else :class="['status-spot', `${stateMapStyle.get(state)}`]" />
    <span>{{ stateMap.get(state) }}</span>
  </div>
</template>
<script lang="ts" setup>
import { Loading } from '@element-plus/icons-vue'

const props = defineProps({
  stateMap: {
    type: Map,
    required: true
  },
  isLoadingArr: {
    type: Array<number>,
    required: true
  },
  stateMapStyle: {
    type: Map,
    required: true
  },
  state: {
    type: Number,
    default: 0
  },
  canClick: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['click'])

const onClick = (e) => {
  if (props.canClick) {
    emits('click', e)
  }
}
</script>
<style lang="scss" scoped>
.can-click {
  cursor: pointer;
}
</style>
