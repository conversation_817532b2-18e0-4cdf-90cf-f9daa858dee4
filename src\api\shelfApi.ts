import service from '@/utils/Http'

/**
 * @description 列表查询
 * @param {ShelfApi.GetFramePageList} data
 * @return {*}
 */
export const getFramePageList = (data: ShelfApi.GetFramePageList) => {
  return service.post<ApiResponse<PageList<ShelfApi.GetFramePageListItem>>>('/rack/pageList', data)
}

/**
 * @description 获取没有绑定机架号的主机
 * @return {*}
 */
export const getHostWithNoBindRack = () => {
  return service.post<ApiResponse<Array<ShelfApi.HostWithNoBindRackItem>>>('/hosts/getHostWithNoBindRack')
}

/**
 * @description 添加机架
 * @param {ShelfApi.AddFrame} data
 * @return {*}
 */
export const addFrame = (data: ShelfApi.AddFrame) => {
  return service.post<ApiResponse<boolean>>('/rack/addFrame', data)
}

/**
 * @description 删除机架
 * @param {ShelfApi.DeleteFrame} data
 * @return {*}
 */
export const deleteFrame = (data: ShelfApi.DeleteFrame) => {
  return service.post<ApiResponse<boolean>>('/rack/deleteFrame', data)
}

/**
 * @description 编辑机架
 * @param {ShelfApi.UpdateFrame} data
 * @return {*}
 */
export const updateFrame = (data: ShelfApi.UpdateFrame) => {
  return service.post<ApiResponse<boolean>>('/rack/updateFrame', data)
}

/**
 * @description 不分页机架列表查询
 * @param {ShelfApi.GetRackList} data
 * @return {*}
 */
export const getRackList = (data: ShelfApi.GetRackList) => {
  return service.post<ApiResponse<PageList<ShelfApi.GetFramePageListItem>>>('/rack/list', data)
}
