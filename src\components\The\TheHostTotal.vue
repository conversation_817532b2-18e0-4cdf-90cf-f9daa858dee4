<template>
  <div :class="props.inline ? 'inline' : ''">
    <span v-if="utilization" key="utilizationNO" class="total-pencent">{{ Math.round((utilization ?? 0) * 100) }}%</span>
    <span v-else key="utilizationOFF" class="total-pencent">-</span>
    <span v-if="used && total" key="usedORTotalNO" class="total-number">{{ isDeficiency(used) }}/{{ isDeficiency(total) }} {{ company }}</span>
    <span v-else key="usedORTotalOFF" class="tipText">{{ $t('replenish.dataCollecting') }}</span>
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const props = defineProps({
  utilization: {
    type: Number,
    default: 0
  },
  used: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 0
  },
  company: {
    type: String,
    default: 'Core'
  },
  inline: {
    type: Boolean,
    default: false
  }
})
function isDeficiency(value: number): string {
  return value > 0.01 ? value.toFixed(2) : t('mixed.insufficient')
}
</script>
<style lang="scss" scoped>
.inline {
  display: inline-flex;
  align-items: center;
  width: 100%;
  span {
    margin-right: 8px;
  }
}
.total-pencent {
  display: block;
}
.total-number {
  color: #a3a9bd;
}
.tipText {
  font-size: 12px;
  color: #a3a9bd;
}
</style>
