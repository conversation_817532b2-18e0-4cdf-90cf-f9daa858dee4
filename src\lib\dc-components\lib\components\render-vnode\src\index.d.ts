import type { PropType } from "vue";
import type { RenderVNodeFn } from "./types";
declare const _default: import("vue").DefineComponent<{
    vnode: {
        type: PropType<RenderVNodeFn>;
        default: () => null;
    };
    scope: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    extraScope: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
}, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    vnode: {
        type: PropType<RenderVNodeFn>;
        default: () => null;
    };
    scope: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    extraScope: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
}>>, {
    vnode: RenderVNodeFn;
    scope: Record<string, any>;
    extraScope: Record<string, any>;
}, {}>;
export default _default;
