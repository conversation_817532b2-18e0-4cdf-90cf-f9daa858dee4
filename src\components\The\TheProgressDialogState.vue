<template>
  <div class="progress-state">
    <el-icon v-if="state === 1" class="is-loading" style="position: relative; top: 2px; left: -3px">
      <Loading />
    </el-icon>
    <i v-else :class="['iconfont', StateIcon[state]]" />
    <slot v-if="ifText"> {{ StateTextMap.get(state) }} </slot>
  </div>
</template>
<script lang="ts" setup>
import { Loading } from '@element-plus/icons-vue'
const { t } = useBasicTool()
interface Props {
  state: number
  ifText: boolean
}
// 状态 0-未执行 1-执行中 2-执行成功 3-执行失败
const StateTextMap = new Map([
  [0, t('replenish.awaitingExecution')],
  [1, t('replenish.executionInProgress')],
  [2, t('replenish.completed')],
  [3, t('replenish.failure')]
])
enum StateIcon {
  'icon-icon_jinhangzhong',
  '',
  'icon-icon_chenggong',
  'icon-icon_shibai'
}
withDefaults(defineProps<Props>(), {
  state: 0,
  ifText: false
})
</script>
<style lang="scss" scoped>
.progress-state {
  display: inline-block;
  .iconfont {
    position: relative;
    top: 1px;
    margin-right: 3px;
    font-size: 16px;
  }
  .icon-icon_chenggong {
    color: var(--ops-state-success-color);
  }
  .icon-icon_shibai {
    color: var(--ops-state-fail-color);
  }
  .icon-icon_jinhangzhong {
    color: var(--ops-state-toberun-color);
  }
}
</style>
