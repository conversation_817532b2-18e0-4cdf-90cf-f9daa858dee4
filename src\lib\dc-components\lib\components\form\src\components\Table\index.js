'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var index$1 = require('../../../../render-vnode/src/index.js');
var index$2 = require('../../../../table/src/index.js');
var index = require('../FormItem/index.js');
require('./index.vue_vue_type_style_index_0_lang.js');
var pluginVue_exportHelper = require('../../../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcFormTable"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Array,
      default: () => []
    },
    topSlot: {
      type: Object,
      default: null
    },
    bottomSlot: {
      type: Object,
      default: null
    },
    tableProps: {
      type: Object,
      default: () => ({})
    },
    fileList: {
      type: Object,
      default: () => ({})
    },
    rowIndex: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue", "triggerEffect", "blur"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const columns = vue.computed(() => {
      return props.columns?.map((item) => {
        const config = { ...item };
        if (config.isFormItem && config.formItemConfig) {
          config.formatter = (row, columns2, cellVal, index$1) => {
            const model = config.formItemConfig.model.replace("[index]", `[${index$1}]`);
            return vue.h(index["default"], {
              config: {
                ...config.formItemConfig,
                model
              },
              visible: true,
              modelValue: props.modelValue[index$1][config.prop],
              rowIndex: index$1,
              margin: config.formItemConfig?.margin,
              onTriggerEffect: triggerEffect,
              ["onUpdate:modelValue"]: (val) => updateModelValue(val, index$1, config.prop)
            }, () => "");
          };
        }
        return config;
      }) || [];
    });
    const triggerEffect = (...args) => {
      emits("triggerEffect", ...args);
    };
    const updateModelValue = (val, index, columnProp) => {
      const newVal = props.modelValue;
      newVal[index][columnProp] = val;
      emits("update:modelValue", newVal);
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock(vue.Fragment, null, [
        vue.createVNode(vue.unref(index$1["default"]), {
          vnode: props.topSlot
        }, null, 8, ["vnode"]),
        vue.createVNode(index$2["default"], vue.mergeProps({ ...__props.tableProps }, {
          data: props.modelValue,
          columns: columns.value,
          class: "dc-form-table"
        }), null, 16, ["data", "columns"]),
        vue.createVNode(vue.unref(index$1["default"]), {
          vnode: props.bottomSlot
        }, null, 8, ["vnode"])
      ], 64);
    };
  }
});
var DcFormTable = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\form\\src\\components\\Table\\index.vue"]]);

exports["default"] = DcFormTable;
//# sourceMappingURL=index.js.map
