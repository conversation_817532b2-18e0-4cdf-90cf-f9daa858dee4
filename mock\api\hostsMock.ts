export default [
    {
        url: '/api/ops/hosts/page',
        method: 'GET',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: {
                total: 1,
                records: [
                    {
                        onlineState: '1',
                        name: 'host001',
                        namespaceName: 'namespace001',
                        ip: '************',
                    }
                ]
            }
        })
    },
    {
        url: '/api/ops/hosts/namespaceList',
        method: 'GET',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: {
                total: 1,
                records: [
                    {
                        onlineState: '1',
                        name: 'host001',
                        namespaceName: 'namespace001',
                        ip: '************',
                    }
                ]
            }
        })
    },
    {
        url: '/api/ops/hosts/unmanaged/list',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: [
                {
                    name: 'host001',
                    namespaceName: 'namespace001',
                    ip: '************',
                    onlineState: '1'
                },
                {
                    name: 'host002',
                    namespaceName: 'namespace002',
                    ip: '************',
                    onlineState: '0'
                },
            ]
        })
    },
    {
        url: '/api/ops/hosts/bind2Namespace',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: '123456'
        })
    },
    {
        url: '/api/ops/hosts/bind/progress/123456',
        method: 'GET',
        response: () => {
            const stete: string = Math.random() > 0.5 ? 'SUCCESS' : 'FAIL'
            return ({
                code: 0,
                msg: 'ok',
                data: {
                    stete: stete
                }
            })
        }
    },
    {
        url: '/api/ops/hosts/verify/connected',
        method: 'POST',
        response: () => ({
            code: 0,
            data: "主机连接正常",
            msg: "成功"
        })
    },
    {
        url: '/api/ops/hosts/add',
        method: 'POST',
        response: () => ({
            code: 0,
            data: "123456",
            msg: "成功"
        })
    },
    {
        url: '/api/ops/hosts/add/progress/123456',
        method: 'GET',
        response: () => {
            const stete: string = Math.random() > 0.5 ? 'SUCCESS' : 'FAIL'
            return ({
                code: 0,
                msg: 'ok',
                data: {
                    stete: stete
                }
            })
        }
    }
]