'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/form/style/css');
require('element-plus/es/components/table/style/css');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcEditTable"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    dataSource: {
      type: Array
    },
    request: {
      type: Object
    }
  },
  setup(__props, { expose: __expose }) {
    ;
    const props = __props;
    const formModel = vue.ref({
      model: []
    });
    const form = vue.ref();
    const formProps = vue.ref(/* @__PURE__ */ new Set());
    const tableData = vue.computed(() => formModel.value.model.map(({ data }) => data));
    const resultData = vue.computed(() => {
      return formModel.value.model.reduce((resultData2, model) => {
        if (model.isNew) {
          return resultData2;
        }
        resultData2.push({
          ...model.data
        });
        return resultData2;
      }, []);
    });
    const convertFormModel = (data) => data.map((row) => ({
      data: { ...row },
      formData: { ...row },
      isEditing: false,
      isNew: false
    }));
    vue.watchEffect(async () => {
      const model = [...props?.dataSource ?? []];
      if (typeof props.request === "function") {
        model.push(...await Promise.resolve(props.request()));
      }
      formModel.value.model = convertFormModel(model);
    });
    const generateValidateFields = (index) => Array.from(formProps.value).map((prop) => `model.${index}.formData.${prop}`);
    const startEditable = (index) => {
      formModel.value.model[index].isEditing = true;
    };
    const deleteRow = (index) => {
      formModel.value.model.splice(index, 1);
    };
    const addRow = (row = {}) => {
      formModel.value.model.push({
        data: { ...row },
        formData: { ...row },
        isEditing: true,
        isNew: true
      });
    };
    const cancelEditable = (index) => {
      if (!form.value) {
        return;
      }
      form.value.resetFields && form.value.resetFields(generateValidateFields(index));
      const formModelItem = formModel.value.model[index];
      formModelItem.formData = { ...formModelItem.data };
      if (formModelItem.isNew) {
        formModel.value.model.splice(index, 1);
      } else {
        formModelItem.isEditing = false;
      }
    };
    const saveEditable = (index) => {
      if (!form.value) {
        return;
      }
      form.value.validateField && form.value.validateField(generateValidateFields(index), (validated) => {
        if (!validated) {
          return;
        }
        const formModelItem = formModel.value.model[index];
        formModelItem.data = { ...formModelItem.formData };
        formModelItem.isEditing = false;
        formModelItem.isNew = false;
      });
    };
    const editActions = {
      addRow,
      deleteRow,
      startEditable,
      cancelEditable,
      saveEditable
    };
    vue.provide("formModel", formModel);
    vue.provide("formProps", formProps);
    vue.provide("editActions", editActions);
    __expose({
      resultData,
      editActions
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", null, [
        vue.createVNode(vue.unref(elementPlus.ElForm), {
          ref_key: "form",
          ref: form,
          model: formModel.value
        }, {
          default: vue.withCtx(() => [
            vue.createVNode(vue.unref(elementPlus.ElTable), vue.mergeProps(_ctx.$attrs, { data: tableData.value }), {
              default: vue.withCtx(() => [
                vue.renderSlot(_ctx.$slots, "default")
              ]),
              _: 3
            }, 16, ["data"])
          ]),
          _: 3
        }, 8, ["model"])
      ]);
    };
  }
});
var EditTable = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\edit-table\\src\\index.vue"]]);

exports["default"] = EditTable;
//# sourceMappingURL=index.js.map
