<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.tenantPermissions')" width="870px" :before-close="resetForm">
    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="auto">
      <el-form-item class="k8s-list" :label="`Namespace${$t('replenish.createPermissions')}：`" prop="K8SList">
        <div class="k8s-list-tip">
          {{ $t('replenish.tenantsWithPermissions') }}
        </div>

        <el-transfer
          ref="tenantTransfer"
          :props="{ key: 'tenantId', label: 'name' }"
          :titles="[$t('replenish.tenantList'), $t('replenish.permissionTenants')]"
          v-model="ruleForm.tenantList"
          :data="tenantData"
          @change="tenantTransferChange"
          class="ce-transfer"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  k8sId: {
    type: String,
    default: ''
  }
})

watch(
  () => props.modelValue,
  () => {
    if (!props.modelValue) {
      resetForm()
    } else {
      getInitData()
    }
  }
)
const ruleForm = reactive<any>({
  tenantList: []
})
const rules = {}
const tenantTransfer = ref()
const tenantData = computed(() => [...tenantDataLeft.value, ...tenantDataRight.value])

const tenantDataLeft = ref<any[]>([])
const tenantDataRight = ref<any[]>([])
const distributionTenant = ref<any[]>([])
const recycleTenant = ref<any[]>([])
function tenantTransferChange(_: any[], direction: any, movedKeys: any[]) {
  if (direction === 'left') {
    movedKeys.forEach((item) => {
      const dataleftIds = tenantDataLeft.value.map((item) => item.tenantId)
      const index = distributionTenant.value.indexOf(item)
      if (index !== -1) distributionTenant.value.splice(index, 1)
      if (!dataleftIds.includes(item)) recycleTenant.value.push(item)
    })
  }
  if (direction === 'right') {
    const datarightIds = tenantDataRight.value.map((item) => item.tenantId)
    movedKeys.forEach((item) => {
      const index = recycleTenant.value.indexOf(item)
      if (index !== -1) recycleTenant.value.splice(index, 1)
      if (!datarightIds.includes(item)) distributionTenant.value.push(item)
    })
  }
}

// 提交
function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      store
        .dispatch('k8s/k8sAssign', {
          k8sId: props.k8sId,
          tenantIds: ruleForm.tenantList
        })
        .then((res: any) => {
          ElMessage.success(res?.msg)
          resetForm()
        })
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.tenantList = []
  distributionTenant.value = []
  recycleTenant.value = []
  emit('update:modelValue', false)
}

function getInitData() {
  store
    .dispatch('k8s/k8sTenantList', {
      id: props.k8sId
    })
    .then((res) => {
      tenantDataLeft.value = res.data.tenantList
      tenantDataRight.value = res.data.k8sTenantList.map((item) => {
        return {
          ...item,
          disabled: item.label !== 'true'
        }
      })
      ruleForm.tenantList = res.data.k8sTenantList.map((item) => item.tenantId)
    })
}
</script>

<style lang="scss">
.tp-form-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}
.tp-form {
  .k8s-list {
    display: flex;
    flex-flow: column;
    .el-form-item__label-wrap {
      display: flex;
      justify-content: flex-start;
      width: 100% !important;
    }
    .el-form-item__label {
      position: relative;
      display: flex;
      justify-content: flex-start;
      width: 100% !important;
      height: 14px;
      padding-left: 6px;
      border-left: 2px solid rgba(71, 119, 255, 1);
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 14px;
      font-weight: 600;
      color: rgba(51, 57, 76, 1);
    }
  }
}
</style>
