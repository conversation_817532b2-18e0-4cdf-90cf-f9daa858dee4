{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/checkbox/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Checkbox from \"./src/index.vue\";\r\n\r\nexport const DcCheckbox = withInstall(Checkbox);\r\n\r\nexport default DcCheckbox;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["Checkbox"], "mappings": ";;;;AAEY,MAAC,UAAU,GAAG,WAAW,CAACA,YAAQ;;;;"}