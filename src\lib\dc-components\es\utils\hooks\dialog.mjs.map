{"version": 3, "file": "dialog.mjs", "sources": ["../../../../../packages/utils/hooks/dialog.ts"], "sourcesContent": ["import { ref } from \"vue\";\r\n\r\nexport function useDialog({\r\n  cancel,\r\n  confirm,\r\n  show,\r\n  defaultData,\r\n}: {\r\n  cancel?: (val?: any, dialogData?: any) => void;\r\n  confirm?: (val?: any, dialogData?: any) => void;\r\n  show?: (val?: any) => void;\r\n  defaultData?: any;\r\n} = {}) {\r\n  const visible = ref(false);\r\n  const dialogData = ref<any>(defaultData);\r\n\r\n  const showDialog = (val?: any) => {\r\n    visible.value = true;\r\n    dialogData.value = val;\r\n    if (show && typeof show === \"function\") {\r\n      show(val);\r\n    }\r\n  };\r\n\r\n  const onCancel = (val?: any) => {\r\n    visible.value = false;\r\n    if (cancel && typeof cancel === \"function\") {\r\n      cancel(val, dialogData.value);\r\n    }\r\n  };\r\n\r\n  const onConfirm = (val?: any) => {\r\n    onCancel();\r\n    if (confirm && typeof confirm === \"function\") {\r\n      confirm(val, dialogData.value);\r\n    }\r\n  };\r\n\r\n  return {\r\n    showDialog,\r\n    onCancel,\r\n    onConfirm,\r\n    visible,\r\n    dialogData,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;AACO,SAAS,SAAS,CAAC;AAC1B,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,IAAI;AACN,EAAE,WAAW;AACb,CAAC,GAAG,EAAE,EAAE;AACR,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;AACtC,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;AAC9B,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACzB,IAAI,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;AAC5C,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;AAC5B,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAChD,MAAM,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AACpC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK;AAC7B,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AAClD,MAAM,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AACrC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}