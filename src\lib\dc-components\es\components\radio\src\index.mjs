import { defineComponent, useAttrs, computed, isRef, openBlock, createBlock, unref, mergeProps, withCtx, createElementBlock, Fragment, renderList, createTextVNode, toDisplayString } from 'vue';
import { ElRadioGroup, ElRadio } from 'element-plus';
import 'element-plus/es/components/radio/style/css';
import 'element-plus/es/components/radio-group/style/css';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcRadio"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: [String, Number]
    },
    options: {
      type: [Array, Object],
      default: () => []
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const attrs = useAttrs();
    const options = computed(() => {
      return isRef(props.options) ? props.options.value : props.options;
    });
    const handleChange = (val) => {
      emits("update:modelValue", val);
      if (typeof attrs.onChange === "function") {
        attrs.onChange(val);
      }
    };
    ;
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElRadioGroup), mergeProps({ ..._ctx.$attrs }, {
        "model-value": props.modelValue,
        onChange: handleChange
      }), {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(options.value, (item, index) => {
            return openBlock(), createBlock(unref(ElRadio), {
              key: index,
              label: item.value
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(item.label), 1)
              ]),
              _: 2
            }, 1032, ["label"]);
          }), 128))
        ]),
        _: 1
      }, 16, ["model-value"]);
    };
  }
});
var Radio = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\radio\\src\\index.vue"]]);

export { Radio as default };
//# sourceMappingURL=index.mjs.map
