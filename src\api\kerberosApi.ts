import service, { downService } from '@/utils/Http'
import { getPath } from '@/utils/Path'

export const kerberosUserPage = (data: KerberosApi.KerberosUserPage) => {
  return service.post<ApiResponse<any>>('/kerberos/user/page', data)
}

export const kerberosUserAdd = (data: KerberosApi.KerberosUserAdd) => {
  return service.post<ApiResponse<any>>('/kerberos/user/add', data)
}

export const kerberosUserDelete = (data: KerberosApi.KerberosUserDelete) => {
  return service.post<ApiResponse<any>>('/kerberos/user/delete', data)
}

export const kerberosUserRealm = (clusterId: string) => {
  return service.get<ApiResponse<any>>(`/kerberos/user/${clusterId}/realm`)
}

export const kerberosKeytab = async ({ clusterId, userId }: { clusterId: string; userId: string }) => {
  return await downService().get(getPath(`/kerberos/user/keytab/download/${clusterId}/${userId}`))
}
