<template>
  <div class="host-add">
    <BaseLayout :is-header="false">
      <template #content>
        <el-alert class="alert" :title="$t('message.addHostTip')" type="warning" show-icon />
        <el-alert class="alert" title="" type="warning" show-icon>
          <template #title>
            平台所管理的K8s集群的master或者worker节点不允许作为主机添加。<br />
            添加前请确保主机已开启RSA加密方式，查看<span class="link" @click="openStartRSA">开启方式</span>。
          </template>
        </el-alert>
        <el-form ref="ruleFormRef" :model="namespaceForm" :rules="rules" label-width="150px" size="large">
          <TheTitle inline>
            <template #titleText>{{ $t('replenish.nodeType') }}</template>
          </TheTitle>
          <el-form-item :label="$t('replenish.selectNodeType')" prop="hostType" class="chooseItem">
            <el-select v-model="namespaceForm.hostType" @change="changeHostType">
              <el-option :value="HostType.HOST" label="cluster-node"></el-option>
              <el-option :value="HostType.GATEWAY" label="gateway"></el-option>
            </el-select>
          </el-form-item>
          <TheTitle inline>
            <template #titleText>{{ $t('title.selectCluster') }}</template>
            <template #tip>
              <BaseElTooltip :content="$t('message.selectClusterTip')" placement="top">
                <i class="iconfont icon-icon_tishi1" />
              </BaseElTooltip>
            </template>
          </TheTitle>
          <el-form-item :label="$t('form.selectCluster') + '：'" prop="namespaceId" class="chooseItem">
            <el-radio-group v-model="namespaceForm.namespaceId">
              <el-radio :label="-1" @click="resetSelectValue"> {{ $t('form.addingHostToDefaultCluster') }} </el-radio>
              <el-radio :label="selectValue" v-if="namespaceForm.hostType === HostType.HOST">
                {{ $t('form.addingHostToExistingCluster') + '：' }}
                <el-select
                  v-model="selectValue"
                  clearable
                  size="default"
                  :placeholder="$t('form.pleaseSelectCluster')"
                  @change="handleNamespaceChange"
                >
                  <el-option v-for="item in namespace" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <TheTitle>
            <template #titleText>{{ $t('replenish.hostInfo') }}</template>
          </TheTitle>
          <HostsAddCollapse
            :show-ds-choose="namespaceForm.hostType === HostType.GATEWAY"
            :hostType="namespaceForm.hostType"
            ref="hostsAddCollapse"
            :namespace-id="namespaceForm.namespaceId"
          />
          <HostsAddButton class="mb-20px" @click="addCollapse" />
        </el-form>
      </template>
      <template #footer>
        <div>
          <BaseButton link @click="router.push({ name: 'hosts' })"> {{ $t('button.back') }} </BaseButton>
          <BaseButton type="primary" :loading="isConfirmLoading" @click="addNewHost">{{ $t('replenish.confirmAddition') }} </BaseButton>
        </div>
      </template>
    </BaseLayout>
    <el-dialog :model-value="startRSAVisible" title="开启RSA" width="550px">
      <div class="start-rsa">
        <div class="start-rsa-item">
          <div class="start-rsa-item_title">Step1:</div>
          <div class="start-rsa-item_content">
            <div class="start-rsa-item_desc">登录到对应服务器</div>
          </div>
        </div>
        <div class="start-rsa-item">
          <div class="start-rsa-item_title">Step2:</div>
          <div class="start-rsa-item_content">
            <div class="start-rsa-item_desc">使用命令查看是否支持RSA加密方式：</div>
            <div class="start-rsa-item_code"># ssh -Q key |grep rsa ssh-rsa #确保执行结果中有这一行内容</div>
          </div>
        </div>
        <div class="start-rsa-item">
          <div class="start-rsa-item_title">Step3:</div>
          <div class="start-rsa-item_content">
            <div class="start-rsa-item_desc">如不支持，可在 /etc/ssh/sshd_config 文件中添加如下信息：</div>
            <div class="start-rsa-item_code">
              # vi /etc/ssh/sshd_config<br />
              HostKeyAlgorithms +ssh-rsa<br />
              PubkeyAcceptedKeyTypes +ssh-rsa<br />
            </div>
          </div>
        </div>
        <div class="start-rsa-item">
          <div class="start-rsa-item_title">Step4:</div>
          <div class="start-rsa-item_content no-border">
            <div class="start-rsa-item_desc">重启sshd</div>
            <div class="start-rsa-item_code"># systemctl restart sshd</div>
          </div>
        </div>
      </div>
      <template #footer>
        <BaseButton type="primary" @click="closeStartRSA">{{ $t('button.sure') }}</BaseButton>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import TheTitle from '@/components/The/TheTitle.vue'
import { HostType } from '../types'
import HostsAddButton from './HostsAddButton.vue'
import HostsAddCollapse from './HostsAddCollapse.vue'
const { t, router, store, setBreadList, route } = useBasicTool()

const ruleFormRef = ref()
const selectValue = ref<stringOrNumber>('')
const namespaceForm = reactive({
  namespaceId: -1,
  hostType: HostType.HOST
})
const startRSAVisible = ref(false)
const rules = reactive({
  namespaceId: [{ required: true, message: t('message.requiredSelect'), trigger: 'change' }],
  hostType: [
    {
      required: true,
      message: '',
      trigger: 'change'
    }
  ]
})

const namespace = ref<any>([])
const hostsAddCollapse = ref()
const isConfirmLoading = ref(false)
function handleNamespaceChange(value: number) {
  namespaceForm.namespaceId = value
}
function resetSelectValue() {
  selectValue.value = ''
}

function changeHostType() {
  namespaceForm.namespaceId = -1
}

function addCollapse() {
  hostsAddCollapse.value.addCollapseList()
}
// 添加主机
function addNewHost() {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      isConfirmLoading.value = true
      hostsAddCollapse.value.submitForm().then((res: boolean) => {
        isConfirmLoading.value = res
      })
    }
  })
}
function openStartRSA() {
  startRSAVisible.value = true
}
function closeStartRSA() {
  startRSAVisible.value = false
}
onMounted(() => {
  store.dispatch('hosts/getNamespaceListExcludeDefault').then((res: any) => {
    namespace.value = res.data.filter((el: any) => el.deployMode === 1)
  })
  setBreadList([
    {
      name: t('menu.host')
    },
    {
      name: t('menu.hostList'),
      to: {
        name: 'hosts'
      }
    },
    {
      name: t('title.addHost')
    }
  ])
  if (route.query.type) {
    namespaceForm.hostType = Number(route.query.type) as HostType
  }
})
</script>

<style lang="scss" scoped>
.host-add {
  height: 100%;
  :deep(.layout) {
    .layout-header {
      height: 48px;
      min-height: auto;
      border-bottom: 1px solid var(--ops-border-color);
    }
    .layout-footer {
      border-top: 1px solid var(--ops-border-color);
    }
  }
  .alert {
    margin: 10px 0px;
    :deep(.el-alert__content) {
      line-height: 18px;
    }
    .link {
      color: var(--ops-primary-color);
      cursor: pointer;
    }
  }
  .start-rsa {
    &-item {
      display: inline-flex;
      flex-flow: row wrap;
      justify-content: stretch;
      width: 100%;
      font-size: 14px;
      line-height: 22px;
      color: rgba(41, 51, 78, 1);
      &_title {
        width: 60px;
        font-weight: 500;
      }
      &_content {
        position: relative;
        top: 6px;
        flex: 1;
        padding-bottom: 24px;
        padding-left: 20px;
        border-left: 1px dashed rgba(41, 52, 78, 0.15);
        &::before {
          content: '';
          position: absolute;
          top: 0px;
          left: -4px;
          display: block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: var(--ops-primary-color);
        }
        &.no-border {
          border-left: none;
        }
      }
      &_desc {
        position: relative;
        flex: 1;
        margin-top: -6px;
        margin-bottom: 4px;
      }
      &_code {
        width: 100%;
        padding: 8px;
        border-radius: 2px;
        background-color: rgba(245, 247, 250, 1);
      }
    }
  }
}
</style>
