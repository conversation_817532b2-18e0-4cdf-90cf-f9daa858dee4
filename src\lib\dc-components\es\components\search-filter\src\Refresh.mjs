import { defineComponent, createVNode } from 'vue';
import { ElIcon } from 'element-plus';
import 'element-plus/es/components/icon/style/css';
import { Refresh as Refresh$1 } from '@element-plus/icons-vue';

var Refresh = defineComponent({
  render() {
    return createVNode(ElIcon, null, {
      default: () => [createVNode(Refresh$1, null, null)]
    });
  }
});

export { Refresh as default };
//# sourceMappingURL=Refresh.mjs.map
