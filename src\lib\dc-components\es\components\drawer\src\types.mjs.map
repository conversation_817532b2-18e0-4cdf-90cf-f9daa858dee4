{"version": 3, "file": "types.mjs", "sources": ["../../../../../../packages/components/drawer/src/types.ts"], "sourcesContent": ["import type { Ref } from \"vue\";\r\nimport type { Dc<PERSON>uttonConfig } from \"../../button\";\r\nimport type { RenderVNodeFn } from \"../../render-vnode/src/types\";\r\nimport type DcDrawer from \"./index.vue\";\r\n\r\nexport interface DcDrawerAction {\r\n  innerText?: string;\r\n  visible?: boolean | Ref<boolean>;\r\n  componentSlot?: Record<string, RenderVNodeFn>;\r\n  props?: Record<string, any>;\r\n  config?: DcButtonConfig;\r\n}\r\n\r\nexport enum DcDrawerTypes {\r\n  HEADER_FOOTER = \"header_footer\",\r\n  FOOTER = \"footer\",\r\n  HEADER = \"header\",\r\n  ONLYCONTENT = \"ONLYCONTENT\",\r\n}\r\n\r\nexport type DcDrawerInstance = InstanceType<typeof DcDrawer>;\r\n"], "names": [], "mappings": "AAAU,IAAC,aAAa,mBAAmB,CAAC,CAAC,cAAc,KAAK;AAChE,EAAE,cAAc,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC;AACpD,EAAE,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACtC,EAAE,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACtC,EAAE,cAAc,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC;AAChD,EAAE,OAAO,cAAc,CAAC;AACxB,CAAC,EAAE,aAAa,IAAI,EAAE;;;;"}