import { defineComponent, computed, h, openBlock, createElementBlock, Fragment, createVNode, unref, mergeProps } from 'vue';
import RenderVNode from '../../../../render-vnode/src/index.mjs';
import table from '../../../../table/src/index.mjs';
import DcFormItem from '../FormItem/index.mjs';
import './index.vue_vue_type_style_index_0_lang.mjs';
import _export_sfc from '../../../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcFormTable"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Array,
      default: () => []
    },
    topSlot: {
      type: Object,
      default: null
    },
    bottomSlot: {
      type: Object,
      default: null
    },
    tableProps: {
      type: Object,
      default: () => ({})
    },
    fileList: {
      type: Object,
      default: () => ({})
    },
    rowIndex: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue", "triggerEffect", "blur"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const columns = computed(() => {
      return props.columns?.map((item) => {
        const config = { ...item };
        if (config.isFormItem && config.formItemConfig) {
          config.formatter = (row, columns2, cellVal, index) => {
            const model = config.formItemConfig.model.replace("[index]", `[${index}]`);
            return h(DcFormItem, {
              config: {
                ...config.formItemConfig,
                model
              },
              visible: true,
              modelValue: props.modelValue[index][config.prop],
              rowIndex: index,
              margin: config.formItemConfig?.margin,
              onTriggerEffect: triggerEffect,
              ["onUpdate:modelValue"]: (val) => updateModelValue(val, index, config.prop)
            }, () => "");
          };
        }
        return config;
      }) || [];
    });
    const triggerEffect = (...args) => {
      emits("triggerEffect", ...args);
    };
    const updateModelValue = (val, index, columnProp) => {
      const newVal = props.modelValue;
      newVal[index][columnProp] = val;
      emits("update:modelValue", newVal);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(Fragment, null, [
        createVNode(unref(RenderVNode), {
          vnode: props.topSlot
        }, null, 8, ["vnode"]),
        createVNode(table, mergeProps({ ...__props.tableProps }, {
          data: props.modelValue,
          columns: columns.value,
          class: "dc-form-table"
        }), null, 16, ["data", "columns"]),
        createVNode(unref(RenderVNode), {
          vnode: props.bottomSlot
        }, null, 8, ["vnode"])
      ], 64);
    };
  }
});
var DcFormTable = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\form\\src\\components\\Table\\index.vue"]]);

export { DcFormTable as default };
//# sourceMappingURL=index.mjs.map
