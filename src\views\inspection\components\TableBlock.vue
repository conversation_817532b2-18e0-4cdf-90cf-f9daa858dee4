<template>
  <div class="flex-1">
    <BaseLayout :calc-minuend="104">
      <template #header>
        <div>
          <TheTitle class="mt-20px" :content="$t('replenish.historicalInspectionReports')"></TheTitle>
          <BaseSearch :searchItemData="[]" @on-submit="submitSearch" @reset-search="resetSearch">
            <template #after>
              <el-form-item :label="$t('form.timeSelection') + '：'">
                <el-date-picker
                  v-model="timeInterval"
                  type="datetimerange"
                  range-separator="⇀"
                  :start-placeholder="$t('form.startDate')"
                  :end-placeholder="$t('form.endDate')"
                  prefix-icon="el-icon-date"
                  clearable
                  :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
                  @change="timeClear"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </template>
          </BaseSearch>
        </div>
      </template>
      <template #content>
        <BaseTable ref="baseTable" v-loading="tableLoading" :columns="tableData.column" :data="tableData.data">
          <template #inspectionStatus="scope">
            <!-- is-loading 为element-plus 转动样式 -->
            <el-icon v-if="[1].includes(scope.row.inspectionStatus)" class="is-loading" style="position: relative; top: 2px; left: -3px">
              <Loading />
            </el-icon>
            <span v-else :class="['status-spot', `${stateClass.get(scope.row.inspectionStatus)}`]" />
            <span>{{ stateText.get(scope.row.inspectionStatus) }}</span>
          </template>
          <template #inspectionTime="scope">
            {{ scope.row.inspectionTime >= 0 ? scope.row.inspectionTime + ' mins' : '-' }}
          </template>
          <template #operate="scope">
            <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" />
          </template>
        </BaseTable>
      </template>
      <template #footer>
        <BasePagination v-model="pageInfo" @page-change="onPageChange" />
      </template>
    </BaseLayout>
  </div>
</template>
<script lang="ts" setup>
import Loop from '@/utils/Loop'
import { bus } from '@/utils/mitt'
import { Loading } from '@element-plus/icons-vue'
import type { DateModelType } from 'element-plus'
const { t, router, store } = useBasicTool()
const { pageInfo, resetPageInfo } = usePage()
const timeInterval = ref<[DateModelType, DateModelType]>(['', ''])
const tableLoading = ref(false)
const tenantId = computed(() => store.state.user.userInfo.tenantId)
// 列表配置项
const tableData = reactive<any>({
  column: [
    { prop: 'inspectionReportName', label: t('replenish.inspectionReport'), 'min-width': 160 },
    { prop: 'startTime', label: computed(() => t('table.startingTime')), 'min-width': 130 },
    { prop: 'endTime', label: computed(() => t('table.endTime')), 'min-width': 130 },
    { prop: 'inspectionTime', label: t('replenish.inspectionDuration'), slot: true, width: 130 },
    { prop: 'inspectionTotalNum', label: t('replenish.inspectionItems'), width: 130 },
    { prop: 'inspectionStatus', label: t('replenish.inspectionStatus'), slot: true, width: 130 },
    { prop: 'operate', label: computed(() => t('table.operate')), slot: true, width: 120, fixed: 'right' }
  ],
  data: []
})
onMounted(() => {
  getData()
  refreshList()
})

// 搜索提交
function submitSearch() {
  pageInfo.pageNo = 1
  getData()
}
// 重置搜索框
function resetSearch() {
  resetPageInfo()
  timeInterval.value = ['', '']
  getData()
}

function timeClear(value: any) {
  if (!value) {
    timeInterval.value = ['', '']
    getData()
  }
}
// 表格操作栏数据
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('replenish.viewReport'),
        disabled: [1].includes(scope.row.inspectionStatus),
        click: () => {
          router.push({
            name: 'inspectionReport',
            query: { ...scope.row }
          })
        },
        isButton: true
      },
      {
        buttonName: t('replenish.export'),
        disabled: false,
        click: () => {},
        isButton: false
      }
    ]
  }
}
function onPageChange() {
  getData()
}

onBeforeUnmount(() => {
  if (refreshLoop?.clearLoop) {
    refreshLoop.clearLoop()
  }
})
let refreshLoop: any
function refreshList() {
  if (refreshLoop?.clearLoop) {
    refreshLoop.clearLoop()
  }
  refreshLoop = new Loop(function () {
    getData()
  }, 10000)
}

function getData() {
  store
    .dispatch('inspection/inspectionPage', {
      startTime: timeInterval.value ? timeInterval.value[0] : '',
      endTime: timeInterval.value ? timeInterval.value[1] : '',
      tenantId: tenantId.value,
      ...pageInfo
    })
    .then((res) => {
      tableData.data = res.data.records
      pageInfo.total = res.data.total
    })
}
const stateClass = new Map().set(3, 'status-spot--failure').set(2, 'status-spot--running')
const stateText = new Map().set(1, t('replenish.inProgress')).set(3, t('replenish.abnormal')).set(2, t('replenish.complete'))

bus.on('inspectionPage', () => {
  getData()
})
</script>
<style lang="scss" scoped></style>
