'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/checkbox/style/css');
require('element-plus/es/components/checkbox-group/style/css');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcCheckbox"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: Array
    },
    options: {
      type: [Array, Object],
      default: () => []
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const attrs = vue.useAttrs();
    const options = vue.computed(() => {
      return vue.isRef(props.options) ? props.options.value : props.options;
    });
    const handleChange = (val) => {
      emits("update:modelValue", val);
      if (typeof attrs.onChange === "function") {
        attrs.onChange(val);
      }
    };
    ;
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElCheckboxGroup), vue.mergeProps({ ..._ctx.$attrs }, {
        "model-value": props.modelValue,
        onChange: handleChange
      }), {
        default: vue.withCtx(() => [
          (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(options.value, (item, index) => {
            return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElCheckbox), {
              key: index,
              label: item.value
            }, {
              default: vue.withCtx(() => [
                vue.createTextVNode(vue.toDisplayString(item.label), 1)
              ]),
              _: 2
            }, 1032, ["label"]);
          }), 128))
        ]),
        _: 1
      }, 16, ["model-value"]);
    };
  }
});
var DcCheckbox = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\checkbox\\src\\index.vue"]]);

exports["default"] = DcCheckbox;
//# sourceMappingURL=index.js.map
