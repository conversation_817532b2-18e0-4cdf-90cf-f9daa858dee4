{"Friday": "金曜日", "Monday": "月曜日", "Saturday": "土曜日", "Sunday": "日曜日", "Thursday": "木曜日", "Tuesday": "火曜日", "Wednesday": "水曜日", "abnormal": "異常", "account": "アカウント", "accountInfo": "アカウント情報", "active": "活発", "add": "追加", "addApplication": "アプリケーションを追加", "addCluster": "クラスターを追加します。", "addComponent": "新規コンポーネント", "addComponents": "コンポーネントを追加します。", "addCustomParameter": "カスタムパラメータを追加します", "addFailed": "追加に失敗しました", "addHBaseTask": "Hbaseタスクの追加", "addHDFTask": "HDFSタスクを追加します。", "addHiveTask": "Hiveタスクを追加します。", "addHost": "ホストの追加", "addHostToDefaultCluster": "デフォルトクラスタへのホストの追加", "addHostToExistingCustomCluster": "既存のカスタムクラスタにホストを追加する", "addHostsToCluster": "ホストグループを追加してクラスタを構成し、エージェントサービスをインストールする", "addJob": "ジョブを追加する", "addK8s": "K8sの追加", "addKerberosUser": "Kerberosユーザーの追加", "addKey": "新しいキーワード", "addNamespace": "Namespaceを追加します。", "addNewDashboard": "新しいダッシュボード", "addNewHost": "新しいホスト", "addParameter": "パラメータを追加する", "addPeople": "メンバーを追加します。", "addPrimarySecondaryPlan": "主バックアッププランを追加します", "addRack": "ラックを追加", "addRobot": "ロボットを追加します。", "addRole": "ロールの追加", "addRoleGroup": "ロールグループの追加", "addServiceType": "サービス・タイプの追加", "addStatus": "ステータスの追加", "addSuccess": "追加成功", "addTask": "タスクを追加", "addTime": "時間の追加", "addUser": "新規ユーザー", "addUserSuccess": "新規ユーザーが成功しました", "addUsers": "ユーザーの追加", "added": "追加されました", "adding": "追加中", "address": "アドレス", "adds": "追加", "advancedConfiguration": "高度な構成", "advancedSettings": "高度な設定機能を使用して、カスタムクラスタの割り当てを行うことができます。", "alarmCategory": "アラート・カテゴリ", "alarmContent": "アラーム内容", "alarmCount": "警告数", "alarmRecord": "アラーム記録", "alarmRuleSetting": "アラート・ルールの設定", "alarmService": "アラームサービス", "alarmTime": "警告時間", "alertAutoRelease": "この警告が手動で解除する必要がない場合、アラートルールは2つの巡回サイクル後にトリガーされないと自動的に「Released」状態に設定されます。", "alertCategory": "アラート・カテゴリ", "alertContent": "アラーム内容", "alertInterval": "アラーム間隔", "alertLevel": "アラームレベル", "alertNotificationLevel": "アラーム通知レベル", "alertNotificationSettings": "アラーム通知設定", "alertPushByHighestSeverity": "複数のレベルを選択した場合、トリガーが発生するときは、高リスク、警告、危険、一般の順で最も高いレベルでアラートが送信されます。", "alertStatus": "警告状態", "alertTitle": "アラームタイトル", "all": "すべて", "allocatedMemoryInSeconds": "割り当てられたメモリーの秒数", "allocatedVCoresInSeconds": "割り当てられたVCoreの秒数", "alphanumericCharactersDashUnderscoreDot": "英数字、-、_および.のみ。最初から始められなかった。", "amend": "変更", "and": "とです", "appliedConfigFileVersion": "実際に適用されたプロファイルのバージョン", "applyWhenNoOtherRulesApply": "他の規則が適用されない場合に適用します。", "asParentPoolThenUserNameSubPool": "root.{value}を親プールとして使用し、そのユーザー名に一致するサブプールを使用します。", "audit": "監査", "auditList": "監査リスト", "auditLogAlarm": "現在のクラスターが選択された対応するレベルの監査操作を実行した場合、監査ログアラームがトリガーされ、同じ操作に対してはアラーム間隔後に再度送信されます。", "auditLogAlert": "監査ログ警告", "authMethod": "認証方法", "authenticationFile": "認証ファイル", "authenticationType": "認証タイプ", "autoGenerate": "自動生成", "autoScaling": "じどうかくだいようりょう", "automaticInspection": "じどうじゅんけん", "availableHosts": "利用可能なホスト", "availableInstances": "選択可能なインスタンス", "awaitingDeletion": "削除対象", "awaitingDeployment": "配備対象", "awaitingDeployment2": "配置待ち", "awaitingExecution": "保留中", "awaitingExpansion": "ぼうちょうしゅくようりょう", "awaitingRestart": "再起動待ち", "awaitingRollingRestart": "スクロールして再起動する", "awaitingScheduling": "スケジュール対象", "awaitingStart": "開始待ち", "awaitingStop": "停止中", "awaitingUpdate": "更新待ち", "backgroundImage": "背景画像", "badRequest": "要求エラー（400）", "bandwidth": "帯域", "basicInformation": "基本情報", "batchDelete": "一括削除", "batchRestart": "一括再起動", "batchUnbind": "一括解除", "beingDelet": "削除中", "beingRestart": "再起動中", "beingScal": "拡張容量", "beingSchedul": "スケジュール中", "beingStart": "起動中", "beingStop": "停止中", "beingUpdat": "更新中", "belongCluster": "所属クラスタ", "belongingCluster": "所属クラスタ", "bind": "バインド", "bindHost": "ホストをバインド", "bindK8s": "バインドk8s", "bindTime": "バインド時間", "bindingSuccess": "バインド成功", "blacklist": "ブラックアウト", "blacklistSuccess": "ブラックアウト成功", "blacklistedNodeMessage": "このノードはブラックリストノードであり、書き込み操作はできません。", "boundHostCannotBeDeleted": "ホストがバインドされているため、コンポーネントのインストールやK8sクラスターのバインドは削除できません。", "browserUpgradeNotice": "システムは現在のブラウザのバージョンが低いことを認識し、一部のページ表示が失われる可能性があります。現在のブラウザのバージョンを69以上にアップグレードしてください", "buildChart": "グラフの構築", "button": "ボタンを使用します。", "buttonText": "ここにボタンテキストを入力", "byTime": "時間別", "cancel": "キャンセル", "cancelScheduledKill": "定時検殺を取り消す", "cannotExceed100": "100より大きくすることはできません", "changesMayNotBeSaved": "システムはおそらくあなたの変更を保存しません。", "charAndSymbolLimit": "数字、英語の大文字と小文字、アンダースコア(_)およびハイフン(-)のみがサポートされています。", "charAndSymbolLimit2": "3～20ビット文字", "charLimit1To20": "1〜20文字、中国語と英語以外の文字入力はサポートされていません", "charLimit3To20": "3～20ビット文字", "character": "文字", "charactersLimitedToAlphanumeric": "英数字のみ", "chart": "グラフ", "chartQuantity": "グラフの数", "chartType": "グラフの種類", "checkAllHosts": "すべてのホストをチェック", "checkComponentConfig": "すべてのコンポーネントの構成が正しいかどうかを確認してください。", "checkHost": "ホストのチェック", "checkStartTime": "検査開始時間", "checkStatus": "ステータスの確認", "choosable": "オプション", "chooseTenant": "テナントの選択", "clear": "クリア", "clientCertificate": "クライアント証明書", "clientToDataSource": "クライアントとデータソース", "clone": "クローン", "cloneResourcePool": "クローンリソースプール", "close": "閉じる", "cluster": "クラスタ化", "clusterAlertNotificationSettings": "クラスター警告通知の設定", "clusterCannotBeDeleted": "現在のクラスタ（xxxx）は削除できません。", "clusterDeletionSuccess": "クラスタが正常に削除されました", "clusterHostList": "クラスタホストのリスト", "clusterInspection": "クラスタ巡回検査", "clusterList": "クラスタ・リスト", "clusterMonitoring": "クラスタ監視", "clusterName": "クラスター名", "clusterNameLength": "クラスタ名は最長で{number}ビットを超えない", "clusterOverview": "クラスター概要", "clusterRenameSuccess": "クラスタの名前変更に成功しました", "clusterResourceUsage": "クラスタリソースの使用状況", "clusterState": "クラスターの状態", "clusterUnbind": "クラスタ解除", "coldDataClusterDirectory": "コールドデータクラスタディレクトリ", "coldDataHDFSName": "HDFSにある冷データの名前", "coldDataList": "コールドデータリスト", "coldDataStorageCluster": "コールドデータストアクラスタ", "collapse": "収納", "collapseAll": "すべて折りたたむ", "commandTerminal": "コマンド端末", "complete": "完了", "completed": "完了", "component": "コンポーネント", "componentAbnormalSituation": "コンポーネントの異常状態", "componentAdded": "コンポーネントが追加されました", "componentCannotBeDeleted": "現在のコンポーネント（xxxx）は削除できません。", "componentConfigRecord": "コンポーネント構成レコード", "componentConfiguration": "コンポーネント構成", "componentCount": "コンポーネント数", "componentDependency": "現在のコンポーネント({value})は({values}に依存しています", "componentDependency1": "現在選択されているコンポーネント({value})は({string})に依存しています。{value}をインストールする前に、{string}のインストールを完了してください。", "componentDependencyNotice": "現在選択されているコンポーネント（xxxx）は（string）に依存しています。xxxxをインストールする前に、xxxのインストールを完了してください。", "componentDetails": "コンポーネントの詳細", "componentHasDependencies": "現在のコンポーネントには依存関係があり、削除できません！", "componentInstallation": "コンポーネントのインストール", "componentInstance": "コンポーネントインスタンス", "componentLogAlert": "コンポーネントログアラート", "componentMonitoring": "コンポーネントの監視", "componentName": "コンポーネント名", "componentNameFormat": "コンポーネント名は小文字と数字のみをサポートし、小文字を含む必要があります", "componentOverview": "コンポーネントの概要", "componentRunningStatusAlert": "コンポーネント運転状態アラーム", "componentService": "コンポーネントサービス", "componentServiceInspection": "コンポーネントサービス巡回検査", "componentStatus": "コンポーネントの状態", "componentStatusAlarm": "指定された間隔内で、コンポーネントの状態が異常または障害の場合にアラートが発生します。", "componentVersion": "コンポーネントのバージョン", "concurrency": "同時数", "configCategory": "構成カテゴリ", "configDownload": "ダウンロードの構成", "configOperation": "構成アクション", "configRollbackSuccess": "ロールバック成功の構成", "configSaveAndPublishFailure": "設定の保存とパブリッシュに失敗しました", "configSaveAndPublishSuccess": "設定の保存とパブリッシュに成功しました", "configSaveFailure": "構成の保存に失敗しました", "configSaveSuccess": "構成が正常に保存されました。", "configSnapshot": "スナップショットの構成", "configurationInformation": "構成情報", "configurationRecord": "構成レコード", "configurationSet": "コンフィギュレーションセット", "configurationSetNameLength1To20": "コンフィギュレーションセット名は1～20文字", "configurationSource": "ソースの構成", "configurationView": "構成の表示", "configureComponentParameters": "構成コンポーネントパラメータ", "configureServiceParameters": "サービスパラメータの設定", "confirm": "を選択して、", "confirmAddition": "追加の確認", "confirmBlacklistDataNode": "DataNodeノードを黒にするかどうかを確認します", "confirmCancelScheduledKill": "スケジュール解除によるタスクの検出を確認するかどうか", "confirmClearSelectedAlertRecords": "選択したアラートレコードを消去しますか？", "confirmComponentInstallation": "関連コンポーネントがインストールされていることを確認してください。", "confirmDelete": "削除してよろしいですか？", "confirmDeleteCluster": "現在のクラスタ（xxxx）を削除してよろしいですか？", "confirmDeleteComponentService": "現在のコンポーネントサービス（xxxx）を削除してもよろしいですか？", "confirmDeleteCurrentTask": "現在のタスクを削除してよろしいですか？", "confirmDeleteHost": "現在のホスト（xxxx）を削除してもよろしいですか？", "confirmDeleteJob": "現在のJob（{value}）を削除することを確認しますか？", "confirmDeleteKerberosUser": "現在のKerberosユーザーを削除することを確認しますか？削除後に履歴生成されたKeytabは無効になります。", "confirmDeleteKeyword": "キーワードを削除しますか？", "confirmDeletePlacementRule": "配置を削除しますか？", "confirmDeleteRetiredNode": "引退ノードの削除を確認しますか？", "confirmDeleteScheduleMode": "計画モードを削除するかどうか", "confirmDeleteSelectedComponent": "選択したコンポーネントを削除してよろしいですか？", "confirmDeleteSelectedHost": "選択したホストを削除してよろしいですか？", "confirmDeleteUserLimit": "ユーザー制限を削除するかどうか", "confirmExitEditing": "OK：現在の編集を終了しますか？", "confirmExportKeytabFile": "現在のKerberosユーザーのKeytabファイルをエクスポートしますか？", "confirmHostUnbinding": "選択したホストをアンバインドしてよろしいですか？", "confirmInstallation": "インストールを確認しますか？", "confirmKillTask": "そのタスクを確認して削除しますか？", "confirmLeavePage": "このページを離れるかどうか確認してくださいか？", "confirmModifyHostname": "主機名{value}を変更してもよろしいですか？", "confirmNewPassword": "パスワードの確認", "confirmRackDeletion": "現在のラックを削除してもよろしいですか？（ラック番号xxx）", "confirmRollback": "ロールバックの決定", "confirmRollbackToVersion": "構成がこのバージョン（xxxx）にロールバックされていることを確認しますか？", "confirmRollingRestart": "スクロールして再起動しますか？", "confirmSubmission": "コミットを確認しますか？", "confirmSubmitForScaling": "提出後、システムは拡張縮小を行い、数分かかる可能性があります。", "confirmToDeleteTenant": "テナントを削除してもよろしいですか？", "confirmUnbindHost": "ホスト（xxxx）をクラスターから解除しますか？", "confirmUnbindK8sCluster": "クラスタのk8s{value}がアンバインドされていることを確認しますか？", "confirmUnblacklistDataNode": "DataNodeノードのブロックを解除するかどうかを確認しますか", "confirmUpdateSelectedComponent": "選択したコンポーネントを更新しますか？", "confirmationPrompt": "確認メッセージ", "connectionAlarm": "ホストがターゲットホストとのネットワーク接続を確立できないか、接続が中断された場合に警告が発生します。この値は0または1であり、0は接続が確立されていないか、接続が中断されていることを示します。1は接続が確立されていることを示します。", "connectionError": "接続エラー(xxxx)!", "connectionMethod": "接続方法", "connectivityTestFailure": "接続のテストに失敗しました。構成を確認してください", "connectivityTestSuccess": "テスト接続に成功し、操作を続行できます", "containerName": "コンテナ名", "copySuccess": "コピー成功", "countSmallFilesInDirectory": "このディレクトリの下の小さなファイルの数を統計して、小さなファイルはカスタム設定を行うことができます。", "cpuResources": "CPUリソース", "cpuUsageAlarm": "ホストのCPU使用率が設定された閾値に達した場合、警告がトリガーされます。", "create": "新規", "createClusterOneComponentModifiable": "クラスタを作成するときは、一度に1つのコンポーネントサービスしか追加できません。構成コンポーネントのパラメータはすべてバックエンドから入力され、デフォルト値で事前記入され、変更をサポート", "createNewKerberosUser": "新規Kerberosユーザー", "createOrExpandCluster": "新しいクラスタを作成したり、既存のクラスタを拡張するためには、ホストを使用してください。クラスタがKerberos認証を使用している場合は、新しいホストにKerberosソフトウェアパッケージがインストールされていることを確認してください。そうしないと、新しいホスト上でサービスが実行されなくなります。", "createPermission": "権限の作成", "createPermissions": "権限の作成", "createPlacementRule": "配置規則の作成", "createPoolIfNotExist": "プールが存在しない場合は作成します。", "createResourcePool": "リソースプールの作成", "createScheduleMode": "計画モードを作成します。", "createScheduleRule": "プランルールを作成する", "createSubPool": "サブプールを作成します", "createTenant": "テナントを作成します。", "createTime": "作成時間", "createUserLimit": "ユーザー制限の作成", "cruxword": "キーワード", "currentCluster": "現在のクラスタ", "currentComponent": "現在のコンポーネント", "currentComponentVersion": "現在のコンポーネントバージョンvxxx", "currentConfiguration": "現在の構成", "currentCustomVersionNumber": "現在のカスタムバージョン番号", "currentHost": "現在のホスト", "currentOperation": "現在の操作", "currentRole": "現在のロール", "currentRoles": "現在のキャラクター", "custom": "カスタム", "customChart": "グラフのカスタマイズ", "customComponentName": "カスタムコンポーネント名", "customComponentNameMaxLength": "カスタムコンポーネント名に最大xxxx文字を入力！", "customConfigExists": "このカスタム構成項目は既に存在しています", "customDeployment": "カスタム展開", "customName": "カスタム名", "customParameter": "カスタムパラメータ", "customRoleAllocation": "カスタム役割割り当て", "customSize": "カスタムサイズ別", "customSmallFileSizeSettings": "小さなファイルのカスタム設定", "dailyInspection": "1日1回", "dailyRepeatFromXxToXxFirstEffectiveAtXxx": "毎日の{beginTime}時00分から{endTimeStr}時00分（CST）まで繰り返し、最初は{startTime}に有効となります。", "dangerous": "危険", "dashboard": "けいきばん", "dashboardName": "ダッシュボード名", "dataCollecting": "データ収集中...", "dataLake": "データ湖", "dataPlatform": "データミドルウェア", "date": "日付", "day": "日", "days": "日", "debugLevel": "DEBUG（ヒント）", "defaultSchedulingPolicy": "デフォルトスケジューリングポリシー", "defaultSettings": "デフォルト設定", "defaultUserLimit": "ユーザーのデフォルト制限", "defaultValue": "デフォルト値", "delete": "削除", "deleteAccount": "従業員アカウントを削除しますか？", "deleteCurrentCustomParameter": "現在のカスタム定義パラメータを削除するかどうか", "deleteCurrentPrimarySecondaryPlan": "現在のメインおよびバックアップ計画を削除しますか？", "deleteCurrentRole": "現在の役割を削除しますか？", "deleteCustomConfigAfterSave": "構成を保存してパブリッシュした後にカスタム構成を削除するには", "deleteDependentComponentFirst": "削除する前に依存するコンポーネントを削除してください", "deleteExistingHistoricalDataInBackupCluster": "スキーマの下の準備クラスタの既存の履歴データの削除", "deleteFailed": "削除に失敗しました", "deleteProgressDetails": "進行状況の詳細を削除", "deleteRoleGroupConfirmation": "ロールグループを削除するかどうか", "deleteSuccess": "削除に成功しました", "deleted": "削除されました", "deleting": "削除中", "dependencyCheck": "依存チェック中", "dependentComponents": "依存コンポーネント", "deployFailed": "配備に失敗しました", "deployImmediately": "今すぐ導入", "deployedHostCount": "配備ホスト数", "deploying": "配備中", "deploymentMethod": "配置方法", "deploymentProgressDetails": "導入の進捗状況の詳細", "description": "説明", "designation": "名前", "diagnosticReport": "診断レポート", "dingding": "くぎを打つ", "dingdingRobot": "くぎ打ちロボット", "directoryAddress": "ディレクトリアドレス", "directoryKeywords": "カタログキーワード", "disable": "非アクティブ化", "disableMaintenanceMode": "メンテナンスモードをオフにする", "disabledTenantCannotLoginSystemNoOperationPermissions": "停用されたテナントはシステムにログインできず、システムの操作権限も持ちません。", "disasterRecoveryTaskCheck": "ディザスタリカバリタスクチェック", "disasterRecoveryTaskInspection": "災害許容任務の巡検", "disk": "ディスク", "diskReadSpeedAlarm": "ホストのディスク読み取り速度が設定したしきい値に達すると、MB/s単位でアラームがトリガーされます", "diskSystem": "ディスクシステム", "diskUsage": "ディスクの使用状況", "diskWriteSpeedAlarm": "ホストのディスク書き込み速度が設定された閾値に達した場合、アラームがトリガーされます。単位はMB/sです。", "doNotRecommendClearIfUnprocessed": "警告が処理されていない場合、削除しないでください。", "dockerDeployment": "<PERSON><PERSON><PERSON>イ", "download": "ダウンロード", "downloadLog": "ダウンロードログ", "downloadNow": "今すぐダウンロード", "downloadTemplate": "テンプレートのダウンロード", "dropdown": "ドロップダウンメニュー", "dueToKubernetesBinding": "Kubernetesでクラスター{value}がバインドされているため、解除してから再度削除してください。", "duration": "継続時間", "dynamicResourceAllocation": "動的リソース割り当て", "edit": "編集", "editDashboard": "ダッシュボードを編集します。", "editJob": "ジョブを編集", "editK8s": "k8sを編集", "editPlacementRule": "配置規則の編集", "editRack": "ラックの編集", "editResourcePool": "リソースプールを編集", "editRoleGroup": "キャラクターグループを編集", "editScheduleMode": "計画モードの編集", "editSuccess": "編集に成功しました", "editTenant": "テナントを編集", "editUser": "ユーザーの編集", "editUserLimit": "ユーザー制限の編集", "editUserSuccess": "ユーザーの編集に成功しました", "editValue": "xx値の編集", "email": "メールボックス", "emailAddress": "メールアドレス：", "emailConfigurationTestResult": "メール構成テスト結果", "emailForTestResult": "テスト結果はメールでメールアドレスに送信されますので、メールアドレスを入力してください。", "emailMaxLength": "メールボックスの長さは30ビット未満", "emailSaved": "メールアドレスが保存されました", "employeeName": "従業員名", "enable": "有効化", "enableMaintenanceMode": "メンテナンスモードをオンにする", "enableTenantWillRestoreLoginSystemAndOperationPermissions": "有効化されたテナントは、ログインシステムとシステムへの操作権限を回復します", "enabledState": "オープン状態", "end": "終了", "endDate": "終了日", "endTime": "終了時間", "enterSecretKey": "キーを入力してください。", "enterVerification": "認証コードを入力してください", "enterVerificationCode": "予期せぬ操作を防ぐため、この認証コードを入力してください。", "equalTo": "等しい", "errorLevel": "ERROR（危険）", "errorLog": "エラーログ", "eventTime": "イベント時間", "everyDay": "毎日", "everyday": "毎日", "example": "例", "executionCycle": "実行周期", "executionFailure": "実行に失敗しました", "executionInProgress": "実行中", "executionResult": "実行結果", "executionSuccess": "実行成功", "executionTime": "じっこうじかん", "executor": "実行ユーザー", "existentialDependence": "コンポーネント依存性の存在（xxxx）", "existingConfigurationSet": "既存のコンフィギュレーションセット", "existingResources": "既存のリソース", "exitWithoutSaving": "終了後、現在変更された内容は保存されません", "expand": "てんかい", "explain": "説明", "export": "エクスポート", "exportKeytab": "Keytabをエクスポートする", "failure": "失敗", "fairSchedulingBasedOnCpuAndMemory": "CPUとメモリに基づいてリソースを公平にスケジューリングします。（推奨）", "fairSchedulingBasedOnMemory": "メモリのみに基づいてリソースを公平にスケジュールします。", "fairShareDurationBeforePreemption": "リソースプールが他のリソースプールからリソースを取得するためにコンテナをプリエンプトしようとするまでの、公平なシェア状態にある秒数。", "fairSharePreemptionThreshold": "公平シェアプリエンプトしきい値", "fairSharePreemptionTimeout": "公平なシェア占有タイムアウト", "fatalLevel": "FATAL（ハイリスク）", "fault": "こしょう", "feishu": "飛書", "fetchingInformation": "情報を取得しています...", "fifo": "先に出てください。", "fileDesignation": "ファイル名", "fileName": "ファイル名", "fileSize": "ファイルサイズ", "fileSizeLimit": "ファイルサイズは100 kbを超えてはいけません", "fileStatus": "ファイルの状態", "firstSyncOfTargetTableOnTaskStartup": "タスクが初めて起動されると、対象テーブルのフル同期が行われます。", "format2To16": "2～16ビット", "format2To16ChineseAndEnglishLetters": "2-16ビット、中国語と英字で構成され、英字大文字とスペースに対応", "format6To16ChineseAndEnglishLetters": "6-16ビット、中国語と英字で構成され、英字大文字とスペースをサポート", "format6To16DigitsLettersAndEnglish": "6-16ビットは数字、英字、英字で構成され、文字は_-をサポートし、_で始まることはできません", "format6To20DigitsAndEnglishLetters": "6～20ビット、数字と英字で構成され、英字大文字と小文字をサポート", "freezeAccount": "従業員アカウントを凍結しますか？", "freezeSuccess": "フリーズ成功", "friday": "金曜日", "frozen": "凍結", "fullLog": "完全なログ", "fullTable": "ぜんりょうけい", "general": "一般", "gettingSyncTables": "同期テーブルの取得...", "globalException": "[グローバル例外]", "goToClusterList": "クラスタ・リストへの移動", "good": "よい", "greaterThan": "より大きい", "hbaseTask": "Hbaseタスク", "hdfsTask": "HDFSタスク", "hideAllDescriptions": "すべての説明を隠す", "highRisk": "ハイリスク", "hint": "ヒント", "historicalInspectionReports": "履歴巡回検査報告書", "hiveTask": "Hiveタスク", "host": "ホスト", "hostAdditionFailure": "ホストの追加に失敗しました。関連パラメータを確認してください", "hostAdditionProgress": "ホスト追加の進行状況", "hostAdditionStatus": "ホスト追加ステータス", "hostAlias": "ホストエイリアス", "hostAliasCharacterLimit": "ホスト別名は1～50文字をサポートしています", "hostAliasSupport": "ホスト別名サポート1", "hostBinding": "ホストバインド", "hostBindingFailure": "ホストバインドに失敗しました", "hostConfigurationIssue": "ホスト構成の問題", "hostConnectivityAlert": "ホスト接続性アラーム", "hostCount": "ホスト数", "hostCounts": "ホスト数", "hostCpuAlert": "ホストCPUアラーム（%）", "hostDetail": "ホストの詳細", "hostDiskInAlert": "ホストディスクIアラーム", "hostDiskOutAlert": "ホストディスクOアラーム", "hostInfo": "ホスト情報", "hostInspection": "ホストの巡回検査", "hostIp": "ホストip", "hostIpAndPort": "ホストip+ポート", "hostList": "ホストのリスト", "hostMemoryAlert": "ホストメモリ警告（％）", "hostMonitoring": "ホスト監視", "hostName": "ホスト名", "hostNetworkIn": "ホストネットワークI（MB）アラーム", "hostNetworkOut": "ホストネットワークO（MB）アラーム", "hostOccupiedCannotDelete": "現在選択されているホストはすでに占有されており、削除できません！", "hostResources": "ホストリソース", "hostRole": "ホストロール", "hostRoleList": "ホストロールのリスト", "hostSearch": "ホスト検索", "hostUnbindingResult": "アンバインド後にホストがデフォルトのリソースプールに復帰し、削除操作を行うことができます", "hostUnbound": "ホストのバインド解除", "hostnameRule": "ホスト名は小文字で始まり、小文字、数字、「-」で構成されています", "hostsSelected": "選択されたホスト", "hotColdDataGrading": "冷熱データ階層化", "hotDataClusterDirectory": "ホットデータクラスタディレクトリ", "hotDataHDFSName": "ホットデータが存在するHDFS名", "hotDataList": "アクティブデータリスト", "hotStorageCluster": "ホットデータストレージクラスタ", "hours": "時間", "httpVersionNotSupported": "HTTPバージョンはサポートされていません（505）", "ifPoolDoesNotExistCreateIt": "プールが存在しない場合に作成します。", "inPoolAttemptsToPreemptContainers": "プールが他のプールからリソースを取得するためにコンテナをプリエンプトしようとするまで、プールが最小シェアで保持する秒数。（オプション）", "inProgress": "進行中", "infoLevel": "INFO（ヒント）", "initializeFrom": "初期化する", "inputClusterName": "クラスター名を入力してください", "inputClusterToDelete": "クラスター名を入力して削除してください", "inputConfigInfo": "設定情報を入力してください。", "inputCustomComponentName": "カスタムコンポーネント名を入力してください", "inputCustomComponentToDelete": "カスタムコンポーネントの名前を入力して削除してください", "inputEmail": "メールアドレスを入力してください", "inputEmployeeName": "従業員名を入力してください", "inputField": "入力ボックス", "inputHostToDelete": "削除するホスト名を入力してください", "inputHostToUnbind": "解除バインドするホスト名を入力してください", "inputNewPassword": "新しいパスワードを入力してください。", "inputNodeCount": "ノード数を入力してください", "inputNumberOfHosts": "ホスト数を入力してください", "inputOriginalPassword": "元のパスワードを入力してください", "inputPassword": "パスワードを入力してください", "inputPhoneNumber": "携帯電話番号を入力してください", "inputPrivilegedUser": "特権ユーザーを入力してください", "inputQuery": "クエリを入力してください。", "inputRackNumber": "ラック番号を入力してください", "inputRoleNodeCount": "キャラクタノードの数を入力してください", "inputSSHPortNumber": "SSHポート番号を入力してください", "inputUserName": "ユーザー名を入力してください", "inspectionConfiguration": "パトロール構成", "inspectionCycle": "じゅんけんしゅうき", "inspectionDescription": "巡回検査項目の説明", "inspectionDuration": "巡検時間", "inspectionDurationLimit": "分、0は制限なし", "inspectionDurationLimitMinutes": "巡回検査の継続制限", "inspectionItemDescription": "巡回検査項目の説明", "inspectionItemResult": "巡回検査項目結果", "inspectionItemSuggestion": "巡回検査項目の提案", "inspectionItems": "巡検事項", "inspectionProgress": "巡回検査の進捗", "inspectionReport": "巡回検査報告書", "inspectionStartTime": "巡回検査開始時間", "inspectionStatus": "じゅんけんじょうたい", "installAgentOnNewHost": "新しいホストにエージェントをインストールすることができます。新しいホストを将来のクラスター追加用に保持したり、既存のクラスターに新しいホストを追加したりすることができます。", "installComplete": "インストールが完了しました", "installComponent": "コンポーネントをインストール", "installFailed": "インストールに失敗しました", "installImmediately": "今すぐインストール", "installationInformation": "インストール情報", "installationOrder": "インストール順序", "installing": "インストール中", "instance": "インスタンス", "instanceAddress": "インスタンスアドレス", "instanceConfiguration": "インスタンスの設定", "instanceInformation": "インスタンス情報", "instanceName": "インスタンス名", "instanceStatusHistory": "インスタンス状態履歴", "instancesWithinRoleGroup": "ロールグループ内のインスタンス", "integratedDataLakes": "データレイクが統合されました。", "interval10Min": "10分ごと", "interval12Hours": "12時間", "interval15Min": "5分ごと", "interval1Day": "1日", "interval1Hour": "1時間", "interval2Hours": "2時間", "interval30Min": "30分ごと", "interval30Minutes": "30分", "interval3Hours": "3時間ごと", "interval5Min": "5分ごと", "interval6Hours": "6時間", "interval7Days": "7日間", "invalidEmail": "メールアドレスが間違っています。再入力してください", "invalidPhoneNumber": "携帯番号のフォーマットが正しくありません", "ipAddress": "IPアドレス", "isAdmin": "管理者権限を持っていますか？", "isClusterAlertAndHealthCheckDisabled": "オープン状態において、現在のクラスタはアラーム規則及び健康巡回検査を行わないチェック項目。", "isDelete": "{value}を削除しますか？", "isDisableThisTenantAccount": "テナントアカウントを無効にするかどうか", "isEnableThisTenant": "テナントを有効にするかどうか", "isResetTheTenantLoginAccountPassword": "テナントログインアカウントパスワードをリセットするか", "isSelected": "選択", "jobConfiguration": "Job構成", "k8SCreatePermission": "K8s作成権限：", "k8SList": "K8sリスト", "k8sAdditionSuccess": "K8s追加成功", "k8sInfo": "K8s情報", "k8sName": "K8s名称", "k8sNamespace": "K8s名前空間", "keyword": "キーワード", "keywordSearch": "キーワード検索", "keywordSetting": "キーワード設定", "keywords": "キーワード", "kill": "に暴行", "kubernetesClusterAddress": "Kubernetesクラスタアドレス", "kubernetesDeployment": "kubernetesの導入", "kubernetesName": "Kubernetes名", "lastDay": "この1日", "lastHour": "1時間近く", "lastInspection": "最後の巡回検査", "lastModifiedTime": "最終更新時間", "lastRestartTime": "最近の再起動時間", "lastTaskDetectionTime": "最終タスクプローブ時間", "lastWeek": "この1週間", "latelyModifiedTime": "最終変更日時", "latestConfiguration": "最新の構成", "latestVersion": "現在の最新バージョン", "lessThan": "より小さい", "lessThan001": "0.01未満", "lessThanBlockSize": "ブロックサイズ未満の", "limitOfApplicationMaster": "ApplicationMasterが実行されるリソースプールの公平な割合を制限します。たとえば、1.0に設定すると、リーフプール内のApplicationMasterは最大100％のメモリおよびCPU公平割当を利用できます。-1.0に設定すると、ApplicationMaster割り当ての監視が無効になります。デフォルト値は0.5です。", "lineChart": "折れ線グラフ", "link": "リンク", "list": "リスト", "logCollection": "ログ収集", "logKeywordAlarm": "現在のクラスタログは、選択されたキーワードに一致してログアラームをトリガーし、同じキーワードはアラーム間隔に基づいて静止してから再送されます。", "logKeywordAlert": "キーワードアラートログ", "logLevel": "ログレベル", "logLevelAlarm": "指定された間隔内で、コンポーネントが指定されたレベルのログを生成するのを傍受するとアラートがトリガーされます（同じ間隔に複数のログが含まれる場合があります）。", "logList": "ログ・リスト", "logTimeRange": "ログ期間", "login": "ログイン", "loginAccount": "ログインアカウント", "loginPassword": "ログインパスワード", "logout": "ログオンの終了", "logs": "ログ", "lowercaseLettersNumbersOnly": "小文字、数字、ハイフン-、点のみを含むおよび左スラッシュ/、最大長制限20", "mail": "メール", "maintenanceMode": "メンテナンスモード", "maintenanceModeActivationFailed": "メンテナンスモードのオープンに失敗しました", "maintenanceModeExemption": "メンテナンスモードがオンの場合、このコンポーネントはアラーム規則および健康巡回検査のチェック項目として使用されません。", "maintenanceModeNote": "メンテナンスモードがオンになると、このホストはアラーム規則および健康巡回検査のチェック項目として動作しません。", "manageAllClustersOrSingleCluster": "権限範囲内のすべてのクラスタを一括管理することも、単一クラスタを選択することもでき、容易で効率的な管理を実現できます。", "manageNamespace": "Namespace管理", "management": "管理", "mandatorySelection": "これは必須です", "manualInspection": "手動巡回検査", "max": "最大値", "maxInput20Characters": "最大20文字まで入力可能", "maximumNumberOfCpuAndMemory": "このプールで使用できるCPUとメモリの最大数。これは、重みに基づく共有よりも優先されます。（オプション）", "maximumResources": "最大リソース数", "maximumRunningApplications": "実行中のアプリケーションの最大数", "maximumRunningApplicationsPerUser": "実行中のアプリケーションの最大数", "memory": "メモリ", "memoryUsage": "メモリ使用量", "memoryUsageAlarm": "ホストのメモリ使用率が設定したしきい値に達するとアラートがトリガーされます", "message": "メッセージ", "migration": "いどう", "migrationBackup": "バックアップの移行", "migrationSuccessful": "移行成功", "minimumLogLevel": "最小ログ・レベル", "minimumNumberOfCpuAndMemory": "このプールで使用できるCPUとメモリの最小数。これは、ウェイトベースの共有よりも優先されます。（オプション）", "minimumResources": "最小リソース数", "minimumSharedPriorityTimeout": "最小共有優先タイムアウト時間", "minutes": "分", "missingOrInvalidServiceParameters": "構成サービスパラメータのリストには、必要な項目が記入されていないか、要件を満たしていません！", "mobile": "携帯電話", "modifyEmailAddress": "メールアドレスの変更", "modifyEmailFailure": "メールアドレスの変更に失敗しました", "modifyHostnameWarning": "ホスト名を変更すると、実行中のコンポーネントが使用できなくなり、ホスト名を変更すると、コンポーネントを再起動する必要があります！", "modifyMobileNumber": "携帯番号の変更", "modifyParametersWhenDeployingCluster": "クラスタの配備時のパラメータの変更", "modifyPassword": "パスワードの変更", "modifyPermission": "権限の変更", "modifyPhoneNumberFailure": "携帯番号の修正に失敗しました", "modifyPhoneNumberSuccess": "携帯番号の修正に成功", "modifyReason": "変更理由", "monday": "月曜日", "monitorChart": "監視グラフ", "monitorNodeCount": "モニタノード数", "monitoring": "監視", "monthly": "毎月", "monthlyInspection": "月に1回", "monthlyRepeatFromDayXxToDayXxFromXxToXxFirstEffectiveAtXx": "毎月の{dayOfMonth 0}日から{dayOfMonth 1}日までの{beginTime}点00分から{endTimeStr}点00分（CST）まで繰り返し、{startTime}で初めて有効になります。", "more": "詳細", "most": "終了", "mustNotStartWithRoot": "最初から始められなかった。", "mutualTrustHelpDocument": "相互信頼支援文書", "mysqlMasterSlaveConfigurationPrompt": "ヒント：billogのオープンやserver _ idの指定など、マスターデータベース間でMySQLのマスター構成が完了していることを確認してください", "name": "名前", "namespaceList": "名前空間リスト", "namespaceName": "名前空間名", "namespaceResource": "Namespaceリソース", "namingRules": "名前は中国語、英字、数字、アンダースコア、ハイフン'-'で構成されており、ハイフンとアンダースコアでの開始と終了はできません。", "networkError": "ネットワークエラー（502）", "networkInputAlarm": "ホストのネットワーク入力速度が設定したしきい値に達すると、MB/s単位でアラームがトリガーされます", "networkOutputAlarm": "ホストのネットワーク出力速度が設定したしきい値に達すると、MB/s単位でアラームがトリガーされます", "networkTimeout": "ネットワークのタイムアウト（504）", "newConfigFileVersion": "最新のプロファイルバージョンは、コンポーネントを再起動してから有効になります", "newCustomConfig": "今回の新しいカスタム構成", "newPassword": "新しいパスワード", "nextDayAtMidnight": "翌日0", "nextStep": "次のステップ", "no": "いいえ", "noChartsAvailable": "グラフがありません", "noChineseOrEmojis": "中国語と表情文字はサポートされていません", "noContent": "しばらく内容がない", "noData": "データはありません", "noDatas": "データなし", "noLeadingOrTrailingSpaces": "スペースで開始および終了することはできません", "noLimit": "X（制限なし）", "noPermission": "権限がありません", "noRequired": "必須ではありません", "nodeCount": "ノード数", "nodeName": "ノード名", "nodeScheduling": "ノードスケジューリング", "nodeStillHasReplicaData": "ノードにはまだ{value}個のレプリカデータが移行されていません。引退ノードの削除を確認しますか？", "nodeType": "ノードタイプ", "none": "しばらく", "normal": "通常", "notDeployed": "未配置", "notExecuted": "未実行", "notStarted": "開始していません", "notes": "コメント", "notificationMethod": "方法をお知らせします", "offline": "オフライン", "oneClickDeployment": "ワンタッチ配置", "oneMonthDataLimit": "クエリーの1か月近くのデータのみをフィルタリングできます", "online": "オンライン", "onlyEnglishInputAllowed": "英文、スペース、数字、最長50文字のみ入力可能", "onlyEnglishNumbersBreveUnderscore": "英数字、ダッシュ、アンダースコアのみ", "onlyPositiveIntegersGreaterThanOrEqualToZero": "0以上の正の整数のみサポート", "onlyPositiveIntegersGreaterThanZero": "0より大きい正の整数のみサポート", "onlyPositiveIntegersSupported": "正の整数のみの入力をサポート", "open": "オープン", "operation": "操作します", "operationAttribute": "操作プロパティ", "operationContent": "操作内容", "operationHistory": "操作履歴", "operationHistoryList": "操作履歴リスト", "operationName": "操作名", "operationStatus": "オペレーションステータス", "operationSuccess": "操作が成功しました", "operationTime": "そうさじかん", "operationType": "操作タイプ", "operator": "オペレータ", "optional": "オプション", "optionalSameAsSourceIfEmpty": "オプションで、NULLの場合はホームクラスタディレクトリと一致するディレクトリ", "originalPassword": "元のパスワード", "parameterConfiguration": "パラメータ設定", "parameterName": "パラメータ名", "parameterSetting": "設定パラメータ", "password": "パスワード", "passwordConnection": "帳簿秘密接続", "passwordLength6To20": "パスワード長は6～20ビット", "passwordMinimumLength8": "パスワードの長さは最小8", "passwordMismatch": "2回の入力パスワードが一致しません", "passwordModificationFailed": "パスワードの変更に失敗しました。サーバエラー", "passwordModificationSuccess": "パスワードの変更に成功しました。再ログインしてください", "passwordsDoNotMatch": "2回入力したパスワードが一致しません。再入力してください", "pastXMinutes": "過去xx分", "pendingRun": "実行待ち", "percentageOfBlockSize": "ブロックサイズ別のパーセンテージ", "perceptionStatus": "知覚状態", "permissionControl": "権限制御", "permissionEdit": "権限の変更", "permissionModificationSuccess": "権限の変更に成功しました", "permissionTenants": "権限テナント", "phone": "電話番号", "phoneNumber": "携帯番号", "phoneNumbers": "電話番号：", "physicalSpace": "物理空間", "plSelectCluster": "クラスタを選択してください", "placementRules": "配置規則", "platformBasicEnvironment": "プラットフォーム基盤環境", "platformInspection": "プラットフォームインテリジェント巡回検査", "pleaseAddRobot": "ロボットを追加してください", "pleaseEnter": "入力してください", "pleaseEnterAddress": "192.68.12.[1-200]などの住所を入力してください。", "pleaseEnterBandwidth": "帯域幅を入力してください", "pleaseEnterConcurrency": "同時数を入力してください", "pleaseEnterConfigurationName": "設定名を入力してください", "pleaseEnterConfigurationSetName": "コンフィギュレーションセット名を入力してください", "pleaseEnterCorrectFormat": "正しいフォーマットを入力してください", "pleaseEnterCorrectHostToUnbind": "バインド解除するホスト名を正しく入力してください", "pleaseEnterCorrectVerificationCode": "正しい認証コードを入力してください", "pleaseEnterDashboardName": "ダッシュボード名を入力してください", "pleaseEnterDescription": "200文字までの説明を入力してください", "pleaseEnterDurationLimit": "継続的な制限を入力してください", "pleaseEnterHDFTargetDirectory": "HDFS同期ディレクトリを入力してください", "pleaseEnterIPAddress": "IPアドレスを入力してください", "pleaseEnterK8sName": "K8s名を入力してください", "pleaseEnterKeyword": "キーワードを入力してください", "pleaseEnterLoginAccount": "ログインアカウントを入力してください", "pleaseEnterLoginPassword": "ログインパスワードを入力してください", "pleaseEnterName": "名前を入力してください", "pleaseEnterNamePhoneNumberOrAccount": "名前/携帯番号/アカウントを入力してください", "pleaseEnterNamespaceName": "namespace名を入力してください", "pleaseEnterPoolName": "プール名を入力してください", "pleaseEnterQuerySQL": "SQLクエリを入力してください", "pleaseEnterServiceAccount": "serviceAccountを入力してください", "pleaseEnterServiceType": "サービスタイプを入力してください", "pleaseEnterTaskName": "タスク名を入力してください", "pleaseEnterTenantName": "テナント名を入力してください。", "pleaseEnterTenantResponsiblePerson": "テナント担当者を入力してください", "pleaseEnterTheCorrectIPAddress": "正しいIPアドレスを入力してください", "pleaseEnterThresholdValue": "対応する閾値を入力してください", "pleaseEnterUserName": "ユーザー名を入力してください", "pleaseFillInConfigurationSet": "コンフィギュレーションセットに入力してください", "pleaseInput": "入力してください...", "pleaseSelect": "選択してください", "pleaseSelectAutoClear": "自動消去を選択してください", "pleaseSelectBelongingCluster": "所属クラスタを選択してください", "pleaseSelectConfigurationSet": "構成セットを選択してください", "pleaseSelectCurrentRole": "現在の役割を選択してください。", "pleaseSelectDataSource": "データソースを選択してください", "pleaseSelectExistingConfigurationSet": "既存のコンフィギュレーションセットを選択してください", "pleaseSelectInitializationConfigurationSet": "初期設定セットを選択してください。", "pleaseSelectIntervalTime": "間隔を選択してください", "pleaseSelectMonthlyTriggerTime": "月次トリガー時刻を選択してください", "pleaseSelectNamespace": "namespaceを選択してください", "pleaseSelectPlacementRule": "配置規則を選択してください", "pleaseSelectPrimaryBackupScheme": "マスター・プランに所属してください", "pleaseSelectScheduledKillTime": "タスクタイミングのチェック時間を選択してください", "pleaseSelectStatus": "状態を選択してください", "pleaseSelectTime": "時間を選択してください", "pleaseSelectUpgradeVersion": "アップグレードバージョンを選択してください", "pleaseSelectWeeklyTriggerTime": "週次トリガ時間を選択してください", "pleaseUploadKubernetesConfigFileAndSizeMustNotExceed100kb": "kubernetes configファイルをアップロードしてください。サイズは100 kbを超えてはいけません", "podName": "pod名", "pool": "プール", "poolName": "プール名", "positiveIntegerOnly": "【xxx】ノードは正の整数しか入力できません", "preemption": "プリエンプト", "previousStep": "前へ", "primaryBackupClusterScheme": "所属主备クラスターソリューション", "primaryBackupScheme": "所属プライマリ・プロビジョン", "primaryClusterName": "主クラスタ名", "primaryClusterTitle": "メインクラスタ名", "primarySecondaryManagement": "マスター管理", "primarySecondaryPlanName": "プライマリ・プロビジョン名", "primarySecondaryPlanTitle": "プライマリ・プロビジョン名", "primarySecondarySwitch": "マスター切替", "priority": "優先度", "priorityExists": "優先度はすでに存在します", "privilegedUser": "特権ユーザー", "processCpuUsage": "プロセスのcpu使用量", "processMemoryUsage": "プロセスのメモリー使用量", "productDoc": "製品文書", "productTechnicalWhitePaper": "製品技術ホワイトペーパー", "productUserManual": "製品ユーザーマニュアル", "progress": "進行状況", "publicKeyConnection": "公開鍵接続", "publicKeyConnectionInstructions": "公開鍵リンクを使用してホストを追加するには、現在のシステム鍵を追加しようとしているホスト認証ファイルに貼り付ける必要があります。ホスト認証ファイルは通常「~/.ssh/authorized _ keys」です。ホストを追加する前に上記の操作を完了してください。現在のシステムキーのダウンロード", "qiyeweixin": "企業のウィーチャット", "qiyeweixinRobot": "企業のウィーチャットロボット", "query": "クエリー", "queueNameExists": "キュー名はすでに存在します", "rack": "ラック", "rackDeletionWarning": "ラックを削除すると、元のラックとホストの展開関係が解除されます。", "rackLabel": "ラック", "rackManagement": "ラック管理", "rackNumber": "ラック番号", "reAdd": "再追加", "reAddAfterDeletion": "削除後にプライマリ・プロビジョニング・シナリオを再追加できます。", "reExecute": "再実行", "reasonForModification": "変更理由", "recentlyUsed": "最近使用した", "recipient": "受信者", "recommendChrome": "より良い使用体験のためには、Google Chromeブラウザを使用することをお勧めします", "recommended": "推奨事項", "reenterClusterName": "意図しない操作を防ぐために、クラスター名をもう一度入力してください。", "reenterCustomComponentName": "意図しない操作を防ぐために、もう一度カスタムコンポーネントの名前を入力してください。", "reenterHostName": "予期しない操作を防ぐために、ホスト名を再入力してください", "reenterPassword": "パスワードを再入力してください", "reenterRackNumber": "予期しない操作を防ぐために、ラック番号を入力してください", "refreshingDynamicResourcePools": "動的リソースプールのリフレッシュ", "relatedCluster": "関連クラスタ", "relatedToSharingWithOtherPools": "他のプールに関連するリソース共有", "released": "解除", "remark": "コメント", "rename": "名前を変更", "reportDetails": "レポートの詳細", "requestNotFound": "要求エラー（404）", "requestTimeout": "要求タイムアウト（408）", "requiredField": "これは必須項目です", "requiresManualClear": "手動で削除する必要がありますか？", "reset": "リセット", "resetEmployeePassword": "従業員のパスワードをリセットしますか？", "resetPassword": "パスワードをリセット", "resetPasswordSuccess": "パスワードのリセットに成功しました", "resourceAllocation": "リソースの割り当て", "resourceAllocationLimit": "リソース割り当て制限：", "resources": "リソース", "responsiblePerson": "責任者", "restart": "再起動", "restartBeforeStopDependencies": "再起動する前に依存するコンポーネントを停止することをお勧めします", "restartComponentForChangesToTakeEffect": "パブリケーションを保存したら、構成を有効にするためにコンポーネントを再起動する必要があります", "restartComponentService": "現在のコンポーネント（xxxx）サービスを再起動しますか？", "restartFailed": "再起動に失敗しました", "restartList": "リストを再起動", "restartProgressDetails": "再起動の進捗詳細", "restartRoleService": "現在の役割（xxxx）サービスを再起動しますか？", "restartSelectedRoleService": "選択したロールサービスを再起動しますか？", "restartSyncTask": "選択したコンポーネントを再起動しますか？", "restartableAfterTermination": "終了後に同期タスクを再起動できます", "restarting": "再起動中", "restoreDefaultValues": "デフォルト値に戻す", "return": "リターンマッチ", "returnToClusterList": "クラスタ・リストに戻る", "returnToComponentList": "コンポーネントのリストに戻る", "returnToHostList": "ホストのリストに戻る", "returnToList": "リストに戻る", "rexNamespaceName": "名前は中国語、英字、数字、アンダースコア、ハイフン'-'で構成されており、ハイフンとアンダースコアでの開始と終了はできません。", "role": "キャラクター", "roleAllocationInstructions": "ここでは、新しいサービスのロール割り当てをカスタマイズできますが、正しく割り当てられていない（たとえば、ホストに割り当てられているロールが多すぎる）場合、パフォーマンスが影響を受けることに注意してください。", "roleConfiguration": "ロール構成", "roleGroup": "ロールグループ", "roleGroupName": "ロールグループ名", "roleInstance": "ロールインスタンス", "roleList": "ロールのリスト", "roleLog": "ロールログ", "roleLogFile": "ロールログファイル", "roleName": "ロール名", "roleScope": "ロール範囲", "roleType": "ロールタイプ", "rollingRestart": "スクロール再起動", "rollingRestartComponent": "{value}コンポーネントをスクロールして再起動するかどうか", "rollingRestartDetails": "再起動の詳細をスクロール", "rollingRestartProgressDetails": "再起動の進捗詳細をスクロール", "rollingRestarting": "スクロール再起動中", "running": "実行中", "runningComponentCannotBeDeleted": "現在のコンポーネントは実行中のため、削除は保留中です", "runtime": "実行時間", "runtimes": "実行時間", "saturday": "土曜日", "save": "保存します", "saveAndPublish": "保存してパブリッシュする", "saveAndPublishCurrentConfig": "現在のプロファイルを保存してパブリッシュしますか？", "saveSuccess": "保存に成功しました", "scaleFailed": "拡張容量の失敗", "scaleUpOrDown": "かくさんようりょう", "scaleUpOrDownProgressDetails": "拡張容量の進捗詳細", "scaling": "かくだいようりょう", "scheduleMode": "計画モード", "scheduledKill": "定時に検殺する.", "scheduledKillTime": "定時スキャンの時間", "scheduling": "スケジュール中", "schedulingFailed": "スケジュール失敗", "schedulingPolicy": "計画ポリシー", "schedulingProgressDetails": "スケジュールの詳細", "secondaryClusterName": "予備クラスタ名", "secondaryClusterTitle": "予備クラスタ名", "select": "せんたく", "selectAll": "すべて選択", "selectAllSelectedRoles": "選択したすべてのロールの表示（value）", "selectAtLeastOneHost": "少なくとも1台のホストを選択", "selectAuthenticationType": "認証タイプを選択してください", "selectCluster": "クラスタの選択", "selectComponent": "コンポーネントを選択してください。", "selectComponentName": "コンポーネント名を選択してください", "selectCustomComponentName": "スケーリングするカスタムコンポーネント名を選択してください", "selectDataRange": "データ範囲の選択", "selectDataSource": "データソースの選択", "selectDeploymentMethod": "配置方法の選択", "selectFile": "ファイルを選択してください", "selectFileName": "ファイル名を選択してください", "selectHost": "ホストの選択", "selectInspectionCycle": "巡回サイクルを選択してください", "selectInstance": "インスタンスの選択", "selectK8s": "K8sを選択してください", "selectK8sCluster": "K8sクラスタを選択してください", "selectManualInspectionType": "手動巡回実行タイプを選択してください", "selectNamespaceForClusterInKubernetes": "現在のクラスタがこのKubernetesで使用している名前空間を選択します（このオプションは空にすることができ、空にするとKubernetesに新しい名前空間が自動的に作成されます）。選択した名前空間が存在し、使用されていないことを確認してください", "selectNode": "ノードを選択してください", "selectNodeType": "ノードのタイプを選択します", "selectRoleName": "キャラクター名を選択してください。", "selectServiceType": "追加するサービスタイプを選択してください。一度に1つのコンポーネントしかインストールできません", "selectServiceTypeToAdd": "追加するサービスタイプを選択してください", "selectStartTime": "開始時間を選択してください", "selectTime": "時間の選択", "selectUpgradeVersion": "アップグレードバージョンの選択", "selectVersion": "バージョンを選択してください", "selectXToYHosts": "最小x台、最大x台を選択", "selected": "選択済み", "selectedComponent": "選択したコンポーネント", "selectedComponents": "選択されたコンポーネント", "selectedConfigurationSet": "選択されたコンフィギュレーションセット", "selectedHost": "選択されたホスト", "selectedRole": "選択したロール", "selectedTablesOnly": "表をチェックするだけ", "selectedXHosts": "xx台が選択されました", "sendTestMessage": "テストメッセージの送信", "serialNumber": "順番", "serious": "深刻", "serverError": "サーバエラー（500）", "serverException": "サーバーの異常、管理者に連絡してください", "service": "サービス", "serviceComponents": "サービスコンポーネント", "serviceNameAndPort": "ServiceName+ポート", "serviceNotImplemented": "サービス未実装（501）", "serviceType": "サービスタイプ", "serviceUnavailable": "サービスが利用できません（503）", "serviceVersion": "サービスバージョン", "sessionExpired": "ログインの有効期限", "setScale": "設定規模", "setToFalseProhibitsPreemptionFromOtherPools": "falseに設定すると、他のプールのプールからの切断が禁止されます。デフォルト値はtrueです。高優先度プールまたはSLAプールでは、値をfalseに設定する必要があります。", "showAllDescriptions": "すべての説明を表示", "showOnly": "{value} 個の構成エラー問題のみを表示しています。クリックして折りたたむ", "smallFileCountMonitoringDelay": "小ファイル数モニタにT+1遅延がある", "smallFileDefinition": "ミニファイル定義", "smallFileDefinitionPolicy": "ミニファイル定義ポリシー", "smallFileManager": "小さなファイル管理", "sourceClusterDataDirectory": "プライマリクラスタソースデータディレクトリ", "sourceClusterHBaseCluster": "ソースクラスタhbaseクラスタ", "sourceClusterSourceDataDirectory": "メインクラスタのソースデータディレクトリ", "sourceDatabaseName": "ソースデータベース名", "sourceHiveCluster": "ソースhiveクラスタ", "sourceList": "ソースリスト", "specifiedHost": "ホストの指定", "specifyPoolName": "プール名の指定", "sshPortNumber": "SSHポート番号", "stackedAreaChart": "スタック面積図", "start": "起動", "startComponentService": "現在のコンポーネント（xxxx）サービスを開始しますか？", "startCurrentPrimaryBackupScheme": "現在のプライマリ・プロビジョニング・シナリオを起動するかどうか", "startDate": "開始日", "startDateCannotBeGreaterThanEndDate": "開始日を終了日より大きくすることはできません", "startFailed": "起動に失敗しました", "startInstallationConfirmation": "インストールを開始すると、現在の構成は表示のみサポートされますので、間違いがないことを確認して「OK」をクリックして開始してください。", "startSyncTask": "現在の同期タスクを開始するかどうか", "startTime": "開始時間", "startTimeMustBeLessThanEndTime": "開始時間は終了時間より小さくなければなりません", "starting": "起動中", "startupProgressDetails": "起動進捗詳細", "status": "ステータス", "statusDelayMinutes": "不健康（この状態には分刻みの遅延がある）", "stepContent": "ステップの内容", "steps": "ステップ", "stop": "ストップ", "stopComponentService": "現在のコンポーネント（xxxx）サービスを停止しますか？", "stopDependenciesBeforeRestart": "再起動する前に、上記の依存コンポーネントを停止することをお勧めします", "stopFailed": "停止失敗", "stopProgressDetails": "停止進捗詳細", "stopSyncTask": "現在の同期タスクを終了するかどうか", "stopped": "停止中", "stopping": "停止中", "storageAndComputationIntegration": "ストレージコンピュート一体化", "storageAndComputationSeparation": "ストレージコンピュート分離", "storageSystem": "ストレージシステム", "subdirectoryCount": "サブスクリプト数", "submit": "送信", "success": "成功", "suggestStoppingDependentComponentsBeforeUpdate": "更新する前に依存するコンポーネントを停止することをお勧めします", "suggestion": "推奨事項", "sunday": "日曜日", "supportS3": "S 3対応", "switchPrimarySecondaryRelation": "現在のシナリオのプライマリ・リレーションシップを切り替えますか？", "switchToAbsoluteValue": "絶対値に切り替え", "switchToPercentage": "パーセントに切り替え", "syncStrategy": "同期ポリシー", "syncTableName": "同期テーブル名", "syncTableSelection": "同期テーブルの選択", "syncTableType": "同期表の種類", "syncTaskCount": "同期タスク数", "syncTaskManagement": "同期タスク管理", "table": "テーブル", "targetClusterHBaseCluster": "ターゲットクラスタhbaseクラスタ", "targetClusterTargetDirectory": "クラスタターゲットディレクトリの準備", "targetDatabase": "ターゲット・データベース", "targetDatabaseName": "ターゲット・データベース名", "targetDatabaseNameShouldBeSameAsSource": "目標データベース名は、ソースデータベース名と同じである必要があります。", "targetHiveCluster": "目標のHiveクラスタ", "targetList": "目標リスト", "task": "タスク", "taskManagement": "タスク管理", "taskMonitoring": "タスク監視", "taskName": "タスク名", "taskNameKeyword": "タスク名", "taskStatus": "タスクステータス", "taskTime": "タスク時間", "tenant": "所属テナント", "tenantAvailableResources": "テナント利用可能なリソース", "tenantBelonging": "テナント", "tenantHasK8SCreatePermission": "このテナントはK8sの作成権限を持っている", "tenantList": "テナントリスト", "tenantName": "テナント名：", "tenantPermissions": "テナント権限", "tenantResponsiblePerson": "テナント担当者：", "tenantStatus": "テナントステータス：", "tenantWillNotAppearInSystemAfterDeletion": "削除するとテナントはシステムに表示されません", "tenantsWithPermissions": "権限を持つテナントは、このK8sのNamespace管理をカスタマイズすることができる。", "terminate": "終了", "testConnectivity": "接続性のテスト", "testConnectivityFirst": "まず接続性をテストしてください", "testEmail": "メールテスト", "testEmailRequestSentSuccessfully": "テストメールリクエスト送信成功", "testing": "テスト中", "thursday": "木曜日", "tieredStoragePolicy": "ストレージ階層化ポリシー", "time": "時間", "timePeriod": "期間", "timeSelection": "時間選択", "timeoutInSeconds": "タイムアウト時間（秒）", "timeoutOrServerException": "リクエストのタイムアウトまたはサーバー異常、ネットワークを確認するか管理者に連絡してください", "tip": "ヒント", "to": "終了", "traceLevel": "TRACE（ヒント）", "tryingToReconnect": "再接続を試みる...", "tuesday": "火曜日", "type": "を選択してオプションを設定します。", "unbind": "ひもを解く", "unbindAndDeleteHostBoundToCluster": "選択したホストがクラスタをバインドしています。クラスタのバインド解除操作を行った後に削除します。", "unbindHostDefaultCluster": "バインド解除後、ホストはデフォルトのクラスタに戻され、後で追加して使用できます", "unbindK8s": "分解K8s", "unblacklist": "ブラックアウト解除", "unblacklistSuccess": "ブラックアウトのキャンセルに成功しました", "unbound": "拘束解除", "undo": "元に戻す", "unfreeze": "解凍", "unfreezeAccount": "従業員アカウントを解凍しますか？", "unfreezeSuccess": "解凍に成功しました", "unhealthy": "不健康", "uninstalled": "アンインストール済み", "unknownReason": "不明な理由", "unlimitedResources": "リソースの制限なし", "unrecognizedIdentifier": "認識できないID", "unsavedChangesWarning": "離れた後にシステムはあなたの操作を保存しないかもしれません", "unselected": "選択されていません", "update": "更新します", "updateComponent": "コンポーネントの更新", "updateFailed": "更新に失敗しました", "updateProgressDetails": "更新の進捗詳細", "updating": "更新中", "uploadFile": "ファイルのアップロード", "usage": "使用量", "usePrimaryGroupPool": "そのユーザーの主要なグループに一致するリソースプールを使用します。", "usePrimaryGroupPoolThenUserNameSubPool": "ユーザーのプライマリ・グループに一致するリソース・プールを使用し、次にユーザー名に一致するサブプールを使用します。", "useRuntimeSpecifiedPool": "実行時に指定したリソースプールの使用", "useSecondaryGroupPool": "そのユーザの二次グループの1つと一致するリソースプールを使用します。", "useSecondaryGroupPoolThenUserNameSubPool": "ユーザーの二次グループの1つに一致するリソースプールを使用し、次にユーザー名に一致するサブプールを使用します", "useSpecifiedPool": "指定したリソースプールを使用します。", "useSpecifiedPoolAsParent": "指定したプールに一致するリソースプールを親プールとして使用し、そのユーザー名に一致するサブプールを使用します。", "useSpecifiedPoolRootXxx": "指定されたリソースプールを使用して、root.{value}", "useUserNamePool": "ユーザー名に一致するリソースプールを使用します。（推奨しない）", "user": "ユーザー", "userCount": "ユーザー数", "userKeywords": "ユーザーキーワード", "userLimit": "ユーザー制限", "userLimits": "ユーザー制限", "userList": "ユーザーリスト", "userManager": "ユーザー管理", "userName": "ユーザー名", "username": "ユーザー名", "validEmailRequired": "正しいメールアドレスを入力してください。", "validPhoneNumberRequired": "正しい携帯番号を入力してください", "validSSHPortRequired": "正しいSSHポート番号を入力してください", "validateHostRoleAssignment": "各ロールに割り当てられたホストが基準を満たしているかどうかを確認してください", "value": "アタイ", "valueBetweenZeroAndOne": "0～1の値。値がxに設定され、リソースプールの公平なシェアがFの場合、割り当てが（x*F）未満の場合は、他のリソースプールからリソースのプリエンプトが開始されます。", "version": "バージョン", "versionNumber": "バージョン番号", "versionRollback": "バージョンロールバック", "versionSelection": "バージョン選択", "view": "表示", "viewAlarms": "アラートの表示", "viewAll": "すべてを表示", "viewAllSelectedHosts": "選択したホストをすべて表示", "viewComponent": "コンポーネントの表示", "viewDataSource": "データソースの表示", "viewDetails": "詳細の表示", "viewDiagnosisReport": "診断レポートの表示", "viewEdit": "変更を表示", "viewEvent": "イベントを表示", "viewInstance": "インスタンスの表示", "viewJob": "Jobの表示", "viewK8s": "K8sを表示", "viewLog": "ログを表示する", "viewModify": "変更の表示", "viewProgress": "進捗の表示", "viewProgressAfterDeletion": "削除後、詳細な操作で進捗状況を表示できます", "viewProgressAfterStart": "起動後、詳細な操作で進捗状況を表示できます", "viewProgressAfterStop": "停止後、詳細な操作で進捗状況を表示できます", "viewReport": "レポートの表示", "viewRestartProgress": "再起動後は、詳細な操作で進捗状況を確認できます", "viewVersion": "バージョンを表示", "virtualCores": "仮想カーネル", "warnLevel": "WARN（一般）", "warning": "に警告", "warningIssues": "警告クラスの問題があります。表示をクリックしてください", "webSocketConnectionClosed": "WebSocket接続が閉じられました", "webSocketConnectionEstablished": "WebSocket接続が確立されました", "webSocketError": "WebSocketエラー", "webTerminal": "ウェブ端末", "wednesday": "水曜日", "weekly": "毎週", "weeklyInspection": "週に1回", "weeklyRepeatOnXxFromXxToXxFirstEffectiveAtXx": "{dayOfWeek}の{beginTime}ポイント00分から{endTimeStr}ポイント00分（CST）まで毎週繰り返し、{startTime}で初めて有効になります。", "weight": "権限", "welcomeBack": "お帰りなさい", "yarnResourcePoolConfiguration": "Yarnリソースプールの構成", "yes": "はい", "isDeleteCurrentRole": "現在のロール（$name）を削除しますか？", "isDeleteCurrentRoleGroup": "ロールグループ（$name）を削除しますか？", "hideAllAlarmDetails": "すべてのアラーム詳細を隠す", "viewAllAlarmDetails": "すべてのアラート詳細の表示", "addAlarmItems": "アラームアイテムの追加", "alarmComponent": "アラームコンポーネント", "alarmOperation": "アラーム操作", "warningKeywords": "アラームキーワード"}