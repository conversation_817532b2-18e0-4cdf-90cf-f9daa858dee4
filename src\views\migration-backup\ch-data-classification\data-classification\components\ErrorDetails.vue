<template>
  <el-dialog :model-value="modelValue" width="900px" :before-close="handleClose">
    <template #header>
      <i class="iconfont icon-icon_shibai c-hex-f3544a rd-m" />
      <span class="c-text font-700 ml-10px">{{ $t('replenish.errorLog') }}</span>
    </template>
    <div class="mt--10px mb--10px">
      <el-scrollbar height="484px">
        <div class="f-14 lh-22px p-16px c-hex-666D80 bg-hex-F4F5F9 min-h-484px">
          {{ content }}
        </div>
      </el-scrollbar>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:modelValue'])
function handleClose() {
  emit('update:modelValue', false)
}
</script>
<style lang="scss" scoped></style>
