<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.submitParameters')" width="800px" :before-close="resetForm">
    <div class="mb-4" v-if="!isBatch">
      <BaseTable :columns="columns" :data="list">
        <template #parameterType>cluster.yaml</template>
        <template #maxMemory="{ row }"> {{ `${row.memoryCapacity}/${row.maxMemory}` }} </template>
      </BaseTable>
    </div>
    <div class="mb-4" v-else>
      <BaseElCollapse v-model="activeNames">
        <el-collapse-item v-for="(el, i) in updateList" :name="i" :key="i">
          <template #title>
            <TheTitle class="ml-20px">
              <template #titleText> {{ el.confKey }} </template>
            </TheTitle>
          </template>
          <div class="h-80 w-full">
            <CodeDiffEditor
              v-if="activeNames.includes(i)"
              :originCode="el.originCode"
              :modifiedCode="el.modifiedCode"
              :isShow="true"
              :readOnly="true"
            />
          </div>
        </el-collapse-item>
      </BaseElCollapse>
    </div>

    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="140px">
      <el-form-item :label="`${$t('replenish.reasonExplanation')}:`" prop="description">
        <el-input style="width: 280px" :placeholder="$t('replenish.pleaseEnter')" type="'textarea'" v-model="ruleForm.description" />
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { createNodeClass } from '@/api/k8sApi'
import { ElMessage } from 'element-plus'

const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  clusterServiceId: {
    type: String,
    default: ''
  },
  clusterServiceInstanceId: {
    type: String,
    default: ''
  },
  list: {
    type: Array as PropType<Record<string, string>[]>,
    default: () => []
  },
  configRequestFrom: {
    type: Object,
    default: () => ({})
  },
  isBatch: {
    type: Boolean,
    default: false
  }
})

const activeNames = ref([])

const updateList = computed(() =>
  props.list?.map((el) => {
    return {
      confKey: el.confKey,
      originCode: {
        code: el?.oldValue,
        language: 'text'
      },
      modifiedCode: {
        code: el?.confValue,
        language: 'text'
      }
    }
  })
)

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    }
  }
)
const ruleForm = reactive<any>({
  description: ''
})
const rules = {
  description: [
    {
      trigger: 'blur',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ]
}
const tenantId = computed(() => store.state.user.tenantId)
const namespaceList = ref<string[]>([])
const columns = [
  // { label: t('replenish.parameterType'), prop: 'parameterType', slot: true },
  { label: t('replenish.parameterName'), prop: 'confKey' },
  { label: t('replenish.beforeModification'), prop: 'oldValue' },
  { label: t('replenish.modify'), prop: 'confValue' }
  // { label: t('replenish.type'), prop: 'updateTime' }
]
function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      isLoading.value = true

      store
        .dispatch('configure/updateAllConfigsByClusterId', {
          ...props.configRequestFrom,
          modifyReason: ruleForm.description,
          clusterServiceId: props.clusterServiceId,
          clusterServiceInstanceId: props.clusterServiceInstanceId,
          ...(props.isBatch
            ? {
                confFile: props.list[0].confValue
              }
            : {})
        })
        .then(() => {
          ElMessage({
            type: 'success',
            message: t('message.saveSuccess')
          })
          emit('submit')
          resetForm()
        })
        .catch(() => {
          ElMessage({
            type: 'error',
            message: t('message.saveFailed')
          })
        })
        .finally(() => {
          isLoading.value = false
        })
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.name = ''
  ruleForm.roleName = ''
  ruleForm.serviceaccount = ''
  activeNames.value = []
  emit('update:modelValue', false)
}
</script>
