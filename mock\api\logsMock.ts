export default [
  {
    url: '/api/ops/service/instance/list',
    method: 'POST',
    response: () => ({
      "code": 0,
      "msg": "成功",
      "data": {
        "records": [{
          "id": 5027,
          "createUser": null,
          "createTime": null,
          "updateUser": null,
          "updateTime": null,
          "instanceName": "pod-1236-zknode-5027",
          "clusterServiceId": 1236,
          "roleSimpleName": "zknode",
          "roleName": "ZKNode",
          "containerName": "pod-1236-zknode-5027",
          "componentModuleRoleId": 12,
          "hostId": null,
          "state": 2,
          "deployState": null,
          "deploySeq": null,
          "groupSeq": null,
          "deployStartTime": null,
          "deployEndTime": null,
          "hostName": null,
          "serialNumber": null,
          "ip": null,
          "dockerDeployConfig": null,
          "deployLog": null,
          "deployWay": 2,
          "msgSource": "kubernetes",
          "webUrl": "http://***********:31177/commands",
          "kubernetesId": null,
          "clientIpAddress": "***********:31485",
          "clientServiceAddress": "service-1236-zknode-5027:2181"
        }]
      }
    })
  },
  {
    url: '/api/ops/service/instance/role/list',
    method: 'POST',
    response: () => ({ "code": 0, "msg": "成功", "data": ["ZKNode"] })
  },
  {
    url: '/api/ops/service/instance/listroles/1',
    method: 'POST',
    response: () => ({
      "code": 0,
      "msg": "成功",
      "data": [{
        "roleName": "dn",
        "containerName": "dn-4726-container-hdfs-1176,dn-4464-container-hdfs-1128,dn-4724-container-hdfs-1176,dn-4465-container-hdfs-1128,dn-4725-container-hdfs-1176,dn-4466-container-hdfs-1128",
        "hostNameStr": "主机171,主机171,主机172,主机172,主机173",
        "kubeNsNameStr": null,
        "containerNames": [
          "dn-4726-container-hdfs-1176",
          "dn-4464-container-hdfs-1128",
          "dn-4724-container-hdfs-1176",
          "dn-4465-container-hdfs-1128",
          "dn-4725-container-hdfs-1176",
          "dn-4466-container-hdfs-1128"
        ],
        "hostNames": [
          "主机171",
          "主机172",
          "主机173"
        ],
        "kubeNsNames": null
      }]
    })
  },
  {
    url: '/api/ops/service/instance/listroles/2',
    method: 'POST',
    response: () => ({
      "code": 0,
      "msg": "成功",
      "data": [{
        "roleName": "dn",
        "containerName": "dn-4726-container-hdfs-1176,dn-4464-container-hdfs-1128,dn-4724-container-hdfs-1176,dn-4465-container-hdfs-1128,dn-4725-container-hdfs-1176,dn-4466-container-hdfs-1128",
        "hostNameStr": "主机171,主机171,主机172,主机172,主机173",
        "kubeNsNameStr": null,
        "containerNames": [
          "dn-4726-container-hdfs-1176",
          "dn-4464-container-hdfs-1128",
          "dn-4724-container-hdfs-1176",
          "dn-4465-container-hdfs-1128",
          "dn-4725-container-hdfs-1176",
          "dn-4466-container-hdfs-1128"
        ],
        "hostNames": [
          "主机171",
          "主机172",
          "主机173"
        ],
        "kubeNsNames": null
      }]
    })
  },
  {
    url: '/api/ops/es/get/all',
    method: 'POST',
    response: () => ({
      code: 0,
      msg: 'ok',
      data: [
        {
          "agent": {},
          "offset": 1415567,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654651172192,
          "message": "2022-06-08 09:19:32,055 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: Receiving BP-1494626058-***********-1646176085818:blk_1073743341_2540 src: /************:47416 dest: /***********:9866",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:19:32.192Z",
          "docker_container": "dn-5975-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 1415764,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654651172192,
          "message": "2022-06-08 09:19:32,068 INFO org.apache.hadoop.hdfs.server.datanode.DataNode.clienttrace: src: /************:57182, dest: /***********:9866, bytes: 91, op: HDFS_WRITE, cliID: DFSClient_NONMAPREDUCE_1144500669_1, offset: 0, srvID: 7ef74223-85aa-40a9-9f01-6712834a7876, blockid: BP-1494626058-***********-1646176085818:blk_1073743340_2539, duration(ns): 900109947406",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:19:32.192Z",
          "docker_container": "dn-5975-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 1416129,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654651172192,
          "message": "2022-06-08 09:19:32,068 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: PacketResponder: BP-1494626058-***********-1646176085818:blk_1073743340_2539, type=LAST_IN_PIPELINE terminating",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:19:32.192Z",
          "docker_container": "dn-5975-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 1444094,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654651160552,
          "message": "2022-06-08 09:19:18,397 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: Receiving BP-1494626058-***********-1646176085818:blk_1073743341_2540 src: /************:36774 dest: /***********:9866",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:19:20.552Z",
          "docker_container": "dn-5977-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1444291,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654651160552,
          "message": "2022-06-08 09:19:18,410 INFO org.apache.hadoop.hdfs.server.datanode.DataNode.clienttrace: src: /************:46536, dest: /***********:9866, bytes: 91, op: HDFS_WRITE, cliID: DFSClient_NONMAPREDUCE_1144500669_1, offset: 0, srvID: 40945ce7-fc66-4cb2-a0d9-2fe29666aa96, blockid: BP-1494626058-***********-1646176085818:blk_1073743340_2539, duration(ns): 900107925918",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:19:20.552Z",
          "docker_container": "dn-5977-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1444656,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654651160552,
          "message": "2022-06-08 09:19:18,410 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: PacketResponder: BP-1494626058-***********-1646176085818:blk_1073743340_2539, type=LAST_IN_PIPELINE terminating",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:19:20.552Z",
          "ecs": {},
          "docker_container": "dn-5977-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1443597,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650995546,
          "message": "2022-06-08 09:16:25,941 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: Successfully sent block report 0x316c9e8e285b343e,  containing 1 storage report(s), of which we sent 1. The reports had 373 total blocks and used 1 RPC(s). This took 1 msec to generate and 2 msecs for RPC and NN processing. Got back one command: FinalizeCommand/5.",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:16:35.546Z",
          "docker_container": "dn-5977-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1443940,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650995546,
          "message": "2022-06-08 09:16:25,941 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: Got finalize command for block pool BP-1494626058-***********-1646176085818",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:16:35.546Z",
          "ecs": {},
          "docker_container": "dn-5977-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1414459,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650517172,
          "message": "2022-06-08 09:08:32,643 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Scheduling blk_1073743327_2526 replica FinalizedReplica, blk_1073743327_2526, FINALIZED",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:37.172Z",
          "docker_container": "dn-5975-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 1414945,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650517172,
          "message": "2022-06-08 09:08:32,643 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Deleted BP-1494626058-***********-1646176085818 blk_1073743328_2527 URI file:/home/<USER>/data/dfs/data/current/BP-1494626058-***********-1646176085818/current/finalized/subdir0/subdir5/blk_1073743328",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:37.172Z",
          "ecs": {},
          "docker_container": "dn-5975-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 1415256,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650517172,
          "message": "2022-06-08 09:08:32,644 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Deleted BP-1494626058-***********-1646176085818 blk_1073743327_2526 URI file:/home/<USER>/data/dfs/data/current/BP-1494626058-***********-1646176085818/current/finalized/subdir0/subdir5/blk_1073743327",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:37.172Z",
          "docker_container": "dn-5975-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 1413973,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5975/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650517171,
          "message": "2022-06-08 09:08:32,643 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Scheduling blk_1073743328_2527 replica FinalizedReplica, blk_1073743328_2527, FINALIZED",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:37.171Z",
          "ecs": {},
          "docker_container": "dn-5975-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机71"
        },
        {
          "agent": {},
          "offset": 48618429,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/4989/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650509378,
          "message": "2022-06-08 09:08:27,678 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: Successfully sent block report 0x103b2d575a1453d,  containing 1 storage report(s), of which we sent 1. The reports had 6 total blocks and used 1 RPC(s). This took 1 msec to generate and 6 msecs for RPC and NN processing. Got back one command: FinalizeCommand/5.",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1238,
          "@timestamp": "2022-06-08T01:08:29.378Z",
          "ecs": {},
          "docker_container": "dn-4989-container-hdfs-1238",
          "topic": "dn",
          "host_name": "************"
        },
        {
          "agent": {},
          "offset": 48618769,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/4989/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650509378,
          "message": "2022-06-08 09:08:27,679 INFO org.apache.hadoop.hdfs.server.datanode.DataNode: Got finalize command for block pool BP-303645150-************-1653876771258",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1238,
          "@timestamp": "2022-06-08T01:08:29.378Z",
          "ecs": {},
          "docker_container": "dn-4989-container-hdfs-1238",
          "topic": "dn",
          "host_name": "************"
        },
        {
          "agent": {},
          "offset": 1441692,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650500530,
          "message": "2022-06-08 09:08:19,918 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Scheduling blk_1073743331_2530 replica FinalizedReplica, blk_1073743331_2530, FINALIZED",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:20.530Z",
          "ecs": {},
          "docker_container": "dn-5977-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1442178,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650500530,
          "message": "2022-06-08 09:08:19,918 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Scheduling blk_1073743327_2526 replica FinalizedReplica, blk_1073743327_2526, FINALIZED",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:20.530Z",
          "ecs": {},
          "docker_container": "dn-5977-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1442664,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650500530,
          "message": "2022-06-08 09:08:19,918 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Deleted BP-1494626058-***********-1646176085818 blk_1073743329_2528 URI file:/home/<USER>/data/dfs/data/current/BP-1494626058-***********-1646176085818/current/finalized/subdir0/subdir5/blk_1073743329",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:20.530Z",
          "docker_container": "dn-5977-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1442975,
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "msg_source": "docker",
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650500530,
          "message": "2022-06-08 09:08:19,918 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Deleted BP-1494626058-***********-1646176085818 blk_1073743331_2530 URI file:/home/<USER>/data/dfs/data/current/BP-1494626058-***********-1646176085818/current/finalized/subdir0/subdir5/blk_1073743331",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:20.530Z",
          "ecs": {},
          "docker_container": "dn-5977-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1443286,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650500530,
          "message": "2022-06-08 09:08:19,919 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Deleted BP-1494626058-***********-1646176085818 blk_1073743327_2526 URI file:/home/<USER>/data/dfs/data/current/BP-1494626058-***********-1646176085818/current/finalized/subdir0/subdir5/blk_1073743327",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:20.530Z",
          "ecs": {},
          "docker_container": "dn-5977-container-hdfs-1330",
          "topic": "dn",
          "host_name": "主机-73"
        },
        {
          "agent": {},
          "offset": 1441206,
          "msg_source": "docker",
          "log": {
            "file": {
              "path": "/host/Data/cyber-ops/hdfs/dn/5977/logs/hadoop.log"
            }
          },
          "level": "INFO",
          "component_name": "HDFS",
          "sort": 1654650500529,
          "message": "2022-06-08 09:08:19,917 INFO org.apache.hadoop.hdfs.server.datanode.fsdataset.impl.FsDatasetAsyncDiskService: Scheduling blk_1073743329_2528 replica FinalizedReplica, blk_1073743329_2528, FINALIZED",
          "role_name": "DataNode",
          "input": {},
          "cluster_id": 1330,
          "@timestamp": "2022-06-08T01:08:20.529Z",
          "docker_container": "dn-5977-container-hdfs-1330",
          "ecs": {},
          "topic": "dn",
          "host_name": "主机-73"
        }
      ]
    })
  }
]