import useGlobalProperties from '@/hooks/useGlobalProperties'
import { useI18n } from 'vue-i18n'
import { RouteLocationRaw } from 'vue-router'

export function useBasicTool() {
  const { t } = useI18n()
  const { $has } = useGlobalProperties()
  const router = useRouter()
  const route = useRoute()
  const store = useStore()
  interface Bread {
    name: string
    to?: RouteLocationRaw
  }
  function setBreadList(value: Bread[]) {
    console.log('setBreadList', value)
    store.commit('app/setBreadList', value)
  }
  function hasBusinessAuthority(authKey: string, authVal: string | number) {
    const authObj = store.state.user.businessAuthority
    if (store.state.user.userInfo.type === '2') {
      return true
    }
    if (authObj[authKey]) {
      return authObj[authKey].includes(authVal)
    }
    return false
  }
  return {
    $has,
    t,
    router,
    route,
    store,
    setBreadList,
    hasBusinessAuthority
  }
}
