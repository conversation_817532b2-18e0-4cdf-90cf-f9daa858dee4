import service from '@/utils/Http'

/**
 * @description 分页获取主备方案列表
 * @param {MasterSlaveApi.GetPageList} data
 * @return {*}
 */
export const getPageList = async (data: MasterSlaveApi.GetPageList) => {
  return service.post<ApiResponse<any>>('/masterSlave/page', data)
}

/**
 * @description 分页获取主备方案列表
 * @param {MasterSlaveApi.AddMasterSlave} data
 * @return {*}
 */
export const addMasterSlave = async (data: MasterSlaveApi.AddMasterSlave) => {
  return service.post<ApiResponse<any>>('/masterSlave/add', data)
}

/**
 * @description 分页获取主备方案列表
 * @param {MasterSlaveApi.DeleteMasterSlave} data
 * @return {*}
 */
export const deleteMasterSlave = async (data: MasterSlaveApi.DeleteMasterSlave) => {
  return service.post<ApiResponse<any>>('/masterSlave/delete', data)
}

/**
 * @description 分页获取主备方案列表
 * @param {MasterSlaveApi.GetAllList} data
 * @return {*}
 */
export const getAllList = async (data: MasterSlaveApi.GetAllList) => {
  return service.post<ApiResponse<any>>('/masterSlave/list', data)
}

/**
 * @description 查询组件迁移方案列表
 * @param {MasterSlaveApi.GetDataMigrationList} data
 * @return {*}
 */
export const getDataMigrationList = async (data: MasterSlaveApi.GetDataMigrationList) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/list', data)
}

/**
 * @description 删除方案列表
 * @param {MasterSlaveApi.DeleteDataMigration} data
 * @return {*}
 */
export const deleteDataMigration = async (data: MasterSlaveApi.DeleteDataMigration) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/delete', data)
}

/**
 * @description 添加HDFS任务
 * @param {MasterSlaveApi.AddHdfsTask} data
 * @return {*}
 */
export const addHdfsTask = async (data: MasterSlaveApi.AddHdfsTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hdfs/add', data)
}

/**
 * @description 添加Hive任务
 * @param {MasterSlaveApi.AddHiveTask} data
 * @return {*}
 */
export const addHiveTask = async (data: MasterSlaveApi.AddHiveTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hive/add', data)
}

/**
 * @description hdfs迁移方案关闭开启
 * @param {MasterSlaveApi.HdfsStatusSwitch} data
 * @return {*}
 */
export const hdfsStatusSwitch = async (data: MasterSlaveApi.HdfsStatusSwitch) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hdfs/statusSwitch', data)
}

/**
 * @description hdfs迁移方案关闭开启
 * @param {MasterSlaveApi.StartHiveTask} data
 * @return {*}
 */
export const startHiveTask = async (data: MasterSlaveApi.StartHiveTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hive/start', data)
}

/**
 * @description hdfs迁移方案关闭开启
 * @param {MasterSlaveApi.StopHiveTask} data
 * @return {*}
 */
export const stopHiveTask = async (data: MasterSlaveApi.StopHiveTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hive/stop', data)
}

/**
 * @description 新增hbase数据迁移方案
 * @param {MasterSlaveApi.AddHbaseTask} data
 * @return {*}
 */
export const addHbaseTask = async (data: MasterSlaveApi.AddHbaseTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hbase/add', data)
}

/**
 * @description 根据主备方案id获取hive列表
 * @param {MasterSlaveApi.GetHiveClusterById} data
 * @return {*}
 */
export const getHiveClusterById = async (data: MasterSlaveApi.GetHiveClusterById) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/getHiveClusterById', data)
}

/**
 * @description 获取hive库名
 * @param {MasterSlaveApi.GetHiveClusterById} data
 * @return {*}
 */
export const getHiveDbNameWithClusterId = async (data: MasterSlaveApi.GetHiveDbNameWithClusterId) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/getHiveDbNameWithClusterId', data)
}

/**
 * @description 获取hive库名
 * @param {MasterSlaveApi.GetHbaseRegionTableList} data
 * @return {*}
 */
export const getHbaseRegionTableList = async (data: MasterSlaveApi.GetHbaseRegionTableList) => {
  return service.post<ApiResponse<any>>('/hbase/region/tableList', data)
}

/**
 * @description 获取hbase集群
 * @param {MasterSlaveApi.GetHBaseCluster} data
 * @return {*}
 */
export const getHBaseCluster = async (data: MasterSlaveApi.GetHBaseCluster) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/getHBaseClusterById', data)
}

/**
 * @description 启动hbase任务
 * @param {MasterSlaveApi.StartHBaseTask} data
 * @return {*}
 */
export const startHBaseTask = async (data: MasterSlaveApi.StartHBaseTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hbase/start', data)
}

/**
 * @description 停止hbase任务
 * @param {MasterSlaveApi.StopHBaseTask} data
 * @return {*}
 */
export const stopHBaseTask = async (data: MasterSlaveApi.StopHBaseTask) => {
  return service.post<ApiResponse<any>>('/masterSlave/dataMigration/hbase/stop', data)
}

/**
 * @description 停止hbase任务
 * @param {MasterSlaveApi.MasterSlaveSwitch} data
 * @return {*}
 */
export const masterSlaveSwitch = async (data: MasterSlaveApi.MasterSlaveSwitch) => {
  return service.post<ApiResponse<any>>('/masterSlave/switch', data)
}
