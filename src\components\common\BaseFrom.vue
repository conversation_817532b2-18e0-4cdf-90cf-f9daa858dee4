<template>
  <el-form v-bind="$attrs" ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px">
    <slot name="before" />
    <el-form-item v-for="item in filterFromData" v-bind="item.fromItemAttribute" :key="item.param">
      <template v-if="item.slotName">
        <slot :name="item.slotName"></slot>
      </template>
      <template v-else>
        <el-input v-if="item.type === 'input'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute" class="input-width" />
        <el-select v-if="item.type === 'select'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute" class="input-width">
          <el-option v-for="(optionItem, optionIndex) in item.selectOptions" :label="optionItem.name" :value="optionItem.value" :key="optionIndex" />
        </el-select>
        <el-switch v-if="item.type === 'switch'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute" />
        <el-checkbox-group v-if="item.type === 'checkbox'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute">
          <el-checkbox v-for="(optionItem, optionIndex) in item.selectOptions" :label="optionItem.value" name="type" :key="optionIndex">
            {{ optionItem.name }}
          </el-checkbox>
        </el-checkbox-group>
        <el-radio-group v-if="item.type === 'radio'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute">
          <el-radio label="Sponsorship" />
          <el-radio label="Venue" />
        </el-radio-group>
        <el-input v-if="item.type === 'textarea'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute" type="textarea" class="input-width" />
        <el-date-picker v-if="item.type === 'time'" v-model="ruleForm[item.param]" v-bind="item.inputAttribute" />
      </template>
    </el-form-item>
    <slot name="after" />
  </el-form>
</template>

<script lang="ts" setup>
import type { FormRules } from 'element-plus'

interface Props {
  fromData?: Array<BaseFrom.FromItem>
  rules?: FormRules // 验证信息
  inputWidth?: string // 输入框宽度
}
const props = withDefaults(defineProps<Props>(), {
  fromData: () => [
    {
      type: 'input',
      param: 'input',
      defaultValue: '',
      fromItemAttribute: { prop: 'input', label: '输入框' },
      inputAttribute: { placeholder: '提示语' }
    },
    {
      type: 'select',
      param: 'select',
      selectOptions: [{ name: '示例', value: 111 }],
      defaultValue: '',
      fromItemAttribute: { prop: 'select', label: '选择' },
      inputAttribute: { placeholder: '提示语' }
    }
  ],
  inputWidth: '200px'
})
const filterFromData = computed(() => props.fromData.filter((item: BaseFrom.FromItem) => item?.isItem ?? true))
const ruleFormRef = ref()
const ruleForm = ref<any>({})
getRuleForm()
watch(
  () => props.fromData,
  () => {
    getRuleForm()
  },
  { deep: true }
)

// 提交表单事件
async function submitForm(): Promise<any> {
  const isSubmit = await ruleFormRef.value.validate((valid: Boolean) => valid)
  return new Promise((resolve) => {
    if (isSubmit) resolve({ ...ruleForm.value })
  })
}

// 重置from
function resetForm() {
  ruleFormRef.value.resetFields()
  getRuleForm()
}

// 获取ruleForm
function getRuleForm() {
  props.fromData.forEach((item: BaseFrom.FromItem) => {
    if (item.param !== '') ruleForm.value[item.param] = item?.defaultValue ?? ''
  })
}
defineExpose({ submitForm, resetForm, ruleForm })
</script>

<style lang="scss" scoped>
.input-width {
  width: v-bind('props.inputWidth');
}
</style>
