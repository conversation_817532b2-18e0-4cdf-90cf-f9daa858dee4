import { defineComponent, useAttrs, computed, isRef, openBlock, createBlock, unref, mergeProps, withCtx, createElementB<PERSON>, Fragment, renderList, createSlots, createVNode } from 'vue';
import { ElSelect, ElOptionGroup, ElOption } from 'element-plus';
import 'element-plus/es/components/option/style/css';
import 'element-plus/es/components/option-group/style/css';
import 'element-plus/es/components/select/style/css';
import RenderVNode from '../../render-vnode/src/index.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcSelect"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: [String, Number, Boolean, Array]
    },
    options: {
      type: [Array, Object],
      default: () => []
    },
    groups: {
      type: [Array, Object],
      default: () => []
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const attrs = useAttrs();
    const options = computed(() => {
      return isRef(props.options) ? props.options.value : props.options;
    });
    const groups = computed(() => {
      return isRef(props.groups) ? props.groups.value : props.groups;
    });
    const handleChange = (val) => {
      emits("update:modelValue", val);
      if (typeof attrs.onChange === "function") {
        attrs.onChange(val);
      }
    };
    ;
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElSelect), mergeProps({ ..._ctx.$attrs }, {
        "model-value": props.modelValue,
        onChange: handleChange
      }), {
        default: withCtx(() => [
          groups.value.length ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(groups.value, (group) => {
            return openBlock(), createBlock(unref(ElOptionGroup), {
              key: group.label,
              label: group.label,
              disabled: group.disabled
            }, {
              default: withCtx(() => [
                (openBlock(true), createElementBlock(Fragment, null, renderList(group.options, (item, index) => {
                  return openBlock(), createBlock(unref(ElOption), {
                    key: index,
                    label: item.label,
                    value: item.value,
                    disabled: item.disabled
                  }, createSlots({ _: 2 }, [
                    item.defaultSlot ? {
                      name: "default",
                      fn: withCtx((scope) => [
                        createVNode(unref(RenderVNode), {
                          vnode: item.defaultSlot,
                          scope,
                          "extra-scope": item
                        }, null, 8, ["vnode", "scope", "extra-scope"])
                      ]),
                      key: "0"
                    } : void 0
                  ]), 1032, ["label", "value", "disabled"]);
                }), 128))
              ]),
              _: 2
            }, 1032, ["label", "disabled"]);
          }), 128)) : (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList(options.value, (item, index) => {
            return openBlock(), createBlock(unref(ElOption), {
              key: index,
              label: item.label,
              value: item.value
            }, createSlots({ _: 2 }, [
              item.defaultSlot ? {
                name: "default",
                fn: withCtx((scope) => [
                  createVNode(unref(RenderVNode), {
                    vnode: item.defaultSlot,
                    scope,
                    "extra-scope": item
                  }, null, 8, ["vnode", "scope", "extra-scope"])
                ]),
                key: "0"
              } : void 0
            ]), 1032, ["label", "value"]);
          }), 128))
        ]),
        _: 1
      }, 16, ["model-value"]);
    };
  }
});
var Select = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\select\\src\\index.vue"]]);

export { Select as default };
//# sourceMappingURL=index.mjs.map
