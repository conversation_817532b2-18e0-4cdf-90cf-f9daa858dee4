'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/button/style/css');
var lodash = require('lodash');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcButton"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    debounce: {
      type: Boolean,
      default: false
    },
    debounceInterval: {
      type: Number,
      default: 300
    }
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const handleClick = (e) => {
      emits("click", e);
    };
    const onClick = props.debounce ? lodash.debounce(handleClick, props.debounceInterval) : handleClick;
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElButton), vue.mergeProps(_ctx.$attrs, { onClick: vue.unref(onClick) }), vue.createSlots({
        default: vue.withCtx(() => [
          vue.renderSlot(_ctx.$slots, "default")
        ]),
        _: 2
      }, [
        _ctx.$slots.loading ? {
          name: "loading",
          fn: vue.withCtx(() => [
            vue.renderSlot(_ctx.$slots, "loading")
          ]),
          key: "0"
        } : void 0,
        _ctx.$slots.icon ? {
          name: "icon",
          fn: vue.withCtx(() => [
            vue.renderSlot(_ctx.$slots, "icon")
          ]),
          key: "1"
        } : void 0
      ]), 1040, ["onClick"]);
    };
  }
});
var DcButton = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\button\\src\\index.vue"]]);

exports["default"] = DcButton;
//# sourceMappingURL=index.js.map
