<template>
  <div class="config-info">
    <TheTitle inline>
      <template #titleText>{{ $t('replenish.configurationInformation') }}</template>
    </TheTitle>
    <div class="bg-hex-FAFBFC p-20px relative">
      <el-form ref="ruleFormRef" :model="ruleForm" inline :rules="rules" :disabled="!isEdit" label-width="auto">
        <el-form-item :label="$t('replenish.enabledState') + '：'" class="w-100%" prop="status">
          <el-switch v-model="ruleForm.status" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item :label="$t('replenish.storageSystem') + '：'" class="w-540px" prop="componentId">
          <el-radio-group v-model="ruleForm.componentId">
            <el-radio :label="1">HDFS</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('replenish.tieredStoragePolicy') + '：'" class="w-540px" prop="strategy">
          <el-radio-group v-model="ruleForm.strategy">
            <el-radio :label="1">{{ $t('replenish.byTime') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('replenish.executionCycle') + '：'" class="w-100%" prop="executionCycle">
          <el-input-number v-model="ruleForm.executionCycle" :min="1" /><span class="ml-10px">天</span>
        </el-form-item>
        <el-form-item :label="$t('replenish.executionTime') + '：'" class="w-100% timer" prop="executionTime">
          <el-time-select v-model="ruleForm.executionTime" start="00:00" step="01:00" end="23:00" :placeholder="$t('replenish.pleaseSelectTime')" />
        </el-form-item>
        <el-form-item :label="$t('replenish.hotStorageCluster') + '：'" class="w-540px" prop="fromCluster">
          <el-select v-model="ruleForm.fromCluster" class="w-320px" @change="fromClusterChange" :placeholder="$t('form.pleaseChoose')">
            <el-option v-for="item in namespaceList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('replenish.hotDataClusterDirectory') + '：'" class="w-540px" prop="fromDir">
          <el-input v-model="ruleForm.fromDir" class="w-320px!" :placeholder="$t('form.pleaseEnter')" />
        </el-form-item>
        <el-form-item :label="$t('replenish.coldDataStorageCluster') + '：'" class="w-540px" prop="toCluster">
          <el-select v-model="ruleForm.toCluster" class="w-320px" @change="toClusterChange" :placeholder="$t('form.pleaseChoose')">
            <el-option v-for="item in namespaceList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('replenish.coldDataClusterDirectory') + '：'" class="w-540px" prop="toDir">
          <el-input v-model="ruleForm.toDir" class="w-320px!" :placeholder="$t('form.pleaseEnter')" />
        </el-form-item>
        <el-form-item :label="$t('replenish.hotDataHDFSName') + '：'" class="w-540px" prop="fromClusterService">
          <el-select v-model="ruleForm.fromClusterService" class="w-320px" :placeholder="$t('form.pleaseChoose')">
            <el-option v-for="item in fromClusterHdfs" :key="item.id" :label="item.clusterName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('replenish.coldDataHDFSName') + '：'" class="w-540px" prop="toClusterService">
          <el-select v-model="ruleForm.toClusterService" class="w-320px" :placeholder="$t('form.pleaseChoose')">
            <el-option v-for="item in toClusterhdfs" :key="item.id" :label="item.clusterName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div v-if="$has('migration-backup-edit')" class="absolute right-20px top-20px">
        <template v-if="isEdit">
          <BaseButton type="primary" @click="submitForm(ruleFormRef)"> {{ $t('button.save') }} </BaseButton>
          <BaseButton type="info" @click="resetForm(ruleFormRef)"> {{ $t('button.cancel') }} </BaseButton>
        </template>
        <template v-else>
          <BaseButton type="info" v-if="!isDisabled" @click="onEdit">{{ $t('button.edit') }}</BaseButton>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
const { store, t, $has } = useBasicTool()
const isEdit = ref(false)
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  isDisabled: {
    type: Boolean,
    default: false
  }
})
onMounted(() => {
  getNamespaceList()
})
const emit = defineEmits(['refresh'])
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({
  status: 0,
  componentId: 1,
  strategy: 1,
  executionCycle: 1,
  executionTime: '',
  fromCluster: '',
  fromDir: '',
  toCluster: '',
  toDir: '',
  fromClusterService: '',
  toClusterService: ''
})
const userInfo = computed(() => store.state.user.userInfo)
const tenantId = computed(() => store.state.user.tenantId)
watch(
  () => props.data,
  () => {
    getHdfsClusterByNameSpaceId(props.data.toCluster, true).then((res) => {
      toClusterhdfs.value = res.data
      getHdfsClusterByNameSpaceId(props.data.fromCluster, false).then((res) => {
        fromClusterHdfs.value = res.data
        Object.keys(ruleForm).forEach((item) => {
          ruleForm[item] = props.data[item]
        })
        ruleForm.executionTime = setExecutionTime(Number(props.data.executionTime))
        ruleForm.fromClusterService = setClusterService(fromClusterHdfs.value, ruleForm.fromClusterService)
        ruleForm.toClusterService = setClusterService(toClusterhdfs.value, ruleForm.toClusterService)
      })
    })
  },
  {
    deep: true
  }
)

function setExecutionTime(value: number) {
  if (value > 9) return value + ':00'
  else return '0' + value + ':00'
}
const rules = reactive<any>({
  status: [{ required: true, message: t('form.pleaseChoose'), trigger: 'change' }],
  componentId: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  strategy: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  executionCycle: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  executionTime: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  fromCluster: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  fromDir: [
    {
      required: true,
      message: t('form.pleaseEnter'),
      trigger: 'change'
    }
  ],
  toCluster: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  toDir: [
    {
      required: true,
      message: t('form.pleaseEnter'),
      trigger: 'change'
    }
  ],
  fromClusterService: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  toClusterService: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ]
})

async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      let id = 0
      if (Number(userInfo.value.type) !== 2 && tenantId.value === '') id = userInfo.value.tenantId
      else id = tenantId.value
      store.dispatch('migration/editRule', { ...ruleForm, id }).then((res) => {
        isEdit.value = false
        emit('refresh')
        ElMessage({
          message: t('replenish.editSuccess'),
          type: 'success'
        })
      })
    }
  })
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  isEdit.value = false
  emit('refresh')
}

function onEdit() {
  isEdit.value = true
}
const namespaceList = ref()
function getNamespaceList() {
  store
    .dispatch('namespace/namespacePageByName', {
      pageNo: -1,
      pageSize: -1
    })
    .then((res) => {
      namespaceList.value = res.data?.records
    })
}
const fromClusterHdfs = ref<any>([])
const toClusterhdfs = ref<any>([])
function fromClusterChange(value: number) {
  return getHdfsClusterByNameSpaceId(value, false).then((res) => {
    fromClusterHdfs.value = res.data
    ruleForm.fromClusterService = ''
    if (res.data.length === 1) ruleForm.fromClusterService = res.data[0].id
  })
}
function toClusterChange(value: number) {
  return getHdfsClusterByNameSpaceId(value, true).then((res) => {
    toClusterhdfs.value = res.data
    ruleForm.toClusterService = ''
    if (res.data.length === 1) ruleForm.toClusterService = res.data[0].id
  })
}

function setClusterService(data: any[], id: number) {
  if (!data.map((item: any) => item.id).includes(id)) return ''
  return id
}

function getHdfsClusterByNameSpaceId(id: number, isto: boolean) {
  return store.dispatch('cluster/getHdfsClusterByNameSpaceId', { id, isto })
}
</script>
<style lang="scss" scoped>
.timer {
  :deep(.el-input__wrapper) {
    width: 100%;
  }
}
</style>
