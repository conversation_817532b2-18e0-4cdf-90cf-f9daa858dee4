<template>
  <BaseLayout>
    <template #header>
      <BaseHeaderTitle :title-text="route.query.title?.toString()" />
    </template>
    <template #content>
      <div class="add-state-content">
        <div>
          <div v-if="route.query.state === 'SUCCESS'" key="icon-icon_chenggong">
            <i class="iconfont icon-icon_chenggong" />
            <div class="add-state-content__title">{{ $t('title.addSuccessfully') }}</div>
            <div class="add-state-content__tips">{{ route.query.name }}{{ $t('message.added') }}!</div>
          </div>
          <div v-else key="icon-icon_shibai">
            <i class="iconfont icon-icon_shibai" />
            <div class="add-state-content__title">{{ $t('title.addFailed') }}</div>
            <div class="add-state-content__tips">
              {{ route.query.stepMsg }}
            </div>
          </div>
        </div>
        <BaseButton type="primary" class="back-botton" @click="baskView">
          <span>{{ route.query.backButtonName }}</span>
          <span v-if="route.query.state === 'SUCCESS'">({{ time }}s)</span>
        </BaseButton>
        <BaseButton v-if="isReinstall" type="primary" class="back-botton" @click="reinstall">{{ $t('button.ReAdd') }}</BaseButton>
      </div>
    </template>
  </BaseLayout>
</template>

<script lang="ts" setup>
const router = useRouter()
const route = useRoute()
const timer = ref()
const time = ref(5)
const isReinstall = computed(() => route.query.state !== 'SUCCESS' && route.query.isReinstall === '1')
onMounted(() => {
  if (route.query.state === 'SUCCESS') {
    timer.value = setInterval(() => {
      time.value--
      if (time.value === 0) {
        baskView()
      }
    }, 1000)
  }
})
function baskView() {
  const { routerPash } = route.query
  router.push({
    name: routerPash?.toString()
  })
}

function reinstall() {
  const { reinstallRouterPash, backFillForm } = route.query
  router.push({
    name: reinstallRouterPash?.toString(),
    state: {
      backFillForm: backFillForm
    }
  })
}

onBeforeUnmount(() => {
  clearInterval(timer.value)
})
</script>

<style lang="scss" scoped>
.add-state-content {
  padding-top: 20vh;
  text-align: center;
  background-color: var(--ops-bg-white-color);
  .icon-icon_chenggong {
    font-size: 64px;
    color: var(--ops-state-success-color);
  }
  .icon-icon_shibai {
    font-size: 64px;
    color: var(--ops-state-fail-color);
  }
  .add-state-content__title {
    margin: 10px auto 6px;
    font-size: 16px;
    font-weight: 600;
    color: var(--ops-text-color);
  }
  .add-state-content__tips {
    width: 840px;
    height: 20px;
    margin: 0 auto 10px;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    color: var(--ops-text-color);
  }
  .back-botton {
    margin-top: 15px;
  }
}
</style>
