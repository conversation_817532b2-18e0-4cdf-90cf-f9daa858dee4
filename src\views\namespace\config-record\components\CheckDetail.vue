<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.viewParameters')" width="950px" :before-close="resetForm" v-loading="loading">
    <div v-if="!isBatch">
      <BaseTable :columns="columns" :data="list" :maxHeight="400">
        <template #parameterType>cluster.yaml</template>
        <template #maxMemory="{ row }"> {{ `${row.memoryCapacity}/${row.maxMemory}` }} </template>
      </BaseTable>
    </div>
    <div v-else>
      <BaseElCollapse v-model="activeNames" v-if="updateList.length">
        <el-collapse-item v-for="(el, i) in updateList" :name="i" :key="i">
          <template #title>
            <TheTitle class="ml-20px">
              <template #titleText> {{ el.confKey }} </template>
            </TheTitle>
          </template>
          <div class="h-80 w-full">
            <CodeDiffEditor
              v-if="activeNames.includes(i)"
              :originCode="el.originCode"
              :modifiedCode="el.modifiedCode"
              :isShow="true"
              :readOnly="true"
            />
          </div>
        </el-collapse-item>
      </BaseElCollapse>
    </div>

    <template #footer>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { updateDetail } from '@/api/clusterServiceConfigApi'
import { createNodeClass } from '@/api/k8sApi'
import { ElMessage } from 'element-plus'

const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  recordId: {
    type: Number,
    default: 0
  },
  clusterServiceId: {
    type: String,
    default: ''
  },
  clusterServiceInstanceId: {
    type: String,
    default: ''
  },
  isBatch: {
    type: Boolean,
    default: false
  }
})

const activeNames = ref([])

const loading = ref(false)

const list = ref<clusterServiceConfigApi.MergeConfigsByClusterIdItem[]>([])

const updateList = computed(() =>
  list.value?.map((el) => {
    return {
      confKey: el.confKey,
      originCode: {
        code: el?.historyConfValue,
        language: 'text'
      },
      modifiedCode: {
        code: el?.confValue,
        language: 'text'
      }
    }
  })
)

const getDetail = () => {
  loading.value = true
  updateDetail({
    clusterServiceId: props.clusterServiceId,
    recordId: props.recordId,
    type: 1,
    pageNo: 1,
    pageSize: 1000
  }).then((res) => {
    if (res.data.code === 0) {
      list.value = res.data.data.records || []
    }
  })
  loading.value = false
}

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    } else {
      getDetail()
    }
  }
)
const ruleForm = reactive<any>({
  description: ''
})
const rules = {
  description: [
    {
      trigger: 'blur',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ]
}
const tenantId = computed(() => store.state.user.tenantId)
const namespaceList = ref<string[]>([])
const columns = [
  // { label: t('replenish.parameterType'), prop: 'parameterType', slot: true },
  { label: t('replenish.parameterName'), prop: 'confKey' },
  { label: t('replenish.beforeModification'), prop: 'historyConfValue' },
  { label: t('replenish.modify'), prop: 'confValue' }
  // { label: t('replenish.type'), prop: 'updateTime' }
]

// 重置
function resetForm() {
  activeNames.value = []
  loading.value = false
  emit('update:modelValue', false)
}
</script>
