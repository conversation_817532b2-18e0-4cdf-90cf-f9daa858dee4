{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/radio/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Radio from \"./src/index.vue\";\r\n\r\nexport const DcRadio = withInstall(Radio);\r\n\r\nexport default DcRadio;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "Radio"], "mappings": ";;;;;;;;AAIa,MAAA,OAAA,GAAUA,qBAAYC,gBAAK;;;;;"}