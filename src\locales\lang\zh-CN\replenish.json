{"buttonText": "在此填写按钮文本", "copySuccess": "复制成功", "name": "姓名", "hint": "提示语", "example": "示例", "select": "选择", "dropdown": "下拉框", "inputField": "输入框", "date": "日期", "button": "按钮", "pleaseInput": "请输入...", "selectedHost": "已选主机", "enterVerificationCode": "为防止意外操作，请输入该验证码到输入框", "enterVerification": "请输入验证码", "checkHost": "检查主机", "completed": "已完成", "inProgress": "进行中", "viewDiagnosisReport": "查看诊断报告", "hostIp": "主机ip", "checkStatus": "检查状态", "checkStartTime": "开始检查时间", "hostAlias": "主机别名", "hostAliasSupport": "主机别名支持1", "character": "字符", "modifyHostnameWarning": "修改主机名会导致运行中的组件无法使用,且修改主机名后,需要对组件进行重启操作!", "hostnameRule": "主机名由小写字母开头，且由小写字母、数字、“-”组成", "confirmModifyHostname": "是否确定对主机名{value}进行修改?", "dataCollecting": "数据采集中...", "progress": "进度", "starting": "启动中", "restarting": "重启中", "stopping": "停止中", "deleting": "删除中", "scaling": "扩缩容中", "scheduling": "调度中", "updating": "更新中", "rollingRestarting": "滚动重启中", "awaitingDeployment": "待部署", "awaitingStart": "待启动", "awaitingRestart": "待重启", "awaitingStop": "待停止", "awaitingDeletion": "待删除", "awaitingExpansion": "待扩缩容", "awaitingScheduling": "待调度", "awaitingUpdate": "待更新", "awaitingRollingRestart": "待滚动重启", "awaitingExecution": "待执行", "executionInProgress": "执行中", "viewAllSelectedHosts": "查看全部已选主机", "dataPlatform": "数据中台", "platformInspection": "平台智能巡检", "chooseTenant": "选择租户", "migrationBackup": "迁移备份", "customChart": "自定义图表", "recentlyUsed": "最近使用", "addPrimarySecondaryPlan": "添加主备方案", "primarySecondaryPlanName": "主备方案名称", "primaryClusterName": "主集群名称", "secondaryClusterName": "备集群名称", "primarySecondaryManagement": "主备管理", "primarySecondaryPlanTitle": "主备方案名称", "primaryClusterTitle": "主集群名称", "secondaryClusterTitle": "备集群名称", "syncTaskCount": "同步任务数", "primarySecondarySwitch": "主备切换", "deleteCurrentPrimarySecondaryPlan": "是否删除当前的主备方案", "reAddAfterDeletion": "删除后可重新添加主备方案。", "switchPrimarySecondaryRelation": "是否切换当前方案中的主备关系?", "addCustomParameter": "添加自定义参数", "addParameter": "添加参数", "deleteCurrentCustomParameter": "是否删除当前自定定义参数", "customParameter": "自定义参数", "parameterName": "参数名称", "value": "值", "explain": "说明", "pleaseEnterDescription": "请输入说明，限200个字符", "noChineseOrEmojis": "不支持中文与表情符", "customConfigExists": "该自定义配置项已存在", "custom": "自定义", "deleteCustomConfigAfterSave": "保存并发布配置后删除该自定义配置", "newCustomConfig": "本次新增自定义配置", "selectFileName": "请选择文件名称", "roleScope": "角色范围", "configCategory": "配置类别", "processMemoryUsage": "进程的内存用量", "service": "服务", "roleInstance": "角色实例", "memoryUsage": "内存用量", "processCpuUsage": "进程的cpu用量", "usage": "用量", "blacklistedNodeMessage": "该节点为拉黑节点，不可进行写入操作", "viewLog": "查看日志", "componentService": "组件服务", "link": "链接", "runtime": "运行时间", "fullLog": "完整日志", "hostRole": "主机角色", "resources": "资源", "restartSelectedRoleService": "是否重启选中的角色服务?", "selectedRole": "选中的角色", "lastRestartTime": "近期重启时间", "blacklist": "拉黑", "confirmBlacklistDataNode": "是否确认拉黑该DataNode节点", "blacklistSuccess": "拉黑成功", "unblacklist": "取消拉黑", "confirmUnblacklistDataNode": "是否确认取消拉黑该DataNode节点", "unblacklistSuccess": "取消拉黑成功", "confirmDeleteRetiredNode": "是否确认删除该退役节点?", "nodeStillHasReplicaData": "该节点仍有{value}个副本数据尚未迁移完成, 是否确认删除该退役节点?", "deleteCurrentRole": "是否删除当前角色", "operationSuccess": "操作成功", "hostAdditionStatus": "主机添加状态", "steps": "步骤", "close": "关闭", "awaitingDeployment2": "等待部署", "hostAdditionProgress": "主机添加进度", "returnToList": "返回列表", "nodeType": "节点类型", "hostInfo": "主机信息", "confirmAddition": "确认添加", "addNewHost": "新增主机", "pleaseEnterAddress": "请输入地址，如192.68.12.[1-200]", "connectionMethod": "连接方式", "passwordConnection": "账密连接", "publicKeyConnection": "公钥连接", "publicKeyConnectionInstructions": "使用公钥链接方式添加主机，需要将当前系统密钥粘贴到您即将添加的主机认证文件中，主机认证文件通常为“~/.ssh/authorized_keys”，请确保在添加主机之前完成上述操作；当前系统密钥下载", "downloadNow": "立即下载", "viewDataSource": "查看数据源", "selectDataSource": "选择数据源", "edit": "编辑", "cancel": "取消", "save": "保存", "confirmExitEditing": "确定退出当前编辑？", "exitWithoutSaving": "退出后，当前修改的内容不会被保存", "saveSuccess": "保存成功", "maintenanceModeNote": "维护模式开启时，该主机将不作为告警规则及健康巡检的校验项。", "pleaseEnterCorrectVerificationCode": "请输入正确的验证码", "pleaseEnterCorrectHostToUnbind": "请输入正确的要解绑的主机名称", "hostOccupiedCannotDelete": "当前选中主机已被占用，不可删除！", "confirmDeleteSelectedHost": "是否确定删除选中主机？", "unbindAndDeleteHostBoundToCluster": "选中主机已经绑定集群我们将为你进行集群解绑操作后进行删除", "hostsSelected": "已选择主机", "maintenanceModeActivationFailed": "维护模式开启失败", "lastInspection": "最后一次巡检", "manualInspection": "手动巡检", "inspectionProgress": "巡检进度", "selectManualInspectionType": "请选择手动巡检执行类型", "platformBasicEnvironment": "平台基础环境", "clusterInspection": "集群巡检", "componentServiceInspection": "组件服务巡检", "hostInspection": "主机巡检", "disasterRecoveryTaskInspection": "容灾任务巡检", "inspectionConfiguration": "巡检配置", "selectInspectionCycle": "请选择巡检周期", "selectStartTime": "请选择开始时间", "pleaseEnterDurationLimit": "请输入持续限制", "historicalInspectionReports": "历史巡检报告", "inspectionReport": "巡检报告", "inspectionDuration": "巡检时长", "inspectionItems": "巡检事项", "inspectionStatus": "巡检状态", "viewReport": "查看报告", "export": "导出", "inspectionItemDescription": "巡检项说明", "inspectionItemResult": "巡检项结果", "inspectionItemSuggestion": "巡检项建议", "collapseAll": "全部收起", "disasterRecoveryTaskCheck": "容灾任务检查", "reportDetails": "报告详情", "addNamespace": "添加Namespace", "pleaseEnterNamespaceName": "请输入namespace名称", "pleaseEnterServiceAccount": "请输入serviceAccount", "autoGenerate": "自动生成", "resourceAllocation": "资源分配", "clientCertificate": "客户端证书", "tenantPermissions": "租户权限", "createPermissions": "创建权限", "tenantsWithPermissions": "拥有权限的租户可以自定义对该k8s进行Namespace管理。", "tenantList": "租户列表", "permissionTenants": "权限租户", "management": "管理", "isDelete": "是否删除{value}?", "dueToKubernetesBinding": "由于Kubernetes上绑定了集群{value}, 请解绑后再次删除", "tenantBelonging": "所属租户", "relatedCluster": "关联集群", "backgroundImage": "背景图片", "configurationInformation": "配置信息", "editSuccess": "编辑成功", "errorLog": "错误日志", "viewDetails": "查看详情", "basicInformation": "基本信息", "executionSuccess": "执行成功", "executionFailure": "执行失败", "fileName": "文件名", "fileSize": "文件大小", "lastModifiedTime": "最后修改时间", "directoryAddress": "目录地址", "subdirectoryCount": "子目录数", "fileStatus": "文件状态", "hotColdDataGrading": "冷热数据分级", "hotDataList": "热数据列表", "coldDataList": "冷数据列表", "addTask": "添加任务", "pendingRun": "待运行", "notStarted": "未开始", "taskName": "任务名称", "addHBaseTask": "添加Hbase任务", "pleaseEnterTaskName": "请输入任务名称", "primaryBackupScheme": "所属主备方案", "pleaseSelectPrimaryBackupScheme": "请所属主备方案", "sourceClusterHBaseCluster": "源集群hbase集群", "targetClusterHBaseCluster": "目标集群hbase集群", "fullTable": "全量表", "selectedTablesOnly": "仅勾选表", "syncTableSelection": "同步表选择", "sourceList": "源列表", "targetList": "目标列表", "firstSyncOfTargetTableOnTaskStartup": "任务首次启动时进行目标表的全量同步", "mutualTrustHelpDocument": "互信帮助文档", "maxInput20Characters": "最多可输入20个字符", "gettingSyncTables": "获取同步表中...", "syncTableName": "同步表名称", "keywords": "关键词", "taskNameKeyword": "任务名称", "primaryBackupClusterScheme": "所属主备集群方案", "syncTableType": "同步表类型", "terminate": "终止", "startCurrentPrimaryBackupScheme": "是否启动当前的主备方案", "deleteExistingHistoricalDataInBackupCluster": "删除方案下备集群已有的历史数据", "addHDFTask": "添加HDFS任务", "sourceClusterDataDirectory": "主集群源数据目录", "pleaseEnterHDFTargetDirectory": "请输入HDFS同步目录", "optionalSameAsSourceIfEmpty": "可选,为空时目录与主集群目录一致", "targetClusterTargetDirectory": "备集群目标目录", "syncStrategy": "同步策略", "pleaseSelectTime": "请选择时间", "bandwidth": "带宽", "pleaseEnterBandwidth": "请输入带宽", "concurrency": "并发数", "pleaseEnterConcurrency": "请输入并发数", "onlyPositiveIntegersSupported": "只支持输入正整数", "sourceClusterSourceDataDirectory": "主集群源数据目录", "everyDay": "每天", "addHiveTask": "添加Hive任务", "sourceHiveCluster": "源hive集群", "targetHiveCluster": "目标hive集群", "targetDatabaseName": "目标数据库名称", "targetDatabaseNameShouldBeSameAsSource": "目标数据库名称应与源数据库名称相同", "sourceDatabaseName": "源数据库名称", "targetDatabase": "目标数据库", "mysqlMasterSlaveConfigurationPrompt": "提示：请确认主备数据库间是否完成MySQL的主备配置，如开启binlog以及指定server_id", "task": "任务", "syncTaskManagement": "同步任务管理", "manageAllClustersOrSingleCluster": "您可对权限范围内的所有集群进行统一管理，也可选择单个集群，实现轻松高效的管理。", "componentNameFormat": "组件名称仅支持小写字母和数字，且必须包含小写字母", "pleaseEnterCorrectFormat": "请输入正确格式", "modifyParametersWhenDeployingCluster": "部署集群时修改参数", "unsavedChangesWarning": "离开后系统可能不会保存你的操作", "pleaseEnterServiceType": "请输入服务类型", "supportS3": "支持S3", "dataLake": "数据湖", "roleGroup": "角色组", "addRoleGroup": "添加角色组", "roleGroupName": "角色组名称", "roleType": "角色类型", "configurationSource": "配置来源", "editRoleGroup": "编辑角色组", "selectInstance": "选择实例", "availableInstances": "可选择的实例", "instanceName": "实例名称", "instancesWithinRoleGroup": "角色组内实例", "deleteRoleGroupConfirmation": "是否删除角色组", "selectNamespaceForClusterInKubernetes": "选择当前集群在该Kubernetes中使用的命名空间（该选项可以为空，若为空，系统则会自动在Kubernetes中创建一个新的命名空间），请确保选择的命名空间存在且未被使用", "pleaseSelectNamespace": "请选择namespace", "viewJob": "查看Job", "downloadTemplate": "下载模板", "configurationView": "配置查看", "confirmDeleteJob": "确认要对当前Job({value})进行删除？", "clusterState": "集群状态", "maintenanceMode": "维护模式", "confirmUnbindK8sCluster": "是否确认解绑该集群的k8s {value}？", "editDashboard": "编辑仪表盘", "addNewDashboard": "新增仪表盘", "dashboardName": "仪表盘名", "pleaseEnterDashboardName": "请输入仪表盘名", "createNewKerberosUser": "新建Kerberos用户", "username": "用户名", "optional": "选填", "lowercaseLettersNumbersOnly": "只包含小写字母、数字、连字符-、点.和左斜杠/，最大长度限制20", "passwordMinimumLength8": "密码长度最少为8", "passwordsDoNotMatch": "两次输入的密码不一致，请重新输入", "viewModify": "查看修改", "componentDetails": "组件详情", "rollingRestart": "滚动重启", "disableMaintenanceMode": "关闭维护模式", "enableMaintenanceMode": "开启维护模式", "viewAlarms": "查看告警", "viewProgressAfterStart": "启动后，可在更多操作里查看进度详情", "viewProgressAfterStop": "停止后，可在更多操作里查看进度详情", "confirmRollingRestart": "是否滚动重启", "viewProgressAfterDeletion": "删除后，可在更多操作里查看进度详情", "currentCustomVersionNumber": "当前自定义版本号", "instanceInformation": "实例信息", "lastHour": "近一小时", "lastDay": "近一天", "lastWeek": "近一周", "instanceStatusHistory": "实例状态历史", "deleted": "已删除", "maintenanceModeExemption": "维护模式开启时，该组件将不作为告警规则及健康巡检的校验项。", "componentHasDependencies": "当前组件存在依赖关系，不可删除！", "deleteDependentComponentFirst": "删除前请先删除依赖它的组件", "dependentComponents": "依赖组件", "selectedComponents": "已选择组件", "addKerberosUser": "添加Kerberos用户", "user": "用户", "userKeywords": "用户关键词", "exportKeytab": "导出Keytab", "confirmExportKeytabFile": "是否导出当前Kerberos用户的Keytab文件？", "confirmDeleteKerberosUser": "确认要删除当前Kerberos用户？删除后历史生成的Keytab将失效。", "smallFileCountMonitoringDelay": "小文件数量监控存在T+1延迟", "customSmallFileSizeSettings": "小文件自定义设置", "countSmallFilesInDirectory": "统计该目录下的小文件数量，小文件可进行自定义设置。", "directoryKeywords": "目录关键词", "currentOperation": "当前操作", "operationHistoryList": "操作历史列表", "operationName": "操作名称", "operationStatus": "操作状态", "notExecuted": "未执行", "migration": "迁移", "migrationSuccessful": "迁移成功", "statusDelayMinutes": "不健康（该状态存在分钟级延迟）", "lastTaskDetectionTime": "上次任务探测时间", "suggestion": "建议", "updateComponent": "更新组件", "pleaseSelectUpgradeVersion": "请选择升级版本", "confirmUpdateSelectedComponent": "是否更新选中的组件？", "suggestStoppingDependentComponentsBeforeUpdate": "更新前前建议先停止依赖它的组件", "componentOverview": "组件概览", "smallFileManager": "小文件管理", "taskMonitoring": "任务监控", "taskTime": "任务时间", "taskStatus": "任务状态", "duration": "持续时间", "allocatedVCoresInSeconds": "已分配VCore的秒数", "allocatedMemoryInSeconds": "已分配内存的秒数", "scheduledKillTime": "定时查杀的时间", "kill": "查杀", "scheduledKill": "定时查杀", "cancelScheduledKill": "取消定时查杀", "confirmKillTask": "是否确认查杀该任务", "confirmCancelScheduledKill": "是否确认取消定时查杀该任务", "pleaseSelectScheduledKillTime": "请选择任务定时查杀时间", "chartQuantity": "图表数量", "confirmDelete": "是否确定删除？", "pleaseEnterQuerySQL": "请输入查询SQL", "buildChart": "构建图表", "chartType": "图表类型", "selectDataRange": "选择数据范围", "lineChart": "折线图", "stackedAreaChart": "堆叠面积图", "table": "表格", "alarmRecord": "告警记录", "alarmRuleSetting": "告警规则设置", "existingResources": "既有资源", "tenantAvailableResources": "租户可用资源", "list": "列表", "tenantHasK8SCreatePermission": "该租户拥有创建K8S权限", "createPermission": "创建权限", "tenantName": "租户名称：", "userCount": "用户数", "hostCount": "主机数", "responsiblePerson": "负责人", "phone": "电话", "disable": "停用", "enable": "启用", "permissionControl": "权限控制", "tenantStatus": "租户状态：", "isEnableThisTenant": "是否启用该租户", "enableTenantWillRestoreLoginSystemAndOperationPermissions": "启用的租户将恢复登录系统及对系统的操作权限", "isDisableThisTenantAccount": "是否停用该租户账号", "disabledTenantCannotLoginSystemNoOperationPermissions": "停用的租户将无法登录系统没有对系统的操作权限", "isResetTheTenantLoginAccountPassword": "是否重置该租户登录账号密码", "confirmToDeleteTenant": "是否确定删除租户", "tenantWillNotAppearInSystemAfterDeletion": "删除后租户将不会出现在系统中", "unrecognizedIdentifier": "无法识别的标识", "pleaseSelectDataSource": "请选择数据源", "pleaseEnterTheCorrectIPAddress": "请输入正确的ip地址", "beingSchedul": "正在调度", "schedulingFailed": "调度失败", "beingUpdat": "正在更新", "updateFailed": "更新失败", "beingRestart": "正在重启", "restartFailed": "重启失败", "unhealthy": "不健康", "webSocketConnectionEstablished": "WebSocket连接已建立", "webSocketConnectionClosed": "WebSocket连接已关闭", "webSocketError": "WebSocket错误", "tryingToReconnect": "尝试重新连接...", "deploymentProgressDetails": "部署进度详情", "startupProgressDetails": "启动进度详情", "restartProgressDetails": "重启进度详情", "stopProgressDetails": "停止进度详情", "deleteProgressDetails": "删除进度详情", "scaleUpOrDownProgressDetails": "扩缩容进度详情", "schedulingProgressDetails": "调度进度详情", "updateProgressDetails": "更新进度详情", "rollingRestartProgressDetails": "滚动重启进度详情", "rollingRestartDetails": "滚动重启详情", "confirm": "确定", "query": "查询", "reset": "重置", "submit": "提交", "previousStep": "上一步", "nextStep": "下一步", "complete": "完成", "return": "返回", "saveAndPublish": "保存并发布", "addHost": "添加主机", "addCluster": "添加集群", "addK8s": "添加K8s", "oneClickDeployment": "一键部署", "addComponents": "添加组件", "scaleUpOrDown": "扩缩容", "addUser": "新增用户", "login": "登录", "unbind": "解绑", "bind": "绑定", "add": "添加", "returnToHostList": "返回主机列表", "returnToClusterList": "返回集群列表", "returnToComponentList": "返回组件列表", "showAllDescriptions": "显示所有描述", "hideAllDescriptions": "隐藏所有描述", "selectAll": "全选", "componentInstance": "组件实例", "componentConfiguration": "组件配置", "latestConfiguration": "最新配置", "testConnectivity": "测试连通性", "testing": "测试中", "configurationRecord": "配置记录", "instanceConfiguration": "实例配置", "collapse": "收起", "expand": "展开", "addComponent": "新增组件", "selectHost": "选择主机", "confirmRollback": "确定回滚", "installImmediately": "立即安装", "deployImmediately": "立即部署", "advancedConfiguration": "高级配置", "reAdd": "重新添加", "dockerDeployment": "Docker部署", "kubernetesDeployment": "kubernetes部署", "restoreDefaultValues": "恢复默认值", "undo": "撤销", "commandTerminal": "命令终端", "uploadFile": "上传文件", "yarnResourcePoolConfiguration": "Yarn资源池配置", "createSubPool": "创建子池", "batchDelete": "批量删除", "batchUnbind": "批量解绑", "clear": "清除", "downloadLog": "下载日志", "addRack": "添加机架", "viewAll": "查看全部", "batchRestart": "批量重启", "reExecute": "重新执行", "restartList": "重启列表", "operationHistory": "操作历史", "taskManagement": "任务管理", "addJob": "添加Job", "currentComponent": "当前组件", "timeSelection": "时间选择", "currentHost": "当前主机", "designation": "名称", "belongingCluster": "所属集群", "minimumLogLevel": "最低日志级别", "timeoutInSeconds": "超时时间 (秒)", "timePeriod": "时间段", "selectTime": "选择时间", "keywordSearch": "关键字搜索", "inputQuery": "输入查询", "fileDesignation": "文件名称", "reasonForModification": "修改原因", "editValue": "xx 编辑值", "phoneNumber": "手机号码", "emailAddress": "邮箱地址：", "loginPassword": "登录密码", "password": "密码", "selectDeploymentMethod": "选择部署方式", "customComponentName": "自定义组件名称", "hostSearch": "主机搜索", "selectCluster": "选择集群", "sshPortNumber": "SSH端口号", "privilegedUser": "特权用户", "authenticationType": "认证类型", "authenticationFile": "认证文件", "nodeCount": "节点数量", "originalPassword": "原密码", "newPassword": "新密码", "confirmNewPassword": "确认密码", "addHostToDefaultCluster": "将主机添加到默认集群中", "addHostToExistingCustomCluster": "将主机添加到已有自定义集群中", "alertContent": "告警内容", "alertCategory": "告警类别", "keyword": "关键字", "notes": "备注", "email": "邮箱", "type": "类型", "pool": "池", "pleaseEnterKeyword": "请输入关键字", "pleaseSelectStatus": "请选择状态", "pleaseEnterIPAddress": "请输入IP地址", "pleaseSelectBelongingCluster": "请选择所属集群", "startDate": "开始日期", "endDate": "结束日期", "pleaseEnterK8sName": "请输入K8s名称", "pleaseSelect": "请选择", "pleaseEnter": "请输入", "pastXMinutes": "过去xx分钟", "pleaseEnterName": "请输入名称", "pleaseUploadKubernetesConfigFileAndSizeMustNotExceed100kb": "请上传kubernetes config文件，并且大小不得超过100kb", "pleaseSelectCurrentRole": "请选择当前角色", "pleaseEnterNamePhoneNumberOrAccount": "请输入姓名/手机号/账号", "pleaseEnterLoginAccount": "请输入登录账号", "pleaseEnterLoginPassword": "请输入登录密码", "delete": "删除", "view": "查看", "download": "下载", "create": "新建", "end": "结束", "warning": "警告", "day": "天", "component": "组件", "noData": "暂无数据", "webTerminal": "web终端", "clusterList": "集群列表", "clusterHostList": "集群主机列表", "installComponent": "安装组件", "userManager": "用户管理", "componentConfigRecord": "组件配置记录", "viewComponent": "查看组件", "audit": "审计", "alarmService": "告警服务", "hostDetail": "主机详情", "hostRoleList": "主机角色列表", "productDoc": "产品文档", "diagnosticReport": "诊断报告", "notDeployed": "未部署", "deploying": "部署中", "deployFailed": "部署失败", "running": "运行中", "stopped": "已停止", "beingStart": "正在启动", "beingStop": "正在停止", "beingDelet": "正在删除", "startFailed": "启动失败", "stopFailed": "停止失败", "deleteFailed": "删除失败", "beingScal": "正在扩缩容", "scaleFailed": "扩缩容失败", "online": "在线", "offline": "离线", "good": "良好", "fault": "故障", "abnormal": "异常", "serious": "严重", "uninstalled": "已卸载", "frozen": "冻结", "normal": "正常", "installing": "正在安装", "installFailed": "安装失败", "installComplete": "安装完成", "open": "开启", "yes": "是", "no": "否", "unbindK8s": "解绑k8s", "bindK8s": "绑定k8s", "rename": "重命名", "clusterUnbind": "集群解绑", "start": "启动", "stop": "停止", "restart": "重启", "instanceAddress": "实例地址", "configOperation": "配置操作", "configDownload": "配置下载", "viewEdit": "查看修改", "configSnapshot": "配置快照", "versionRollback": "版本回滚", "permissionEdit": "权限修改", "resetPassword": "重置密码", "unfreeze": "解冻", "monitorChart": "监控图表", "roleLogFile": "角色日志文件", "none": "暂无", "viewProgress": "查看进度", "nodeScheduling": "节点调度", "update": "更新", "viewEvent": "查看事件", "clusterName": "集群名称", "bindHost": "绑定主机", "operation": "操作", "status": "状态", "hostName": "主机名称", "belongCluster": "所属集群", "memory": "内存", "kubernetesName": "Kubernetes名称", "kubernetesClusterAddress": "Kubernetes集群地址", "authMethod": "认证方式", "createTime": "创建时间", "componentName": "组件名称", "containerName": "容器名称", "logLevel": "日志级别", "time": "时间", "message": "消息", "nodeName": "节点名称", "namespaceName": "命名空间名称", "podName": "pod名称", "addTime": "添加时间", "customName": "自定义名称", "deploymentMethod": "部署方式", "roleName": "角色名称", "instance": "实例", "versionNumber": "版本号", "operator": "操作人", "operationTime": "操作时间", "serviceType": "服务类型", "description": "描述", "serviceVersion": "服务版本", "stepContent": "步骤内容", "startTime": "开始时间", "runtimes": "运行时长", "currentRoles": "现有角色", "addRole": "添加角色", "ipAddress": "IP地址", "k8sName": "K8s名称", "k8sNamespace": "K8s命名空间", "bindTime": "绑定时间", "employeeName": "员工姓名", "currentRole": "当前角色", "loginAccount": "登录账号", "mobile": "手机", "account": "账号", "alarmTime": "告警时间", "alarmContent": "告警内容", "alarmCategory": "告警类别", "executor": "执行用户", "operationContent": "操作内容", "operationAttribute": "操作属性", "executionResult": "执行结果", "rackNumber": "机架号", "perceptionStatus": "感知状态", "deployedHostCount": "部署主机数", "remark": "备注", "rack": "机架", "physicalSpace": "物理空间", "serialNumber": "序号", "autoScaling": "自动扩缩容", "eventTime": "事件时间", "operationType": "操作类型", "endTime": "结束时间", "hostCounts": "主机数量", "componentCount": "组件数量", "alarmCount": "告警数量", "k8sInfo": "K8s信息", "clusterResourceUsage": "集群资源使用情况", "specifiedHost": "指定主机", "selectCustomComponentName": "请选择您需要扩缩容的自定义组件名称", "addServiceType": "添加服务类型", "selectServiceType": "请选择您要添加的服务类型，一次只能安装一个组件", "versionSelection": "版本选择", "componentInstallation": "组件安装", "customRoleAllocation": "自定义角色分配", "confirmationPrompt": "确认提示", "createClusterOneComponentModifiable": "创建集群时，一次只能添加一个组件服务；配置组件参数均由后端传入，按默认值进行预填写，支持修改", "installationInformation": "安装信息", "addFailed": "添加失败", "addSuccess": "添加成功", "componentAdded": "组件已添加", "currentConfiguration": "当前配置", "customDeployment": "自定义部署", "roleConfiguration": "角色配置", "setScale": "设置规模", "parameterSetting": "参数设置", "parameterConfiguration": "参数配置", "addUsers": "添加用户", "editUser": "编辑用户", "modifyMobileNumber": "修改手机号", "modifyEmailAddress": "修改邮箱地址", "welcomeBack": "欢迎回来", "viewK8s": "查看K8s", "configureComponentParameters": "配置组件参数", "hostBinding": "主机绑定", "configureServiceParameters": "配置服务参数", "modifyPassword": "修改密码", "viewInstance": "查看实例", "modifyPermission": "修改权限", "monitorNodeCount": "监控节点数量", "roleLog": "角色日志", "diskSystem": "磁盘系统", "roleList": "角色列表", "diskUsage": "磁盘使用情况", "jobConfiguration": "Job配置", "editK8s": "编辑k8s", "globalException": "[全局异常]", "namespaceList": "Namespace列表", "hdfsTask": "HDFS任务", "hbaseTask": "Hbase任务", "hiveTask": "Hive任务", "dashboard": "仪表盘", "browserUpgradeNotice": "系统识别当前浏览器版本较低，部分页面展示可能会缺失，请升级当前浏览器版本至69以上", "recommendChrome": "为了更好的使用体验，建议使用Google Chrome浏览器", "tenant": "所属租户", "rackLabel": "机架", "addStatus": "添加状态", "traceLevel": "TRACE(提示)", "debugLevel": "DEBUG(提示)", "infoLevel": "INFO(提示)", "warnLevel": "WARN(一般)", "errorLevel": "ERROR(危险)", "fatalLevel": "FATAL(高危)", "startSyncTask": "是否启动当前的同步任务", "stopSyncTask": "是否终止当前的同步任务", "restartSyncTask": "是否重启选中的组件？", "selectedComponent": "选中的组件", "componentDependency": "当前组件 ({value}) 被 ({values}) 依赖", "restartBeforeStopDependencies": "重启前建议先停止依赖它的组件", "selectAllSelectedRoles": "查看全部已选角色(value)", "rollingRestartComponent": "是否滚动重启{value}组件", "viewRestartProgress": "重启后，可在更多操作里查看进度详情", "networkOutputAlarm": "当主机的网络输出速率达到设定阈值时触发告警，单位为MB/s", "networkInputAlarm": "当主机的网络输入速率达到设定阈值时触发告警，单位为MB/s", "cpuUsageAlarm": "当主机的CPU使用率达到设定阈值时触发告警", "memoryUsageAlarm": "当主机的内存使用率达到设定阈值时触发告警", "connectionAlarm": "当主机无法与目标主机建立网络连接或连接中断时触发告警，该项取值为0和1,0表示未建立连接或连接已中断。1表示连接已建立", "diskReadSpeedAlarm": "当主机的磁盘读取速率达到设定阈值时触发告警，单位为MB/s", "diskWriteSpeedAlarm": "当主机的磁盘写入速率达到设定阈值时触发告警，单位为MB/s", "componentStatusAlarm": "在指定间隔内,组件状态为异常或者故障时触发告警。", "logLevelAlarm": "在指定间隔内,监听到组件产生指定等级的日志时触发告警(同一间隔可能包含多条日志)。", "auditLogAlarm": "当前集群若执行了选中的对应等级的审计操作，触发审计日志告警，同一操作下告警会根据告警间隔静默后再次发送。", "logKeywordAlarm": "当前集群日志匹配到选中关键词触发日志告警，同一关键词会根据告警间隔静默后再次发送。", "alertNotificationSettings": "告警通知设置", "clusterAlertNotificationSettings": "集群告警通知设置", "pleaseAddRobot": "请添加机器人", "addPeople": "添加人员", "unselected": "未选择", "selected": "已选择", "emailConfigurationTestResult": "邮件配置测试结果", "testEmailRequestSentSuccessfully": "测试邮件请求发送成功", "alertAutoRelease": "当该告警无需手动消除时，告警规则默认于2个轮巡周期后不触发时将状态置为Released。", "active": "活跃", "released": "解除", "alertTitle": "告警标题", "alertLevel": "告警等级", "alertStatus": "告警状态", "confirmClearSelectedAlertRecords": "是否选择清除选中的告警记录？", "doNotRecommendClearIfUnprocessed": "若告警未处理，不建议清除", "hostNetworkIn": "主机网络I(MB)告警", "hostNetworkOut": "主机网络O(MB)告警", "hostCpuAlert": "主机CPU告警（%）", "hostMemoryAlert": "主机内存告警（%）", "hostConnectivityAlert": "主机联通性告警", "hostDiskOutAlert": "主机磁盘O告警", "hostDiskInAlert": "主机磁盘I告警", "componentRunningStatusAlert": "组件运行状态告警", "componentLogAlert": "组件日志告警", "auditLogAlert": "审计日志告警", "logKeywordAlert": "日志关键词告警", "enabledState": "开启状态", "pleaseSelectIntervalTime": "请选择间隔时间", "pleaseSelectAutoClear": "请选择是否自动清除", "greaterThan": "大于", "equalTo": "等于", "lessThan": "小于", "pleaseEnterThresholdValue": "请输入对应阈值", "keywordSetting": "关键词设置", "cruxword": "关键词", "confirmDeleteKeyword": "是否删除关键词?", "onlyEnglishInputAllowed": "只能输入英文、空格、数字，最长50个字符", "checkAllHosts": "检查所有主机", "role": "角色", "disk": "磁盘", "smallFileDefinitionPolicy": "小文件定义策略", "percentageOfBlockSize": "按块大小的百分比", "customSize": "按自定义大小", "smallFileDefinition": "小文件定义", "lessThanBlockSize": "小于块大小的", "cannotExceed100": "不能大于100", "chart": "图表", "componentAbnormalSituation": "组件异常情况", "hostConfigurationIssue": "主机配置问题", "showOnly": "仅展示 {value} 个配置错误问题，点击收起", "warningIssues": "有 {value} 条警告类问题，点击展示", "more": "更多", "logTimeRange": "日志时间段", "storageAndComputationSeparation": "存算分离", "storageAndComputationIntegration": "存算一体", "noDatas": "无数据", "dynamicResourceAllocation": "动态资源分配", "logCollection": "日志收集", "editJob": "编辑Job", "addApplication": "添加 Application", "cloneResourcePool": "克隆资源池", "clone": "克隆", "editResourcePool": "编辑资源池", "createResourcePool": "创建资源池", "poolName": "池名称", "schedulingPolicy": "计划策略", "preemption": "抢占", "pleaseEnterPoolName": "请输入池名称", "charactersLimitedToAlphanumeric": "仅限字母数字字符", "and": "和", "mustNotStartWithRoot": "不得以根开头。", "fairSchedulingBasedOnCpuAndMemory": "根据 CPU 和内存公平调度资源。(建议)", "recommended": "建议", "fairSchedulingBasedOnMemory": "仅根据内存公平调度资源。", "fifo": "先进先出。", "setToFalseProhibitsPreemptionFromOtherPools": "设置为 false 将禁止其他池从该池抢占。默认值为 true。在高优先级池或 SLA 池上，应将该值设置为 false。", "fairSharePreemptionThreshold": "公平份额抢占阈值", "valueBetweenZeroAndOne": "介于0和1之间的值。如果该值设置为 x，且资源池的公平份额为 F，则当分配小于(x * F)时，将开始从其他资源池抢占资源。", "fairSharePreemptionTimeout": "公平份额抢占超时", "minimumSharedPriorityTimeout": "最小共享优先权超时时间", "inPoolAttemptsToPreemptContainers": "在池尝试抢占容器以从其他池中获得资源前，池在其最小份额中保持的秒数。(可选)", "choosable": "可选", "weight": "权", "relatedToSharingWithOtherPools": "与其他池相关的资源共享", "minimumResources": "最小资源数", "virtualCores": "虚拟内核", "minimumNumberOfCpuAndMemory": "这个池可用的 CPU 和内存的最小数量。这优先于基于权重的共享。(可选)", "maximumResources": "最大资源数", "switchToPercentage": "切换到百分比", "switchToAbsoluteValue": "切换到绝对值", "maximumNumberOfCpuAndMemory": "此池可用的 CPU 和内存的最大数量。这比基于权重的共享具有优先权。(可选)", "maximumRunningApplications": "正在运行的应用程 序最大数量", "max": "最大", "limitOfApplicationMaster": "限制可用于运行 ApplicationMaster 的资源池公平份额的比例。例如，如果设为 1.0，叶池中的 ApplicationMaster 最多可使用 100% 的内存和 CPU公平份额。如果值为 -1.0，将禁用 ApplicationMaster 份额的监控。默认值为 0.5。", "defaultSettings": "默认设置", "defaultSchedulingPolicy": "默认调度策略", "placementRules": "放置规则", "userLimits": "用户限制", "editPlacementRule": "编辑放置规则", "createPlacementRule": "创建放置规则", "pleaseSelectPlacementRule": "请选择放置规则", "specifyPoolName": "指定池名称", "priority": "优先级", "createPoolIfNotExist": "如果该池不存在则进行创建", "useSpecifiedPoolAsParent": "使用与指定的池匹配的资源池作为父池，然后使用与该用户名匹配的子池。", "usePrimaryGroupPool": "使用与该用户的主要组匹配的资源池", "usePrimaryGroupPoolThenUserNameSubPool": "使用与该用户的主要组匹配的资源池，然后使用与该用户名匹配的子池", "useSecondaryGroupPool": "使用与该用户的次要组之一匹配的资源池。", "useSecondaryGroupPoolThenUserNameSubPool": "使用与该用户的次要组之一匹配的资源池，然后使用与该用户名匹配的子池", "useSpecifiedPool": "使用指定的资源池。", "useUserNamePool": "使用与该用户名匹配的资源池。(不推荐)", "useRuntimeSpecifiedPool": "使用在运行时指定的资源池", "onlyPositiveIntegersGreaterThanZero": "仅支持大于0的正整数", "alphanumericCharactersDashUnderscoreDot": "仅限字母数字字符、-、_和.。不得以根开头。", "priorityExists": "优先级已存在", "ifPoolDoesNotExistCreateIt": "如果该池不存在则进行创建。", "useSpecifiedPoolRootXxx": "使用指定的资源池，root.{value}", "asParentPoolThenUserNameSubPool": "使用root.{value}作为父池，然后使用与该用户名匹配的子池", "confirmDeletePlacementRule": "是否删除该放置规则", "editScheduleMode": "编辑计划模式", "createScheduleMode": "创建计划模式", "configurationSet": "配置集", "existingConfigurationSet": "已有配置集", "pleaseEnterConfigurationName": "请输入配置名称", "initializeFrom": "初始化自", "pleaseSelectConfigurationSet": "请选择配置集", "selectedConfigurationSet": "已选配置集", "startTimeMustBeLessThanEndTime": "开始时间必须小于结束时间", "pleaseFillInConfigurationSet": "请填写配置集", "pleaseSelectInitializationConfigurationSet": "请选择初始化配置集", "pleaseEnterConfigurationSetName": "请输入配置集名称", "configurationSetNameLength1To20": "配置集名称为1-20个字符", "pleaseSelectExistingConfigurationSet": "请选择已有配置集", "pleaseSelectWeeklyTriggerTime": "请选择每周触发时间", "pleaseSelectMonthlyTriggerTime": "请选择每月触发时间", "startDateCannotBeGreaterThanEndDate": "开始日不能大于结束日", "scheduleMode": "计划模式", "confirmDeleteScheduleMode": "是否删除该计划模式", "applyWhenNoOtherRulesApply": "在其他规则皆不适用的情况下应用", "monday": "星期一", "tuesday": "星期二", "wednesday": "星期三", "thursday": "星期四", "friday": "星期五", "saturday": "星期六", "sunday": "星期日", "nextDayAtMidnight": "次日0", "dailyRepeatFromXxToXxFirstEffectiveAtXxx": "每天的 {beginTime}点00分 到 {endTimeStr}点00分 (CST)重复，首次于 {startTime} 开始生效。", "weeklyRepeatOnXxFromXxToXxFirstEffectiveAtXx": "每周于 {dayOfWeek} 的 {beginTime}点00分 到 {endTimeStr}点00分 (CST)重复，首次于 {startTime} 开始生效。", "monthlyRepeatFromDayXxToDayXxFromXxToXxFirstEffectiveAtXx": "每月的 第{dayOfMonth0}天 到 第{dayOfMonth1}天 的 {beginTime}点00分 到 {endTimeStr}点00分 (CST) 重复，首次于 {startTime} 开始生效。", "defaultUserLimit": "用户默认限制", "maximumRunningApplicationsPerUser": "正在运行的应用程序最大数量", "onlyPositiveIntegersGreaterThanOrEqualToZero": "仅支持大于等于0的正整数", "editUserLimit": "编辑用户限制", "createUserLimit": "创建用户限制", "userName": "用户名称", "pleaseEnterUserName": "请输入用户名称", "onlyEnglishNumbersBreveUnderscore": "仅英文数字字符、短折号、下划线", "userLimit": "用户限制", "confirmDeleteUserLimit": "是否删除该用户限制", "productUserManual": "产品用户手册", "productTechnicalWhitePaper": "产品技术白皮书", "editRack": "编辑机架", "editTenant": "编辑租户", "createTenant": "创建租户", "tenantResponsiblePerson": "租户负责人：", "phoneNumbers": "电话号码：", "format6To16DigitsLettersAndEnglish": "6-16位由数字，字母，英文组成，字符支持_ -，不能以_开头", "format6To20DigitsAndEnglishLetters": "6-20位，由数字和英文字母组成，支持英文大小写", "format2To16": "2-16位", "pleaseEnterTenantName": "请输入租户名称", "format2To16ChineseAndEnglishLetters": "2-16位，由中文和英文字母组成，支持英文大小写和空格", "format6To16ChineseAndEnglishLetters": "6-16位，由中文和英文字母组成，支持英文大小写和空格", "pleaseEnterTenantResponsiblePerson": "请输入租户负责人", "hostResources": "主机资源", "resourceAllocationLimit": "资源分配限制：", "unlimitedResources": "不限资源", "inputEmployeeName": "请输入员工姓名", "inputPhoneNumber": "请输入手机号码", "inputEmail": "请输入邮箱地址", "selectComponentName": "请选择组件名称", "selectRoleName": "请选择角色名称", "inputNodeCount": "请输入节点个数", "inputUserName": "请输入用户名", "inputPassword": "请输入密码", "inputPrivilegedUser": "请输入特权用户", "inputSSHPortNumber": "请输入SSH端口号", "inputConfigInfo": "请输入配置信息", "modifyReason": "修改原因", "inputClusterName": "请输入集群名称", "plSelectCluster": "请选择集群", "all": "全部", "cluster": "集群", "host": "主机", "monitoring": "监控", "logs": "日志", "userList": "用户列表", "version": "版本", "accountInfo": "账号信息", "logout": "退出登录", "goToClusterList": "前往集群列表", "clusterOverview": "集群概览", "currentCluster": "当前集群", "hostList": "主机列表", "serviceComponents": "服务组件", "hostMonitoring": "主机监控", "clusterMonitoring": "集群监控", "componentMonitoring": "组件监控", "rackManagement": "机架管理", "logList": "日志列表", "auditList": "审计列表", "freezeAccount": "是否冻结该员工账号?", "resetEmployeePassword": "是否重置该员工密码?", "deleteAccount": "是否删除该员工账号?", "checkComponentConfig": "请检查每个组件的配置是否正确", "selectK8s": "请选择K8s", "clusterNameLength": "集群名称长度最长不超过{number}位", "confirmLeavePage": "请确认是否离开本页面?", "inputHostToUnbind": "请输入要解绑的主机名称", "reenterHostName": "为防止意外操作，请再次输入主机名称", "changesMayNotBeSaved": "系统可能不会保存您所做的更改。", "confirmUnbindHost": "是否确定对主机(xxxx)进行集群解绑？", "unbindHostDefaultCluster": "解除绑定后，主机将归入默认集群中，可后续添加使用", "componentCannotBeDeleted": "当前组件 (xxxx) 不可删除.", "existentialDependence": "存在组件依赖（xxxx)", "runningComponentCannotBeDeleted": "由于当前组件正在运行中，暂不可删除", "restartComponentService": "是否重启当前组件（ xxxx）服务?", "restartRoleService": "是否重启当前角色（xxxx）服务?", "confirmRollbackToVersion": "是否确认配置回滚至该版本（xxxx)?", "confirmInstallation": "是否确认安装?", "startInstallationConfirmation": "开始安装后，当前配置将仅支持查看，请确认无误后点击确定开始。", "customComponentNameMaxLength": "自定义组件名称最多输入xxxx个字符!", "boundHostCannotBeDeleted": "已绑定主机，安装组件或绑定K8s的集群不可删除", "clusterCannotBeDeleted": "当前集群 (xxxx) 不可删除.", "dependencyCheck": "依赖检查中", "createOrExpandCluster": "使用主机来创建新集群或扩展现有集群；如果集群使用 Kerberos 身份验证，请确保 Kerberos 软件包安装在新主机上，否则新主机上的服务将无法运行。", "installAgentOnNewHost": "允许您在新主机上安装Agent。您可以保留新主机以供将来添加到集群中，也可以将新主机添加到现有集群中。", "invalidPhoneNumber": "手机号格式不正确", "invalidEmail": "邮箱地址错误，请重新输入", "unknownReason": "未知原因", "confirmDeleteHost": "是否确定删除当前主机(xxxx)？", "hostUnbound": "主机已解绑", "inputHostToDelete": "请输入要删除的主机名称", "confirmDeleteCluster": "是否确定删除当前集群(xxxx)?", "reenterClusterName": "为防止意外操作，请再次输入集群名称", "inputClusterToDelete": "请输入要删除的集群名称", "clusterDeletionSuccess": "集群删除成功", "selectServiceTypeToAdd": "请选择添加的服务类型", "inputCustomComponentName": "请输入自定义组件名称", "noLeadingOrTrailingSpaces": "不能以空格开头和结尾", "selectVersion": "请选择版本", "missingOrInvalidServiceParameters": "配置服务参数列表有必填项未填写或不符合要求项!", "validateHostRoleAssignment": "请检查每个角色分配的主机是否符合标准", "selectAtLeastOneHost": "至少选择一台主机", "confirmSubmitForScaling": "提交后，系统将进行扩缩容，用时可能花费几分钟", "confirmSubmission": "是否确认提交？", "clusterRenameSuccess": "集群重命名成功", "configRollbackSuccess": "配置回滚成功", "hostAdditionFailure": "主机添加失败，请检查相关参数", "startComponentService": "是否启动当前组件（xxxx）服务?", "stopComponentService": "是否停止当前组件（xxxx）服务?", "confirmDeleteComponentService": "是否确定删除当前组件服务(xxxx)?", "inputCustomComponentToDelete": "请输入要删除的自定义组件名称", "reenterCustomComponentName": "为防止意外操作，请再次输入自定义组件名称", "selectComponent": "请选择组件", "adding": "正在添加中", "selectK8sCluster": "请选择K8s集群", "inputNumberOfHosts": "请输入主机数", "modifyPhoneNumberSuccess": "修改手机号成功", "modifyPhoneNumberFailure": "修改手机号失败", "modifyEmailFailure": "修改邮箱地址失败", "emailSaved": "邮箱地址已保存", "inputNewPassword": "请输入新密码", "passwordMismatch": "两次输入密码不一致", "reenterPassword": "请再次输入密码", "inputOriginalPassword": "请输入原密码", "passwordModificationFailed": "修改密码失败.服务器错误", "passwordModificationSuccess": "修改密码成功，请重新登录", "permissionModificationSuccess": "权限修改成功", "addUserSuccess": "新增用户成功", "editUserSuccess": "编辑用户成功", "emailMaxLength": "邮箱长度不超过30位", "validEmailRequired": "请输入正确的邮箱地址", "validPhoneNumberRequired": "请输入正确的手机号码", "charLimit1To20": "1-20位字符，不支持中英文以外的字符输入", "passwordLength6To20": "密码长度为6～20个位", "charLimit3To20": "3-20位字符", "charAndSymbolLimit": "仅支持数字，英文大小写字母，下划线_和英文短划线“-”", "freezeSuccess": "冻结成功", "resetPasswordSuccess": "重置密码成功", "deleteSuccess": "删除成功", "unfreezeSuccess": "解冻成功", "unfreezeAccount": "是否解冻该员工账号?", "selectFile": "请选择文件", "charAndSymbolLimit2": "3-20位字符", "k8sAdditionSuccess": "K8s添加成功", "selectAuthenticationType": "请选择认证类型", "configSaveAndPublishFailure": "配置保存并发布失败", "configSaveAndPublishSuccess": "配置保存并发布成功", "restartComponentForChangesToTakeEffect": "保存发布后，需重启组件，配置方可生效", "saveAndPublishCurrentConfig": "是否保存并发布当前配置文件？", "configSaveFailure": "配置保存失败", "configSaveSuccess": "配置保存成功", "serverException": "服务器异常，请联系管理员", "connectionError": "连接出错(xxxx)!", "httpVersionNotSupported": "HTTP版本不受支持(505)", "networkTimeout": "网络超时(504)", "serviceUnavailable": "服务不可用(503)", "networkError": "网络错误(502)", "serviceNotImplemented": "服务未实现(501)", "serverError": "服务器错误(500)", "requestTimeout": "请求超时(408)", "requestNotFound": "请求出错(404)", "noPermission": "暂无权限", "sessionExpired": "登陆过期", "badRequest": "请求错误(400)", "validSSHPortRequired": "请填写正确的SSH端口号", "mandatorySelection": "此为必选项", "requiredField": "此为必填项", "noChartsAvailable": "暂无图表", "advancedSettings": "您可通过高级设置功能，进行自定义集群分配", "unbound": "已解绑", "bindingSuccess": "绑定成功", "hostBindingFailure": "主机绑定失败", "roleAllocationInstructions": "您可以在此处自定义新服务的角色分配，但请注意，如果分配不正确（例如，分配到某个主机上的角色太多），性能会受到影响。", "inputRoleNodeCount": "请输入角色节点个数", "positiveIntegerOnly": "【xxx】节点只能输入正整数", "testConnectivityFirst": "请先测试连通性", "connectivityTestSuccess": "测试连通成功，可继续操作", "connectivityTestFailure": "测试连通失败，请检查配置", "timeoutOrServerException": "请求超时或服务器异常，请检查网络或联系管理员", "added": "已添加", "fetchingInformation": "正在获取信息...", "fileSizeLimit": "文件大小不能超过100kb", "confirmComponentInstallation": "请确认是否已安装相关组件？", "componentDependencyNotice": "当前选中组件(xxxx)依赖于({string})，安装xxxx前，请先完成xxx安装；", "namingRules": "名称由中文、字母、数字、下划线，连字符‘-’组成，不能以连字符和下划线开头和结尾。", "queueNameExists": "队列名已存在", "confirmRackDeletion": "是否确定删除当前机架?（机架号xxx)", "rackDeletionWarning": "删除机架后，原机架和主机的部署关系将进行解除", "reenterRackNumber": "为了防止意外操作,请输入机架号", "inputRackNumber": "请输入机架号", "confirmHostUnbinding": "是否确定解绑选中主机？", "hostUnbindingResult": "解绑后主机回归默认资源池，可进行删除操作", "selectNode": "请选择节点", "currentComponentVersion": "当前组件版本vxxx", "viewVersion": "查看版本", "selectUpgradeVersion": "选择升级版本", "success": "成功", "failure": "失败", "integratedDataLakes": "已集成数据湖", "availableHosts": "可用主机", "noRequired": "非必选", "selectedXHosts": "已选择xx台", "addHostsToCluster": "添加一组主机组成集群并安装Agent服务", "oneMonthDataLimit": "只能筛选查询近一个月的数据", "hours": "小时", "minutes": "分钟", "latestVersion": "当前最新版本", "latelyModifiedTime": "最近修改时间", "defaultValue": "默认值", "noContent": "暂无内容", "to": "至", "selectXToYHosts": "最少选择x台，最多选择x台", "noLimit": "X (暂无限制)", "lessThan001": "不足0.01", "address": "地址", "hostIpAndPort": "主机ip+端口", "serviceNameAndPort": "ServiceName+端口", "installationOrder": "安装顺序", "newConfigFileVersion": "最新配置文件版本，需组件重启后方可生效", "appliedConfigFileVersion": "实际应用的配置文件版本", "highRisk": "高危", "dangerous": "危险", "general": "一般", "tip": "提示", "adds": "新增", "amend": "修改", "k8SCreatePermission": "K8S创建权限：", "k8SList": "K8S列表", "namespaceResource": "Namespace资源", "isClusterAlertAndHealthCheckDisabled": "开启状态下 当前集群不进行告警规则及健康巡检的校验项。", "refreshingDynamicResourcePools": "刷新动态资源池", "createScheduleRule": "创建计划规则", "days": "日", "most": "至", "weekly": "每周", "monthly": "每月", "everyday": "每日", "Monday": "周一", "Tuesday": "周二", "Wednesday": "周三", "Thursday": "周四", "Friday": "周五", "Saturday": "周六", "Sunday": "周日", "fairShareDurationBeforePreemption": "资源池尝试抢占容器以从其他资源池中获得资源前，其处于公平份额状态的秒数。", "addKey": "新增关键词", "alertPushByHighestSeverity": "当选择多个等级时，触发时按照高危、警告、危险、一般顺序以最高等级进行告警推送。", "emailForTestResult": "测试结果将会以邮件的形式发送到您的邮箱， 请输入邮箱地址。", "alertNotificationLevel": "告警通知等级", "feishu": "飞书", "qiyeweixin": "企业微信", "dingding": "钉钉", "recipient": "收件人", "testEmail": "邮件测试", "mail": "邮件", "sendTestMessage": "发送测试消息", "addRobot": "添加机器人", "qiyeweixinRobot": "企业微信机器人", "dingdingRobot": "钉钉机器人", "enterSecretKey": "请输入密钥", "interval30Minutes": "30分钟", "interval1Hour": "1小时", "interval2Hours": "2小时", "interval6Hours": "6小时", "interval12Hours": "12小时", "interval1Day": "1天", "interval7Days": "7天", "componentVersion": "组件版本", "componentStatus": "组件状态", "stopDependenciesBeforeRestart": "重启前建议先停止上述依赖组件", "isSelected": "已选中", "confirmDeleteSelectedComponent": "是否确定删除选中组件？", "componentDependency1": "当前选中组件({ value})依赖于({string})，安装{value}前，请先完成{string}安装；", "rexNamespaceName": "名称由中文、字母、数字、下划线，连字符‘-’组成，不能以连字符和下划线开头和结尾。", "restartableAfterTermination": "终止后可重新启动该同步任务", "confirmDeleteCurrentTask": "是否确定删除当前任务?", "storageSystem": "存储系统", "tieredStoragePolicy": "存储分级策略", "executionCycle": "执行周期", "executionTime": "执行时间", "hotStorageCluster": "热数据存储集群", "hotDataClusterDirectory": "热数据集群目录", "coldDataClusterDirectory": "冷数据集群目录", "hotDataHDFSName": "热数据所在HDFS名称", "coldDataHDFSName": "冷数据所在HDFS名称", "coldDataStorageCluster": "冷数据存储集群", "byTime": "按时间", "manageNamespace": "Namespace管理", "inspectionDescription": "巡检项说明", "inspectionCycle": "巡检周期", "automaticInspection": "自动巡检", "dailyInspection": "每天一次", "weeklyInspection": "每周一次", "monthlyInspection": "每月一次", "inspectionStartTime": "巡检开始时间", "inspectionDurationLimit": "分钟，0为无限制", "inspectionDurationLimitMinutes": "巡检持续限制", "clientToDataSource": "客户端与数据源", "selectNodeType": "选择节点类型", "cpuResources": "CPU资源", "hostAliasCharacterLimit": "主机别名支持1-50字符", "alertInterval": "告警间隔", "interval5Min": "每隔5分钟", "interval10Min": "每隔10分钟", "interval15Min": "每隔5分钟", "interval30Min": "每隔30分钟", "interval3Hours": "每隔3小时", "requiresManualClear": "是否需要手动消除", "notificationMethod": "通知方式", "isAdmin": "是否具备admin权限", "belongingRegion": "所属区域", "clusterManagement": "集群管理", "alreadyBound": "已绑定", "unbound2": "未绑定", "bindResources": "绑定资源", "standardEdition": "标准版", "lightweightVersion": "轻量版", "deployModeTip": "Kubernetes: 所有组件均安装在k8s集群上；docker: 所有组件均以容器模式安装在主机上", "isDeleteCurrentRole": "是否删除当前角色($name)?", "isDeleteCurrentRoleGroup": "是否删除角色组($name)?", "hideAllAlarmDetails": "隐藏全部告警详情", "viewAllAlarmDetails": "查看全部告警详情", "addAlarmItems": "增加告警项", "alarmComponent": "告警组件", "alarmOperation": "告警操作", "warningKeywords": "告警关键词", "licenseTip1": "许可将于 $date 生效", "licenseTip2": "许可已于 $date 失效", "licenseTip3": "您的license还有$date天过期，请尽快续期！", "namespacePermissionTip": "k8s集群所属serviceaccount账号不具备添加namespace权限，请联系k8s集群管理员添加"}