'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./images/index.js');
var listLight = require('./images/list-light.js');
var unListLight = require('./images/unList-light.js');
var echarts = require('./images/echarts.js');
var file = require('./images/file.js');
var powerLight = require('./images/power-light.js');
var feature = require('./images/feature.js');
var bloodLight = require('./images/blood-light.js');
var model = require('./images/model.js');
var networkLight = require('./images/network-light.js');
var _404Light = require('./images/404-light.js');
var _405Light = require('./images/405-light.js');
var message = require('./images/message.js');
var image = require('./images/image.js');
var imagePlaceholder = require('./images/imagePlaceholder.js');
var api = require('./images/api.js');
var listDark = require('./images/list-dark.js');
var unListDark = require('./images/unList-dark.js');
var powerDark = require('./images/power-dark.js');
var bloodDark = require('./images/blood-dark.js');
var networkDark = require('./images/network-dark.js');
var _404Dark = require('./images/404-dark.js');
var _405Dark = require('./images/405-dark.js');

const imgConfig = {
  light: {
    list: listLight["default"],
    unList: unListLight["default"],
    echarts: echarts["default"],
    file: file["default"],
    power: powerLight["default"],
    feature: feature["default"],
    blood: bloodLight["default"],
    model: model["default"],
    network: networkLight["default"],
    "404": _404Light["default"],
    "405": _405Light["default"],
    message: message["default"],
    image: image["default"],
    imagePlaceholder: imagePlaceholder["default"],
    api: api["default"]
  },
  dark: {
    list: listDark["default"],
    unList: unListDark["default"],
    echarts: echarts["default"],
    file: file["default"],
    power: powerDark["default"],
    feature: feature["default"],
    blood: bloodDark["default"],
    model: model["default"],
    network: networkDark["default"],
    "404": _404Dark["default"],
    "405": _405Dark["default"],
    message: message["default"],
    image: image["default"],
    imagePlaceholder: imagePlaceholder["default"],
    api: api["default"]
  }
};
const descConfig = {
  "zh-CN": {
    list: "\u6682\u65E0\u6570\u636E",
    unList: "\u6682\u65E0\u5185\u5BB9",
    echarts: "\u6682\u65E0\u56FE\u8868",
    file: "\u6682\u672A\u6DFB\u52A0\u6587\u4EF6",
    power: "\u6682\u65E0\u6743\u9650",
    feature: "\u6682\u65E0\u8BE5\u529F\u80FD",
    blood: "\u6682\u65E0\u8840\u7F18\u4FE1\u606F",
    model: "\u6682\u65E0\u6A21\u578B\uFF0C\u8BF7\u5148\u65B0\u5EFA",
    network: "\u7F51\u7EDC\u4E2D\u65AD\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5",
    "404": "404",
    "405": "405",
    message: "\u6682\u65E0\u6D88\u606F",
    image: "\u6682\u65E0\u56FE\u7247\uFF0C\u8BF7\u5148\u6DFB\u52A0",
    imagePlaceholder: "\u56FE\u7247\u5360\u4F4D",
    api: "\u6682\u65E0API\uFF0C\u8BF7\u5148\u65B0\u5EFA"
  },
  "en-US": {
    list: "No data",
    unList: "No content",
    echarts: "No chart",
    file: "No files added",
    power: "No permission",
    feature: "This function is not available",
    blood: "No bloodline information",
    model: "There is no model, please create a new one first",
    network: "The network is interrupted, please refresh and try again.",
    "404": "404",
    "405": "405",
    message: "No news",
    image: "There are no pictures, please add them first",
    imagePlaceholder: "Picture placeholder",
    api: "There is no API, please create a new one first"
  }
};

exports.descConfig = descConfig;
exports.imgConfig = imgConfig;
//# sourceMappingURL=constants.js.map
