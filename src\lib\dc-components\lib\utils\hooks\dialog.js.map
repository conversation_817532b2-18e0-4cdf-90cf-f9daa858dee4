{"version": 3, "file": "dialog.js", "sources": ["../../../../../packages/utils/hooks/dialog.ts"], "sourcesContent": ["import { ref } from \"vue\";\r\n\r\nexport function useDialog({\r\n  cancel,\r\n  confirm,\r\n  show,\r\n  defaultData,\r\n}: {\r\n  cancel?: (val?: any, dialogData?: any) => void;\r\n  confirm?: (val?: any, dialogData?: any) => void;\r\n  show?: (val?: any) => void;\r\n  defaultData?: any;\r\n} = {}) {\r\n  const visible = ref(false);\r\n  const dialogData = ref<any>(defaultData);\r\n\r\n  const showDialog = (val?: any) => {\r\n    visible.value = true;\r\n    dialogData.value = val;\r\n    if (show && typeof show === \"function\") {\r\n      show(val);\r\n    }\r\n  };\r\n\r\n  const onCancel = (val?: any) => {\r\n    visible.value = false;\r\n    if (cancel && typeof cancel === \"function\") {\r\n      cancel(val, dialogData.value);\r\n    }\r\n  };\r\n\r\n  const onConfirm = (val?: any) => {\r\n    onCancel();\r\n    if (confirm && typeof confirm === \"function\") {\r\n      confirm(val, dialogData.value);\r\n    }\r\n  };\r\n\r\n  return {\r\n    showDialog,\r\n    onCancel,\r\n    onConfirm,\r\n    visible,\r\n    dialogData,\r\n  };\r\n}\r\n"], "names": ["ref"], "mappings": ";;;;;;AAE0B,SAAA,SAAA,CAAA;AAAA,EACxB,MAAA;AAAA,EACA,OAAA;AAAA,EACA,IAAA;AAAA,EACA,WAAA;AAAA,CAAA,GAME,EAAI,EAAA;AACN,EAAM,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AACzB,EAAM,MAAA,UAAA,GAAaA,QAAS,WAAW,CAAA,CAAA;AAEvC,EAAM,MAAA,UAAA,GAAa,CAAC,GAAc,KAAA;AAChC,IAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AAChB,IAAA,UAAA,CAAW,KAAQ,GAAA,GAAA,CAAA;AACnB,IAAI,IAAA,IAAA,IAAQ,OAAO,IAAA,KAAS,UAAY,EAAA;AACtC,MAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AAAA,KACV;AAAA,GACF,CAAA;AAEA,EAAM,MAAA,QAAA,GAAW,CAAC,GAAc,KAAA;AAC9B,IAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA,CAAA;AAChB,IAAI,IAAA,MAAA,IAAU,OAAO,MAAA,KAAW,UAAY,EAAA;AAC1C,MAAO,MAAA,CAAA,GAAA,EAAK,WAAW,KAAK,CAAA,CAAA;AAAA,KAC9B;AAAA,GACF,CAAA;AAEA,EAAM,MAAA,SAAA,GAAY,CAAC,GAAc,KAAA;AAC/B,IAAS,QAAA,EAAA,CAAA;AACT,IAAI,IAAA,OAAA,IAAW,OAAO,OAAA,KAAY,UAAY,EAAA;AAC5C,MAAQ,OAAA,CAAA,GAAA,EAAK,WAAW,KAAK,CAAA,CAAA;AAAA,KAC/B;AAAA,GACF,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,UAAA;AAAA,IACA,QAAA;AAAA,IACA,SAAA;AAAA,IACA,OAAA;AAAA,IACA,UAAA;AAAA,GACF,CAAA;AACF;;;;"}