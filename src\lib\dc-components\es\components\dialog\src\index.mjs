import { createElementVNode, defineComponent, useSlots, computed, openBlock, createBlock, unref, mergeProps, createSlots, withCtx, renderSlot, createVNode } from 'vue';
import { ElDialog } from 'element-plus';
import 'element-plus/es/components/dialog/style/css';
import Footer from './Footer.mjs';
import { DcDialogTypes } from './types.mjs';
import './index.vue_vue_type_style_index_0_lang.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const _hoisted_1 = /* @__PURE__ */ createElementVNode("div", { style: { "display": "none" } }, null, -1);
const _hoisted_2 = /* @__PURE__ */ createElementVNode("div", { style: { "display": "none" } }, null, -1);
const __default__ = defineComponent({
  name: "DcDialog"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: Boolean
    },
    type: {
      type: String,
      default: DcDialogTypes.HEADER_FOOTER
    },
    confirmAction: {
      type: Object,
      default: () => ({})
    },
    cancelAction: {
      type: Object,
      default: () => ({})
    },
    footerLeftSlot: {
      type: Object
    },
    footerRightSlot: {
      type: Object
    },
    closeIsCancel: {
      type: Boolean,
      default: true
    },
    class: {
      type: String,
      default: ""
    }
  },
  emits: ["update:modelValue", "cancel", "confirm", "close"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const slots = useSlots();
    const thisFooter = computed(() => {
      return props.type !== DcDialogTypes.HEADER && !slots.footer;
    });
    const noHeader = computed(() => {
      return [DcDialogTypes.FOOTER, DcDialogTypes.ONLYCONTENT].includes(props.type);
    });
    const noFooter = computed(() => {
      return [DcDialogTypes.HEADER, DcDialogTypes.ONLYCONTENT].includes(props.type);
    });
    const close = () => {
      emits("update:modelValue", false);
      emits("close");
      props.closeIsCancel && emits("cancel");
    };
    const onCancel = () => {
      emits("cancel");
    };
    const onConfirm = () => {
      emits("confirm");
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElDialog), mergeProps(_ctx.$attrs, {
        "model-value": props.modelValue,
        class: `dc-dialog ${noFooter.value ? "no-footer" : ""} ${noHeader.value ? "no-header" : ""} ${props.class}`,
        onClose: close
      }), createSlots({
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default")
        ]),
        _: 2
      }, [
        props.type === unref(DcDialogTypes).FOOTER ? {
          name: "header",
          fn: withCtx(() => [
            _hoisted_1
          ]),
          key: "0"
        } : _ctx.$slots.header ? {
          name: "header",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "header")
          ]),
          key: "1"
        } : void 0,
        props.type === unref(DcDialogTypes).HEADER ? {
          name: "footer",
          fn: withCtx(() => [
            _hoisted_2
          ]),
          key: "2"
        } : _ctx.$slots.footer ? {
          name: "footer",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "footer")
          ]),
          key: "3"
        } : void 0,
        thisFooter.value ? {
          name: "footer",
          fn: withCtx(() => [
            createVNode(Footer, {
              "confirm-action": props.confirmAction,
              "cancel-action": props.cancelAction,
              "footer-left-slot": props.footerLeftSlot,
              "footer-right-slot": props.footerRightSlot,
              onCancel,
              onConfirm
            }, null, 8, ["confirm-action", "cancel-action", "footer-left-slot", "footer-right-slot"])
          ]),
          key: "4"
        } : void 0
      ]), 1040, ["model-value", "class"]);
    };
  }
});
var Dialog = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\dialog\\src\\index.vue"]]);

export { Dialog as default };
//# sourceMappingURL=index.mjs.map
