{"version": 3, "file": "ColumnsFilter.js", "sources": ["../../../../../../../packages/components/table/src/components/ColumnsFilter.vue"], "sourcesContent": ["<template>\r\n  <el-dropdown\r\n    ref=\"dropdownRef\"\r\n    trigger=\"click\"\r\n    :hide-on-click=\"false\"\r\n    min-height=\"380px\"\r\n    placement=\"right\"\r\n    @visible-change=\"visibleChange\"\r\n  >\r\n    <span class=\"dc-table-columns-filter_icon\">\r\n      <el-icon><Operation /></el-icon>\r\n    </span>\r\n    <template #dropdown>\r\n      <div class=\"dc-table-columns-filter_dropdown-box\">\r\n        <el-dropdown-menu>\r\n          <div class=\"dc-table-columns-filter_operator\">\r\n            <el-checkbox\r\n              v-model=\"checkAll\"\r\n              :label=\"filterAllText ?? '全选'\"\r\n              :indeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n            />\r\n            <el-button link @click=\"onReset\">{{\r\n              filterResetText ?? \"重置\"\r\n            }}</el-button>\r\n          </div>\r\n          <el-checkbox-group v-model=\"checkList\" @change=\"handleCheckedChange\">\r\n            <template v-for=\"item in props.columns\">\r\n              <el-dropdown-item v-if=\"item.label\" :key=\"item.label\">\r\n                <el-checkbox\r\n                  :label=\"item.label\"\r\n                  :checked=\"item.columnRequired || item.columnDefault\"\r\n                  :disabled=\"item.columnRequired\"\r\n                />\r\n              </el-dropdown-item>\r\n            </template>\r\n          </el-checkbox-group>\r\n        </el-dropdown-menu>\r\n        <div class=\"dc-table-columns-filter_button-box\">\r\n          <div>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"onChange\">\r\n              {{ filterSubmitText ?? \"确定\" }}\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"clone\">{{\r\n              filterCancelText ?? \"取消\"\r\n            }}</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </template>\r\n  </el-dropdown>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { onMounted, ref, watch } from \"vue\";\r\nimport {\r\n  ElButton,\r\n  ElCheckbox,\r\n  ElCheckboxGroup,\r\n  ElDropdown,\r\n  ElDropdownItem,\r\n  ElDropdownMenu,\r\n  ElIcon,\r\n} from \"element-plus\";\r\nimport { Operation } from \"@element-plus/icons-vue\";\r\nimport type { CheckboxValueType } from \"element-plus\";\r\n// eslint-disable-next-line import/order\r\nimport type { PropType } from \"vue\";\r\n// eslint-disable-next-line import/order\r\n\r\nconst props = defineProps({\r\n  columns: {\r\n    type: Array as PropType<\r\n      { label: string; columnDefault?: boolean; columnRequired?: boolean }[]\r\n    >,\r\n    default: () => [],\r\n  },\r\n  filterSubmitText: {\r\n    type: String,\r\n  },\r\n  filterCancelText: {\r\n    type: String,\r\n  },\r\n  filterAllText: {\r\n    type: String,\r\n  },\r\n  filterResetText: {\r\n    type: String,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits<{\r\n  (e: \"change\", value: Array<string>): void;\r\n}>();\r\n\r\nconst dropdownRef = ref();\r\nconst isChanged = ref(false);\r\nconst initCheckData = ref<any>([]);\r\nconst checkAll = ref(false);\r\nconst isIndeterminate = ref(true);\r\nconst checkList = ref<string[]>([]);\r\n// 多选浮窗显隐性改变时触发\r\nfunction visibleChange(visible: boolean) {\r\n  if (visible) {\r\n    isChanged.value = false;\r\n    initCheckData.value = checkList.value;\r\n    handleCheckedChange(checkList.value);\r\n  } else {\r\n    !isChanged.value && (checkList.value = initCheckData.value);\r\n  }\r\n}\r\nonMounted(() => {\r\n  initCheckList();\r\n});\r\n\r\n// 初始化checkList\r\nfunction initCheckList() {\r\n  checkList.value = props.columns\r\n    .filter((item) => item.label && (item.columnDefault || item.columnRequired))\r\n    .map((item) => item.label);\r\n  onChange();\r\n}\r\n\r\n// 当columns发生变化时初始化\r\nwatch(\r\n  () => props.columns,\r\n  () => {\r\n    initCheckList();\r\n  }\r\n);\r\n\r\n// 全选切换逻辑\r\nfunction handleCheckAllChange(val: CheckboxValueType) {\r\n  checkList.value = val\r\n    ? props.columns.map((item) => item.label)\r\n    : props.columns\r\n        .filter((item) => item.label && item.columnRequired)\r\n        .map((item) => item.label);\r\n  const length = props.columns.filter((item) => item.label).length;\r\n  if (checkList.value.length < length && !val) {\r\n    isIndeterminate.value = true;\r\n  } else {\r\n    isIndeterminate.value = false;\r\n  }\r\n}\r\n// 全选判断逻辑\r\nfunction handleCheckedChange(value: CheckboxValueType[]): any {\r\n  const checkedCount = value.length;\r\n  const length = props.columns.filter((item) => item.label).length;\r\n  checkAll.value = checkedCount === length;\r\n  isIndeterminate.value = checkedCount > 0 && checkedCount < length;\r\n}\r\n\r\n// 确认时触发\r\nfunction onChange() {\r\n  dropdownRef.value.handleClose();\r\n  isChanged.value = true;\r\n  emits(\"change\", checkList.value);\r\n}\r\n// 取消是触发\r\nfunction clone() {\r\n  dropdownRef.value.handleClose();\r\n}\r\n// 重置时触发\r\nfunction onReset() {\r\n  initCheckList();\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dc-table-columns-filter {\r\n  &_icon {\r\n    cursor: pointer;\r\n  }\r\n  &_operator {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0px 10px;\r\n  }\r\n  &_button-box {\r\n    display: flex;\r\n    flex-direction: row-reverse;\r\n    padding: 10px 10px;\r\n  }\r\n  &_dropdown-box {\r\n    min-width: 200px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["ref", "onMounted", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAqBd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAId,IAAA,MAAM,cAAcA,OAAI,EAAA,CAAA;AAClB,IAAA,MAAA,SAAA,GAAYA,QAAI,KAAK,CAAA,CAAA;AACrB,IAAA,MAAA,aAAA,GAAgBA,OAAS,CAAA,EAAE,CAAA,CAAA;AAC3B,IAAA,MAAA,QAAA,GAAWA,QAAI,KAAK,CAAA,CAAA;AACpB,IAAA,MAAA,eAAA,GAAkBA,QAAI,IAAI,CAAA,CAAA;AAC1B,IAAA,MAAA,SAAA,GAAYA,OAAc,CAAA,EAAE,CAAA,CAAA;AAElC,IAAA,SAAA,aAAA,CAAuB,OAAkB,EAAA;AACvC,MAAA,IAAI,OAAS,EAAA;AACX,QAAA,SAAA,CAAU,KAAQ,GAAA,KAAA,CAAA;AAClB,QAAA,aAAA,CAAc,QAAQ,SAAU,CAAA,KAAA,CAAA;AAChC,QAAA,mBAAA,CAAoB,UAAU,KAAK,CAAA,CAAA;AAAA,OAC9B,MAAA;AACL,QAAA,CAAC,SAAU,CAAA,KAAA,KAAoB,SAAA,CAAA,KAAA,GAAQ,aAAc,CAAA,KAAA,CAAA,CAAA;AAAA,OACvD;AAAA,KACF;AACA,IAAAC,aAAA,CAAU,MAAM;AACA,MAAA,aAAA,EAAA,CAAA;AAAA,KACf,CAAA,CAAA;AAGwB,IAAA,SAAA,aAAA,GAAA;AACvB,MAAA,SAAA,CAAU,QAAQ,KAAM,CAAA,OAAA,CACrB,MAAO,CAAA,CAAC,SAAS,IAAK,CAAA,KAAA,KAAe,IAAA,CAAA,aAAA,IAAiB,KAAK,cAAe,CAAA,CAAA,CAC1E,IAAI,CAAC,IAAA,KAAS,KAAK,KAAK,CAAA,CAAA;AAClB,MAAA,QAAA,EAAA,CAAA;AAAA,KACX;AAIE,IAAAC,SAAA,CAAA,MAAM,KAAM,CAAA,OAAA,EACZ,MAAM;AACU,MAAA,aAAA,EAAA,CAAA;AAAA,KAElB,CAAA,CAAA;AAGA,IAAA,SAAA,oBAAA,CAA8B,GAAwB,EAAA;AAC1C,MAAA,SAAA,CAAA,KAAA,GAAQ,GACd,GAAA,KAAA,CAAM,OAAQ,CAAA,GAAA,CAAI,CAAC,IAAS,KAAA,IAAA,CAAK,KAAK,CAAA,GACtC,KAAM,CAAA,OAAA,CACH,OAAO,CAAC,IAAA,KAAS,IAAK,CAAA,KAAA,IAAS,IAAK,CAAA,cAAc,EAClD,GAAI,CAAA,CAAC,IAAS,KAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AACzB,MAAA,MAAA,MAAA,GAAS,MAAM,OAAQ,CAAA,MAAA,CAAO,CAAC,IAAS,KAAA,IAAA,CAAK,KAAK,CAAE,CAAA,MAAA,CAAA;AAC1D,MAAA,IAAI,SAAU,CAAA,KAAA,CAAM,MAAS,GAAA,MAAA,IAAU,CAAC,GAAK,EAAA;AAC3C,QAAA,eAAA,CAAgB,KAAQ,GAAA,IAAA,CAAA;AAAA,OACnB,MAAA;AACL,QAAA,eAAA,CAAgB,KAAQ,GAAA,KAAA,CAAA;AAAA,OAC1B;AAAA,KACF;AAEA,IAAA,SAAA,mBAAA,CAA6B,KAAiC,EAAA;AAC5D,MAAA,MAAM,eAAe,KAAM,CAAA,MAAA,CAAA;AACrB,MAAA,MAAA,MAAA,GAAS,MAAM,OAAQ,CAAA,MAAA,CAAO,CAAC,IAAS,KAAA,IAAA,CAAK,KAAK,CAAE,CAAA,MAAA,CAAA;AAC1D,MAAA,QAAA,CAAS,QAAQ,YAAiB,KAAA,MAAA,CAAA;AAClB,MAAA,eAAA,CAAA,KAAA,GAAQ,YAAe,GAAA,CAAA,IAAK,YAAe,GAAA,MAAA,CAAA;AAAA,KAC7D;AAGoB,IAAA,SAAA,QAAA,GAAA;AAClB,MAAA,WAAA,CAAY,MAAM,WAAY,EAAA,CAAA;AAC9B,MAAA,SAAA,CAAU,KAAQ,GAAA,IAAA,CAAA;AACZ,MAAA,KAAA,CAAA,QAAA,EAAU,UAAU,KAAK,CAAA,CAAA;AAAA,KACjC;AAEiB,IAAA,SAAA,KAAA,GAAA;AACf,MAAA,WAAA,CAAY,MAAM,WAAY,EAAA,CAAA;AAAA,KAChC;AAEmB,IAAA,SAAA,OAAA,GAAA;AACH,MAAA,aAAA,EAAA,CAAA;AAAA,KAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}