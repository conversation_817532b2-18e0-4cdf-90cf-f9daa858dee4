<template>
  <el-dialog :model-value="modelValue" :title="title" width="700px" :before-close="resetForm">
    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="140px">
      <el-form-item label="" prop="customCode" label-width="0">
        <div class="h-80 w-full">
          <MonacoEditor v-model="ruleForm.customCode" :readOnly="false" @change="codeChange" ref="editorRef" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import { updateNodeClass, updateNodePool } from '@/api/k8sApi'

const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  k8sId: {
    type: Number,
    default: 0
  },
  id: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    default: ''
  },
  yaml: {
    type: String,
    default: 'Nodeclass'
  }
})

const title = computed(() => (props.type === 'Nodeclass' ? 'Nodeclass YAML' : t('replenish.nodePoolYAML')))

const editorRef = ref()

const ruleForm = reactive<any>({
  customCode: ''
})

const rules = {
  customCode: [
    {
      trigger: 'change',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ]
}
const tenantId = computed(() => store.state.user.tenantId)

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    } else {
      ruleForm.customCode = props.yaml
      nextTick(() => {
        editorRef.value.setValue(ruleForm.customCode)
      })
    }
  }
)

const codeChange = () => {
  nextTick(() => {
    formRef.value.validateField(['customCode'])
  })
}

function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      isLoading.value = true
      if (props.type === 'Nodeclass') {
        updateNodeClass({
          k8sClusterId: props.k8sId,
          nodeClassId: props.id,
          config: ruleForm.customCode,
          tenantId: tenantId.value
        })
          .then((res) => {
            if (res.data.code === 0) {
              emit('submit')
              resetForm()
            }
          })
          .finally(() => {
            isLoading.value = false
          })
      } else {
        updateNodePool({
          k8sClusterId: props.k8sId,
          nodePoolId: props.id,
          config: ruleForm.customCode,
          tenantId: tenantId.value
        })
          .then((res) => {
            if (res.data.code === 0) {
              emit('submit')
              resetForm()
            }
          })
          .finally(() => {
            isLoading.value = false
          })
      }
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.customCode = ''
  emit('update:modelValue', false)
}
</script>
