'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var elementPlus = require('element-plus');
require('element-plus/es/components/autocomplete/style/css');
require('element-plus/es/components/cascader/style/css');
require('element-plus/es/components/cascader-panel/style/css');
require('element-plus/es/components/color-picker/style/css');
require('element-plus/es/components/date-picker/style/css');
require('element-plus/es/components/input/style/css');
require('element-plus/es/components/input-number/style/css');
require('element-plus/es/components/rate/style/css');
require('element-plus/es/components/select-v2/style/css');
require('element-plus/es/components/slider/style/css');
require('element-plus/es/components/switch/style/css');
require('element-plus/es/components/time-picker/style/css');
require('element-plus/es/components/time-select/style/css');
require('element-plus/es/components/transfer/style/css');
require('element-plus/es/components/upload/style/css');
var index$3 = require('../../../../checkbox/src/index.js');
var index$2 = require('../../../../radio/src/index.js');
var index$1 = require('../../../../select/src/index.js');
var types = require('../../types.js');
var index$4 = require('../Custom/index.js');
var index$5 = require('../Table/index.js');
var index = require('../Text/index.js');

const Component = {
  [types.DcFormComponentEnum.INPUT]: elementPlus.ElInput,
  [types.DcFormComponentEnum.TEXT]: index["default"],
  [types.DcFormComponentEnum.SELECT]: index$1["default"],
  [types.DcFormComponentEnum.RADIO]: index$2["default"],
  [types.DcFormComponentEnum.AUTOCOMPLETE]: elementPlus.ElAutocomplete,
  [types.DcFormComponentEnum.CHECKBOX]: index$3["default"],
  [types.DcFormComponentEnum.CASCADER]: elementPlus.ElCascader,
  [types.DcFormComponentEnum.COLORPICKER]: elementPlus.ElColorPicker,
  [types.DcFormComponentEnum.CASCADERPANEL]: elementPlus.ElCascaderPanel,
  [types.DcFormComponentEnum.DATEPICKER]: elementPlus.ElDatePicker,
  [types.DcFormComponentEnum.INPUTNUMBER]: elementPlus.ElInputNumber,
  [types.DcFormComponentEnum.RATE]: elementPlus.ElRate,
  [types.DcFormComponentEnum.SELECTV2]: elementPlus.ElSelectV2,
  [types.DcFormComponentEnum.SLIDER]: elementPlus.ElSlider,
  [types.DcFormComponentEnum.SWITCH]: elementPlus.ElSwitch,
  [types.DcFormComponentEnum.TIMEPICKER]: elementPlus.ElTimePicker,
  [types.DcFormComponentEnum.TIMESELECT]: elementPlus.ElTimeSelect,
  [types.DcFormComponentEnum.TRANSFER]: elementPlus.ElTransfer,
  [types.DcFormComponentEnum.UPLOAD]: elementPlus.ElUpload,
  [types.DcFormComponentEnum.CUSTOM]: index$4["default"],
  [types.DcFormComponentEnum.TABLE]: index$5["default"]
};

exports.Component = Component;
//# sourceMappingURL=constants.js.map
