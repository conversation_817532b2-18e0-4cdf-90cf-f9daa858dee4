import Mock from 'mockjs'
import namespaceMock from './api/namespaceMock'
import userMock from './api/userMock'
import k8sMock from './api/k8sMock'
import componentMock from './api/componentMock'
import clusterMock from './api/clusterMock'
import hostsMock from './api/hostsMock'
import configureMock from './api/configureMock'
import logsMock from './api/logsMock'
import panelMock from './api/panelMock'

const mocks = [
    ...userMock,
    ...namespaceMock,
    ...k8sMock,
    ...componentMock,
    ...clusterMock,
    ...hostsMock,
    ...configureMock,
    ...logsMock,
    ...panelMock,
]

export default () => ({
    name: 'mock-server-plugin',
    configureServer(server: any) {
        server.middlewares.use((req: any, res: any, next: any) => {
            const { method, url } = req
            const mock = mocks.find(mock => mock.url === url && mock.method === method)
            if (mock) {
                return res.end(JSON.stringify(Mock.mock(mock.response)))
            } else {
                next();
            }
        })
    }
})