{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/checkbox/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-checkbox-group\r\n    v-bind=\"{ ...$attrs }\"\r\n    :model-value=\"props.modelValue\"\r\n    @change=\"handleChange\"\r\n  >\r\n    <el-checkbox\r\n      v-for=\"(item, index) in options\"\r\n      :key=\"index\"\r\n      :label=\"item.value\"\r\n    >\r\n      {{ item.label }}\r\n    </el-checkbox>\r\n  </el-checkbox-group>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, isRef, useAttrs } from \"vue\";\r\nimport { ElCheckbox, ElCheckboxGroup } from \"element-plus\";\r\nimport type { CheckboxValueType } from \"element-plus\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcCheckboxOptions } from \"./types\";\r\n\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: Array as PropType<string[] | number[]>,\r\n  },\r\n  options: {\r\n    type: [Array, Object] as PropType<DcCheckboxOptions>,\r\n    default: () => [],\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\"]);\r\nconst attrs = useAttrs();\r\n\r\nconst options = computed(() => {\r\n  return isRef(props.options) ? props.options.value : props.options;\r\n});\r\n\r\nconst handleChange = (val: CheckboxValueType[]) => {\r\n  emits(\"update:modelValue\", val);\r\n  if (typeof attrs.onChange === \"function\") {\r\n    attrs.onChange(val);\r\n  }\r\n};\r\n\r\ndefineOptions({\r\n  name: \"DcCheckbox\",\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["DO_defineComponent", "useAttrs", "computed", "isRef"], "mappings": ";;;;;;;;;;AA+Cc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;AA1BA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAUd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AACd,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AAEjB,IAAA,MAAA,OAAA,GAAUC,aAAS,MAAM;AAC7B,MAAA,OAAOC,UAAM,KAAM,CAAA,OAAO,IAAI,KAAM,CAAA,OAAA,CAAQ,QAAQ,KAAM,CAAA,OAAA,CAAA;AAAA,KAC3D,CAAA,CAAA;AAEK,IAAA,MAAA,YAAA,GAAe,CAAC,GAA6B,KAAA;AACjD,MAAA,KAAA,CAAM,qBAAqB,GAAG,CAAA,CAAA;AAC1B,MAAA,IAAA,OAAO,KAAM,CAAA,QAAA,KAAa,UAAY,EAAA;AACxC,QAAA,KAAA,CAAM,SAAS,GAAG,CAAA,CAAA;AAAA,OACpB;AAAA,KACF,CAAA;AAIE,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}