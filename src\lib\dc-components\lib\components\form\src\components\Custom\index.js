'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('./index2.js');

var DcFormCustom = vue.defineComponent({
  name: "DcFormCustom",
  props: {
    modelValue: {},
    renderCustom: {
      type: Function
    },
    rowIndex: {
      type: Number,
      default: 0
    }
  },
  emits: {
    "update:modelValue": (val) => true
  },
  setup(props, ctx) {
    const onChange = (val) => {
      ctx.emit("update:modelValue", val);
    };
    return {
      onChange
    };
  },
  render() {
    const vNode = this.$props.renderCustom ? this.$props.renderCustom({
      value: this.$props.modelValue,
      onChange: this.onChange,
      rowIndex: this.$props.rowIndex
    }) : null;
    return vNode;
  }
});

exports["default"] = DcFormCustom;
//# sourceMappingURL=index.js.map
