import axios from 'axios'
import Base from '@/utils/Base'
import { getPath } from '@/utils/Path'
import { LStorage } from '@/utils/storage'
import router from '@/router'
/**
 * @description 根据服务实例id下载配置信息流
 * @param {ConfigureApi.IDownloadConfigStreamByServiceInstanceId} data
 * @return {*}
 */
export const downloadConfigStreamByServiceInstanceId = (data: ConfigureApi.IDownloadConfigStreamByServiceInstanceId) => {
  const headers: any = {}
  const route = useRoute()
  const namespace = router.currentRoute.value.query?.namespaceTitle
  const namespaceId = router.currentRoute.value.query?.namespaceId
  headers[`${Base.cookie}`] = LStorage.get(Base.cookie)
  if (namespace) headers[`Namespace`] = encodeURIComponent(namespace.toString())
  if (namespaceId) headers[`NamespaceId`] = encodeURIComponent(namespaceId.toString())
  return axios
    .create({
      baseURL: import.meta.env.VITE_BASE_URL,
      headers,
      responseType: 'blob'
    })
    .post(`${getPath('/cluster-service-config/downloadConfigStreamByServiceInstanceId')}`, data)
}
