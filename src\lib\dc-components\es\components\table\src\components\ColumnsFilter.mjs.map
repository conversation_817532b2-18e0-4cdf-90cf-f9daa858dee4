{"version": 3, "file": "ColumnsFilter.mjs", "sources": ["../../../../../../../packages/components/table/src/components/ColumnsFilter.vue"], "sourcesContent": ["<template>\r\n  <el-dropdown\r\n    ref=\"dropdownRef\"\r\n    trigger=\"click\"\r\n    :hide-on-click=\"false\"\r\n    min-height=\"380px\"\r\n    placement=\"right\"\r\n    @visible-change=\"visibleChange\"\r\n  >\r\n    <span class=\"dc-table-columns-filter_icon\">\r\n      <el-icon><Operation /></el-icon>\r\n    </span>\r\n    <template #dropdown>\r\n      <div class=\"dc-table-columns-filter_dropdown-box\">\r\n        <el-dropdown-menu>\r\n          <div class=\"dc-table-columns-filter_operator\">\r\n            <el-checkbox\r\n              v-model=\"checkAll\"\r\n              :label=\"filterAllText ?? '全选'\"\r\n              :indeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n            />\r\n            <el-button link @click=\"onReset\">{{\r\n              filterResetText ?? \"重置\"\r\n            }}</el-button>\r\n          </div>\r\n          <el-checkbox-group v-model=\"checkList\" @change=\"handleCheckedChange\">\r\n            <template v-for=\"item in props.columns\">\r\n              <el-dropdown-item v-if=\"item.label\" :key=\"item.label\">\r\n                <el-checkbox\r\n                  :label=\"item.label\"\r\n                  :checked=\"item.columnRequired || item.columnDefault\"\r\n                  :disabled=\"item.columnRequired\"\r\n                />\r\n              </el-dropdown-item>\r\n            </template>\r\n          </el-checkbox-group>\r\n        </el-dropdown-menu>\r\n        <div class=\"dc-table-columns-filter_button-box\">\r\n          <div>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"onChange\">\r\n              {{ filterSubmitText ?? \"确定\" }}\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"clone\">{{\r\n              filterCancelText ?? \"取消\"\r\n            }}</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </template>\r\n  </el-dropdown>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { onMounted, ref, watch } from \"vue\";\r\nimport {\r\n  ElButton,\r\n  ElCheckbox,\r\n  ElCheckboxGroup,\r\n  ElDropdown,\r\n  ElDropdownItem,\r\n  ElDropdownMenu,\r\n  ElIcon,\r\n} from \"element-plus\";\r\nimport { Operation } from \"@element-plus/icons-vue\";\r\nimport type { CheckboxValueType } from \"element-plus\";\r\n// eslint-disable-next-line import/order\r\nimport type { PropType } from \"vue\";\r\n// eslint-disable-next-line import/order\r\n\r\nconst props = defineProps({\r\n  columns: {\r\n    type: Array as PropType<\r\n      { label: string; columnDefault?: boolean; columnRequired?: boolean }[]\r\n    >,\r\n    default: () => [],\r\n  },\r\n  filterSubmitText: {\r\n    type: String,\r\n  },\r\n  filterCancelText: {\r\n    type: String,\r\n  },\r\n  filterAllText: {\r\n    type: String,\r\n  },\r\n  filterResetText: {\r\n    type: String,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits<{\r\n  (e: \"change\", value: Array<string>): void;\r\n}>();\r\n\r\nconst dropdownRef = ref();\r\nconst isChanged = ref(false);\r\nconst initCheckData = ref<any>([]);\r\nconst checkAll = ref(false);\r\nconst isIndeterminate = ref(true);\r\nconst checkList = ref<string[]>([]);\r\n// 多选浮窗显隐性改变时触发\r\nfunction visibleChange(visible: boolean) {\r\n  if (visible) {\r\n    isChanged.value = false;\r\n    initCheckData.value = checkList.value;\r\n    handleCheckedChange(checkList.value);\r\n  } else {\r\n    !isChanged.value && (checkList.value = initCheckData.value);\r\n  }\r\n}\r\nonMounted(() => {\r\n  initCheckList();\r\n});\r\n\r\n// 初始化checkList\r\nfunction initCheckList() {\r\n  checkList.value = props.columns\r\n    .filter((item) => item.label && (item.columnDefault || item.columnRequired))\r\n    .map((item) => item.label);\r\n  onChange();\r\n}\r\n\r\n// 当columns发生变化时初始化\r\nwatch(\r\n  () => props.columns,\r\n  () => {\r\n    initCheckList();\r\n  }\r\n);\r\n\r\n// 全选切换逻辑\r\nfunction handleCheckAllChange(val: CheckboxValueType) {\r\n  checkList.value = val\r\n    ? props.columns.map((item) => item.label)\r\n    : props.columns\r\n        .filter((item) => item.label && item.columnRequired)\r\n        .map((item) => item.label);\r\n  const length = props.columns.filter((item) => item.label).length;\r\n  if (checkList.value.length < length && !val) {\r\n    isIndeterminate.value = true;\r\n  } else {\r\n    isIndeterminate.value = false;\r\n  }\r\n}\r\n// 全选判断逻辑\r\nfunction handleCheckedChange(value: CheckboxValueType[]): any {\r\n  const checkedCount = value.length;\r\n  const length = props.columns.filter((item) => item.label).length;\r\n  checkAll.value = checkedCount === length;\r\n  isIndeterminate.value = checkedCount > 0 && checkedCount < length;\r\n}\r\n\r\n// 确认时触发\r\nfunction onChange() {\r\n  dropdownRef.value.handleClose();\r\n  isChanged.value = true;\r\n  emits(\"change\", checkList.value);\r\n}\r\n// 取消是触发\r\nfunction clone() {\r\n  dropdownRef.value.handleClose();\r\n}\r\n// 重置时触发\r\nfunction onReset() {\r\n  initCheckList();\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dc-table-columns-filter {\r\n  &_icon {\r\n    cursor: pointer;\r\n  }\r\n  &_operator {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0px 10px;\r\n  }\r\n  &_button-box {\r\n    display: flex;\r\n    flex-direction: row-reverse;\r\n    padding: 10px 10px;\r\n  }\r\n  &_dropdown-box {\r\n    min-width: 200px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;;;;;;;;;;;;AAgEA,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAqBd,IAAA,MAAM,WAAQ,GAAA,GAAA,EAAA,CAAA;AAId,IAAA,MAAM,eAAkB,CAAA,KAAA,CAAA,CAAA;AACxB,IAAM,MAAA,gBAAgB,GAAK,CAAA,EAAA,CAAA,CAAA;AAC3B,IAAM,MAAA,QAAA,GAAA,GAAA,CAAA,KAAyB,CAAC,CAAC;AACjC,IAAM,MAAA,eAAe,GAAK,GAAA,CAAA,IAAA,CAAA,CAAA;AAC1B,IAAM,MAAA,SAAA,GAAA,GAAA,CAAA,EAAkB;AACxB,IAAM,SAAA,aAA0B,CAAC,OAAC,EAAA;AAElC,MAAA,IAAA,OAAA,EAAA;AACE,QAAA,SAAa,CAAA,KAAA,GAAA,KAAA,CAAA;AACX,QAAA,aAAkB,CAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AAClB,QAAA,6BAAgC,CAAA,KAAA,CAAA,CAAA;AAChC,OAAA,MAAA;AAAmC,QAC9B,CAAA,SAAA,CAAA,KAAA,KAAA,SAAA,CAAA,KAAA,GAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AACL,OAAA;AAAqD,KACvD;AAAA,IACF,SAAA,CAAA,MAAA;AACA,MAAA,aAAgB,EAAA,CAAA;AACd,KAAc,CAAA,CAAA;AAAA,IAChB,SAAC,aAAA,GAAA;AAGD,MAAyB,SAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,KAAA,IAAA,CAAA,aAAA,IAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACvB,MAAA,QAAA,EAAU;AAGV,KAAS;AAAA,IACX,KAAA,CAAA,MAAA,KAAA,CAAA,OAAA,EAAA,MAAA;AAGA,MACE,aAAY,EAAA,CAAA;AAEV,KAAc,CAAA,CAAA;AAAA,IAChB,SACF,oBAAA,CAAA,GAAA,EAAA;AAGA,MAAA,SAAA,CAAA,KAAA,GAAA,GAAA,GAAA,KAAsD,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,cAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACpD,MAAU,MAAA,MAAA,GAAA,KACN,CAAA,OAAA,CAAA,MAAc,CAAA,CAAA,IAAA,KAAc,IAAA,CAAA,KAAA,CAAK,CAAK,MAAA,CAAA;AAI1C,MAAM,IAAA,SAAA,CAAA,YAAuB,GAAA,MAAA,IAAiB,CAAA,GAAA,EAAA;AAC9C,QAAA,eAAc,CAAA,KAAe,GAAA,IAAA,CAAA;AAC3B,OAAA,MAAA;AAAwB,QACnB,eAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACL,OAAA;AAAwB,KAC1B;AAAA,IACF,SAAA,mBAAA,CAAA,KAAA,EAAA;AAEA,MAAA,MAAA,YAAA,GAAA,KAAA,CAA6B,MAAiC,CAAA;AAC5D,MAAA,MAAM,eAAe,OAAM,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA;AAC3B,MAAM,QAAA,CAAA,KAAA,eAAuB,KAAA,MAAiB,CAAA;AAC9C,MAAA,qBAAkC,GAAA,YAAA,GAAA,CAAA,IAAA,YAAA,GAAA,MAAA,CAAA;AAClC,KAAgB;AAA2C,IAC7D,SAAA,QAAA,GAAA;AAGA,MAAoB,WAAA,CAAA,KAAA,CAAA,WAAA,EAAA,CAAA;AAClB,MAAA,SAAA,CAAA,QAAkB,IAAY,CAAA;AAC9B,MAAA,KAAA,CAAA,QAAkB,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAClB,KAAM;AAAyB,IACjC,SAAA,KAAA,GAAA;AAEA,MAAiB,WAAA,CAAA,KAAA,CAAA,WAAA,EAAA,CAAA;AACf,KAAA;AAA8B,IAChC,SAAA,OAAA,GAAA;AAEA,MAAmB,aAAA,EAAA,CAAA;AACjB,KAAc;AAAA,IAChB,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}