{"numberOfHosts": "Host Quantity", "numberOfComponents": "Component Quantity", "numberOfAlarms": "Alert Quantity", "serviceComponent": "Service Component", "k8sInformation": "Kubernetes Information", "clusterResourceUsage": "Cluster Resource Usage", "designatedHost": "Specify Host ", "selectCustomComponents": "Select the custom component name that you want to scale", "addServiceType": "Add a service type", "oneComponent": "Select the service type you want to add. You can install only one component at a time.", "deploymentMethod": "Deployment Mode", "versionSelection": " Select Version", "componentInstallation": "Install Component", "customRoleAssignment": "Assign for Role", "selectHost": "Select Host", "tip": "Prompt", "confirmTip": "Confirmation prompt", "groupService": "When creating a cluster, you can add only one component at a time. Parameters of the component are passed from the backend, and are set with default values. You can modify the parameter values as needed.", "installationInformation": "Installation Info", "addFailed": "Add Failed", "addSuccessfully": "Added successfully", "componentAdded": "Component added", "errorLog": "Invalid logs", "version": "Version", "currentConfiguration": "Current Configuration", "oneClickDeployment": "One-click Deploy", "customDeployment": "Custom Deployment", "installComponents": "Install Component", "advancedConfiguration": "Advanced Configuration", "roleConfiguration": "Role Configuration", "setScale": "<PERSON>", "parameterSettings": "Set Parameters", "addHost": "Add Host", "addCluster": "Add Cluster", "selectCluster": "select cluster", "configParameters": "Parameter Configuration", "addK8s": "Add <PERSON>", "editK8s": "<PERSON>", "userList": "User List", "addUser": "Add User", "editUser": "Edit User", "modifyMobileNumber": "Change Mobile Number", "changeEmailAddress": "Change Email Address", "welcomeBack": "Welcome back", "accountInfo": "Account Info", "rename": "<PERSON><PERSON>", "viewK8s": "View Kubernetes", "configComponentParameters": "Configure Component Parameters", "hostBind": "Host bound", "warning": "Warning", "unbindK8s": "<PERSON><PERSON><PERSON>", "bindK8s": "Bind Kubernetes", "configureServiceParameters": "Configure Service Parameters", "changePassword": "Change Password", "viewInstances": "View instances", "modifyPermissions": "Modify Permissions", "latestConfiguration": "Latest Configuration", "numberOfMonitoringNodes": "Number of monitoring nodes", "hostMonitoring": "Monitor Host", "componentMonitoring": "Monitor Component", "roleLog": "Role log", "hostInformation": "Host information", "hostDetails": "Host details", "diskSystem": "Disk system", "roleList": "Role list", "diskUsage": "Disk usage", "nodeScheduling": "Node scheduling", "edit": "edit", "jobConfiguration": "Job configuration", "expansionAndContraction": "Automatic expansion and contraction"}