<template>
  <BaseLayout :calc-minuend="152" :is-footer="false">
    <template #header>
      <div class="search mt-20px">
        <div class="open-box">
          <el-form :inline="true" :model="searchModelObj" label-width="auto">
            <el-form-item :label="$t('form.keywordSearch') + '：'">
              <el-input v-model="searchModelObj.key" :placeholder="$t('form.pleaseEnterKeyWords')" class="key-input" clearable />
            </el-form-item>
            <el-form-item label-width="0">
              <BaseButton class="btn1" debounce type="primary" @click="searchTable">{{ $t('button.inquire') }}</BaseButton>
              <BaseButton type="info" debounce class="btn2" @click="resetTable"> {{ $t('button.reset') }} </BaseButton>
            </el-form-item>
          </el-form>
          <div>
            <BaseButton
              type="primary"
              class="es-button"
              v-if="$has('down-logs')"
              debounce
              :disabled="searchModelObj.index === ''"
              @click="
                () => {
                  download(getSearchParams())
                }
              "
            >
              <i class="iconfont icon-xiazai" />
              &nbsp;{{ $t('button.downloadLog') }}
            </BaseButton>
            <img
              v-if="isSearch"
              @click="
                () => {
                  switchSearch()
                  wacthActive(isSearch)
                }
              "
              class="inline-block ml-10px mb-16px cursor-pointer"
              src="@/assets/icons/icon_you.svg"
              alt="icon"
            />
            <img
              v-else
              @click="
                () => {
                  switchSearch()
                  wacthActive(isSearch)
                }
              "
              class="inline-block ml-10px mb-16px cursor-pointer"
              src="@/assets/icons/icon_mieyou.svg"
              alt="icon"
            />
          </div>
        </div>
        <div :class="{ 'search-box': true, 'search-box--off': !isSearch }">
          <el-form :inline="true" :model="searchModelObj" label-width="auto">
            <el-form-item>
              <template #label>
                <span class="asterisk">{{ $t('form.roleName') + '：' }}</span>
              </template>
              <el-select v-model="searchModelObj.index" :placeholder="$t('form.pleaseChoose')" class="input-width" clearable @change="roleNameChange">
                <el-option v-for="item in roleNameOptions" :key="item.roleName" :label="item.roleName" :value="item.roleName" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.hostName') + '：'">
              <el-select v-model="searchModelObj.host_name" class="input-width" :placeholder="$t('form.pleaseChoose')" clearable @change="hostChange">
                <el-option v-for="(item, key) in hostOptions" :key="key" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('replenish.hostAlias') + '：'">
              <el-select
                v-model="searchModelObj.host_alias"
                :placeholder="$t('form.pleaseChoose')"
                value-key="value"
                class="input-width"
                clearable
                @change="hostAliasChange"
              >
                <el-option v-for="(item, key) in hostAliasOptions" :key="key" v-show="item" :label="item" :value="key" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.logLevel') + '：'">
              <el-select v-model="searchModelObj.level" :placeholder="$t('form.pleaseChoose')" class="input-width">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.timeout') + '：'">
              <el-input v-model="searchModelObj.timeOut" placeholder="≤60" clearable class="input-width" />
            </el-form-item>
            <el-form-item :label="$t('form.containerName') + '：'">
              <el-select v-model="searchModelObj.docker_container" :placeholder="$t('form.pleaseChoose')" class="input-width">
                <el-option v-for="item in containerNamesOptions" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.timeRange') + '：'">
              <el-select
                v-model="searchModelObj.lastTime"
                :placeholder="$t('form.pleaseChoose')"
                class="input-width"
                clearable
                @change="lastTimeChange"
              >
                <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('form.selectionPeriod') + '：'">
              <el-date-picker
                v-model="searchModelObj.searchTimes"
                type="datetimerange"
                range-separator="⇀"
                :start-placeholder="$t('form.startDate')"
                :end-placeholder="$t('form.endDate')"
                prefix-icon="el-icon-date"
                clearable
                :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="searchTimesChange"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
    <template #content>
      <LogContentTable :table-data="tableData" :loading="loading" class="dockerTable" />
    </template>
  </BaseLayout>
</template>

<script lang="ts" setup>
import { dayFormat, tableDayFormat } from '@/utils/Day'
import LogContentTable from './LogContentTable.vue'
import { useJournal } from './hooks/useJournal'
import { useSwitchSearchCon } from './hooks/useSwitchSearchCon'
const { route, t, store, $has } = useBasicTool()
const { switchSearch, isSearch } = useSwitchSearchCon()
const props = defineProps<{
  isLoading: boolean
  clusterId: string
}>()
const emits = defineEmits<{
  (e: 'update:isLoading', value: boolean): void
}>()
const { load, total, loading, timeOptions, levelOptions, wacthActive, onScroll, download } = useJournal()
const { deployWay, roleSimpleName = '', msgSource, containerName = '', instanceName, namespaceId } = route.query

// 搜索参数对象键值对数据类型，不定义遍历对象时会报错
interface SearchModelObj {
  [key: string]: string
}
// 搜索参数
const searchModelObj: SearchModelObj = reactive({
  index: '', // 角色名称(zknode*)
  level: 'INFO', // 日志级别
  timeOut: '', // 超时时间
  key: '', // 关键字搜索
  lastTime: '1800000', // 时间段
  host_name: '', // 主机名称
  host_alias: '', // 主机别名
  docker_container: '', // 容器名称
  searchTimes: '' // 时间范围
})

const roleNameOptions = ref<any>()
const containerNamesOptions = ref<any>()

// 表格数据
const tableData = reactive({
  columns: [
    {
      prop: 'host_name',
      label: computed(() => t('table.hostName'))
    },
    {
      prop: 'component_name',
      label: computed(() => t('table.componentName'))
    },
    {
      prop: 'docker_container',
      label: computed(() => t('table.containerName'))
    },
    {
      prop: 'level',
      label: computed(() => t('table.logLevel')),
      slot: true
    },
    {
      prop: '@timestamp',
      label: computed(() => t('table.time')),
      formatter: tableDayFormat,
      default: true
    },
    {
      prop: 'message',
      label: computed(() => t('table.information')),
      'min-width': 400,
      default: true
    }
  ],
  data: []
})

const hostOptions = ref<string[]>([])
const hostAliasOptions = ref([])
onMounted(() => {
  store.dispatch('serviceInstance/getServiceInstancelistroles', { type: 1, clusterId: props.clusterId }).then((res: any) => {
    roleNameOptions.value = res.data
    if (msgSource === 'docker') {
      searchModelObj.index = String(roleSimpleName)
      roleNameChange()
      searchModelObj.docker_container = String(containerName)

      getAllEs()
    }
    if (deployWay == '1') {
      searchModelObj.index = String(roleSimpleName)
      roleNameChange()
      searchModelObj.docker_container = String(instanceName)
      getAllEs()
    }
  })
})
nextTick(() => {
  onScroll(tableData, getAllEs, '.dockerTable')
})

function roleNameChange() {
  containerNamesOptions.value = []
  searchModelObj.docker_container = ''
  for (const item of roleNameOptions.value) {
    if (item.roleName === searchModelObj.index) {
      containerNamesOptions.value = item.containerNames
    }
  }
  searchModelObj.host_name = ''
  const data = roleNameOptions.value.find((item: any) => item.roleName === searchModelObj.index)?.hostNames
  const dataAlias = roleNameOptions.value.find((item: any) => item.roleName === searchModelObj.index)?.aliasNames
  hostOptions.value = data ?? []
  hostAliasOptions.value = dataAlias ?? []
}

// 选中时间段发生变化时
function lastTimeChange() {
  if (searchModelObj.lastTime !== '') {
    searchModelObj.searchTimes = ''
  }
}

// 选中时间范围发生变化时
function searchTimesChange() {
  if (searchModelObj.searchTimes !== '') {
    searchModelObj.lastTime = ''
  }
}

// 根据条件查询表格
function searchTable() {
  emits('update:isLoading', true)
  tableData.data = []
  getAllEs()
}

// 重置表格
function resetTable() {
  for (const item in searchModelObj) {
    searchModelObj[item] = ''
  }
  searchModelObj.lastTime = '1800000'
  searchModelObj.level = 'INFO'
  tableData.data = []
}
function getSearchParams() {
  const { index, level, timeOut, key, lastTime, host_name, docker_container, searchTimes } = searchModelObj
  const tableLength = tableData.data.length
  const searchAfter = tableLength ? tableData.data[tableLength - 1]['sort'] : new Date().getTime()
  let start = ''
  let end = ''
  if (lastTime) {
    start = dayFormat(new Date().getTime() - Number(lastTime))
    end = dayFormat(new Date().getTime())
  } else if (searchTimes) {
    start = searchTimes[0]
    end = searchTimes[1]
  }
  return {
    searchAfter,
    index,
    level,
    timeOut,
    key,
    msgSource: 'docker',
    where: {
      host_name,
      docker_container,
      searchTimes: {
        start,
        end
      }
    },
    clusterId: props.clusterId
  }
}
// 根据条件获取列表数据
function getAllEs() {
  const param = getSearchParams()
  if (!param.index) {
    return
  }
  store
    .dispatch('es/getAllEs', param)
    .then((res: any) => {
      const result = res.data.records
      tableData.data = tableData.data.concat(result)
      total.value = res.data.total
      load.value = true
    })
    .finally(() => {
      loading.value = false
      emits('update:isLoading', false)
    })
}
function hostAliasChange(index: number) {
  searchModelObj.host_name = hostOptions.value[index]
}

function hostChange(value: string) {
  searchModelObj.host_alias = hostAliasOptions.value[hostOptions.value.indexOf(value)] ?? ''
}
</script>

<style lang="scss" scoped>
@import '../scss/log';
</style>
