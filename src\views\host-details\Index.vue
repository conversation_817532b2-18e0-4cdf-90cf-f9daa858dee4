<template>
  <div class="host-details">
    <el-tabs v-model="activeTab" class="host-details-tabs">
      <el-tab-pane :lazy="true" :label="$t('replenish.status')" :name="Tabs.STATUS">
        <div class="host-details-content">
          <HostInformation v-loading="loading" :data="hostDetailsData?.hostBasicInfo" />
          <div v-loading="loading">
            <TheTitle>
              <template #titleText>{{ $t('title.diskSystem') }}</template>
            </TheTitle>
            <div class="disk-system">
              <BaseEmpty
                v-if="!hostDetailsData?.diskSystemPanel"
                image="nochart.png"
                :image-size="120"
                :description="$t('message.noChartAvailable')"
              />
              <iframe v-else :src="hostDetailsData?.diskSystemPanel.panelUrl" class="disk-system-iframe" frameborder="0" />
            </div>
          </div>
          <RoleList v-loading="loading" class="mb-20px" :data="hostDetailsData?.hostDetailRoleInfoList" />
          <DiskUsage v-loading="loading" :data="hostDetailsData?.hostMonitorPanels" />
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="$has('monitor-hosts')" :lazy="true" :label="$t('replenish.chart')" :name="Tabs.CHART">
        <MonitorHost />
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('replenish.role')" :name="Tabs.ROLE">
        <div class="host-details-content nopadding">
          <TheTitle padding="20px 0 0 20px">
            <template #titleText> {{ $t('replenish.hostRole') }}</template>
          </TheTitle>
          <div style="height: calc(100% - 65px); padding: 0 20px">
            <HostRoleList :data="hostResourceData" @refresh="getHostResource" />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('replenish.resources')" :name="Tabs.RESOURCE">
        <div class="host-details-content nopadding">
          <TheTitle padding="20px 0 0 20px">
            <template #titleText>{{ $t('replenish.cpuResources') }}</template>
          </TheTitle>
          <div style="height: 300px; padding: 0 20px 20px">
            <HostCPUResource :data="hostResourceData" />
          </div>
          <TheTitle padding="20px 0 0 20px">
            <template #titleText>{{ $t('replenish.memory') }}</template>
          </TheTitle>
          <div style="height: 300px; padding: 0 20px 20px">
            <HostCoreResource :data="hostResourceData" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <BaseButton class="host-details-opbtn" @click="back" type="info">{{ $t('replenish.return') }}</BaseButton>
  </div>
</template>

<script lang="ts" setup>
import MonitorHost from '../monitor/components/MonitorHost.vue'
import HostCPUResource from './components/HostCPUResource.vue'
import HostCoreResource from './components/HostCoreResource.vue'
import DiskUsage from './components/HostDetailsDiskUsage.vue'
import HostInformation from './components/HostDetailsHostInformation.vue'
import RoleList from './components/HostDetailsRoleList.vue'
import HostRoleList from './components/HostRoleList.vue'

const { route, setBreadList, t, router, store, $has } = useBasicTool()
const hostDetailsData = ref()
const loading = ref(true)

enum Tabs {
  STATUS = 'status',
  CHART = 'chart',
  ROLE = 'role',
  RESOURCE = 'resource'
}
const activeTab = ref(route.query.tab || Tabs.STATUS)

const hostResourceData = ref<any>([])

onMounted(() => {
  setBreadList([])
  getHostDetailData()
  getHostResource()
})
function getHostDetailData() {
  store
    .dispatch('hosts/hostDetail', { hostId: route.query.hostId })
    .then((res) => {
      hostDetailsData.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}
function getHostResource() {
  store.dispatch('hosts/hostRoleInfo', { hostId: route.query.hostId, pageNo: 1, pageSize: 1000 }).then((res) => {
    hostResourceData.value = res.data.records
  })
}
function back() {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.host-details {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  background-color: var(--ops-bg-white-color);
  :deep(.host-details-opbtn) {
    position: absolute;
    top: 8px;
    right: 20px;
  }
  &-tabs {
    height: 100%;
    :deep(.el-tabs__header) {
      margin-bottom: 0;
      .el-tabs__nav-scroll {
        padding: 0 20px;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
      }
      .el-tabs__item {
        height: 48px;
        font-family: PingFangSC, PingFang SC;
        font-size: 16px;
        &.is-active {
          font-weight: 600;
          color: rgba(86, 122, 255, 1);
        }
      }
    }
    :deep(.el-tabs__content) {
      overflow-y: auto;
      height: calc(100% - 48px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  .host-details-content {
    box-sizing: border-box;
    height: 100%;
    padding: 20px;
    &.nopadding {
      padding: 0;
    }
    .disk-system {
      height: 240px;
      border: 1px solid #e6e9f4;
      margin-bottom: 20px;
    }
    .disk-system-iframe {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
