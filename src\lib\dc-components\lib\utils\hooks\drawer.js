'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');

function useDrawer({
  cancel,
  confirm,
  show,
  defaultData
} = {}) {
  const visible = vue.ref(false);
  const drawerData = vue.ref(defaultData);
  const showDrawer = (val) => {
    visible.value = true;
    drawerData.value = val;
    if (show && typeof show === "function") {
      show(val);
    }
  };
  const onCancel = (val) => {
    visible.value = false;
    if (cancel && typeof cancel === "function") {
      cancel(val, drawerData.value);
    }
  };
  const onConfirm = (val) => {
    onCancel();
    if (confirm && typeof confirm === "function") {
      confirm(val, drawerData.value);
    }
  };
  return {
    showDrawer,
    onCancel,
    onConfirm,
    visible,
    drawerData
  };
}

exports.useDrawer = useDrawer;
//# sourceMappingURL=drawer.js.map
