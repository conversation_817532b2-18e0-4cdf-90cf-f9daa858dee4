<template>
  <div class="components">
    <BaseTitle :title="t('router.viewComponents')" />
    <div class="components-view">
      <OverviewHeader ref="ohRef" @monitorLabelChange="monitorLabelChange" />
      <BaseElTabs :activeBarTransAni="false" v-model="isTabs" @tab-change="changeTab">
        <el-tab-pane :lazy="true" :label="t('replenish.componentOverview')" name="5">
          <ComponentOverview @linkToInstance="linkToInstance" />
        </el-tab-pane>
        <el-tab-pane v-if="$has('monitor-components')" :lazy="true" :label="$t('replenish.componentMonitoring')" name="11">
          <ComponentMonitor :templateLabel="templateLabel" :id="String(clusterServiceId)" />
        </el-tab-pane>
        <el-tab-pane :lazy="true" :label="$t('button.tabCombinationInstance')" name="0">
          <ComponentInstance @linkToConfig="openConfig" :roleName="linkEventData?.roleName" />
        </el-tab-pane>
        <el-tab-pane :lazy="true" :label="$t('button.tabCombinedConfiguration')" name="1">
          <div class="components-view-content">
            <div class="components-view__tab">
              <span
                :class="{
                  'components-view__tab-buttom': true,
                  'components-view__tab-buttom--selected': activetabButtom === '0'
                }"
                @click="tabButtomClick('0')"
                >{{ $t('replenish.parameterConfiguration') }}</span
              >
              <span
                :class="{
                  'components-view__tab-buttom': true,
                  'components-view__tab-buttom--selected': activetabButtom === '1'
                }"
                @click="tabButtomClick('1')"
                >{{ $t('button.configurationRecord') }}</span
              >
              <span
                v-if="$has('yarn-resource-pool-configuration') && route.query.name === 'YARN'"
                :class="{
                  'components-view__tab-buttom': true,
                  'components-view__tab-buttom--selected': activetabButtom === '2'
                }"
                @click="tabButtomClick('2')"
                >{{ $t('button.yarnResource') }}</span
              >
              <span
                :class="{
                  'components-view__tab-buttom': true,
                  'components-view__tab-buttom--selected': activetabButtom === '3'
                }"
                @click="tabButtomClick('3')"
                >配置对比</span
              >
            </div>
            <div class="components-view-content_list" v-if="isTabs === '1'">
              <ComponentConfig
                :configKey="linkEventData?.configKey"
                v-if="activetabButtom === '0'"
                :clusterServiceId="String(clusterServiceId)"
                :clusterServiceInstanceId="linkEventData?.clusterServiceInstanceId"
              />
              <ComponentConfigRecord
                v-if="activetabButtom === '1'"
                :clusterServiceId="String(clusterServiceId)"
                :clusterServiceInstanceId="linkEventData?.clusterServiceInstanceId"
              />
              <YarnResource v-if="activetabButtom === '2'" @linkToConfig="openConfig" />
              <div v-if="activetabButtom === '3'" class="diff-editor-container">
                <h3>配置对比</h3>
                <CodeDiffEditor :originCode="originCode" :modifiedCode="modifiedCode" :isShow="true" :readOnly="true" />
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="!['StarRocks'].includes(route.query.name?.toString() ?? '') && !NeedControlOperateComponent.includes(componentModuleName)"
          :label="$t('button.operationHistory')"
          name="2"
          :lazy="true"
        >
          <OperateHistory />
        </el-tab-pane>
        <el-tab-pane :lazy="true" v-if="route.query.name === 'YARN' && $has('yarn-task-management')" :label="$t('button.taskManagement')" name="3">
          <YarnTaskManage />
        </el-tab-pane>
        <!-- 根据componentModuleId判断{{ t('replenish.yes') }}{{ t('replenish.no') }}存在 -->
        <el-tab-pane
          v-if="route.query.name === 'Flink' && componentModuleId === '24' && $has('flink-operator-task-management')"
          :label="$t('button.taskManagement')"
          name="4"
          :lazy="true"
        >
          <FlinkTaskManage />
        </el-tab-pane>
        <!-- 根据componentModuleId判断{{ t('replenish.yes') }}{{ t('replenish.no') }}存在 -->
        <el-tab-pane
          :lazy="true"
          v-if="route.query.name === 'Spark' && $has('spark-operator-task-management')"
          :label="$t('button.taskManagement')"
          name="6"
        >
          <SparkTaskManage />
        </el-tab-pane>
        <el-tab-pane
          :lazy="true"
          v-if="route.query.name === 'HBase' && $has('hbase-region-management')"
          :label="'Region' + $t('replenish.management')"
          name="7"
        >
          <ComponentRegion />
        </el-tab-pane>
        <el-tab-pane
          :lazy="true"
          v-if="route.query.name === 'HDFS' && $has('hdfs-little-file-management')"
          :label="$t('replenish.smallFileManager')"
          name="8"
        >
          <LittleFile />
        </el-tab-pane>
        <el-tab-pane
          :lazy="true"
          v-if="route.query.name === 'Kerberos' && $has('kerberos-user-management')"
          :label="$t('replenish.userManager')"
          name="9"
        >
          <KerberUser />
        </el-tab-pane>
        <el-tab-pane :lazy="true" v-if="route.query.name === 'Hive' && $has('hive-task-monitor')" :label="$t('replenish.taskMonitoring')" name="10">
          <HiveTaskManage />
        </el-tab-pane>
      </BaseElTabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import ComponentConfig from '@/views/configure/Index.vue'
import ComponentConfigRecord from '@/views/namespace/config-record/Index.vue'
import FlinkTaskManage from '@/views/namespace/flink-task-management/Index.vue'
import HiveTaskManage from '@/views/namespace/overview/components/ComponentsHiveTaskMonitor.vue'
import KerberUser from '@/views/namespace/overview/components/ComponentsKerberosUser.vue'
import LittleFile from '@/views/namespace/overview/components/ComponentsLitterFile.vue'
import OperateHistory from '@/views/namespace/overview/components/ComponentsOperationHistory.vue'
import ComponentRegion from '@/views/namespace/overview/components/ComponentsRegion.vue'
import YarnTaskManage from '@/views/namespace/overview/components/ConponentsYarnTaskManagement.vue'
import SparkTaskManage from '@/views/namespace/spark-task-management/Index.vue'
import YarnResource from '@/views/namespace/yarn-resource/Index.vue'
import ComponentMonitor from '../../../monitor/components/MonitorComponent.vue'
import ComponentInstance from '../../components/CurrentComponentInstance.vue'
import { NeedControlOperateComponent } from '../../constants'
import ComponentOverview from './ComponentOverview.vue'
import OverviewHeader from './ComponentOverviewHeader.vue'
import Base from '@/utils/Base'

const { route, t, router, store, setBreadList, $has } = useBasicTool()
const { name: comName, componentModuleId, clusterServiceId, clusterServiceInstanceId = '', configKey = '', roleName = '' } = route.query
const isTabs = ref(route.query.isTabs || '5')
const activetabButtom = ref(route.query.activetabButtom || '0')
const componentModuleName = computed(() => `${comName}-${componentModuleId}`)
const templateLabel = ref('')

const ohRef = ref()
const linkEventData = ref({
  clusterServiceInstanceId: String(clusterServiceInstanceId),
  configKey: String(configKey),
  roleName: String(roleName)
})

const originCode = reactive({
  code: `# 原始配置文件
server:
  port: 8080
  host: localhost

database:
  url: ********************************
  username: root
  password: password123

logging:
  level: INFO
  file: /var/log/app.log`,
  language: 'yaml'
})

const modifiedCode = reactive({
  code: `# 修改后的配置文件
server:
  port: 9090
  host: 0.0.0.0

database:
  url: ****************************************
  username: admin
  password: newPassword456
  pool:
    max-connections: 20

logging:
  level: DEBUG
  file: /var/log/app.log

cache:
  enabled: true
  ttl: 3600`,
  language: 'yaml'
})

onMounted(() => {
  setBreadList([])
  getData()
  getConfigDiffData()
})

function getData() {
  store.dispatch('cluster/serviceComponentInfo', { clusterServiceId }).then((res) => {
    templateLabel.value = res.data.monitorLabel
  })
}

// 获取配置对比数据
function getConfigDiffData() {
  // 如果有具体的配置记录ID，可以调用真实的API
  if (linkEventData.value.clusterServiceInstanceId && linkEventData.value.configKey) {
    // 这里可以调用真实的配置对比API
    // store.dispatch('clusterServiceConfig/compareSnapShotDetail', {
    //   sourceRecordId: 'xxx',
    //   targetRecordId: 'yyy',
    //   confName: linkEventData.value.configKey
    // }).then((res) => {
    //   if (res.data && res.data.records && res.data.records.length > 0) {
    //     const record = res.data.records[0]
    //     originCode.code = record.oldValue || ''
    //     modifiedCode.code = record.newValue || ''
    //     // 根据配置文件类型设置语言
    //     const fileExtension = linkEventData.value.configKey.split('.').pop()
    //     const languageMap = {
    //       'xml': 'xml',
    //       'properties': 'properties',
    //       'yaml': 'yaml',
    //       'yml': 'yaml',
    //       'json': 'json',
    //       'conf': 'ini'
    //     }
    //     const language = languageMap[fileExtension] || 'text'
    //     originCode.language = language
    //     modifiedCode.language = language
    //   }
    // })
  }
}

function tabButtomClick(name: string) {
  activetabButtom.value = name
}

function changeTab() {
  if (isTabs.value !== '1') {
    activetabButtom.value = '0'
  }
  linkEventData.value = {
    clusterServiceInstanceId: '',
    configKey: '',
    roleName: ''
  }
}

function openConfig(val: any) {
  linkEventData.value = val
  isTabs.value = '1'
  activetabButtom.value = '0'
  // 当打开配置时，重新获取配置对比数据
  getConfigDiffData()
}

function linkToInstance(roleName: string) {
  isTabs.value = '0'
  linkEventData.value.roleName = roleName
}

function monitorLabelChange(label: string) {
  if (label && label !== templateLabel.value) {
    templateLabel.value = label
  }
}
</script>
<style lang="scss" scoped>
.components {
  height: 100%;
  .components-view {
    display: flex;
    flex-flow: column;
    width: 100%;
    height: calc(100% - 48px);
    background-color: var(--ops-bg-white-color);
    :deep(.base-el-tabs) {
      overflow: hidden;
      flex: 1;
      .el-tabs {
        height: 100%;
      }
      .el-tabs__content {
        height: calc(100% - 48px);
        padding: 0;
      }
      .el-tab-pane {
        overflow-y: auto;
        height: 100%;
      }
      .components-view-content {
        position: relative;
        height: 100%;
        &_list {
          height: calc(100% - 52px);
        }
      }
    }
  }
  .components-view__tab {
    position: relative;
    z-index: 2;
    display: inline-flex;
    padding: 20px 20px 0;
    .components-view__tab-buttom {
      display: inline-block;
      min-width: 80px;
      height: 32px;
      padding: 0px 10px;
      border: 1px solid #f4f5f9;
      border-radius: 16px;
      margin-right: 20px;
      font-size: 14px;
      line-height: 32px;
      text-align: center;
      color: #99a0b5;
      background-color: #f4f5f9;
      cursor: pointer;
    }
    .components-view__tab-buttom--selected {
      border-color: var(--ops-primary-color);
      color: var(--ops-primary-color);
      background-color: rgb(71 119 255 / 10%);
    }
  }
}

.diff-editor-container {
  padding: 20px;

  h3 {
    margin-bottom: 16px;
    color: var(--ops-text-color);
    font-size: 16px;
    font-weight: 600;
  }

  :deep(.container-code) {
    height: 500px !important;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }
}
</style>
