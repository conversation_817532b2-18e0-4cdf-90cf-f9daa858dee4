<template>
  <BaseLayout>
    <template #header>
      <BaseSearch :searchItemData="searchItemData" @on-submit="submitSearch" @reset-search="resetSearch" />
      <BaseAddButton :is-button="$has('k8s-add')" @click="handleAddK8s">{{ $t('button.addK8s') }}</BaseAddButton>
    </template>
    <template #content>
      <BaseTable v-loading="tableLoading" :columns="tableData.columns" :data="k8sStore.k8sList">
        <template #after>
          <el-table-column
            v-if="$has('k8s-update') || $has('k8s-delete') || $has('k8s-namespace')"
            :label="$t('table.operate')"
            :width="300"
            fixed="right"
          >
            <template #default="scope">
              <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" :dropdown-data="tableToolbar(scope).dropdownData" />
            </template>
          </el-table-column>
        </template>
      </BaseTable>
    </template>
    <template #footer>
      <BasePagination v-model="pageInfo" @page-change="onPageChange" />
    </template>
  </BaseLayout>
  <TenantPermission v-model="tenantP" :k8sId="currentK8sId" @submit="getK8sList" />
  <OperateK8s v-model="dialogVisible" :is-edit="isEdit" :edit-data="editData" @clone-dialog="cloneK8sDialog" />
</template>

<script lang="ts" setup>
import { tableDayFormat } from '@/utils/Day'
import elConfim from '@/utils/elConfim'
import OperateK8s from './components/OperateK8s.vue'
import TenantPermission from './components/TenantPermission.vue'
const { t, store, router, $has, setBreadList } = useBasicTool()
const { pageInfo, resetPageInfo } = usePage()
onMounted(() => {
  getK8sList()
  setBreadList([
    {
      name: t('router.kubernetesManagement')
    }
  ])
})
const tableLoading = ref(true)
const k8sStore = computed(() => store.state.k8s)
const searchValue = ref('')
const tenantP = ref(false)
const currentK8sId = ref('')
const tenantId = computed(() => store.state.user.tenantId)
const tableData = reactive({
  columns: [
    { label: computed(() => t('table.kubernetesName')), prop: 'k8sName' },
    { label: computed(() => t('table.kubernetesClusterAddress')), prop: 'k8sBasePath' },
    { label: computed(() => t('table.verificationMethod')), prop: 'clientAuthType', formatter: getClientAuthType },
    { label: computed(() => t('table.region')), prop: 'region' },
    { label: computed(() => t('table.creationTime')), prop: 'createTime', formatter: tableDayFormat }
  ]
})
// 搜索配置项
const searchItemData = reactive<Array<BaseSearch.SearchItemData>>([
  {
    label: computed(() => t('form.name') + '：'),
    type: 'input',
    param: 'k8sName',
    defaultValue: '',
    placeholder: computed(() => t('form.pleaseEnterK8sName'))
  }
])
const type = computed(() => Number(store.state.user.userInfo.type))
// 搜索提交
function submitSearch(value: any) {
  pageInfo.pageNo = 1
  searchOpera(value)
}
// 重置搜索框
function resetSearch(value: any) {
  resetPageInfo()
  searchOpera(value)
}
// 搜索操作
function searchOpera(value: any) {
  searchValue.value = value.k8sName
  tableLoading.value = true
  getK8sList()
}

function cloneK8sDialog() {
  getK8sList()
}

const dialogVisible = ref(false)
const isEdit = ref(false)
const editData = ref()

function getClientAuthType(row: any, column: any, cellValue: any, index: any) {
  const stateMap = new Map().set(1, t('replenish.clientCertificate')).set(2, 'Token').set(3, 'kubeconfig')
  return stateMap.get(cellValue) ? String(stateMap.get(cellValue)) : cellValue
}
function getK8sList() {
  store
    .dispatch('k8s/getK8sList', { k8sName: searchValue.value, pageNo: pageInfo.pageNo, pageSize: pageInfo.pageSize, tenantId: tenantId.value })
    .then((res) => {
      pageInfo.total = res.data.total
      tableLoading.value = false
    })
}
function handleAddK8s() {
  dialogVisible.value = true
  isEdit.value = false
}
function onPageChange() {
  getK8sList()
}

// 表格操作栏数据
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('replenish.tenantPermissions'),
        disabled: false,
        click: () => {
          currentK8sId.value = scope.row.id
          tenantP.value = true
        },
        isButton: type.value === 2 && !tenantId.value
      },
      {
        buttonName: t('replenish.clusterManagement'),
        disabled: false,
        click: () => {
          store.commit('k8s/SET_K8S_ID', scope.row.id)
          store.commit('k8s/SET_K8S_ADMIN_AUTH', scope.row.adminAuth)
          router.push({ name: 'k8snamespace', query: { id: scope.row.id, adminAuth: scope.row.adminAuth, k8sName: scope.row.k8sName } })
        },
        isButton: $has('k8s-namespace')
      },
      {
        buttonName: t('table.edit'),
        disabled: false,
        click: () => {
          editK8s(scope)
        },
        isButton: $has('k8s-update') && scope.row.tenantId === tenantId.value
      },
      {
        buttonName: t('table.delete'),
        disabled: false,
        click: () => {
          deleteK8s(scope)
        },
        isButton: $has('k8s-delete')
      }
    ]
  }
}

function editK8s(scope: any) {
  dialogVisible.value = true
  isEdit.value = true
  editData.value = scope.row
}

function deleteK8s(scope: any) {
  elConfim
    .confim({
      isCancelButton: true,
      message: t('replenish.isDelete', {
        valuie: scope.row.k8sName
      })
    })
    .then(() => {
      store.dispatch('k8s/deleteK8s', { id: scope.row.id }).then((res) => {
        if (Array.isArray(res.data) && res.data.length > 0) {
          elConfim.confim({
            isCancelButton: false,
            title: t('title.tip'),
            message: t('replenish.dueToKubernetesBinding', {
              value: res.data?.join('、')
            })
          })
        } else {
          getK8sList()
        }
      })
    })
}
</script>
