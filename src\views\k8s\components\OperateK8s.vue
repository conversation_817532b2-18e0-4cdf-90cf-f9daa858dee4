<template>
  <el-dialog :model-value="modelValue" width="580px" :title="isEdit ? $t('title.editK8s') : $t('title.addK8s')" :before-close="handleClose">
    <el-form ref="K8sRef" :model="K8sForm" class="k8sForm" :rules="K8sFormRules" label-width="auto">
      <el-form-item :label="`${$t('form.certificationDocuments')}:`" prop="clientAuthenticationType">
        <span v-if="isEdit">{{ stateMap.get(K8sForm.clientAuthenticationType) }}</span>
        <el-select v-else class="input-width" v-model="K8sForm.clientAuthenticationType" :placeholder="$t('form.pleaseChoose')" @change="typeChange">
          <el-option v-for="(opt, index) in clientAuthenticationType" :key="index" :label="opt.label" :value="opt.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t('form.k8sName')}:`" prop="K8sName">
        <el-input class="input-width" v-model="K8sForm.K8sName" :placeholder="$t('form.pleaseEnterAName')" />
      </el-form-item>
      <el-form-item class="storage-class" :label="`${$t('replenish.belongingRegion')}:`" prop="region">
        <el-select class="input-width" v-model="K8sForm.region" :placeholder="$t('form.pleaseChoose')">
          <el-option v-for="opt in regionList" :key="opt.value" :label="opt.label" :value="opt.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="kerb-config" :label="`${$t('form.certificationDocuments')}:`" style="padding-bottom: 10px" prop="kubeConfig">
        <el-upload
          ref="uploadRef"
          action=""
          v-bind="$attrs"
          :file-list="fileList"
          :limit="1"
          :on-change="fileChange"
          :on-remove="fileRemove"
          :on-exceed="handleExceed"
          :auto-upload="false"
          class="upload"
        >
          <BaseButton type="primary">{{ $t('button.uploadFiles') }}</BaseButton>
          <template #tip>
            <div class="el-upload__tip">{{ $t('form.k8sUpload') }}</div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item class="storage-class" :label="'StorageClass:'" prop="storageClass">
        <span v-if="isEdit">{{ K8sForm.storageClass }}</span>
        <el-select v-else class="input-width" v-model="K8sForm.storageClass" :placeholder="$t('form.pleaseChoose')">
          <el-option v-for="(opt, index) in storageClassList" :key="index" :value="opt" />
        </el-select>
      </el-form-item>
      <el-form-item :label="`${$t('replenish.isAdmin')}:`" prop="adminAuth">
        <el-radio-group v-model="K8sForm.adminAuth">
          <el-radio :label="true">{{ $t('replenish.yes') }}</el-radio>
          <el-radio :label="false">{{ $t('replenish.no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!K8sForm.adminAuth" :label="'ServiceAccount:'" prop="serviceAccount">
        <el-input class="input-width" v-model="K8sForm.serviceAccount" :placeholder="$t('form.pleaseEnter')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <BaseButton :loading="isLoading" type="primary" @click="onSubmitForm(K8sRef)">{{ $t('button.sure') }}</BaseButton>
        <BaseButton type="info" @click="onCancel(K8sRef)">{{ $t('button.cancel') }}</BaseButton>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { getK8sRegion } from '@/api/k8sApi'
import type { FormRules, UploadFile, UploadFiles } from 'element-plus'
import { ElForm, ElMessage } from 'element-plus'
type FormInstance = InstanceType<typeof ElForm>
const { t, store } = useBasicTool()
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object as any,
    default: () => ({})
  }
})
const storageClassList = ref<string[]>([])
const emit = defineEmits(['update:modelValue', 'cloneDialog'])
const isLoading = ref(false)
type K8sFormType = {
  clientAuthenticationType: string
  K8sName: string
  kubeConfig: any[]
  storageClass: string
  adminAuth: any
  serviceAccount: string
  region: string
}
const K8sForm = reactive<K8sFormType>({
  clientAuthenticationType: '',
  K8sName: '',
  kubeConfig: [],
  storageClass: '',
  adminAuth: false,
  serviceAccount: '',
  region: ''
})
const clientAuthenticationType = [
  // { label: t('replenish.clientCertificate'), value: 'CERTIFICATE' },
  // { label: 'Token', value: 'BEARER_TOKEN' },
  { label: 'Kubeconfig', value: 'KUBECONFIG' }
]
const regionList = ref<
  {
    label: string
    value: string
  }[]
>([])
const configEdit = ref<any>(false)
const stateMap = new Map().set(1, t('replenish.clientCertificate')).set(2, 'Token').set(3, 'kubeconfig')
const K8sFormRules = reactive<FormRules>({
  clientAuthenticationType: [
    {
      required: true,
      message: t('message.selectAuthentication'),
      trigger: 'change'
    }
  ],
  K8sName: [
    {
      required: true,
      message: t('form.pleaseEnterK8sName'),
      trigger: 'blur'
    }
  ],
  kubeConfig: [
    {
      required: true,
      trigger: 'blur',
      validator: checkFile
    }
  ],
  storageClass: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  region: [
    {
      required: false,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  adminAuth: [
    {
      required: true,
      message: t('form.pleaseChoose'),
      trigger: 'change'
    }
  ],
  serviceAccount: [
    {
      required: true,
      message: t('form.pleaseEnter'),
      trigger: 'change'
    }
  ]
})
const fileList = ref<any>([])
const K8sRef = ref<FormInstance>()
const uploadRef = ref()
watch(
  () => fileList.value,
  () => {
    K8sRef.value?.validateField('kubeConfig', () => null)
  }
)
watch(
  () => props.modelValue,
  () => {
    if (props.isEdit && props.modelValue) {
      K8sForm.clientAuthenticationType = props.editData.clientAuthType
      K8sForm.K8sName = props.editData.k8sName
      K8sForm.storageClass = props.editData.storageClass
      K8sForm.adminAuth = props.editData.adminAuth
      K8sForm.serviceAccount = props.editData.serviceAccount
      K8sForm.region = props.editData.region
      const formData = new FormData()
      const blob = new Blob([props.editData.kubeConfig])
      formData.append('file', blob, 'config')
      fileList.value.push({ name: 'config', raw: formData.get('file') })
    } else {
      K8sForm.clientAuthenticationType = ''
      K8sForm.K8sName = ''
      K8sForm.kubeConfig = []
      K8sForm.storageClass = ''
      K8sForm.adminAuth = false
      K8sForm.serviceAccount = ''
      K8sForm.region = ''
    }
  }
)
function checkFile(rule: any, value: any, callback: any) {
  if (!fileList.value || fileList.value.length < 1) {
    return callback(new Error(t('message.selectFile')))
  }
  return callback()
}

function onSubmitForm(formEl: FormInstance | undefined) {
  if (!formEl || isLoading.value) return
  formEl.validate((valid) => {
    if (valid) {
      isLoading.value = true
      const paramsData = new FormData()
      fileList.value.forEach((f: any) => paramsData.append('kubeConfig', f.raw))
      paramsData.append('K8sName', K8sForm.K8sName)
      paramsData.append('adminAuth', K8sForm.adminAuth)
      paramsData.append('serviceAccount', K8sForm.serviceAccount)
      paramsData.append('region', K8sForm.region)
      if (props.isEdit) {
        paramsData.append('configEdit', configEdit.value)
        paramsData.append('id', props.editData.id)
        store
          .dispatch('k8s/editK8s', paramsData)
          .then((response) => {
            if (response?.code === 0) {
              emit('cloneDialog')
              onCancel(K8sRef.value)
            } else {
              ElMessage.error(response?.msg)
            }
          })
          .finally(() => {
            isLoading.value = false
          })
      } else {
        paramsData.append('clientAuthenticationType', K8sForm.clientAuthenticationType)
        paramsData.append('storageClass', K8sForm.storageClass)
        store
          .dispatch('k8s/k8sAuth', paramsData)
          .then((response) => {
            if (response?.code === 0) {
              emit('cloneDialog')
              onCancel(K8sRef.value)
              ElMessage({
                message: t('message.k8sAddedSuccessful'),
                type: 'success'
              })
            } else {
              ElMessage.error(response?.msg)
            }
          })
          .finally(() => {
            isLoading.value = false
          })
      }
    }
  })
}
function onCancel(formEl: FormInstance | undefined) {
  if (!formEl) return
  uploadRef?.value?.clearFiles()
  K8sForm.K8sName = ''
  configEdit.value = false
  K8sForm.kubeConfig = []
  storageClassList.value = []
  formEl.resetFields()
  fileList.value = []
  emit('update:modelValue', false)
}
function verify(file: UploadFile) {
  if (file.size && file.size > 100 * 1024) {
    ElMessage.error(t('message.fileSize'))
    return false
  }
  return true
}
function handleExceed(files: any) {
  if (verify(files[0])) {
    uploadRef?.value?.clearFiles()
    uploadRef?.value?.handleStart(files[0])
  }
}
function fileChange(file: UploadFile, files: UploadFiles) {
  if (verify(file)) {
    fileList.value = files
    setStorageClassList()
  } else {
    uploadRef?.value?.clearFiles()
    fileList.value = []
  }
  configEdit.value = true
  K8sRef.value?.validateField('kubeConfig', () => null)
}

function typeChange() {
  setStorageClassList()
}
function setStorageClassList() {
  if (!K8sForm.clientAuthenticationType || fileList.value.length === 0 || props.isEdit) return
  const paramsData = new FormData()
  fileList.value.forEach((f: any) => paramsData.append('kubeConfig', f.raw))
  paramsData.append('clientAuthenticationType', K8sForm.clientAuthenticationType)
  store.dispatch('k8s/getStorageClassList', paramsData).then((res) => {
    if (res?.code === 0) {
      storageClassList.value = res.data
    } else {
      ElMessage.error(res?.msg)
    }
  })
}

function fileRemove(file: UploadFile, files: UploadFiles) {
  fileList.value = files
  K8sRef.value?.validateField('kubeConfig', () => null)
}
function handleClose() {
  onCancel(K8sRef.value)
}

const getRegion = () => {
  getK8sRegion().then((response) => {
    regionList.value = response.data.data.map((el) => {
      return {
        label: el,
        value: el
      }
    })
  })
}

onMounted(() => {
  getRegion()
})
</script>
<style lang="scss" scoped>
.input-width {
  width: 320px;
}
:deep(.el-form-item__content) {
  line-height: 10px;
}
// 解决文件名展示不全问题
:deep(.el-upload-list__item-name) {
  height: 28px;
}
:deep(.el-upload-list__item-file-name) {
  height: 28px;
  line-height: 28px;
}
.k8sForm {
  .kerb-config {
    align-items: flex-start;
  }
  .upload {
    .el-upload__tip {
      margin-top: 2px;
      line-height: 18px;
      word-break: normal;
    }
  }
}
</style>
