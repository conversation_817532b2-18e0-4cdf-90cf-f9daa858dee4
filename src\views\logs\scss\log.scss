.search {
	.asterisk {
		&::before {
			content: '*';
			width: 10px;
			font-size: 14px;
			font-weight: 600;
			color: #f27272;
		}
	}
  :deep(.input-width) {
    width: 240px;
   }
}
.key-input {
	width: 400px;
}
:deep(.el-form--inline .el-form-item) {
	margin-right: 20px;
}
.open-box {
  @include flex()
}
.es-button {
  position: relative;
  bottom:8px;
}
.search-box {
  padding-top: 20px;
  padding-left: 20px;
  margin-bottom: 20px;
  background-color:#f9fafb;
  transition: all 0.3s;
}
.search-box--off {
  overflow: hidden;
  height: 0;
  padding-top: 0;
  margin-bottom: 0;
}

