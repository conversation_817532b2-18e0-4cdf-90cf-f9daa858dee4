{"version": 3, "file": "index.js", "sources": ["../../../../../packages/utils/install/index.ts"], "sourcesContent": ["import type { SFCWithInstall } from \"./types\";\r\n\r\nexport const withInstall = <T, E extends Record<string, any>>(\r\n  main: T,\r\n  extra?: E\r\n) => {\r\n  (main as SFCWithInstall<T>).install = (app): void => {\r\n    for (const comp of [main, ...Object.values(extra ?? {})]) {\r\n      app.component(comp.name, comp);\r\n    }\r\n  };\r\n\r\n  if (extra) {\r\n    for (const [key, comp] of Object.entries(extra)) {\r\n      (main as any)[key] = comp;\r\n    }\r\n  }\r\n  return main as SFCWithInstall<T> & E;\r\n};\r\n\r\nexport * from \"./types\";\r\n"], "names": [], "mappings": ";;;;;;AAEa,MAAA,WAAA,GAAc,CACzB,IAAA,EACA,KACG,KAAA;AACH,EAAC,IAAA,CAA2B,OAAU,GAAA,CAAC,GAAc,KAAA;AACnD,IAAW,KAAA,MAAA,IAAA,IAAQ,CAAC,IAAA,EAAM,GAAG,MAAA,CAAO,OAAO,KAAS,IAAA,EAAE,CAAC,CAAG,EAAA;AACxD,MAAI,GAAA,CAAA,SAAA,CAAU,IAAK,CAAA,IAAA,EAAM,IAAI,CAAA,CAAA;AAAA,KAC/B;AAAA,GACF,CAAA;AAEA,EAAA,IAAI,KAAO,EAAA;AACT,IAAA,KAAA,MAAW,CAAC,GAAK,EAAA,IAAA,CAAA,IAAS,MAAO,CAAA,OAAA,CAAQ,KAAK,CAAG,EAAA;AAC/C,MAAC,KAAa,GAAO,CAAA,GAAA,IAAA,CAAA;AAAA,KACvB;AAAA,GACF;AACA,EAAO,OAAA,IAAA,CAAA;AACT;;;;"}