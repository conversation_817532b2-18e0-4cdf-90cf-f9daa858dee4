{"version": 3, "file": "form.js", "sources": ["../../../../../packages/utils/hooks/form.ts"], "sourcesContent": ["import { reactive } from \"vue\";\r\n\r\ntype IData = Record<string, any>;\r\nexport function useDependModel(defaultModelData: IData = {}) {\r\n  const dependModel = reactive<IData>({ ...defaultModelData });\r\n  const changeDependModel = (val: IData) => {\r\n    Object.keys(val).forEach((item) => {\r\n      dependModel[item] = val[item];\r\n    });\r\n  };\r\n\r\n  return {\r\n    dependModel,\r\n    changeDependModel,\r\n  };\r\n}\r\n"], "names": ["reactive"], "mappings": ";;;;;;AAG+B,SAAA,cAAA,CAAA,gBAAA,GAA0B,EAAI,EAAA;AAC3D,EAAA,MAAM,WAAc,GAAAA,YAAA,CAAgB,EAAE,GAAG,kBAAkB,CAAA,CAAA;AAC3D,EAAM,MAAA,iBAAA,GAAoB,CAAC,GAAe,KAAA;AACxC,IAAA,MAAA,CAAO,IAAK,CAAA,GAAG,CAAE,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACjC,MAAA,WAAA,CAAY,QAAQ,GAAI,CAAA,IAAA,CAAA,CAAA;AAAA,KACzB,CAAA,CAAA;AAAA,GACH,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,WAAA;AAAA,IACA,iBAAA;AAAA,GACF,CAAA;AACF;;;;"}