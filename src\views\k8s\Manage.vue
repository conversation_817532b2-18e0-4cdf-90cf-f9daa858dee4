<template>
  <div class="host-manage">
    <TheTemplateTabs ref="theTemplateTabsRef" :tab-data="tabData" />
  </div>
</template>
<script lang="ts" setup>
const theTemplateTabsRef = ref()
const { t, route, $has, store } = useBasicTool()
const userData = computed(() => store.state.user)
const tabData = ref<{ label: string; name: string; isTabs?: boolean }[]>([
  { label: t('router.resourceManagement'), name: 'k8snamespace', isTabs: $has('k8s-namespace') },
  { label: t('router.monitor'), name: 'k8smonitor', isTabs: $has('k8s-namespace') }
])

watch(
  () => userData.value.points,
  () => {
    tabData.value = [
      { label: t('router.resourceManagement'), name: 'k8snamespace', isTabs: $has('k8s-namespace') },
      { label: t('router.monitor'), name: 'k8smonitor', isTabs: $has('k8s-namespace') }
    ]
  },
  {
    deep: true
  }
)

nextTick(() => {
  watchEffect(() => {
    theTemplateTabsRef.value?.setActiveTabs(route.query.tabs)
  })
})
</script>
<style lang="scss" scoped>
.host-manage {
  width: 100%;
  height: 100%;
}
</style>
