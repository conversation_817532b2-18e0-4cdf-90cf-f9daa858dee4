import { useI18n } from 'vue-i18n'
export function useComponentStatus() {
  const { t } = useI18n()
  // 获取状态映射值
  const stateMap = new Map()
    .set(0, t('state.notDeployed'))
    .set(1, t('state.deploying'))
    .set(2, t('state.deploymentFailure'))
    // .set(3, '无状态')
    .set(4, t('state.inOperation'))
    .set(5, t('state.stopped'))
    .set(6, t('state.uninstalled'))
    // .set(7, t('replenish.restarting'))
    .set(8, t('state.abnormal'))
    // .set(9, '升级中')
    .set(10, t('state.turningOn'))
    .set(11, t('state.restarting'))
    .set(12, t('state.isStopping'))
    .set(13, t('state.deleting'))
    .set(14, t('state.startupFailure'))
    .set(15, t('state.restartFailed'))
    .set(16, t('state.stopFailure'))
    .set(17, t('state.deletionFailed'))
    .set(18, t('state.expandingAndShrinkingTheCapactly'))
    .set(19, t('state.enlargingAndShrinkingCapacityFailed'))
    .set(20, t('replenish.beingSchedul'))
    .set(21, t('replenish.schedulingFailed'))
    .set(22, t('replenish.beingUpdat'))
    .set(23, t('replenish.updateFailed'))
    .set(24, t('replenish.awaitingRestart'))
    .set(25, t('replenish.awaitingRestart')) // 实际状态 -- 等待滚动重启
    .set(26, t('replenish.beingRestart')) // 实际状态 -- 正在滚动重启
    .set(27, t('replenish.restartFailed')) // 实际状态 -- 滚动重启失败
    .set(28, t('replenish.unhealthy'))
  //状态颜色类映射
  const stateMapStyle = new Map()
    .set(0, 'status-spot--standby')
    .set(1, 'status-spot--have-in-hand')
    .set(2, 'status-spot--failure')
    // .set(3, 'status-spot--standby')
    .set(4, 'status-spot--running')
    .set(5, 'status-spot--standby')
    .set(6, 'status-spot--standby')
    // .set(7, t('replenish.restarting'))
    .set(8, 'status-spot--abnormal')
    .set(10, 'status-spot--have-in-hand')
    .set(11, 'status-spot--have-in-hand')
    .set(12, 'status-spot--have-in-hand')
    .set(13, 'status-spot--have-in-hand')
    .set(14, 'status-spot--failure')
    .set(15, 'status-spot--failure')
    .set(16, 'status-spot--failure')
    .set(17, 'status-spot--failure')
    .set(18, 'status-spot--have-in-hand')
    .set(19, 'status-spot--failure')
    .set(21, 'status-spot--failure')
    .set(22, 'status-spot--failure')
    .set(23, 'status-spot--failure')
    .set(24, 'status-spot--standby')
    .set(25, 'status-spot--standby') // 实际状态 -- 等待滚动重启
    .set(26, 'status-spot--have-in-hand') // 实际状态 -- 正在滚动重启
    .set(27, 'status-spot--failure') // 实际状态 -- 滚动重启失败
    .set(28, 'status-spot--unhealthy')
  // 获取表格状态映射
  const getStateLabel = (row: any, column: any, cellValue: any, index: any) => {
    return stateMap.get(cellValue) ? String(stateMap.get(cellValue)) : cellValue
  }

  const errList = [14, 15, 27]
  return { stateMap, stateMapStyle, getStateLabel, errList }
}
