{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/select/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-select\r\n    v-bind=\"{ ...$attrs }\"\r\n    :model-value=\"props.modelValue\"\r\n    @change=\"handleChange\"\r\n  >\r\n    <template v-if=\"groups.length\">\r\n      <el-option-group\r\n        v-for=\"group in groups\"\r\n        :key=\"group.label\"\r\n        :label=\"group.label\"\r\n        :disabled=\"group.disabled\"\r\n      >\r\n        <el-option\r\n          v-for=\"(item, index) in group.options\"\r\n          :key=\"index\"\r\n          :label=\"item.label\"\r\n          :value=\"item.value\"\r\n          :disabled=\"item.disabled\"\r\n        >\r\n          <template v-if=\"item.defaultSlot\" #default=\"scope\">\r\n            <DcRenderVNode\r\n              :vnode=\"item.defaultSlot\"\r\n              :scope=\"scope\"\r\n              :extra-scope=\"item\"\r\n            />\r\n          </template>\r\n        </el-option>\r\n      </el-option-group>\r\n    </template>\r\n    <template v-else>\r\n      <el-option\r\n        v-for=\"(item, index) in options\"\r\n        :key=\"index\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n      >\r\n        <template v-if=\"item.defaultSlot\" #default=\"scope\">\r\n          <DcRenderVNode\r\n            :vnode=\"item.defaultSlot\"\r\n            :scope=\"scope\"\r\n            :extra-scope=\"item\"\r\n          />\r\n        </template>\r\n      </el-option>\r\n    </template>\r\n  </el-select>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, isRef, useAttrs } from \"vue\";\r\nimport { ElOption, ElOptionGroup, ElSelect } from \"element-plus\";\r\nimport DcRenderVNode from \"../../render-vnode/src/index\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcSelectGroups, DcSelectOptions } from \"./types\";\r\n\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: [String, Number, Boolean, Array],\r\n  },\r\n  options: {\r\n    type: [Array, Object] as PropType<DcSelectOptions>,\r\n    default: () => [],\r\n  },\r\n  groups: {\r\n    type: [Array, Object] as PropType<DcSelectGroups>,\r\n    default: () => [],\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\"]);\r\nconst attrs = useAttrs();\r\n\r\nconst options = computed(() => {\r\n  return isRef(props.options) ? props.options.value : props.options;\r\n});\r\n\r\nconst groups = computed(() => {\r\n  return isRef(props.groups) ? props.groups.value : props.groups;\r\n});\r\n\r\nconst handleChange = (val: string | number | boolean | any[]) => {\r\n  emits(\"update:modelValue\", val);\r\n  if (typeof attrs.onChange === \"function\") {\r\n    attrs.onChange(val);\r\n  }\r\n};\r\n\r\ndefineOptions({\r\n  name: \"DcSelect\",\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["DO_defineComponent", "useAttrs", "computed", "isRef"], "mappings": ";;;;;;;;;;;;AAwFc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;AAlCA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAcd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AACd,IAAA,MAAM,QAAQC,YAAS,EAAA,CAAA;AAEjB,IAAA,MAAA,OAAA,GAAUC,aAAS,MAAM;AAC7B,MAAA,OAAOC,UAAM,KAAM,CAAA,OAAO,IAAI,KAAM,CAAA,OAAA,CAAQ,QAAQ,KAAM,CAAA,OAAA,CAAA;AAAA,KAC3D,CAAA,CAAA;AAEK,IAAA,MAAA,MAAA,GAASD,aAAS,MAAM;AAC5B,MAAA,OAAOC,UAAM,KAAM,CAAA,MAAM,IAAI,KAAM,CAAA,MAAA,CAAO,QAAQ,KAAM,CAAA,MAAA,CAAA;AAAA,KACzD,CAAA,CAAA;AAEK,IAAA,MAAA,YAAA,GAAe,CAAC,GAA2C,KAAA;AAC/D,MAAA,KAAA,CAAM,qBAAqB,GAAG,CAAA,CAAA;AAC1B,MAAA,IAAA,OAAO,KAAM,CAAA,QAAA,KAAa,UAAY,EAAA;AACxC,QAAA,KAAA,CAAM,SAAS,GAAG,CAAA,CAAA;AAAA,OACpB;AAAA,KACF,CAAA;AAIE,IAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}