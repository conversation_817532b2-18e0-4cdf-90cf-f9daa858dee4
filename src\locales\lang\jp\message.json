{"ClusterDeleteSuccessed": "クラスタ削除に成功しました", "HTTPVersion": "HTTPバージョンはサポートされていません（505）", "SSHPortNumber": "正しいSSHポート番号を入力してください", "accidentalClusterName": "予期しない操作を防ぐために、クラスタ名を再入力してください", "accidentalComponent": "予期しない操作を防ぐために、カスタムコンポーネント名をもう一度入力してください", "accidentalHostname": "意図しない操作を防ぐために、ホスト名をもう一度入力してください。", "accidentalRack": "意図しない操作を防ぐために、ラック番号を入力してください。", "addHostFailed": "ホストの追加に失敗しました。関連パラメータを確認してください", "addHostTip": "新しいホストにエージェントをインストールすることができます。新しいホストを将来のクラスター追加用に保持したり、既存のクラスターに新しいホストを追加したりすることができます。", "addUserSuccess": "新規ユーザーが成功しました", "added": "追加されました", "adding": "追加中", "advancedSettings": "高度な設定機能を使用して、カスタムクラスターの割り当てを行うことができます。", "afterTheHostIsUnbound": "解除绑定后、ホストはデフォルトのリソースプールに戻り、削除操作が可能です。", "bindSuccessful": "バインドに成功しました", "cannotSpace": "空白で始めたり終わったりしてはいけません", "changeFailed": "パスワードの変更に失敗しました。サーバーエラーです。", "changePhoneFailed": "電話番号の変更に失敗しました", "changePhoneSuccessed": "携帯番号の修正に成功", "changeSuccessful": "パスワードの変更に成功しました。再ログインしてください", "changes": "変更内容は保存されない場合があります。", "characters": "3～20ビット文字", "charactersLength": "1〜20文字、漢字または英語以外の文字をサポートしていません", "checkVersion": "バージョンを表示", "clusterDeleted": "現在クラスタ（{value}）は削除できません。", "clusterRenameSuccessful": "クラスタの名前変更が成功しました", "clustersDeletedTip": "主機がバインドされ、コンポーネントがインストールされているかK8sクラスターにバイドしていますので、削除することはできません。", "componentDeleted": "現在のコンポーネント ({value}) は削除できません。", "componentDependencies": "コンポーネントの依存関係が存在します（{value}）", "componentDependency": "現在選択されているコンポーネント({value}) は({string}) に依存しています。{value} のインストールを行う前に、まず {string} のインストールを完了してください。", "componentIsRunning": "現在コンポーネントが実行中のため、削除できません。", "componentNameLength": "カスタムコンポーネント名は最大{value}文字まで入力できます！", "componentsCheck": "関連するコンポーネントがインストールされているか確認してくださいか？", "configRollbackSuccessful": "構成がロールバックされました。", "configurationSaveFailed": "構成の保存と公開に失敗しました", "configurationSavesuccess": "設定の保存とパブリッシュに成功しました", "configurationServiceParameters": "構成サービスパラメータのリストには、必要な項目が記入されていないか、要件を満たしていません！", "configured": "各コンポーネントの構成が正しいかどうかを確認してください", "confirmCommit": "コミットを確認しますか？", "connectionError": "接続エラー（{value}）！", "currentComponentVersion": "現在のコンポーネントバージョン：v{value}", "defaultClusterHost": "バインドが解除された後、ホストはデフォルトのクラスタに入り、その後使用を追加できます。", "delete": "従業員アカウントを削除しますか？", "deleteRack": "現在のラックを削除しますか？（機架番号：{name}）", "deleteSuccessful": "削除成功", "deletedClusterName": "クラスター名を入力して削除してください", "deletedHostName": "削除するホスト名を入力してください", "dependencyChecking": "依存チェック中", "eachRoleMeet": "各役割に割り当てられたホストが基準を満たしているかどうかを確認してください。", "edituserSuccess": "ユーザーの編集に成功しました", "emailAddressFailed": "メールアドレスの変更に失敗しました。", "emailAddressIncorrect": "メールアドレスが間違っています。もう一度入力してください。", "emailAddressSaved": "メールアドレスが保存されました", "enterComponentName": "カスタムコンポーネントの名前を入力してください", "enterCustomComponent": "カスタムコンポーネント名を入力してください", "enterEmail": "正しいメールアドレスを入力してください。", "enterHostsNumber": "ホスト数を入力してください", "enterNewPassword": "新しいパスワードを入力してください。", "enterOriginalPassword": "元のパスワードを入力してください", "enterPasswordAgain": "パスワードをもう一度入力してください。", "enterPhone": "正しい携帯番号を入力してください", "enterRoleNodes": "キャラクタノードの数を入力してください", "enterTheRackNumber": "ラック番号を入力してください", "error": "に失敗", "expandedAndReduced": "コミット後、システムは拡張され、数分かかる可能性があります", "fileSize": "ファイルサイズは100 kbを超えてはいけません", "freeze": "従業員アカウントを凍結しますか？", "frozenSuccessful": "フリーズ成功", "gettingInformation": "情報を取得しています...", "hasDataLake": "統合されたデータ湖：", "hostBindFailed": "ホストのバインドに失敗しました。", "hostName": "バインド解除するホスト名を入力してください", "hostUnbundled": "ホストがアンバインドされました。", "isDeleteCluster": "現在のクラスタ ({value}) を削除しますか？", "isDeleteComponent": "現在のコンポーネントサービス({value})を削除してもよろしいですか？", "isDeleteHost": "現在のホスト({value})を削除してもよろしいですか？", "isInstallation": "インストールを確認しますか？", "isSaveConfigurationFile": "現在の設定ファイルを保存して公開しますか？", "k8sAddedSuccessful": "K8sが正常に追加されました", "leavePage": "このページを離れますか？", "loginExpired": "ログインが期限切れ", "mailboxlength": "メールボックスの長さは30文字を超えてはいけません。", "modifyPermissionsSuccess": "権限の変更に成功しました", "namespaceLength": "クラスタ名の長さは{number}文字を超えないようにしてください。", "networkError": "ネットワークエラー（502）", "networkTimeout": "ネットワークタイムアウト（504）", "noChartAvailable": "グラフがありません", "noPermission": "権限が一時的にありません", "only": "数字、英字大文字小文字、下線_、英字ダッシュ「-」のみサポート", "onlyCanRole": "【｛value｝ノードには正の整数しか入力できません", "passwordLength": "パスワード長は6～20ビット", "phoneNumberIncorrect": "携帯番号のフォーマットが正しくありません", "queue": "キュー名はすでに存在します", "rackIsDeleted": "ラックを削除すると、元のラックとホストの配備関係が解消されます", "requestError": "要求エラー（404）", "requestTimedOut": "リクエストがタイムアウトしましたか、サーバーに異常が発生しています。ネットワークを確認するか、管理者に連絡してください。", "requestTimeout": "要求タイムアウト（408）", "requetError": "要求エラー（400）", "requiredSelect": "これは必須です", "requiredinput": "これは必須項目です", "reset": "従業員のパスワードをリセットしますか？", "resetPasswordSuccessful": "パスワードのリセットに成功しました", "restartComponent": "現在のコンポーネント（{value}）のサービスを再起動しますか？", "restartRole": "現在の役割（{value}）サービスを再起動しますか？", "rexNamespaceName": "名前は漢字、アルファベット、数字、アンダースコアで構成されており、ハイフン「-」で始まることや終わることはできません。", "roleAssignmentTip": "ここでは、新しいサービスのロール割り当てをカスタマイズできますが、正しく割り当てられていない（たとえば、ホストに割り当てられているロールが多すぎる）場合、パフォーマンスが影響を受けることに注意してください。", "rolledBack": "構成がバージョン（{value}）にロールバックされていることを確認しますか？", "saveFailed": "構成の保存に失敗しました", "saveSuccess": "構成が正常に保存されました。", "savingAndPublishing": "パブリケーションを保存したら、構成を有効にするためにコンポーネントを再起動する必要があります", "selectAuthentication": "認証タイプを選択してください", "selectClusterTip": "ホストを使用して新しいクラスタを作成するか、既存のクラスタを拡張するか、クラスタがKerberos認証を使用している場合は、Kerberosパッケージが新しいホストにインストールされていることを確認してください。そうしないと、新しいホスト上のサービスは実行できません。", "selectComponent": "コンポーネントを選択してください。", "selectFile": "ファイルを選択してください", "selectK8s": "K8sを選択してください", "selectK8sCluster": "K8sクラスタを選択してください", "selectNode": "ノードを選択してください", "selectOneHost": "少なくとも1台のホストを選択", "selectServiceType": "追加するサービスタイプを選択してください", "selectUpdateVersion": "バージョンをアップグレードする", "selectVersion": "バージョンを選択してください。", "serverError": "サーバーエラー（500）", "serverException": "サーバーの異常、管理者に連絡してください", "serviceUnavailable": "サービスが利用できません（503）", "serviceUnrealized": "サービス未実装（501）", "startComponent": "現在のコンポーネント（{value}）サービスを開始しますか？", "startInstallationTip": "インストールを開始すると、現在の設定は表示のみサポートされます。確認後、「OK」をクリックして開始してください。", "stopComponent": "現在のコンポーネント（{value}）のサービスを停止しますか？", "success": "成功", "testConnectionFailed": "接続のテストに失敗しました。構成を確認してください", "testConnectivityFirst": "まず接続性をテストしてください", "tetstConnectionSuccessed": "テスト接続に成功し、操作を続行できます", "twoPassword": "2回の入力パスワードが一致しません", "ubindHost": "ホスト（{value}）をクラスタ化解除してよろしいですか？", "unbindSelectedHost": "選択したホストをアンバインドしてよろしいですか？", "unbundled": "拘束解除", "unfrozenSuccessful": "解凍に成功しました", "unknownReason": "不明な理由", "whetherUnfreeze": "従業員のアカウントを解凍しますか？"}