<template>
  <div class="migration-backup">
    <div class="px-20px mx-auto my-0px">
      <div v-if="data?.executionStatus === 3" class="restart-tip mt-20px">
        <div class="flex justify-between items-center flex-1">
          <div class="flex-1">
            <i class="iconfont icon-icon_shibai c-hex-f3544a rd-m" />
            <span class="no-wrap f-14 ml-10px">
              <span ref="errorContentRef">{{ data?.executionMessage }}</span>
            </span>
          </div>
          <BaseButton v-if="offsetWidth > 1000" link @click="viewDetails">{{ $t('replenish.viewDetails') }}</BaseButton>
        </div>
      </div>
      <TheTitle inline>
        <template #titleText>{{ $t('replenish.basicInformation') }}</template>
      </TheTitle>
      <div class="f-14 mb-20px">
        <span>
          <span class="c-hex-99A0B5 mr-10px">{{ $t('replenish.status') }}</span>
          <span class="inline-block w-270px">
            {{
              data?.status === 1
                ? data?.executionStatus
                  ? stateMap.get(data?.executionStatus)
                  : $t('replenish.notStarted')
                : $t('replenish.notStarted')
            }}
          </span>
        </span>
        <span>
          <span class="c-hex-99A0B5 mr-10px">{{ $t('replenish.runtime') }}</span>
          <span class="inline-block w-270px">{{ data?.status === 1 ? (data?.executionStatus === 1 ? '-' : data?.runTime ?? '-') : '-' }}</span>
        </span>
      </div>
      <ConfigInfo :data="data" @refresh="getData" :is-disabled="isDisabled" />
    </div>
    <ErrorDetails v-model="errorDetails" :content="data?.executionMessage" />
  </div>
</template>

<script lang="ts" setup>
import ConfigInfo from './components/ConfigInfo.vue'
import ErrorDetails from './components/ErrorDetails.vue'
const { route, t, router, store } = useBasicTool()
const userInfo = computed(() => store.state.user.userInfo)
const tenantId = computed(() => store.state.user.tenantId)
const errorContentRef = ref()
const errorDetails = ref(false)
const isDisabled = computed(() => Number(data.value?.executionMessage) === 1)
const offsetWidth = computed(() => {
  // data变化时再次触发computed
  data.value
  return errorContentRef.value?.offsetWidth
})
onMounted(() => {
  getData()
})
const { clearTimer } = scheduleApiCalls()
onBeforeUnmount(() => {
  clearTimer()
})
const stateMap = new Map().set(1, t('replenish.inProgress')).set(2, t('replenish.executionSuccess')).set(3, t('replenish.executionFailure'))
const data = ref()

function getData() {
  let id = 0
  if (Number(userInfo.value.type) !== 2 && tenantId.value === '') id = userInfo.value.tenantId
  else id = tenantId.value
  store.dispatch('migration/getRule', { id }).then((res) => {
    data.value = res.data
  })
}
function viewDetails() {
  errorDetails.value = true
}

function scheduleApiCalls() {
  const now = new Date()
  const millisecondsUntilNextHour = (60 - now.getMinutes()) * 60 * 1000
  // 计算距离下一个整点的毫秒数，并设置定时器
  const timer = setTimeout(function () {
    getData()
    scheduleApiCalls()
  }, millisecondsUntilNextHour)
  function clearTimer() {
    clearTimeout(timer)
  }
  return { clearTimer }
}
</script>

<style lang="scss" scoped>
.migration-backup {
  overflow-y: auto;
  box-sizing: border-box;
  height: 100%;
  padding: 20px 0;
}
.restart-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  padding: 0px 16px;
  border-radius: var(--radius-m);
  margin-bottom: 20px;
  line-height: 40px;
  background-color: #fdeeed;
}
.no-wrap {
  display: inline-block;
  width: 1000px;
  height: 26px;
  @include text-no-wrap();
}
</style>
