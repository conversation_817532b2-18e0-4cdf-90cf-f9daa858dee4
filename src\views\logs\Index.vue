<template>
  <div class="bg-bgWhite" style="height: 100%">
    <BaseElTabs v-model="currPane" class="demo-tabs">
      <el-tab-pane :label="$t('button.tabDockerDeploy')" name="docker">
        <LogDockerDeployment v-model:isLoading="isLoading" />
      </el-tab-pane>
      <el-tab-pane :label="$t('button.tabKubernetesDeploy')" name="kubernetes">
        <LogK8sDeployment v-model:isLoading="isLoading" />
      </el-tab-pane>
    </BaseElTabs>
  </div>
</template>

<script lang="ts" setup>
import LogDockerDeployment from './components/LogDockerDeployment.vue'
import LogK8sDeployment from './components/LogK8sDeployment.vue'
const { route, setBreadList, t } = useBasicTool()
const { msgSource, deployWay } = route.query
const isLoading = ref(false)

const currPane = ref('docker')

onMounted(() => {
  setBreadList([
    {
      name: t('menu.log')
    },
    {
      name: t('menu.logList')
    }
  ])
  if (msgSource === 'kubernetes') {
    currPane.value = 'kubernetes'
  }
  if (deployWay == '1') currPane.value = 'docker'
  if (deployWay == '2') currPane.value = 'kubernetes'
  if (deployWay == '3') currPane.value = 'kubernetes'
})
</script>

<style lang="scss" scoped>
.demo-tabs {
  height: 100%;
}
:deep(.el-tabs.el-tabs--top) {
  height: 100%;
  border: none;
}
:deep(.el-tabs__nav-scroll) {
  padding: 0px 20px;
}
:deep(.el-tabs__nav-wrap.is-top::after) {
  height: 1px;
  background-color: var(--ops-border-color);
}
:deep(.el-tabs__content) {
  height: calc(100% - 48px);
  background-color: var(--ops-bg-white-color);
  .el-tab-pane {
    height: 100%;
  }
}
:deep(.el-tabs__item) {
  padding: 0 16px;
  font-size: 14px;
}
</style>
