import { ElMessage } from 'element-plus';
import 'element-plus/es/components/message/style/css';

const defaultCopyValue = {
  showTip: true,
  text: "",
  tips: "\u590D\u5236\u6210\u529F"
};
const copy = {
  mounted(el, binding) {
    el.copyValue = { ...defaultCopyValue, ...binding.value };
    el.addEventListener("click", handleClick);
  },
  updated(el, binding) {
    el.copyValue = { ...defaultCopyValue, ...binding.value };
  },
  beforeUnmount(el) {
    el.removeEventListener("click", el.__handleClick__);
  }
};
function handleClick() {
  const input = document.createElement("input");
  input.value = this.copyValue.text.toLocaleString();
  document.body.appendChild(input);
  input.select();
  document.execCommand("Copy");
  document.body.removeChild(input);
  if (this.copyValue.showTip) {
    ElMessage({
      type: "success",
      message: this.copyValue.tips
    });
  }
}

export { copy as default };
//# sourceMappingURL=copy.mjs.map
