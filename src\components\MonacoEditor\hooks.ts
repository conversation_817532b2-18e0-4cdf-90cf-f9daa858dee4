import { ref, watch } from 'vue'

import type { Ref } from 'vue'

export function useInitContent({
  isShow,
  taskContent,
  codeRef
}: {
  isShow: Ref<boolean>
  taskContent: Ref<string>
  codeRef: Ref<{ setValueNotTriggleChange: (content: string) => void }>
}) {
  const hasInit = ref(false)
  watch([() => isShow.value, () => taskContent.value], ([isShow, content]) => {
    if (isShow && content && !hasInit.value) {
      hasInit.value = true
      setTimeout(() => {
        codeRef.value.setValueNotTriggleChange(content)
      }, 0)
    }
  })
}
