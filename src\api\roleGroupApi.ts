import service from '@/utils/Http'

/**
 * @description 查询角色组列表信息
 * @param {RoleGroupApi.GetRoleGroupList} data
 * @return {*}
 */

export const getRoleGroupList = async (data: RoleGroupApi.GetRoleGroupList) => {
  return service.post<ApiResponse<any>>('/roleGroup/list', data)
}

/**
 * @description 查询角色组列表信息
 * @param {RoleGroupApi.GetGroup} data
 * @return {*}
 */

export const getGroup = async (data: RoleGroupApi.GetGroup) => {
  return service.post<ApiResponse<any>>('/roleGroup/getGroup', data)
}

/**
 * @description 添加角色组
 * @param {RoleGroupApi.AddRoleGroup} data
 * @return {*}
 */

export const addRoleGroup = async (data: RoleGroupApi.AddRoleGroup) => {
  return service.post<ApiResponse<any>>('/roleGroup/add', data)
}

/**
 * @description 修改角色组
 * @param {RoleGroupApi.UpdateRoleGroup} data
 * @return {*}
 */

export const updateRoleGroup = async (data: RoleGroupApi.UpdateRoleGroup) => {
  return service.post<ApiResponse<any>>('/roleGroup/update', data)
}

/**
 * @description 获取角色组下可添加的实例
 * @param {RoleGroupApi.ListInstances} data
 * @return {*}
 */

export const listInstances = async (data: RoleGroupApi.ListInstances) => {
  return service.post<ApiResponse<any>>('/roleGroup/listInstances', data)
}

/**
 * @description 删除角色组
 * @param {RoleGroupApi.DeleteRoleGroup} data
 * @return {*}
 */

export const deleteRoleGroup = async (data: RoleGroupApi.DeleteRoleGroup) => {
  return service.post<ApiResponse<any>>('/roleGroup/delete', data)
}
