{"check": "查看", "unbindK8s": "解绑k8s", "rename": "重命名", "delete": "删除", "clusterUnbinding": "集群解绑", "startUp": "启动", "stop": "停止", "reboot": "重启", "viewLog": "查看日志", "instanceAddress": "实例地址", "configurationOperation": "配置操作", "configurationDownload": "配置下载", "viewModification": "查看修改", "configurationSnapshot": "配置快照", "versionRollback": "版本回滚", "edit": "编辑", "permissionModification": "权限修改", "freeze": "冻结", "resetPassword": "重置密码", "thaw": "解冻", "clear": "清除", "monitorChart": "监控图表", "roleLogFile": "角色日志文件", "temporarilyAbsent": "暂无", "viewingProgress": "查看进度", "nodeScheduling": "节点调度", "update": "更新", "viewEvents": "查看事件", "save": "保存", "cancel": "取消", "clusterName": "集群名称", "bindHost": "绑定主机", "installComponents": "安装组件", "bindK8s": "绑定k8s", "operate": "操作", "state": "状态", "hostName": "主机名称", "belongingCluster": "所属集群", "memory": "内存", "kubernetesName": "Kubernetes名称", "kubernetesClusterAddress": "Kubernetes集群地址", "verificationMethod": "认证方式", "creationTime": "创建时间", "componentName": "组件名称", "containerName": "容器名称", "logLevel": "日志级别", "time": "时间", "information": "消息", "nodeName": "节点名称", "namespaceName": "命名空间名称", "podName": "pod名称", "addTime": "添加时间", "version": "版本", "customName": "自定义名称", "deploymentMethod": "部署方式", "roleName": "角色名称", "instanceName": "实例名称", "instance": "实例", "versionNumber": "版本号", "operator": "操作人", "modifyReason": "修改原因", "operatingTime": "操作时间", "serviceType": "服务类型", "describe": "描述", "serviceVersion": "服务版本", "stepContent": "步骤内容", "startingTime": "开始时间", "durationOfOperation": "运行时长", "existingRoles": "现有角色", "addRole": "添加角色", "ip": "IP地址", "k8sName": "K8s名称", "k8sNamespace": "K8s命名空间", "bindTime": "绑定时间", "employeeSName": "员工姓名", "currentRole": "当前角色", "loginAccount": "登录账号", "phone": "手机", "account": "账号", "alarmTime": "告警时间", "alarmContent": "告警内容", "alarmCategory": "告警类别", "executUser": "执行用户", "operationContent": "操作内容", "actionProperties": "操作属性", "result": "执行结果", "frameNumber": "机架号", "perceptualState": "感知状态", "numberOfDeployedHosts": "部署主机数", "remarks": "备注", "rack": "机架", "physicalSpace": "物理空间", "serialNumber": "序号", "name": "名称", "expansionAndContraction": "自动扩缩容", "eventTime": "事件时间", "typeOfOperation": "操作类型", "endTime": "结束时间", "region": "区域", "baseStatus": "底座状态", "deploymentMode": "部署模式", "resourceName": "资源名称"}