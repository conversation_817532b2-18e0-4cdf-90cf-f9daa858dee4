{"version": 3, "file": "hooks.mjs", "sources": ["../../../../../../packages/components/view-layout/src/hooks.ts"], "sourcesContent": ["import { type Ref, onMounted, onUnmounted, ref } from \"vue\";\n\ninterface ElementSize {\n  width: Ref<number>;\n  height: Ref<number>;\n}\n\n/**\n * 监听DOM元素尺寸变化的Hook\n * @param target 目标元素的Ref或DOM元素\n * @param options 配置选项\n * @returns 包含width和height的响应式对象\n */\nexport function useElementSize(\n  target: Ref<HTMLElement | null> | HTMLElement | null,\n  options: {\n    initialWidth?: number;\n    initialHeight?: number;\n    debounce?: number;\n  } = {}\n): ElementSize {\n  const width = ref(options.initialWidth || 0);\n  const height = ref(options.initialHeight || 0);\n\n  let observer: ResizeObserver | null = null;\n  let timeoutId: number | null = null;\n\n  const updateSize = (entry: ResizeObserverEntry) => {\n    if (options.debounce) {\n      if (timeoutId) {\n        window.clearTimeout(timeoutId);\n      }\n      timeoutId = window.setTimeout(() => {\n        width.value = entry.contentRect.width;\n        height.value = entry.contentRect.height;\n      }, options.debounce);\n    } else {\n      width.value = entry.contentRect.width;\n      height.value = entry.contentRect.height;\n    }\n  };\n\n  const observeElement = (element: HTMLElement) => {\n    observer = new ResizeObserver((entries) => {\n      if (entries[0]) {\n        updateSize(entries[0]);\n      }\n    });\n    observer.observe(element);\n  };\n\n  onMounted(() => {\n    const element = target instanceof HTMLElement ? target : target?.value;\n\n    if (element) {\n      // 初始尺寸\n      width.value = element.clientWidth;\n      height.value = element.clientHeight;\n\n      observeElement(element);\n    }\n  });\n\n  onUnmounted(() => {\n    if (observer) {\n      observer.disconnect();\n    }\n    if (timeoutId) {\n      window.clearTimeout(timeoutId);\n    }\n  });\n\n  return {\n    width,\n    height,\n  };\n}\n"], "names": [], "mappings": ";;AACO,SAAS,cAAc,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AACrD,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;AAC/C,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC;AACjD,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC;AACtB,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,MAAM,UAAU,GAAG,CAAC,KAAK,KAAK;AAChC,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC1B,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACvC,OAAO;AACP,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM;AAC1C,QAAQ,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;AAC9C,QAAQ,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;AAChD,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC3B,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;AAC5C,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;AAC9C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,OAAO,KAAK;AACtC,IAAI,QAAQ,GAAG,IAAI,cAAc,CAAC,CAAC,OAAO,KAAK;AAC/C,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;AACtB,QAAQ,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,OAAO,GAAG,MAAM,YAAY,WAAW,GAAG,MAAM,GAAG,MAAM,EAAE,KAAK,CAAC;AAC3E,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC;AACxC,MAAM,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC;AAC1C,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACrC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,MAAM;AACV,GAAG,CAAC;AACJ;;;;"}