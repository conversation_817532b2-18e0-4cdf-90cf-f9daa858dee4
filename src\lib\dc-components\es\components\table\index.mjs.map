{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/table/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport table from \"./src/index.vue\";\r\n\r\nexport const DcTable = withInstall(table);\r\n\r\nexport default DcTable;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}