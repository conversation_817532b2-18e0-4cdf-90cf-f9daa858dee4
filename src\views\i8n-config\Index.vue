<template>
  <div class="il8n-config">
    <div class="flex">
      <BaseButton @click="exportExcelBySheets">导出excel</BaseButton>
      <el-upload ref="uploadRef" :auto-upload="false" :on-change="handleChangeFile" accept=".xlsx, .xls" class="flex mll">
        <template #trigger>
          <el-button type="primary">上传excel</el-button>
        </template>
      </el-upload>
    </div>
    <div class="flex">
      <el-radio-group v-model="locale" @change="diffFun">
        <el-radio v-for="el in locales" :key="el" :label="el">{{ el }}</el-radio>
      </el-radio-group>
    </div>
    <div class="flex">
      <el-radio-group v-model="module" @change="moduleChange">
        <template v-for="el in modules">
          <el-radio v-if="!['name', 'el'].includes(el.label)" :key="el.label" :label="el.label">{{ `${el.label}(${el.num})` }}</el-radio>
        </template>
      </el-radio-group>
    </div>
    <!-- <el-input v-model="modifiedCode.code"></el-input> -->
    <CodeDiff
      :maxHeight="elementHeight + 'px'"
      :old-string="originCode.code"
      :new-string="modifiedCode.code"
      file-name=""
      output-format="side-by-side"
      language="json"
    />
    <!-- <Codemirror :model-value="modifiedCode.code" :default-value="originCode.code"></Codemirror> -->
    <!-- <CodeDiffEditor :originCode="originCode" :modifiedCode="modifiedCode" :isShow="true" :readOnly="true" /> -->
  </div>
</template>

<script setup lang="ts">
import jsonConfigs from '@/locales/lang'
import type { UploadFile } from 'element-plus'
import json from 'highlight.js/lib/languages/json'
import { cloneDeep } from 'lodash-es'
import vdiff, { CodeDiff } from 'v-code-diff'
import { importJson, useJsonToXlsx, useXlsxToJson } from './useJsonXlsx'
// Extend C language
vdiff.hljs.registerLanguage('c', json)
const oldJson = ref()

const locales = ref(['en', 'jp'])

const locale = ref('en')
// 定义一个响应式变量来存储窗口高度
const windowHeight = ref(0)

// 计算属性，根据窗口高度减去 100px 返回元素高度
const elementHeight = computed(() => {
  return windowHeight.value - 115
})

// 监听窗口大小变化
const handleResize = () => {
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  // 初始化窗口高度
  windowHeight.value = window.innerHeight
  // 添加窗口大小变化事件监听器
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 移除窗口大小变化事件监听器
  window.removeEventListener('resize', handleResize)
})

const { exportExcelBySheets } = useJsonToXlsx()
const { updateJson, newJson, module, modules, diffFun, updateObj } = useXlsxToJson(locale)

const uploadRef = ref()

const originCode = reactive({
  code: '',
  language: 'json'
})
const modifiedCode = reactive({
  code: '',
  language: 'json'
})

const handleChangeFile = async (rawFile: UploadFile) => {
  updateJson(rawFile)
}

const moduleChange = () => {
  const info = jsonConfigs[locale.value]
  originCode.code = JSON.stringify(info[module.value], null, 2)
  const oldObj = cloneDeep(info[module.value])
  const newObj = cloneDeep(newJson?.[module.value]?.[locale.value] || {})
  updateObj(oldObj, newObj)
  modifiedCode.code = JSON.stringify(oldObj, null, 2)
}

onMounted(async () => {
  locales.value = Object.keys(jsonConfigs).filter((el) => el !== 'zh-CN')
  locale.value = locales.value[0]
  oldJson.value = await importJson()
  diffFun()
  nextTick(() => {
    if (module.value) moduleChange()
  })
})
</script>

<style lang="scss" scoped>
.il8n-config {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  height: 100%;
}
</style>
