{"version": 3, "file": "index.js", "sources": ["../../../../../../../../packages/components/form/src/components/Text/index.tsx"], "sourcesContent": ["import { defineComponent, PropType } from \"vue\";\r\nimport \"./index.scss\";\r\n\r\nexport default defineComponent({\r\n  name: \"DcFormText\",\r\n  props: {\r\n    modelValue: {\r\n      type: [String, Number, Boolean] as PropType<string | number | boolean>,\r\n    },\r\n  },\r\n\r\n  render() {\r\n    return <div class=\"dc-form-text\">{this.$props.modelValue}</div>;\r\n  },\r\n});\r\n"], "names": ["defineComponent", "name", "props", "modelValue", "type", "String", "Number", "Boolean", "render", "_createVNode", "$props"], "mappings": ";;;;;;;AAGA,iBAAeA,mBAAgB,CAAA;AAAA,EAC7BC,IAAM,EAAA,YAAA;AAAA,EACNC,KAAO,EAAA;AAAA,IACLC,UAAY,EAAA;AAAA,MACVC,IAAM,EAAA,CAACC,MAAQC,EAAAA,MAAAA,EAAQC,OAAO,CAAA;AAAA,KAChC;AAAA,GACF;AAAA,EAEAC,MAAS,GAAA;AACP,IAAA,OAAAC,gBAAA,KAAA,EAAA;AAAA,MAAA,OAAA,EAAA,cAAA;AAAA,KAAA,EAAA,CAAkC,IAAKC,CAAAA,MAAAA,CAAOP,UAAU,CAAA,CAAA,CAAA;AAAA,GAC1D;AACF,CAAC,CAAA;;;;"}