import { defineComponent, ref, computed, nextTick, watch, openBlock, createBlock, unref, mergeProps, withCtx, renderSlot, createElementBlock, Fragment, createTextVNode, toDisplayString, resolveDynamicComponent, createCommentVNode, createVNode, renderList } from 'vue';
import { ElLoading, ElTable, ElTableColumn } from 'element-plus';
import 'element-plus/es/components/loading/style/css';
import 'element-plus/es/components/table/style/css';
import 'element-plus/es/components/table-column/style/css';
import DcEmpty from '../../empty/src/index.mjs';
import ColumnsFilter from './components/ColumnsFilter.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcTable"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    columns: {
      type: Array
    },
    isColumnFilter: {
      type: <PERSON><PERSON><PERSON>
    },
    append: {
      type: Function
    },
    filterSubmitText: {
      type: String
    },
    filterCancelText: {
      type: String
    },
    filterAllText: {
      type: String
    },
    filterResetText: {
      type: String
    },
    height: {
      type: String,
      default: "100%"
    },
    loading: {
      type: Boolean,
      default: false
    },
    showSelectColumn: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Function,
      default: () => true
    },
    selectionWidth: {
      type: Number,
      default: 40
    },
    showEmptyImg: {
      type: Boolean,
      default: true
    },
    locale: {
      type: String,
      default: "zh-CN"
    },
    theme: {
      type: String,
      default: "light"
    }
  },
  setup(__props, { expose: __expose }) {
    ;
    const props = __props;
    const $refs = ref();
    const columnsFilter = ref([]);
    let loadingInstance;
    const columnsData = computed(() => {
      if (props?.isColumnFilter) {
        return props?.columns?.filter((column) => (column?.columnDefault ? columnsFilter.value.includes(column.label) && column?.columnDefault : columnsFilter.value.includes(column.label)) || column?.columnRequired || !column.label);
      } else {
        return props.columns;
      }
    });
    const doLoading = (loading) => {
      if (loading) {
        nextTick(() => {
          if ($refs.value?.$el) {
            loadingInstance = ElLoading.service({
              target: $refs.value.$el,
              background: "transparent",
              body: false,
              fullscreen: false
            });
          }
        });
      } else {
        loadingInstance?.close();
      }
    };
    watch(() => props.loading, (loading) => {
      doLoading(loading);
    }, { immediate: true });
    function onColumnsFilterChange(checkList) {
      columnsFilter.value = checkList;
    }
    __expose({ $refs });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTable), mergeProps({
        ref_key: "$refs",
        ref: $refs,
        class: "dc-table"
      }, _ctx.$attrs, {
        style: { height: props.height }
      }), {
        append: withCtx(() => [
          renderSlot(_ctx.$slots, "append", {}, () => [
            __props.append && typeof __props.append() === "string" ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
              createTextVNode(toDisplayString(__props.append()), 1)
            ], 64)) : __props.append ? (openBlock(), createBlock(resolveDynamicComponent(__props.append), { key: 1 })) : createCommentVNode("v-if", true)
          ])
        ]),
        empty: withCtx(() => [
          createVNode(DcEmpty, {
            "show-img": __props.showEmptyImg,
            locale: __props.locale,
            theme: __props.theme
          }, null, 8, ["show-img", "locale", "theme"])
        ]),
        default: withCtx(() => [
          __props.isColumnFilter ? (openBlock(), createBlock(unref(ElTableColumn), {
            key: 0,
            width: "48px",
            align: "center"
          }, {
            header: withCtx(() => [
              createVNode(ColumnsFilter, {
                style: { "position": "relative", "top": "3px" },
                columns: __props.columns,
                "filter-submit-text": __props.filterSubmitText,
                "filter-cancel-text": __props.filterCancelText,
                "filter-all-text": __props.filterAllText,
                "filter-reset-text": __props.filterResetText,
                onChange: onColumnsFilterChange
              }, null, 8, ["columns", "filter-submit-text", "filter-cancel-text", "filter-all-text", "filter-reset-text"])
            ]),
            _: 1
          })) : createCommentVNode("v-if", true),
          renderSlot(_ctx.$slots, "before"),
          props.showSelectColumn ? (openBlock(), createBlock(unref(ElTableColumn), {
            key: 1,
            type: "selection",
            align: "center",
            width: props.selectionWidth,
            selectable: props.selectable
          }, null, 8, ["width", "selectable"])) : createCommentVNode("v-if", true),
          (openBlock(true), createElementBlock(Fragment, null, renderList(columnsData.value, (col, index) => {
            return openBlock(), createElementBlock(Fragment, null, [
              col?.slot ? (openBlock(), createBlock(unref(ElTableColumn), mergeProps({ key: 0 }, col, { key: index }), {
                default: withCtx(({ row, column, $index }) => [
                  createCommentVNode("  eslint-disable-next-line vue/valid-attribute-name "),
                  renderSlot(_ctx.$slots, col.prop, {
                    row,
                    column,
                    $index
                  })
                ]),
                _: 2
              }, 1040)) : createCommentVNode("v-if", true),
              col.type === "expand" && !col?.slot ? (openBlock(), createBlock(unref(ElTableColumn), mergeProps({ key: 1 }, col, { key: index }), {
                default: withCtx(({ row, column, $index }) => [
                  (openBlock(), createBlock(resolveDynamicComponent(col.formatter(row, column, row[col.prop], $index))))
                ]),
                _: 2
              }, 1040)) : createCommentVNode("v-if", true),
              !col?.slot && col.type !== "expand" ? (openBlock(), createBlock(unref(ElTableColumn), mergeProps({ key: 2 }, col, { key: index }), null, 16)) : createCommentVNode("v-if", true)
            ], 64);
          }), 256)),
          renderSlot(_ctx.$slots, "after")
        ]),
        _: 3
      }, 16, ["style"]);
    };
  }
});
var table = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\table\\src\\index.vue"]]);

export { table as default };
//# sourceMappingURL=index.mjs.map
