<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.createNodePool')" width="700px" :before-close="resetForm">
    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="140px">
      <el-form-item :label="`nodeclass:`" prop="nodeClassId">
        <el-select style="width: 280px" v-model="ruleForm.nodeClassId" class="base-search__input" filterable>
          <el-option v-for="(optionItem, optionIndex) in nodeClassList" :key="optionIndex" :label="optionItem.lable" :value="optionItem.value" />
        </el-select>
      </el-form-item>

      <el-form-item :label="`${$t('replenish.selectionMethod')}:`" prop="createMode">
        <el-select style="width: 280px" v-model="ruleForm.createMode" class="base-search__input" filterable>
          <el-option
            v-for="(optionItem, optionIndex) in createModeList"
            :key="optionIndex"
            :label="optionItem.lable"
            :value="optionItem.value"
            :disabled="optionItem.disabled"
          />
        </el-select>
        <BaseElTooltip :content="$t('replenish.selectionMethodTip')" placement="top">
          <img src="@/assets/icons/icon_tishi.svg" class="inline-block relative top--1px right--5px w-16px" alt="icon" />
        </BaseElTooltip>
      </el-form-item>
      <el-tabs v-model="activeTab" class="host-details-tabs" v-if="ruleForm.createMode === 'template'">
        <el-tab-pane :lazy="true" v-for="el in tabs" :key="el.label" :label="el.label" :name="el.value">
          <div class="h-60">
            <MonacoEditor :modelValue="ruleForm[el.value]" :readOnly="true" />
          </div>
        </el-tab-pane>
      </el-tabs>
      <template v-if="ruleForm.createMode === 'custom'">
        <el-form-item :label="`${$t('replenish.nodePoolName')}:`" prop="name">
          <el-input style="width: 280px" :placeholder="$t('replenish.pleaseEnter')" v-model="ruleForm.name" />
        </el-form-item>
        <el-form-item label="" prop="customCode" label-width="0">
          <div class="h-60 w-full">
            <MonacoEditor v-model="ruleForm.customCode" :readOnly="false" @change="codeChange" />
          </div>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import { createNodePool, findNodeClassList, queryNodePoolTpls, templateHasCreated } from '@/api/k8sApi'

const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  k8sId: {
    type: Number,
    default: 0
  },
  adminAuth: {
    type: Boolean,
    default: false
  }
})
const ruleForm = reactive<any>({
  nodeClassId: '',
  createMode: '',
  name: '',
  demand: '',
  demandService: '',
  consolidated: '',
  customTemplate: '',
  customCode: ''
})

const tabs = [
  {
    label: 'pool-graviton-on-demand',
    value: 'demand'
  },
  {
    label: 'pool-graviton-on-demand-service',
    value: 'demandService'
  },
  {
    label: 'pool-graviton-spot-with-most-consolidated',
    value: 'consolidated'
  }
]

const rules = {
  nodeClassId: [
    {
      required: true,
      message: t('replenish.pleaseSelect'),
      trigger: 'change'
    }
  ],
  createMode: [
    {
      required: true,
      message: t('replenish.pleaseSelect'),
      trigger: 'change'
    }
  ],
  name: [
    {
      trigger: 'blur',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ],
  customCode: [
    {
      trigger: 'change',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ]
}
const tenantId = computed(() => store.state.user.tenantId)
const nodeClassList = ref<
  {
    lable: string
    value: stringOrNumber
  }[]
>([])
const canUseTemplate = ref(true)
const createModeList = computed(() => [
  {
    lable: t('replenish.createBasedTemplate'),
    value: 'template',
    disabled: !canUseTemplate.value
  },
  {
    lable: t('replenish.custom'),
    value: 'custom',
    disabled: false
  }
])

const activeTab = ref('demand')

const getTemplate = () => {
  queryNodePoolTpls().then((res) => {
    if (res.data.code === 0) {
      ruleForm.demand = res.data.data['pool-graviton-on-demand']
      ruleForm.demandService = res.data.data['pool-graviton-on-demand-service']
      ruleForm.consolidated = res.data.data['pool-graviton-spot-with-most-consolidated']
      ruleForm.customTemplate = res.data.data['custom-template']
      ruleForm.customCode = res.data.data['custom-template']
    }
  })
}

const getTemplateHasCreated = () => {
  templateHasCreated({
    k8sId: props.k8sId
  }).then((res) => {
    if (res.data.code === 0) {
      canUseTemplate.value = !res.data.data
    }
  })
}

const getNodeClassList = () => {
  findNodeClassList({
    k8sClusterId: props.k8sId,
    tenantId: tenantId.value
  }).then((res) => {
    if (res.data.code === 0) {
      nodeClassList.value =
        res.data.data?.map((el) => {
          return {
            lable: el.name,
            value: el.id
          }
        }) || []
    }
  })
}

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    } else {
      getTemplateHasCreated()
      getNodeClassList()
    }
  }
)

const codeChange = () => {
  nextTick(() => {
    formRef.value.validateField(['customCode'])
  })
}

function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      isLoading.value = true
      createNodePool({
        k8sClusterId: props.k8sId,
        nodeClassId: ruleForm.nodeClassId,
        createMode: ruleForm.createMode,
        name: ruleForm.name,
        config: ruleForm.createMode === 'custom' ? ruleForm.customCode : '',
        tenantId: tenantId.value
      })
        .then((res) => {
          if (res.data.code === 0) {
            emit('submit')
            resetForm()
          }
        })
        .finally(() => {
          isLoading.value = false
        })
      // store
      //   .dispatch('name/namespaceK8sCreate', {
      //     k8sId: props.k8sId,
      //     namespaceName: isServiceAccount.value ? ruleForm.name : ruleForm.nodeClassId,
      //     createMode: ruleForm.createMode,
      //     tenantId: tenantId.value
      //   })
      //   .then((res) => {
      //     emit('submit')
      //     resetForm()
      //   })
      //   .finally(() => {
      //     isLoading.value = false
      //   })
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.nodeClassId = ''
  ruleForm.name = ''
  ruleForm.createMode = ''
  ruleForm.customCode = ruleForm.customTemplate
  emit('update:modelValue', false)
}

onMounted(() => {
  getTemplate()
})
</script>
