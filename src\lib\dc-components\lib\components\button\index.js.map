{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/button/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Button from \"./src/index.vue\";\r\n\r\nexport const DcButton = withInstall(Button);\r\n\r\nexport default DcButton;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "<PERSON><PERSON>"], "mappings": ";;;;;;;;AAIa,MAAA,QAAA,GAAWA,qBAAYC,gBAAM;;;;;"}