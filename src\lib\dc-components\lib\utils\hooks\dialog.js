'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');

function useDialog({
  cancel,
  confirm,
  show,
  defaultData
} = {}) {
  const visible = vue.ref(false);
  const dialogData = vue.ref(defaultData);
  const showDialog = (val) => {
    visible.value = true;
    dialogData.value = val;
    if (show && typeof show === "function") {
      show(val);
    }
  };
  const onCancel = (val) => {
    visible.value = false;
    if (cancel && typeof cancel === "function") {
      cancel(val, dialogData.value);
    }
  };
  const onConfirm = (val) => {
    onCancel();
    if (confirm && typeof confirm === "function") {
      confirm(val, dialogData.value);
    }
  };
  return {
    showDialog,
    onCancel,
    onConfirm,
    visible,
    dialogData
  };
}

exports.useDialog = useDialog;
//# sourceMappingURL=dialog.js.map
