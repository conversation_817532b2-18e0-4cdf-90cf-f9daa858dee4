<template>
  <div class="table-box">
    <BaseTable :columns="getColumnsFilter" :data="tableData.data" v-bind="$attrs">
      <template #level="scope">
        <div :class="['level', getLevelColor(scope.row.level)]">
          {{ tableLevelFormatter(scope.row.level) }}
        </div>
      </template>
      <template #before>
        <el-table-column width="48px" align="center">
          <template #header>
            <TheColumnsFilter style="position: relative; top: 3px" :columns="tableData.columns" @change="onColumnsFilterChange" />
          </template>
          <!-- 占位元素防止此处展示 -  -->
          <span>&nbsp;</span>
        </el-table-column>
      </template>
    </BaseTable>
    <!-- 下拉加载时的loading -->
    <div v-loading="loading" :class="{ empty: true, 'empty-block': loading }" />
  </div>
</template>
<script lang="ts" setup>
import TheColumnsFilter from '@/components/The/TheColumnsFilter.vue'
import { useJournal } from './hooks/useJournal'
const { tableLevelFormatter, getLevelColor } = useJournal()
const props = defineProps({
  tableData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  tableDeleteHeight: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  }
})
type tableDataColumn = {
  label: string
  prop: string
  default: string
}
const columnsFilter = ref<any>([])
const getColumnsFilter = computed(() =>
  props.tableData.columns.filter((column: tableDataColumn) => columnsFilter.value.includes(column.label) || column.default)
)
function onColumnsFilterChange(checkList: any) {
  columnsFilter.value = checkList
}
</script>
<style lang="scss" scoped>
.table-box {
  position: relative;
  height: 100%;
  .empty {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background-color: #ffffff;
    transition: height 0.5s ease-in-out;
  }
  .empty-block {
    height: 44px;
  }
}
</style>
