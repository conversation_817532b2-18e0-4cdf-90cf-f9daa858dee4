{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/drawer/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-drawer\r\n    v-bind=\"$attrs\"\r\n    :model-value=\"props.modelValue\"\r\n    :custom-class=\"`dc-drawer ${noFooter ? 'no-footer' : ''} ${\r\n      noHeader ? 'no-header' : ''\r\n    } ${props.class}`\"\r\n    @close=\"close\"\r\n  >\r\n    <template v-if=\"props.type === DcDrawerTypes.FOOTER\" #header>\r\n      <div style=\"display: none\" />\r\n    </template>\r\n    <template v-else-if=\"$slots.header\" #header>\r\n      <slot name=\"header\" />\r\n    </template>\r\n    <template v-if=\"props.type === DcDrawerTypes.HEADER\" #footer>\r\n      <div style=\"display: none\" />\r\n    </template>\r\n    <template v-else-if=\"$slots.footer\" #footer>\r\n      <slot name=\"footer\" />\r\n    </template>\r\n    <template v-if=\"thisFooter\" #footer>\r\n      <Footer\r\n        :confirm-action=\"props.confirmAction\"\r\n        :cancel-action=\"props.cancelAction\"\r\n        :footer-left-slot=\"props.footerLeftSlot\"\r\n        :footer-right-slot=\"props.footerRightSlot\"\r\n        @cancel=\"onCancel\"\r\n        @confirm=\"onConfirm\"\r\n      />\r\n    </template>\r\n    <slot />\r\n  </el-drawer>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, useSlots } from \"vue\";\r\nimport { ElDrawer } from \"element-plus\";\r\nimport Footer from \"./Footer.vue\";\r\nimport { DcDrawerTypes } from \"./types\";\r\nimport type { PropType, VNode } from \"vue\";\r\nimport type { DcDrawerAction } from \"./types\";\r\n\r\ndefineOptions({\r\n  name: \"DcDrawer\",\r\n});\r\n\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: Boolean,\r\n  },\r\n  type: {\r\n    type: String as PropType<DcDrawerTypes>,\r\n    default: DcDrawerTypes.HEADER_FOOTER,\r\n  },\r\n  confirmAction: {\r\n    type: Object as PropType<DcDrawerAction>,\r\n    default: () => ({}),\r\n  },\r\n  cancelAction: {\r\n    type: Object as PropType<DcDrawerAction>,\r\n    default: () => ({}),\r\n  },\r\n  footerLeftSlot: {\r\n    type: Object as PropType<VNode>,\r\n  },\r\n  footerRightSlot: {\r\n    type: Object as PropType<VNode>,\r\n  },\r\n  closeIsCancel: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  class: {\r\n    type: String,\r\n    default: \"\",\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\", \"cancel\", \"confirm\", \"close\"]);\r\n\r\nconst slots = useSlots();\r\n\r\nconst thisFooter = computed(() => {\r\n  return props.type !== DcDrawerTypes.HEADER && !slots.footer;\r\n});\r\n\r\nconst noHeader = computed(() => {\r\n  return [DcDrawerTypes.FOOTER, DcDrawerTypes.ONLYCONTENT].includes(props.type);\r\n});\r\n\r\nconst noFooter = computed(() => {\r\n  return [DcDrawerTypes.HEADER, DcDrawerTypes.ONLYCONTENT].includes(props.type);\r\n});\r\n\r\nconst close = () => {\r\n  emits(\"update:modelValue\", false);\r\n  emits(\"close\");\r\n  props.closeIsCancel && emits(\"cancel\");\r\n};\r\n\r\nconst onCancel = () => {\r\n  emits(\"cancel\");\r\n};\r\n\r\nconst onConfirm = () => {\r\n  emits(\"confirm\");\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.dc-drawer {\r\n  &.no-footer {\r\n    .el-drawer__footer {\r\n      display: none;\r\n    }\r\n  }\r\n  &.no-header {\r\n    .el-drawer__header {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;;;;;AA2Cc,EAAA,IAAA,EAAA,UAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAgCd,IAAA,MAAM,KAAQ,GAAA,QAAA,EAAA,CAAA;AAEd,IAAA,MAAM,UAAiB,GAAA,QAAA,CAAA,MAAA;AAEvB,MAAM,OAAA,KAAA,CAAA,IAAa,kBAAe,CAAA,MAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AAChC,KAAA,CAAA,CAAA;AAAqD,IACvD,MAAC,QAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,CAAA,oBAA0B,EAAA,aAAA,CAAA,WAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAC9B,KAAO,CAAA,CAAA;AAAqE,IAC9E,MAAC,QAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAM,OAAA,CAAA,oBAA0B,EAAA,aAAA,CAAA,WAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAC9B,KAAO,CAAA,CAAA;AAAqE,IAC9E,MAAC,KAAA,GAAA,MAAA;AAED,MAAA,yBAAoB,EAAA,KAAA,CAAA,CAAA;AAClB,MAAA,KAAA,CAAM;AACN,MAAA,KAAA,CAAM,aAAO,IAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACb,KAAM,CAAA;AAA+B,IACvC,MAAA,QAAA,GAAA,MAAA;AAEA,MAAA,eAAiB,CAAM;AACrB,KAAA,CAAA;AAAc,IAChB,MAAA,SAAA,GAAA,MAAA;AAEA,MAAA,gBAAkB,CAAM;AACtB,KAAA,CAAA;AAAe,IACjB,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}