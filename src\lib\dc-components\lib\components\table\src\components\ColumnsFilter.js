'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/button/style/css');
require('element-plus/es/components/checkbox/style/css');
require('element-plus/es/components/checkbox-group/style/css');
require('element-plus/es/components/dropdown/style/css');
require('element-plus/es/components/dropdown-item/style/css');
require('element-plus/es/components/dropdown-menu/style/css');
require('element-plus/es/components/icon/style/css');
var iconsVue = require('@element-plus/icons-vue');
require('./ColumnsFilter.vue_vue_type_style_index_0_scoped_true_lang.js');
var pluginVue_exportHelper = require('../../../../_virtual/plugin-vue_export-helper.js');

const _withScopeId = (n) => (vue.pushScopeId("data-v-7570be4d"), n = n(), vue.popScopeId(), n);
const _hoisted_1 = { class: "dc-table-columns-filter_icon" };
const _hoisted_2 = { class: "dc-table-columns-filter_dropdown-box" };
const _hoisted_3 = { class: "dc-table-columns-filter_operator" };
const _hoisted_4 = { class: "dc-table-columns-filter_button-box" };
const _sfc_main = vue.defineComponent({
  __name: "ColumnsFilter",
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    filterSubmitText: {
      type: String
    },
    filterCancelText: {
      type: String
    },
    filterAllText: {
      type: String
    },
    filterResetText: {
      type: String
    }
  },
  emits: ["change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const dropdownRef = vue.ref();
    const isChanged = vue.ref(false);
    const initCheckData = vue.ref([]);
    const checkAll = vue.ref(false);
    const isIndeterminate = vue.ref(true);
    const checkList = vue.ref([]);
    function visibleChange(visible) {
      if (visible) {
        isChanged.value = false;
        initCheckData.value = checkList.value;
        handleCheckedChange(checkList.value);
      } else {
        !isChanged.value && (checkList.value = initCheckData.value);
      }
    }
    vue.onMounted(() => {
      initCheckList();
    });
    function initCheckList() {
      checkList.value = props.columns.filter((item) => item.label && (item.columnDefault || item.columnRequired)).map((item) => item.label);
      onChange();
    }
    vue.watch(() => props.columns, () => {
      initCheckList();
    });
    function handleCheckAllChange(val) {
      checkList.value = val ? props.columns.map((item) => item.label) : props.columns.filter((item) => item.label && item.columnRequired).map((item) => item.label);
      const length = props.columns.filter((item) => item.label).length;
      if (checkList.value.length < length && !val) {
        isIndeterminate.value = true;
      } else {
        isIndeterminate.value = false;
      }
    }
    function handleCheckedChange(value) {
      const checkedCount = value.length;
      const length = props.columns.filter((item) => item.label).length;
      checkAll.value = checkedCount === length;
      isIndeterminate.value = checkedCount > 0 && checkedCount < length;
    }
    function onChange() {
      dropdownRef.value.handleClose();
      isChanged.value = true;
      emits("change", checkList.value);
    }
    function clone() {
      dropdownRef.value.handleClose();
    }
    function onReset() {
      initCheckList();
    }
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElDropdown), {
        ref_key: "dropdownRef",
        ref: dropdownRef,
        trigger: "click",
        "hide-on-click": false,
        "min-height": "380px",
        placement: "right",
        onVisibleChange: visibleChange
      }, {
        dropdown: vue.withCtx(() => [
          vue.createElementVNode("div", _hoisted_2, [
            vue.createVNode(vue.unref(elementPlus.ElDropdownMenu), null, {
              default: vue.withCtx(() => [
                vue.createElementVNode("div", _hoisted_3, [
                  vue.createVNode(vue.unref(elementPlus.ElCheckbox), {
                    modelValue: checkAll.value,
                    "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => checkAll.value = $event),
                    label: __props.filterAllText ?? "\u5168\u9009",
                    indeterminate: isIndeterminate.value,
                    onChange: handleCheckAllChange
                  }, null, 8, ["modelValue", "label", "indeterminate"]),
                  vue.createVNode(vue.unref(elementPlus.ElButton), {
                    link: "",
                    onClick: onReset
                  }, {
                    default: vue.withCtx(() => [
                      vue.createTextVNode(vue.toDisplayString(__props.filterResetText ?? "\u91CD\u7F6E"), 1)
                    ]),
                    _: 1
                  })
                ]),
                vue.createVNode(vue.unref(elementPlus.ElCheckboxGroup), {
                  modelValue: checkList.value,
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => checkList.value = $event),
                  onChange: handleCheckedChange
                }, {
                  default: vue.withCtx(() => [
                    (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(props.columns, (item) => {
                      return vue.openBlock(), vue.createElementBlock(vue.Fragment, null, [
                        item.label ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElDropdownItem), {
                          key: item.label
                        }, {
                          default: vue.withCtx(() => [
                            vue.createVNode(vue.unref(elementPlus.ElCheckbox), {
                              label: item.label,
                              checked: item.columnRequired || item.columnDefault,
                              disabled: item.columnRequired
                            }, null, 8, ["label", "checked", "disabled"])
                          ]),
                          _: 2
                        }, 1024)) : vue.createCommentVNode("v-if", true)
                      ], 64);
                    }), 256))
                  ]),
                  _: 1
                }, 8, ["modelValue"])
              ]),
              _: 1
            }),
            vue.createElementVNode("div", _hoisted_4, [
              vue.createElementVNode("div", null, [
                vue.createVNode(vue.unref(elementPlus.ElButton), {
                  type: "primary",
                  size: "small",
                  onClick: onChange
                }, {
                  default: vue.withCtx(() => [
                    vue.createTextVNode(vue.toDisplayString(__props.filterSubmitText ?? "\u786E\u5B9A"), 1)
                  ]),
                  _: 1
                }),
                vue.createVNode(vue.unref(elementPlus.ElButton), {
                  size: "small",
                  onClick: clone
                }, {
                  default: vue.withCtx(() => [
                    vue.createTextVNode(vue.toDisplayString(__props.filterCancelText ?? "\u53D6\u6D88"), 1)
                  ]),
                  _: 1
                })
              ])
            ])
          ])
        ]),
        default: vue.withCtx(() => [
          vue.createElementVNode("span", _hoisted_1, [
            vue.createVNode(vue.unref(elementPlus.ElIcon), null, {
              default: vue.withCtx(() => [
                vue.createVNode(vue.unref(iconsVue.Operation))
              ]),
              _: 1
            })
          ])
        ]),
        _: 1
      }, 512);
    };
  }
});
var ColumnsFilter = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__scopeId", "data-v-7570be4d"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\table\\src\\components\\ColumnsFilter.vue"]]);

exports["default"] = ColumnsFilter;
//# sourceMappingURL=ColumnsFilter.js.map
