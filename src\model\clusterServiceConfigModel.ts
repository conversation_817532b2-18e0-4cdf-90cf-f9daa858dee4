declare namespace clusterServiceConfigApi {
  /**
   * @description 获取修改记录列表
   * @param {stringOrNumber} pageNo -当前页
   * @param {stringOrNumber} pageSize -每页条数
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} clusterServiceInstanceId -实例id
   * @interface updateRecordList
   */
  interface updateRecordList extends PageArgument {
    clusterServiceId?: stringOrNumber
    clusterServiceInstanceId?: stringOrNumber
  }
  /**
   * @description 获取配置操作页面的文件问下拉内容
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} clusterServiceInstanceId -实例id
   * @interface confNameList
   */
  interface confNameList {
    clusterServiceId?: stringOrNumber
    clusterServiceInstanceId?: stringOrNumber
  }
  /**
   * @description 新的 获取configure列表
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} clusterServiceInstanceId -实例id
   * @interface newUpdateAllConfigsByClusterId
   */
  interface newgetMergeConfigsByClusterId {
    clusterServiceId?: stringOrNumber
    clusterServiceInstanceId?: stringOrNumber
  }
  /**
   * @description 回滚接口
   * @param {stringOrNumber} recordId -id
   * @interface rollback
   */
  interface rollback {
    recordId: stringOrNumber
  }
  /**
   * @description 查看修改详情与配置快照
   * @param {stringOrNumber} recordId -修改记录id
   * @param {stringOrNumber} type -1.修改记录 2.快照
   * @param {string} confName -文件名
   * @param {string} searchKey -关键字
   * @param {stringOrNumber} pageNo -页码
   * @param {stringOrNumber} pageSize -每页条数
   * @interface updateDetail
   */
  interface updateDetail extends PageArgument {
    recordId: stringOrNumber
    type: stringOrNumber
    confName?: string
    searchKey?: string
  }

  /**
   * @description 版本回滚配置对比列表
   * @param {stringOrNumber} sourceRecordId	 -当前版本id
   * @param {stringOrNumber} targetRecordId -选定回滚版本id
   * @param {string} confName -文件名
   * @param {string} searchKey -关键字
   * @param {stringOrNumber} pageNo -页码
   * @param {stringOrNumber} pageSize -每页条数
   * @interface compareSnapShotDetail
   */
  interface compareSnapShotDetail extends PageArgument {
    sourceRecordId: stringOrNumber
    targetRecordId: stringOrNumber
    confName?: string
    searchKey?: string
  }
  /**
   * @description 修改配置文件信息
   * @param {Array<object>} configRequestDtos 已修改的配置项列表
   * @param {string} modifyReason 修改理由
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} clusterServiceInstanceId -实例id
   * @param {stringOrNumber} applied - 0保存 1保存并发布
   * @export
   * @interface newUpdateAllConfigsByClusterId
   */
  interface newUpdateAllConfigsByClusterId {
    configRequestDtos: Array<object>
    modifyReason: string
    clusterServiceId?: stringOrNumber
    clusterServiceInstanceId?: stringOrNumber
    applied: stringOrNumber
    recordId?: stringOrNumber
    customDelete: any[]
    confFile?: string
  }

  interface UpdateRecordListRecordsItem {
    createTime: string
    createUserName: string
    currentVersion: boolean
    id: stringOrNumber
    modifyReason: string
    rollbackFlag: number
    versionNoL: number
  }

  interface MergeConfigsByClusterIdItem {
    clusterServiceId: number
    confDefaultValue: string
    confKey: string
    confName: string
    confValue: string
    configTemplateId: number
    descCh: string
    descEn: string
    id: stringOrNumber
  }

  interface ComponentListModelByIdRoleListData {
    editRecords: Array<MergeConfigsByClusterIdItem> | null
    mergeConfigs: Array<MergeConfigsByClusterIdItem>
    recordId: number
  }

  /**
   * @description 修改配置文件信息
   * @param {string} configKey 现阶段默认唯一值 yarn.scheduler.fair.config
   * @param {stringOrNumber} clusterServiceId -集群id
   * @param {number} defaultValue - 是否为默认值 1.为默认查询 2.为非默认
   * @param {stringOrNumber} id - 计划模式id
   * @param {stringOrNumber} type - 1 yarn
   * @export
   * @interface  GetSelectExtraConf
   */
  interface GetSelectExtraConf {
    clusterServiceId: stringOrNumber
    type: stringOrNumber
    configKey: string
    defaultValue?: number
    id?: number
  }
  interface QueueItem {
    '-name': string
    aclAdministerApps: string
    aclSubmitApps: string
    allowPreemptionFrom: string
    maxResources: string
    maxResourcesLimit?: string
    minResources: string
    schedulingPolicy?: string
    weight: number
    fairSharePreemptionThreshold?: string
    minSharePreemptionTimeout?: string
    fairSharePreemptionTimeout?: string
    maxRunningApps?: string
    maxAMShare?: string
    queue: QueueItem[]
  }

  /**
   * @description 修改配置文件信息
   * @param {string} configKey 现阶段默认唯一值 yarn.scheduler.fair.config
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} type - 1 yarn
   * @param {ConfigExtraInformationRequestDTOS[]} information - 固定格式的json字符串
   * @export
   * @interface  ACEOaadddefnoprrttux
   */
  interface ACEOaadddefnoprrttux extends GetSelectExtraConf {
    configExtraInformationRequestDTOS: ConfigExtraInformationRequestDTOS[]
  }
  type ConfigExtraInformationRequestDTOS = {
    information: string
    id: string
  }
  /**
   * @description 修改配置文件信息
   * @param {stringOrNumber} clusterServiceId -组件id
   * @export
   * @interface  GetRoleNames
   */
  interface GetRoleNames {
    clusterServiceId: stringOrNumber
  }

  /**
   * @description 添加自定义参数
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {string} confName -名
   * @param {string} confKey -key
   * @param {string} confValue -值
   * @param {string} descCh -描述
   * @export
   * @interface  GetRoleNames
   */
  interface AddCustomConf {
    clusterServiceId: stringOrNumber
    confName: string
    confKey: string
    confValue: string
    descCh: string
  }

  /**
   * @description 校验自定义配置
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {string} confKey -名
   * @export
   * @interface  CheckCustomConf
   */
  interface CheckCustomConf {
    clusterServiceId: stringOrNumber
    confKey: string
    confName: string
  }

  /**
   * @description 校验权限是够存在
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {string} priority -权限等级
   * @export
   * @interface  CheckPriority
   */
  interface CheckPriority {
    clusterServiceId: stringOrNumber
    priority: string
    type: number
  }

  /**
   * @description 获取当前执行的计划模式配置集id
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {string} priority -权限等级
   * @export
   * @interface  CheckPriority
   */
  interface GetYarnSchedule {
    clusterServiceId: stringOrNumber
  }

  /**
   * @description 删除自定义参数
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {string} confName -名
   * @param {string} confKey -key
   * @export
   * @interface  DeleteCustomConf
   */
  interface DeleteCustomConf {
    clusterServiceId: stringOrNumber
    confName: string
    confKey: string
  }
  /**
   * @description 组件概览——修改详情
   * @param {stringOrNumber} pageNo -当前页
   * @param {stringOrNumber} pageSize -每页条数
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {string} confName -组件id
   * @param {string} searchKey -组件id
   * @interface allUpdateDetail
   */
  interface allUpdateDetail extends PageArgument {
    clusterServiceId?: stringOrNumber
    confName: string
    searchKey: string
  }

  /**
   * @description 设置yarn资源池的默认配置
   * @param {stringOrNumber} id -有id为更新没有则为新增
   * @param {stringOrNumber} clusterServiceId -集群id
   * @param {number} type -表示yarn资源池的默认配置 1
   * @param {stringOrNumber} defaultSchedulePolicy -默认调度策略： 1.DRF 2.FAIR 3.FIFO
   * @param {stringOrNumber} defaultMaxRunningApps -默认正在运行的应用程 序最大数量
   * @param {stringOrNumber} defaultMaxAmShare -默认Application Master 最大份额
   * @interface UpdateOrAddDefaultExtraConf
   */
  interface UpdateOrAddDefaultExtraConf {
    clusterServiceId?: stringOrNumber
    id: stringOrNumber
    type: number
    defaultSchedulePolicy: stringOrNumber
    defaultMaxRunningApps: stringOrNumber
    defaultMaxAmShare: stringOrNumber
  }

  /**
   * @description 设置yarn资源池的默认配置
   * @param {stringOrNumber} id -集群id
   * @interface GetDefaultExtraConf
   */
  interface GetDefaultExtraConf {
    id: stringOrNumber
  }

  /**
   * @description 获取计划模式列表
   * @param {stringOrNumber} pageNo -当前页
   * @param {stringOrNumber} pageSize -每页条数
   * @param {stringOrNumber} clusterServiceId -集群id
   * @interface GetSchedulePage
   */
  interface GetSchedulePage extends PageArgument {
    clusterServiceId: stringOrNumber
  }

  /**
   * @description 获取全部计划模式配置集
   * @param {stringOrNumber} clusterServiceId -集群id
   * @interface GetConfigSet
   */
  interface GetConfigSet {
    clusterServiceId: stringOrNumber
  }

  /**
   * @description 创建计划模式
   * @param {stringOrNumber} scheduleName -计划名称
   * @param {stringOrNumber} parentId -初始化于哪个配置集合，已有配置集的id
   * @param {stringOrNumber} repeatType -1.每天2.每周3.每日
   * @param {stringOrNumber} dayOfWeek -每周那几天执行数组
   * @param {stringOrNumber} dayOfMonth -每个月执行的区间数组第一个为开始，第二个为结束
   * @param {stringOrNumber} beginTime -整点如果下午三点为15
   * @param {stringOrNumber} endTime -结束时间段
   * @param {stringOrNumber} clusterServiceId -集群id
   * @param {stringOrNumber} priority -计划优先级
   * @param {string} type -1.新建 2.已有配置集
   * @interface CreateYarnSchedule
   */
  interface CreateYarnSchedule {
    scheduleName: string
    clusterServiceId: stringOrNumber
    parentId: stringOrNumber
    repeatType: number
    dayOfWeek: string[]
    dayOfMonth: [string, string]
    beginTime: string
    endTime: string
    scheduleType: number
    priority: string
  }

  /**
   * @description 创建计划模式
   * @param {stringOrNumber} scheduleName -计划名称
   * @param {stringOrNumber} parentId -初始化于哪个配置集合，已有配置集的id
   * @param {stringOrNumber} repeatType -1.每天2.每周3.每日
   * @param {stringOrNumber} dayOfWeek -每周那几天执行数组
   * @param {stringOrNumber} dayOfMonth -每个月执行的区间数组第一个为开始，第二个为结束
   * @param {stringOrNumber} beginTime -整点如果下午三点为15
   * @param {stringOrNumber} endTime -结束时间段
   * @param {stringOrNumber} clusterServiceId -集群id
   * @param {stringOrNumber} priority -计划优先级
   * @param {string} type -1.新建 2.已有配置集
   * @param {string} id-计划模式id
   * @interface UpdateYarnSchedule
   */
  interface UpdateYarnSchedule {
    scheduleName: string
    clusterServiceId: stringOrNumber
    parentId: stringOrNumber
    repeatType: number
    dayOfWeek: string[]
    dayOfMonth: [string, string]
    beginTime: string
    endTime: string
    scheduleType: number
    priority: string
    id: stringOrNumber
  }

  /**
   * @description 删除计划模式
   * @param {string} id-计划模式id
   * @interface SYacddeeeeehllnrtu
   */
  interface SYacddeeeeehllnrtu {
    id: stringOrNumber
  }
  /**
   * @description 组件配置查询(分页)
   * @param {stringOrNumber} pageNo -当前页
   * @param {stringOrNumber} pageSize -每页条数
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} searchKey -关键字搜索
   * @param {stringOrNumber} labelType -类别筛选
   * @param {stringOrNumber} roleName -角色范围
   * @param {stringOrNumber} confName -文件名称
   * @interface GetClusterConfigsPage
   */
  interface GetClusterConfigsPage extends PageArgument {
    clusterServiceId: stringOrNumber
    searchKey: string
    labelType: string
    roleName: string
    confName: string
  }

  /**
   * @description 实例配置查询(分页)
   * @param {stringOrNumber} pageNo -当前页
   * @param {stringOrNumber} pageSize -每页条数
   * @param {stringOrNumber} clusterServiceId -组件id
   * @param {stringOrNumber} clusterServiceInstanceId -组件实例id clusterServiceInstanceId
   * @param {stringOrNumber} searchKey -关键字搜索
   * @param {stringOrNumber} labelType -类别筛选
   * @param {stringOrNumber} roleName -角色范围
   * @param {stringOrNumber} confName -文件名称
   * @interface GetClusterConfigsPage
   */
  interface GetInstanceConfigsPage extends PageArgument {
    clusterServiceId: stringOrNumber
    clusterServiceInstanceId: stringOrNumber
    searchKey: string
    labelType: string
    roleName: string
    confName: string
  }

  interface UpdateOrAddPlacementPolicyParam {
    clusterServiceId: stringOrNumber
    information: stringOrNumber
    id?: string
    priority: string
    ruleType: string
  }

  interface deletePlacementPolicyParams {
    id: string
  }

  interface placementPolicyPageParams {
    clusterServiceId: stringOrNumber
    pageNo: number
    pageSize: number
  }
  interface userLimitPageParams {
    limitType: string
    clusterServiceId: string
    pageNo: number
    pageSize: number
  }

  interface userLimitAddParams {
    limitType: string
    clusterServiceId: string
    information: string
    priority?: number
  }
}
