import { defineComponent, inject, watchEffect, openBlock, createBlock, unref, mergeProps, withCtx, renderSlot, createTextVNode, toDisplayString } from 'vue';
import { ElTableColumn, ElFormItem } from 'element-plus';
import 'element-plus/es/components/form-item/style/css';
import 'element-plus/es/components/table-column/style/css';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcEditTableColumn"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    prop: {
      type: String
    },
    label: {
      type: String
    },
    width: {
      type: String
    },
    rules: {
      type: Array
    }
  },
  setup(__props) {
    ;
    const props = __props;
    const defaultEditActions = {};
    const editActions = inject("editActions");
    const formModel = inject("formModel");
    const formProps = inject("formProps");
    watchEffect(() => {
      if (props.prop) {
        formProps?.value?.add(props.prop);
      }
    });
    const getEditModel = (index) => {
      if (!formModel || !formModel.value?.model) {
        return {
          isEditing: false,
          isNew: false,
          formData: {},
          data: {}
        };
      }
      return formModel.value.model[index];
    };
    const getEditRow = (index) => getEditModel(index).formData;
    const isEditing = (index) => getEditModel(index).isEditing ?? false;
    const calculateColumnDefaultValue = (scope) => {
      if (props.prop)
        return scope.row?.[props.prop];
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTableColumn), mergeProps(_ctx.$attrs, {
        prop: __props.prop,
        label: __props.label,
        width: __props.width
      }), {
        default: withCtx((scope) => [
          isEditing(scope.$index) ? (openBlock(), createBlock(unref(ElFormItem), {
            key: 0,
            prop: `model.${scope.$index}.formData.${__props.prop}`,
            rules: __props.rules
          }, {
            default: withCtx(() => [
              renderSlot(_ctx.$slots, "edit", {
                $index: scope.$index,
                row: getEditRow(scope.$index),
                column: scope.column,
                actions: unref(editActions) ?? defaultEditActions
              }, () => [
                createTextVNode(toDisplayString(calculateColumnDefaultValue(scope)), 1)
              ])
            ]),
            _: 2
          }, 1032, ["prop", "rules"])) : renderSlot(_ctx.$slots, "default", {
            key: 1,
            $index: scope.$index,
            row: scope.row,
            column: scope.column,
            actions: unref(editActions) ?? defaultEditActions
          }, () => [
            createTextVNode(toDisplayString(calculateColumnDefaultValue(scope)), 1)
          ])
        ]),
        _: 3
      }, 16, ["prop", "label", "width"]);
    };
  }
});
var EditTableColumn = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\edit-table-column\\src\\index.vue"]]);

export { EditTableColumn as default };
//# sourceMappingURL=index.mjs.map
