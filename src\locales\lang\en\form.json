{"SSHPortNumber": "SSHPortNo.", "addingHostToDefaultCluster": "Add the host to the default cluster", "addingHostToExistingCluster": "Add the host to a custom cluster", "alarmCategory": "Alarm category", "alarmContent": "Alarm content", "all": "All", "authenticationType": "AuthType", "belongingCluster": "Cluster", "bindK8s": "BindKubernetes", "certificationDocuments": "AuthFile", "clusterName": "Cluster Name", "componentName": "ComponentName", "confirmPassword": "ConfirmPassword", "containerName": "Container", "currentCluster": "Current Cluster", "currentComponent": "Current Component", "currentHost": "Current Host", "currentRole": "CurrentRole", "customComponentName": "CustomComponentName", "durationOfOperation": "Duration", "editValue": "TotalEditValue: {value}", "email": "Email", "emailAddress": "EmailAddres", "employeeSName": "EmployeeName", "endDate": "EndDate", "fileName": "FileName", "host": "Host", "hostName": "Host Name", "hostSearch": "HostSearch", "inputQuery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ipAddress": "<PERSON><PERSON><PERSON>", "k8sName": "KubernetesName", "k8sUpload": "Upload a Kubernetes configuration file. The file cannot exceed 100KB in size.", "keyWord": "Key Word", "keywordSearch": "KeywordSearch", "logLevel": "Log Level", "loginAccount": "<PERSON><PERSON><PERSON><PERSON>unt", "loginPassword": "Password", "minimumLogLevel": "MinLogLevel", "modifyReason": "ReasonforChange", "modifyReasonLabel": "ReasonforChange", "name": "Name", "namespaceName": "Namespace", "newPassword": "NewPassword", "nnodesNumber": "NodeQuantity", "nodeName": "Node", "operationAttribute": "OperationAttribute", "originalPassword": "OriginalPassword", "password": "Password", "pastMinutes": "Last{number}  Minutes", "phoneNumber": "MobileNumber", "pleaseChoose": "Select", "pleaseEnter": "Enter", "pleaseEnterAName": "Enter a name", "pleaseEnterClusterName": "Please enter cluster name", "pleaseEnterConfigurationInformation": "Enter configurations", "pleaseEnterEmployeeName": "Enter employee name", "pleaseEnterIPAddress": "Enter an IP address", "pleaseEnterK8sName": "EnterKubernetes", "pleaseEnterKeyWords": "Enter a keyword", "pleaseEnterLoginAccount": "Enter logon account", "pleaseEnterPassword": "Enter a password", "pleaseEnterPrivilegedUser": "Enter a privileged user", "pleaseEnterTheNumberOfSessions": "Enter the node quantity", "pleaseEnterThePhoneNumber": "Enter mobile number", "pleaseEnterTheSSHPortNumber": "Enter SSH port number", "pleaseEnterUserName": "Enter the user name", "pleaseEnterYourName": "Enter name mobile number or account name", "pleaseEnterYourPassword": "Enter logon password", "pleaseInputTheEmailAddress": "Enter email address", "pleaseSelectAStatus": "Select a status", "pleaseSelectCluster": "Please Select Cluster", "pleaseSelectComponentName": "Select a component", "pleaseSelectCurrentRole": "Select the current role", "pleaseSelectRoleName": "Select a role", "podName": "Pod", "privilegedUser": "PrivilegedUser:", "queue": "queue", "remarks": "Remarks", "roleName": "RoleName", "selectAConfigurationMethod": "SelectDeploymentMode", "selectCluster": "SelectCluster", "selectHost": "SelectHost", "selectK8sCluster": "SelectKubernetesCluster", "selectTheOwningCluster": "Select a cluster", "selectionPeriod": "SelectTime", "startDate": "StartDate", "startingTime": "StartTime", "state": "Status", "time": "Time", "timeRange": "TimePeriod", "timeSelection": "Select Time", "timeout": "Timeout (s)", "type": "type", "typeOfOperation": "Typeofoperation", "user": "user", "selectResources": "Select Resources", "ipsPlaceholder": "Please enter the address, such as 192.68.12 [1-200]"}