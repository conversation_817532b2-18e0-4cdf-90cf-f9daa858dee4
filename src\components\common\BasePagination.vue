<template>
  <div class="page">
    <el-pagination
      v-bind="$attrs"
      :current-page="modelValue.pageNo"
      :page-sizes="[10, 15, 20, 25]"
      :page-size="modelValue.pageSize"
      layout="total, prev, pager, next, sizes"
      :total="modelValue?.total ?? 0"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { debounce } from 'lodash'
interface PageInfo {
  pageNo: number
  pageSize: number
  total?: number
}
interface Props {
  modelValue?: PageInfo
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: () => {
    return {
      pageNo: 1,
      pageSize: 15
    }
  }
})
const emits = defineEmits<{
  (e: 'update:modelValue', value: object): void
  (e: 'pageChange', value: object): void
}>()

const { modelValue } = toRefs(props)
function handleSizeChange(value: number) {
  emits('update:modelValue', Object.assign(modelValue.value, { pageSize: value }))
  pageChange()
}
function handleCurrentChange(value: number) {
  emits('update:modelValue', Object.assign(modelValue.value, { pageNo: value }))
  pageChange()
}

const pageChange = debounce(() => {
  emits('pageChange', modelValue.value)
}, 100)
</script>

<style lang="scss" scoped>
.page {
  :deep(.el-pagination) {
    .el-pager li,
    .btn-prev,
    .btn-next {
      min-width: 32px;
      height: 32px;
      border: 1px solid var(--ops-border-color);
      color: var(--ops-text-color);
    }
    .el-pager li {
      margin: 0px 4px;
    }
    .btn-prev {
      margin-right: 4px;
    }
    .btn-next {
      margin-left: 4px;
    }
    .el-pager li.is-active {
      border: 1px solid var(--ops-primary-color);
      color: var(--ops-primary-color);
    }
  }
}
</style>
