<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.resourceAllocation')" width="400px" :before-close="resetForm">
    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="auto">
      <el-form-item :label="$t('replenish.tenantName') + '：'" prop="tenantId">
        <el-select v-model="ruleForm.tenantId">
          <el-option v-for="item in tenantList" :label="item.label" :key="item.value" :value="item.value"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  }
})

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      ruleForm.tenantId = ''
      getTenantAll()
    }
  }
)
const ruleForm = reactive<any>({
  tenantId: ''
})
const rules = {}

const tenantList = ref<{ label: string; value: string }[]>([])
const tenantId = computed(() => store.state.user.tenantId)
function getTenantAll() {
  store
    .dispatch('namespace/namespaceTenantList', {
      id: props.id,
      tenantId: tenantId.value
    })
    .then((res) => {
      tenantList.value = res.data.map((item: any) => {
        return {
          label: item.name,
          value: item.id
        }
      })
    })
}

function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      store
        .dispatch('namespace/namespaceAllocation', {
          id: props.id,
          allocationTenantId: ruleForm.tenantId,
          tenantId: tenantId.value
        })
        .then((res) => {
          emit('submit')
          resetForm()
        })
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.tenantId = ''
  emit('update:modelValue', false)
}
</script>
