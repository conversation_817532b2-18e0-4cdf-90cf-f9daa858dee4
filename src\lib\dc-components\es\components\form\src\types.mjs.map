{"version": 3, "file": "types.mjs", "sources": ["../../../../../../packages/components/form/src/types.ts"], "sourcesContent": ["import type { CascaderOption, FormItemRule, Options } from \"element-plus\";\r\nimport type { Ref, VNode } from \"vue\";\r\nimport type { DcCheckboxOptions } from \"../../checkbox/src/types\";\r\nimport type { DcRadioOptions } from \"../../radio/src/types\";\r\nimport type { RenderVNodeFn } from \"../../render-vnode/src/types\";\r\nimport type { DcSelectGroups, DcSelectOptions } from \"../../select/src/types\";\r\nimport type { DcFormTableColumnItem } from \"./components/Table/types\";\r\nimport type DcForm from \"./index.vue\";\r\nexport interface DcFormVisibleControl {\r\n  [k: string]: boolean | Ref<boolean>;\r\n}\r\n\r\nexport type DcFormModel = Record<string, any>;\r\n\r\nexport enum DcFormComponentEnum {\r\n  INPUT = \"input\",\r\n  TEXT = \"text\",\r\n  SELECT = \"select\",\r\n  RADIO = \"radio\",\r\n  CHECKBOX = \"checkbox\",\r\n  AUTOCOMPLETE = \"autocomplete\",\r\n  CASCADER = \"cascader\",\r\n  CASCADERPANEL = \"cascaderPanel\",\r\n  COLORPICKER = \"colorPicker\",\r\n  DATEPICKER = \"datePicker\",\r\n  INPUTNUMBER = \"inputNumber\",\r\n  RATE = \"rate\",\r\n  SELECTV2 = \"selectV2\",\r\n  SLIDER = \"slider\",\r\n  SWITCH = \"switch\",\r\n  TIMEPICKER = \"timePicker\",\r\n  TIMESELECT = \"timeSelect\",\r\n  TRANSFER = \"transfer\",\r\n  UPLOAD = \"upload\",\r\n  TABLE = \"table\",\r\n  CUSTOM = \"custom\",\r\n}\r\n\r\nexport type DcFormItemVisible = (model: DcFormModel) => boolean;\r\n\r\nexport type DcFormItemEffect = (\r\n  model: DcFormModel,\r\n  changeModel: (model: DcFormModel) => void,\r\n  rowIndex?: number\r\n) => void;\r\n\r\nexport interface DcFormItemMargin {\r\n  right?: string;\r\n  bottom?: string;\r\n  top?: string;\r\n  left?: string;\r\n}\r\nexport interface DcFormItemBase {\r\n  model: string;\r\n  defaultValue: any;\r\n  rules?: FormItemRule[];\r\n  label?: string;\r\n  labelWidth?: string | number;\r\n  disabled?: boolean;\r\n  component: DcFormComponentEnum;\r\n  inlineMessage?: boolean;\r\n  labelSlot?: RenderVNodeFn;\r\n  visible?: DcFormItemVisible;\r\n  effect?: DcFormItemEffect;\r\n  props?: Record<string, any>;\r\n  componentSlot?: Record<string, RenderVNodeFn>;\r\n  rightSlot?: RenderVNodeFn;\r\n  margin?: DcFormItemMargin;\r\n  class?: string;\r\n}\r\n\r\nexport type DcFormInput = DcFormItemBase;\r\n\r\nexport type DcFormInputNumber = DcFormItemBase;\r\n\r\nexport type DcFormText = DcFormItemBase;\r\n\r\nexport type DcFormCustomRenderFn = ({\r\n  value,\r\n  onChange,\r\n  rowIndex,\r\n}: {\r\n  value: any;\r\n  onChange: (val: any) => void;\r\n  rowIndex: number;\r\n}) => VNode;\r\n\r\nexport interface DcFormSelect extends DcFormItemBase {\r\n  props: {\r\n    groups?: DcSelectGroups;\r\n    options?: DcSelectOptions;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport interface DcFormRadio extends DcFormItemBase {\r\n  props: {\r\n    options: DcRadioOptions;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport interface DcFormCheckbox extends DcFormItemBase {\r\n  props: {\r\n    options: DcCheckboxOptions;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport type DcFormAutoComplete = DcFormItemBase;\r\n\r\nexport interface DcFormCascader extends DcFormItemBase {\r\n  props: {\r\n    options: CascaderOption[] | Ref<CascaderOption[]>;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport interface DcFormCascaderPanel extends DcFormItemBase {\r\n  props: {\r\n    options: CascaderOption[] | Ref<CascaderOption[]>;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport type DcFormColorPicker = DcFormItemBase;\r\n\r\nexport type DcFormDatePicker = DcFormItemBase;\r\n\r\nexport type DcFormRate = DcFormItemBase;\r\n\r\nexport interface DcFormTable extends DcFormItemBase {\r\n  props: {\r\n    columns: DcFormTableColumnItem[];\r\n    topSlot: RenderVNodeFn;\r\n    bottomSlot: RenderVNodeFn;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport interface DcFormSelectV2 extends DcFormItemBase {\r\n  props: {\r\n    options: Options;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport type DcFormSlider = DcFormItemBase;\r\n\r\nexport type DcFormSwitch = DcFormItemBase;\r\n\r\nexport type DcFormTimePicker = DcFormItemBase;\r\n\r\nexport type DcFormTimeSelect = DcFormItemBase;\r\n\r\nexport type DcFormTransfer = DcFormItemBase;\r\n\r\nexport type DcFormUpload = DcFormItemBase;\r\n\r\nexport interface DcFormCustom extends DcFormItemBase {\r\n  props: {\r\n    renderCustom: DcFormCustomRenderFn;\r\n    [k: string]: any;\r\n  };\r\n}\r\n\r\nexport type DcFormItem =\r\n  | DcFormInput\r\n  | DcFormInputNumber\r\n  | DcFormSelect\r\n  | DcFormSelectV2\r\n  | DcFormText\r\n  | DcFormRadio\r\n  | DcFormCheckbox\r\n  | DcFormAutoComplete\r\n  | DcFormColorPicker\r\n  | DcFormDatePicker\r\n  | DcFormCascaderPanel\r\n  | DcFormRate\r\n  | DcFormSlider\r\n  | DcFormSwitch\r\n  | DcFormTimePicker\r\n  | DcFormTimeSelect\r\n  | DcFormTransfer\r\n  | DcFormUpload\r\n  | DcFormTable\r\n  | DcFormCustom\r\n  | DcFormCascader;\r\n\r\nexport interface DcFormConfig {\r\n  inline?: boolean;\r\n  labelPosition?: \"left\" | \"right\" | \"top\";\r\n  labelWidth?: string | number;\r\n  showMessage?: boolean;\r\n  inlineMessage?: boolean;\r\n  disabled?: boolean;\r\n  itemMargin?: DcFormItemMargin;\r\n  children: DcFormItem[];\r\n}\r\n\r\nexport type DcFormInstance = InstanceType<typeof DcForm>;\r\n"], "names": [], "mappings": "AAAU,IAAC,mBAAmB,mBAAmB,CAAC,CAAC,oBAAoB,KAAE;;AAAC,EAAC,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAA,CAAA;AAC3E,EAAE,oBAAoB,CAAC,QAAQ,CAAC,GAAG,QAAI,CAAA;AACvC,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1C,EAAE,oBAAoB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;AAChD,EAAE,oBAAoB,CAAC,cAAc,CAAC,GAAG,cAAc,CAAC;AACxD,EAAE,oBAAoB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;AAChD,EAAE,oBAAoB,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC;AAC1D,EAAE,oBAAoB,CAAC,aAAa,CAAC,GAAE,aAAA,CAAA;AACvC,EAAE,oBAAoB,CAAC,YAAY,CAAC,GAAG,YAAC,CAAA;AACxC,EAAE,oBAAoB,CAAC,aAAa,CAAC,GAAE,aAAA,CAAA;AACvC,EAAE,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAA,CAAA;AACF,EAAC,oBAAA,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;AACD,EAAE,oBAAoB,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AAC5C,EAAC,oBAAA,CAAA,QAAA,CAAA,GAAA,QAAA,CAAA;AACD,EAAE,oBAAoB,CAAC,YAAW,CAAA,GAAA,YAAA,CAAA;AAClC,EAAE,oBAAiB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;AACnB,EAAE,oBAAe,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;AACjB,EAAE,oBAAmB,CAAA,QAAA,CAAA,GAAA,QAAA,CAAA;AACrB,EAAE,oBAAiB,CAAA,OAAA,CAAA,GAAA,OAAA,CAAA;AACnB,EAAE,oBAAoB,CAAC,QAAE,CAAA,GAAA,QAAA,CAAA;AACzB,EAAE,OAAO,oBAAoB,CAAC;AAC9B,CAAC,EAAE,mBAAmB,IAAG,EAAA;;;;"}