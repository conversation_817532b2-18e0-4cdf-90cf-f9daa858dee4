<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.createNodeClass')" width="500px" :before-close="resetForm">
    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="140px">
      <el-form-item :label="`${$t('replenish.nodeclassName')}:`" prop="name">
        <el-input style="width: 280px" :placeholder="$t('replenish.pleaseEnter')" v-model="ruleForm.name" />
      </el-form-item>

      <!-- <el-form-item :label="`${$t('replenish.clusterName')}:`" prop="clusterName">
        <el-input style="width: 280px" :placeholder="$t('replenish.pleaseEnter')" v-model="ruleForm.clusterName" />
      </el-form-item> -->
      <el-form-item :label="`iam role:`" prop="roleName">
        <el-input style="width: 280px" :placeholder="$t('replenish.pleaseEnter')" v-model="ruleForm.roleName" />
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { createNodeClass } from '@/api/k8sApi'

const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  k8sId: {
    type: Number,
    default: 0
  },
  adminAuth: {
    type: Boolean,
    default: false
  }
})

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    }
  }
)
const ruleForm = reactive<any>({
  name: '',
  clusterName: '',
  roleName: ''
})
const rules = {
  name: [
    {
      trigger: 'blur',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ],
  clusterName: [
    {
      required: true,
      message: t('replenish.pleaseEnter'),
      trigger: 'blur'
    }
  ],
  roleName: [
    {
      trigger: 'blur',
      message: t('replenish.pleaseEnter'),
      required: true
    }
  ]
}
const tenantId = computed(() => store.state.user.tenantId)
const namespaceList = ref<string[]>([])
const isServiceAccount = computed(() => props.adminAuth)
function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      isLoading.value = true
      createNodeClass({
        k8sClusterId: props.k8sId,
        name: ruleForm.name,
        clusterName: ruleForm.clusterName,
        roleName: ruleForm.roleName,
        tenantId: tenantId.value
      })
        .then((res) => {
          if (res.data.code === 0) {
            emit('submit')
            resetForm()
          }
        })
        .finally(() => {
          isLoading.value = false
        })
      // store
      //   .dispatch('roleName/namespaceK8sCreate', {
      //     k8sId: props.k8sId,
      //     namespaceName: isServiceAccount.value ? ruleForm.roleName : ruleForm.name,
      //     clusterName: ruleForm.clusterName,
      //     tenantId: tenantId.value
      //   })
      //   .then((res) => {
      //     emit('submit')
      //     resetForm()
      //   })
      //   .finally(() => {
      //     isLoading.value = false
      //   })
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.name = ''
  ruleForm.roleName = ''
  ruleForm.serviceaccount = ''
  emit('update:modelValue', false)
}
</script>
