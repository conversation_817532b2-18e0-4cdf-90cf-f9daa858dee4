import { PropType } from "vue";
import "./index.scss";
declare const _default: import("vue").DefineComponent<{
    modelValue: {
        type: PropType<string | number | boolean>;
    };
}, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    modelValue: {
        type: PropType<string | number | boolean>;
    };
}>>, {}, {}>;
export default _default;
