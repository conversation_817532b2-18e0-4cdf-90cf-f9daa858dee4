import type { PropType } from "vue";
import type { DcFormCustomRenderFn } from "../../types";
import "./index.scss";
declare const _default: import("vue").DefineComponent<{
    modelValue: {};
    renderCustom: {
        type: PropType<DcFormCustomRenderFn>;
    };
    rowIndex: {
        type: NumberConstructor;
        default: number;
    };
}, {
    onChange: (val: any) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    "update:modelValue": (val: any) => boolean;
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    modelValue: {};
    renderCustom: {
        type: PropType<DcFormCustomRenderFn>;
    };
    rowIndex: {
        type: NumberConstructor;
        default: number;
    };
}>> & {
    "onUpdate:modelValue"?: ((val: any) => any) | undefined;
}, {
    rowIndex: number;
}, {}>;
export default _default;
