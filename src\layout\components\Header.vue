<template>
  <div class="nav">
    <div class="logo">
      <span v-if="VITE_DATA_CENTER" @click="go('namespace')" class="logo-text cursor-pointer">{{ $t('replenish.dataPlatform') }}</span>
      <img v-else-if="Base.IS_DEV" class="logo-img cursor-pointer" @click="go('namespace')" src="@/assets/img/logo.png" alt="logo" />
      <img v-else class="logo-img cursor-pointer" @click="go('namespace')" :src="logoImg" alt="logo" />
      <div class="type" v-if="systemVersion === 1">{{ $t('replenish.standardEdition') }}</div>
      <div class="type light-weight" v-else>{{ $t('replenish.lightweightVersion') }}</div>
    </div>

    <div class="loginout">
      <TenantrySwitch v-if="type === 2" />
      <span v-if="$has('inspection')" @click="go('inspection')" class="inline-block h-24px mr-20px cursor">
        <BaseElTooltip :content="$t('replenish.platformInspection')" placement="bottom">
          <div class="inline-block bg-bgWhite rd-50%">
            <img class="w-24px h-24px" :src="menuIconSrc(['/inspection'], 'xunjianac', 'xunjian')" alt="icon" />
          </div>
        </BaseElTooltip>
      </span>
      <span v-if="$has('productDocumentation')" @click="go('productDocumentation')" class="inline-block h-24px mr-20px cursor">
        <BaseElTooltip :content="$t('replenish.productDoc')" placement="bottom">
          <div class="inline-block bg-bgWhite rd-50%">
            <img class="w-24px h-24px" :src="menuIconSrc(['/productDocumentation'], 'documentac', 'document')" alt="icon_document" />
          </div>
        </BaseElTooltip>
      </span>

      <el-menu
        router
        :default-active="routeRootPath"
        mode="horizontal"
        background-color="transparent"
        active-text-color="var(--ops-primary-color)"
        :ellipsis="false"
      >
        <LangSwitch v-if="$has('locales')" />
        <el-sub-menu index="1">
          <template #title>
            <div class="inline-block bg-bgWhite rd-50%">
              <img class="w-24px h-24px" :src="menuIconSrc(['/user', '/tenantry'], 'shezhiac', 'shezhi')" alt="icon" />
            </div>
          </template>
          <el-menu-item v-if="$has('userList')" index="/user">{{ $t('menu.userList') }}</el-menu-item>
          <el-menu-item v-if="$has('userList') && type === 2" index="/tenantry">{{ $t('replenish.tenantList') }}</el-menu-item>
          <el-menu-item v-if="$has('system-config') && type === 2" index="/systemConfig">平台配置</el-menu-item>
          <div class="version">{{ $t('menu.version') }} {{ versionInfo }}</div>
        </el-sub-menu>
        <el-sub-menu index="2">
          <template #title>
            <div class="inline-block bg-bgWhite rd-50%">
              <img class="w-24px h-24px" :src="menuIconSrc(['/information'], 'userac', 'user')" alt="icon" />
            </div>
            <span class="ml-4px">{{ userName }}</span>
          </template>
          <el-menu-item index="/information"> {{ $t('menu.accountInformation') }} </el-menu-item>
          <el-menu-item index="/login" @click="doLoginout"> {{ $t('menu.signOut') }} </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getVersion, logout } from '@/api/userApi'
import Base from '@/utils/Base'
import { bus } from '@/utils/mitt'
import { LStorage } from '@/utils/storage'
import { useMeunImportIcon } from '../hooks/useMeunImportIcon'
import LangSwitch from './HeaderLangSwitch.vue'
import TenantrySwitch from './HeaderTenantrySwitch.vue'
const VITE_DATA_CENTER = import.meta.env.VITE_DATA_CENTER === 'true'
const { router, store, $has } = useBasicTool()
const type = computed(() => Number(store.state.user.userInfo.type))
const userName = computed(() => store.state.user.userName)
const { meunIconMap, routeRootPath, menuIconSrc } = useMeunImportIcon()
const versionInfo = ref('')
const logoInfo = computed(() => store.state.user.logoInfo)
const logoImg = computed(() => {
  return `data:image/png;base64,${logoInfo.value.logo}`
})
const systemVersion = computed(() => store.state.app.systemVersion)
onMounted(() => {
  store.dispatch('user/getLogoInfo')
  store.dispatch('user/getUserInfo')
  store.dispatch('namespace/getMenuNamespaceList')
  store.dispatch('user/getTerminalVisible')
  getVersion().then((res) => {
    const { version, hash } = res.data.data
    versionInfo.value = `${version}(${hash})`
  })
})
function doLoginout() {
  logout().then((res: any) => {
    if (res.data.code === '200') {
      Base.clearCookie(Base.cookie)
      LStorage.clear()
      window.sessionStorage.removeItem('vuex')
      router.push('/login')
      bus.emit('closeWebSocket')
      store.commit('user/setTenantId', '')
    }
  })
}
function more() {
  store.commit('namespace/setMeunCurrentNamespaceTitle', '')
}

function go(routeName: string) {
  router.push({ name: routeName })
}
</script>

<style lang="scss" scoped>
.nav {
  @include flex(space-between, none);
  user-select: none;
  .logo {
    @include flex(flex-start);
    width: 280px;
    height: 56px;
    .logo-img {
      width: 220px;
      height: 48px;
    }
    .logo-text {
      margin-left: 30px;
      font-size: 24px;
      line-height: 33px;
      font-weight: 600;
      color: var(--ops-primary-color);
    }
    .type {
      min-width: max-content;
      height: 26px;
      padding: 0px 10px;
      border: 1px solid rgba(86, 122, 255, 0.5);
      border-radius: 2px;
      margin-left: 16px;
      font-size: 14px;
      line-height: 24px;
      font-weight: 400;
      color: #567aff;
      &.light-weight {
        border-color: #00b42a;
        color: #00b42a;
      }
    }
  }
  .loginout {
    flex: 1;
    height: 56px;
    @include flex(flex-end, center);
    i {
      font-size: 16px;
      font-weight: 400;
      color: var(--ops-text-color);
    }
    :deep(.el-menu) {
      height: 56px !important;
    }
    :deep(.el-sub-menu__title) {
      padding-right: 20px !important;
    }
    :deep(.el-sub-menu .el-sub-menu__icon-arrow) {
      right: 0px;
    }
  }
}
.version {
  width: 200px;
  height: 36px;
  padding: 0px 10px;
  font-size: 14px;
  line-height: 36px;
  color: var(--ops-disabled-color);
}
.el-menu {
  border: none;
}

// 清除菜单选中时原有的下边框
:deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
  border: none !important;
}
// 设置除了选中状态时焦点状态背景颜色为transparent
:deep(.el-menu--horizontal .el-menu-item:not(.is-disabled):focus) {
  background-color: transparent !important;
}
</style>
