<template>
  <BaseElTooltip :disabled="isDisabled" :content="content" :placement="placement">
    <div class="ellipsis" v-bind="$attrs">
      <span ref="box">{{ content }}</span>
    </div>
  </BaseElTooltip>
</template>
<script lang="ts" setup>
const box = ref()
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  line: {
    type: Number,
    default: 2
  },
  placement: {
    type: String,
    default: 'top'
  }
})
const { line } = toRefs(props)
const isDisabled = computed(() => {
  return box.value?.offsetHeight < line.value * 18
})
</script>
<style lang="scss" scoped>
.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  width: 100%;
  padding: 0px;
  line-height: 18px;
  text-overflow: ellipsis;
  white-space: normal;
  -webkit-line-clamp: v-bind(line);
  -webkit-box-orient: vertical;
}
</style>
