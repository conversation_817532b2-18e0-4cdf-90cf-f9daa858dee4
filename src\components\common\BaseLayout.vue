<template>
  <!-- 空白盒子 -->
  <div v-if="slots.main" class="layout-main" key="main">
    <slot name="main"></slot>
  </div>
  <!-- 上中下布局 -->
  <div v-else class="layout" key="layout">
    <div v-if="isHeader" class="layout-header" :style="{ padding: props.headerPadding }">
      <slot name="header"></slot>
    </div>
    <div class="layout-content">
      <slot name="content"></slot>
    </div>
    <div v-if="isFooter" class="layout-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
const { store } = useBasicTool()

const slots = useSlots()
const props = defineProps({
  isFooter: {
    type: Boolean,
    default: true
  },
  isHeader: {
    type: Boolean,
    default: true
  },
  headerPadding: {
    type: String,
    default: '0 20px'
  }
})
</script>
<style lang="scss" scoped>
.layout-main {
  min-height: calc(100% - 146px);
  margin: 20px;
}
.layout {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: space-between;
  height: 100%;
  border-radius: var(--radius-m);
  background-color: var(--ops-bg-white-color);
  .layout-content,
  .layout-footer {
    width: 100%;
    padding: 0 20px;
  }
  .layout-header {
    @include flex;
    min-height: 60px;
  }
  .layout-content {
    overflow: auto;
    flex: 1;
  }
  .layout-footer {
    @include flex;
    flex-direction: row-reverse;
    height: 60px;
  }
}
</style>
