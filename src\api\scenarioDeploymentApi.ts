// monitorApi.ts
import service from '@/utils/Http'

/**
 * @description 获取场景式部署卡片列表
 * @return {*}
 */

export const getComponentScene = async () => {
  return service.get<ApiResponse<Array<ScenarioDeploymentApi.GetComponentSceneDataItem>>>('/component-scene/selectList')
}

/**
 * @description 集群名称是否可用校验
 * @param {ScenarioDeploymentApi.NameUniqueCheck} data
 * @return {*}
 */

export const nameUniqueCheck = async (data: ScenarioDeploymentApi.NameUniqueCheck) => {
  return service.post<ApiResponse<boolean>>('/namespace/nameUniqueCheck', data)
}

/**
 * @description 集群名称是否可用校验
 * @param {ScenarioDeploymentApi.GetComponentDistributionStrategy} data
 * @return {*}
 */

export const getComponentDistributionStrategy = async (data: ScenarioDeploymentApi.GetComponentDistributionStrategy) => {
  return service.post<ApiResponse<Array<ScenarioDeploymentApi.ConfigurationItem>>>('/component-scene/getComponentDistributionStrategy', data)
}

/**
 * @description 集群名称是否可用校验
 * @return {*}
 */

export const getComponentModuleRoleDic = async () => {
  return service.post<ApiResponse<Array<{ roleName: string; id: stringOrNumber }>>>('/component-scene/getComponentModuleRoleDic')
}
/**
 * @description 批量初始化组件
 * @param {ScenarioDeploymentApi.AddRoleByBatch} data
 * @return {*}
 */

export const addRoleByBatch = async (data: ScenarioDeploymentApi.AddRoleByBatch) => {
  return service.post<ApiResponse<Array<any>>>('/component-scene/addRoleByBatch', data)
}

/**
 * @description 批量初始化组件
 * @param {Array<ScenarioDeploymentApi.ConfigurationItem>} data
 * @return {*}
 */

export const verifyResource = async (data: Array<ScenarioDeploymentApi.ConfigurationItem>) => {
  return service.post<ApiResponse<string>>('/cluster/service/verifyResource', data)
}
/**
 * @description 组件批量部署进度查询
 * @param {string} data
 * @return {*}
 */

export const deployProgres = async (data: string) => {
  return service.get<ApiResponse<ScenarioDeploymentApi.DeployProgresData>>(`/cluster/service/batch/deploy/progres/${data}`)
}

/**
 * @description 批量查询配置
 * @param {ScenarioDeploymentApi.SelectConfigListByBatch} data
 * @return {*}
 */

export const selectConfigListByBatch = async (data: ScenarioDeploymentApi.SelectConfigListByBatch) => {
  return service.post<ApiResponse<Array<ScenarioDeploymentApi.BatchDeployDateItem>>>(`/component-scene/selectConfigListByBatch`, data)
}

/**
 * @description 批量查询配置
 * @param {Array<ScenarioDeploymentApi.BatchDeployDateItem>} data
 * @return {*}
 */

export const batchDeploy = async (data: Array<ScenarioDeploymentApi.BatchDeployDateItem>) => {
  return service.post<ApiResponse<string>>(`/cluster/service/batchDeploy`, data)
}

/**
 * @description 返回到自动分配主机
 * @param {ScenarioDeploymentApi.SelectConfigListByBatch>} data
 * @return {*}
 */

export const backToGetComponentDistributionStrategy = async (data: ScenarioDeploymentApi.SelectConfigListByBatch) => {
  return service.post<ApiResponse<boolean>>(`/component-scene/backToGetComponentDistributionStrategy`, data)
}
