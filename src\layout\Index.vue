<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-08-05 09:33:51
 * @LastEditTime: 2025-08-06 11:16:07
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @Description:
-->
<template>
  <div class="box">
    <div ref="notificationBox" class="notification-box"></div>
  </div>
  <div class="common-layout" v-if="!isIframe">
    <el-container>
      <el-header class="header" height="56px">
        <Header />
      </el-header>
      <el-container style="height: calc(100vh - 56px)">
        <Menu />
        <el-main>
          <Main />
        </el-main>
      </el-container>
    </el-container>
  </div>
  <div class="iframe-layout" v-else>
    <el-container style="height: 100%">
      <router-view />
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import useGlobalMessage from '@/hooks/useGlobalMessage'
import Base from '@/utils/Base'

import { bus } from '@/utils/mitt'
import { LStorage } from '@/utils/storage'
import { ElNotification } from 'element-plus'
import Header from './components/Header.vue'
import Main from './components/Main.vue'
import Menu from './components/Menu.vue'

const notificationBox = ref()

const { connectWebSocket, close } = useGlobalMessage(tip)

if (LStorage.get(Base.cookie)) {
  connectWebSocket()
}
bus.on('connectWebSocket', () => {
  connectWebSocket()
})
bus.on('closeWebSocket', () => {
  close()
})
const index = ref(0)
function tip(data: { msgState: 'success' | 'error'; msg: string; timeStamp: number }) {
  if (!LStorage.get(Base.cookie)) {
    return
  }
  index.value++
  ElNotification({
    title: data.msgState,
    message: data.msg || index.value.toString(),
    type: data.msgState,
    duration: 0,
    offset: 10,
    appendTo: notificationBox.value,
    position: 'bottom-right'
  })
}
const isIframe = ref(false)
onBeforeMount(() => {
  isIframe.value = sessionStorage.getItem('isIframe') === 'true'
})
</script>

<style lang="scss" scoped>
.common-layout {
  min-width: 1400px;
  min-height: 100vh;
  background-color: var(--ops-bg-color);
  :deep(.el-main) {
    --el-main-padding: 0px;
  }
}
.header {
  border-bottom: 1px solid var(--ops-header-border-color);
}
.box {
  position: fixed;
  right: 0px;
  bottom: 50px;
  z-index: 99999;
  overflow-y: auto;
  max-height: 600px;
}
.notification-box {
  display: flex;
  flex-direction: column-reverse;
  flex-wrap: wrap;
  // overflow-y: auto;
  height: auto;
  // max-height: 600px;
}
.iframe-layout {
  width: 100%;
  height: 100vh;
}
</style>
