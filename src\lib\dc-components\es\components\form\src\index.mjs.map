{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/form/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-form\r\n    ref=\"formRef\"\r\n    :inline=\"inline\"\r\n    :label-width=\"props.config.labelWidth\"\r\n    :label-position=\"props.config.labelPosition\"\r\n    :show-message=\"props.config.showMessage\"\r\n    :inline-message=\"props.config.inlineMessage\"\r\n    :disabled=\"props.config.disabled\"\r\n    :model=\"model\"\r\n    class=\"dc-form\"\r\n    @validate=\"handleValidate\"\r\n  >\r\n    <template v-for=\"item in props.config.children\" :key=\"item.model\">\r\n      <DcFormItem\r\n        v-model=\"model[item.model]\"\r\n        :config=\"item\"\r\n        :visible=\"visibleControl[item.model]\"\r\n        :is-inline=\"inline\"\r\n        :margin=\"{\r\n          ...(props.config.itemMargin || {}),\r\n          ...(item.margin || {}),\r\n        }\"\r\n        @triggerEffect=\"itemChange\"\r\n      />\r\n    </template>\r\n  </el-form>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, ref, watch } from \"vue\";\r\nimport { ElForm } from \"element-plus\";\r\nimport { cloneDeep, isObject } from \"lodash\";\r\nimport DcFormItem from \"./components/FormItem/index.vue\";\r\n\r\nimport type { FormItemProp, FormValidateCallback } from \"element-plus\";\r\nimport type { PropType } from \"vue\";\r\nimport type {\r\n  DcFormConfig,\r\n  DcFormItemEffect,\r\n  DcFormModel,\r\n  DcFormVisibleControl,\r\n} from \"./types\";\r\n\r\ndefineOptions({\r\n  name: \"DcForm\",\r\n});\r\n\r\nconst props = defineProps({\r\n  config: {\r\n    type: Object as PropType<DcFormConfig>,\r\n    default: () => ({\r\n      children: [],\r\n    }),\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"validate\"]);\r\n\r\nconst formRef = ref();\r\n\r\nconst model = ref<DcFormModel>({});\r\nconst visibleControl = ref<DcFormVisibleControl>({});\r\n\r\nconst inline = computed(() =>\r\n  typeof props.config.inline !== \"undefined\" ? props.config.inline : false\r\n);\r\n\r\nconst handleValidate = (\r\n  prop: FormItemProp,\r\n  isValid: boolean,\r\n  message: string\r\n) => {\r\n  emits(\"validate\", prop, isValid, message);\r\n};\r\n\r\nconst validate = (callback?: FormValidateCallback) => {\r\n  return formRef.value.validate(callback);\r\n};\r\n\r\nconst validateField = (\r\n  props?: FormItemProp | FormItemProp[] | undefined,\r\n  callback?: FormValidateCallback | undefined\r\n) => {\r\n  return formRef.value.validateField(props, callback);\r\n};\r\n\r\nconst resetFields = (props?: FormItemProp | FormItemProp[] | undefined) => {\r\n  formRef.value.resetFields(props);\r\n};\r\n\r\nconst scrollToField = (prop: FormItemProp) => {\r\n  formRef.value.scrollToField(prop);\r\n};\r\n\r\nconst clearValidate = (props?: FormItemProp | FormItemProp[] | undefined) => {\r\n  formRef.value.clearValidate(props);\r\n};\r\n\r\nconst initModel = (initVal: DcFormModel) => {\r\n  const modelVal = model.value;\r\n  model.value = {\r\n    ...modelVal,\r\n    ...cloneDeep(initVal),\r\n  };\r\n};\r\n\r\nconst getValues = () => {\r\n  return {\r\n    ...model.value,\r\n  };\r\n};\r\n\r\nconst setFieldsValue = (fn: (val: DcFormModel) => DcFormModel) => {\r\n  model.value = fn(model.value);\r\n};\r\n\r\nconst reset = (clearValidate = true) => {\r\n  const modelVal = { ...model.value };\r\n  props.config.children.map((item) => {\r\n    modelVal[item.model] = isObject(item.defaultValue)\r\n      ? cloneDeep(item.defaultValue)\r\n      : item.defaultValue;\r\n    return null;\r\n  });\r\n  model.value = modelVal;\r\n\r\n  clearValidate &&\r\n    setTimeout(() => {\r\n      formRef.value?.clearValidate();\r\n    }, 0);\r\n};\r\n\r\nconst itemChange = (effect?: DcFormItemEffect, rowIndex?: number) => {\r\n  if (effect && typeof effect === \"function\") {\r\n    effect(model.value, initModel, rowIndex);\r\n  }\r\n};\r\n\r\nconst asyncValidate = (errorInfo: any) => {\r\n  return new Promise((resolve, reject) => {\r\n    formRef.value?.validate((isValid: boolean) => {\r\n      if (isValid) {\r\n        resolve({\r\n          ...getValues(),\r\n        });\r\n      } else {\r\n        reject(errorInfo);\r\n      }\r\n    });\r\n  });\r\n};\r\n\r\nwatch(\r\n  () => props.config.children.length,\r\n  () => {\r\n    const modelVal = { ...model.value };\r\n    props.config.children.map((item) => {\r\n      if (typeof modelVal[item.model] === \"undefined\") {\r\n        modelVal[item.model] = isObject(item.defaultValue)\r\n          ? cloneDeep(item.defaultValue)\r\n          : item.defaultValue;\r\n      }\r\n      if (typeof visibleControl.value[item.model] === \"undefined\") {\r\n        visibleControl.value[item.model] = item.visible\r\n          ? computed(() => {\r\n              const res = item.visible ? item.visible(model.value) : false;\r\n              return res;\r\n            })\r\n          : true;\r\n      }\r\n\r\n      return null;\r\n    });\r\n    model.value = modelVal;\r\n  },\r\n  { deep: true, immediate: true }\r\n);\r\n\r\ndefineExpose({\r\n  validate,\r\n  validateField,\r\n  resetFields,\r\n  scrollToField,\r\n  clearValidate,\r\n  getValues,\r\n  initModel,\r\n  reset,\r\n  setFieldsValue,\r\n  asyncValidate,\r\n  model,\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;;AA4Cc,EAAA,IAAA,EAAA,QAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AASd,IAAA,MAAM,OAAQ,GAAA,GAAA,EAAA,CAAA;AAEd,IAAA,MAAM,WAAc,CAAA,EAAA,CAAA,CAAA;AAEpB,IAAM,MAAA,cAA2B,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AACjC,IAAM,MAAA,MAAA,GAAA,QAAiB,CAA0B,MAAE,OAAA,KAAA,CAAA,MAAA,CAAA,MAAA,KAAA,WAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,GAAA,KAAA,CAAA,CAAA;AAEnD,IAAM,MAAA,cAAkB,GAAA,CAAA,IAAA,EACtB,OAAO,EAAA,OAAa,KAAA;AAGtB,MAAA,KAAuB,CAAA,UAAA,EAAA,IACrB,EACA,OAAA,EAAA,OAEG,CAAA,CAAA;AACH,KAAM,CAAA;AAAkC,IAC1C,MAAA,QAAA,GAAA,CAAA,QAAA,KAAA;AAEA,MAAM,OAAA,OAAA,CAAW,KAAqC,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA;AACpD,KAAO,CAAA;AAA+B,IACxC,MAAA,aAAA,GAAA,CAAA,MAAA,EAAA,QAAA,KAAA;AAEA,MAAM,OAAA,OAAA,CAAA,KAAgB,CACpB,aAEG,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AACH,KAAA,CAAA;AAAkD,IACpD,MAAA,WAAA,GAAA,CAAA,MAAA,KAAA;AAEA,MAAM,OAAA,CAAA,KAAA,CAAA,WAAqE,CAAA,MAAA,CAAA,CAAA;AACzE,KAAQ,CAAA;AAAuB,IACjC,MAAA,aAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,OAAA,CAAA,KAAA,CAAA,aAAwC,CAAA,IAAA,CAAA,CAAA;AAC5C,KAAQ,CAAA;AAAwB,IAClC,MAAA,aAAA,GAAA,CAAA,MAAA,KAAA;AAEA,MAAM,OAAA,CAAA,KAAA,CAAA,aAAuE,CAAA,MAAA,CAAA,CAAA;AAC3E,KAAQ,CAAA;AAAyB,IACnC,MAAA,SAAA,GAAA,CAAA,OAAA,KAAA;AAEA,MAAM,MAAA,QAAA,GAAa,KAAyB,CAAA,KAAA,CAAA;AAC1C,MAAA,KAAA,CAAM;AACN,QAAA,GAAA,QAAc;AAAA,QACZ,GAAG,SAAA,CAAA,OAAA,CAAA;AAAA,OACH,CAAA;AAAoB,KACtB,CAAA;AAAA,IACF,MAAA,SAAA,GAAA,MAAA;AAEA,MAAA;AACE,QAAO,GAAA,KAAA,CAAA,KAAA;AAAA,OAAA,CACL;AAAS,KACX,CAAA;AAAA,IACF,MAAA,cAAA,GAAA,CAAA,EAAA,KAAA;AAEA,MAAM,KAAA,CAAA,KAAA,GAAA,EAAA,CAAA,KAAkB,CAA0C,KAAA,CAAA,CAAA;AAChE,KAAM,CAAA;AAAsB,IAC9B,MAAA,KAAA,GAAA,CAAA,cAAA,GAAA,IAAA,KAAA;AAEA,MAAM,MAAA,QAAS,GAAA,EAAA,GAAA,KAAA,CAAA,KAAyB,EAAA,CAAA;AACtC,MAAA,KAAA,CAAM,MAAW,CAAA,QAAK,CAAA,GAAA,CAAA,CAAA,IAAY,KAAA;AAClC,QAAA,QAAa,CAAA,IAAA,CAAA,KAAA,CAAS,GAAI,QAAU,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AAClC,QAAS,OAAA,IAAA,CAAA;AAGT,OAAO,CAAA,CAAA;AAAA,MACT,KAAC,CAAA,KAAA,GAAA,QAAA,CAAA;AACD,MAAA,cAAc,IAAA,UAAA,CAAA,MAAA;AAEd,QAAA,OAAA,CAAA,KAAA,EAAA,aACmB,EAAA,CAAA;AACf,OAAA,EAAA,CAAA,CAAA,CAAA;AAA6B,KAAA,CAC/B;AAAI,IACR,MAAA,UAAA,GAAA,CAAA,MAAA,EAAA,QAAA,KAAA;AAEA,MAAM,IAAA,MAAA,IAAA,OAAc,MAAiD,KAAA,UAAA,EAAA;AACnE,QAAI,MAAA,CAAA,KAAU,CAAO,KAAA,EAAA,SAAA,EAAuB,QAAA,CAAA,CAAA;AAC1C,OAAO;AAAgC,KACzC,CAAA;AAAA,IACF,MAAA,aAAA,GAAA,CAAA,SAAA,KAAA;AAEA,MAAM,OAAA,IAAA,OAAA,CAAA,CAAgB,OAAoB,EAAA,MAAA,KAAA;AACxC,QAAA,OAAW,CAAA,KAAA,EAAA,QAAS,CAAA,CAAA,OAAoB,KAAA;AACtC,UAAQ,IAAA,OAAA,EAAO;AACb,YAAA,OAAa,CAAA;AACX,cAAQ,GAAA,SAAA,EAAA;AAAA,aAAA,CACN;AAAa,WAAA,MACd;AAAA,YACI,MAAA,CAAA,SAAA,CAAA,CAAA;AACL,WAAA;AAAgB,SAClB,CAAA,CAAA;AAAA,OAAA,CACF,CAAC;AAAA,KAAA,CACH;AAAC,IACH,KAAA,CAAA,MAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,MAAA,EAAA,MAAA;AAEA,MAAA,MACQ,QAAA,GAAa,EAAA,GAAA,KAAA,CAAA;AAEjB,MAAA,KAAA,CAAM,MAAW,CAAA,QAAK,CAAA,GAAA,CAAA,CAAA,IAAY,KAAA;AAClC,QAAA,IAAM,OAAO,QAAA,CAAS,IAAI,CAAC,KAAS,CAAA,KAAA,WAAA,EAAA;AAClC,UAAA,QAAW,CAAA,IAAA,CAAA,KAAc,CAAA,GAAA,QAAA,CAAA,IAAwB,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AAC/C,SAAS;AAEA,QACX,IAAA,OAAA,cAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,WAAA,EAAA;AACA,UAAA,cAAW,CAAA,KAAA,CAAA,IAAqB,CAAA,KAAA,CAAA,GAAK,YAAwB,GAAA,QAAA,CAAA,MAAA;AAC3D,YAAA,MAAA,GAAA,GAAA,YAA0B,GAAA,IAAA,CAAA,OAAc,CAAA,KAAA,CAAA,cACrB,CAAA;AACb,YAAA;AACA,WAAO,CAAA,GAAA,IAAA,CAAA;AAAA,SAAA;AAET,QACN,OAAA,IAAA,CAAA;AAEA,OAAO,CAAA,CAAA;AAAA,MACT,KAAC,CAAA,KAAA,GAAA,QAAA,CAAA;AACD,KAAA,EAAA,EAAA,IAAc,EAAA,IAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAChB,QACE,CAAA;AAGJ,MAAa,QAAA;AAAA,MACX,aAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,SAAA;AAAA,MACA,KAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,KAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}