<template>
  <div>
    <div class="relative">
      <div class="minHeight px-20px py-20px bg-hex-ffffff">
        <div class="flex justify-between items-center">
          <div>
            <TheTitle class="inline-block mr-12px" :content="data?.inspectionReportName" />
            <span :class="alertTypeClass.get(data?.inspectionStatus)">{{ stateText.get(data?.inspectionStatus) }}</span>
          </div>
          <BaseButton class="mb-20px" v-if="$has('inspection-report-export')" type="primary" @click="down">
            <span class="ml-1px">{{ $t('replenish.export') }}</span>
          </BaseButton>
        </div>
        <div class="flex justify-between items-center h-86px mb-20px px-28px bg-#FAFBFC">
          <div class="flex-1">
            <div class="label">{{ $t('replenish.startTime') }}</div>
            <div class="value">{{ data.startTime ?? '-' }}</div>
          </div>
          <div class="flex-1">
            <div class="label">{{ $t('replenish.endTime') }}</div>
            <div class="value">{{ data.endTime ?? '-' }}</div>
          </div>
          <div class="flex-1">
            <div class="label">{{ $t('replenish.inspectionDuration') }}</div>
            <div class="value">{{ data.inspectionTime >= 0 ? data.inspectionTime + ' mins' : '-' }}</div>
          </div>
          <div class="flex-1">
            <div class="label">{{ $t('replenish.inspectionItems') }}</div>
            <div class="value">{{ data.inspectionTotalNum ?? '-' }}</div>
          </div>
        </div>
        <el-collapse class="report-collapse" v-model="activeNames">
          <el-collapse-item v-if="data.platformItems?.length > 0" :name="$t('replenish.platformBasicEnvironment')">
            <template #title>
              <TheTitle :content="$t('replenish.platformBasicEnvironment')" />
              <div @click="toggleCollapse" class="toggle-collapse" v-if="isNotAllCollapse">{{ $t('replenish.collapseAll') }}</div>
            </template>

            <div v-for="(item, index) in data.platformItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="data.clusterItems?.length > 0" :name="$t('replenish.clusterInspection')">
            <template #title>
              <TheTitle :content="$t('replenish.clusterInspection')" />
            </template>

            <div v-for="(item, index) in data.clusterItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="data.componentItems?.length > 0" :name="$t('replenish.componentServiceInspection')">
            <template #title>
              <TheTitle :content="$t('replenish.componentServiceInspection')" />
            </template>

            <div v-for="(item, index) in data.componentItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </el-collapse-item>
          <el-collapse-item :name="$t('replenish.hostInspection')" v-if="data.hostItems?.length > 0">
            <template #title>
              <TheTitle :content="$t('replenish.hostInspection')" />
            </template>

            <div v-for="(item, index) in data.hostItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </el-collapse-item>
          <el-collapse-item :name="$t('replenish.disasterRecoveryTaskCheck')" v-if="data.taskItems?.length > 0">
            <template #title> <TheTitle :content="$t('replenish.disasterRecoveryTaskCheck')" /> </template>
            <div v-for="(item, index) in data.taskItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div id="pdf-mark" class="px-20px absolute top-20px z--99 left-0px">
        <div class="minHeight px-20px pt-20px bg-hex-ffffff mt-10px">
          <div class="flex justify-between items-center">
            <div>
              <TheTitle class="inline-block mr-12px" :content="data.inspectionReportName" />
              <span :class="alertTypeClass.get(data.inspectionStatus)">{{ stateText.get(data?.inspectionStatus) }}</span>
            </div>
            <div></div>
          </div>
          <div class="flex justify-between items-center h-86px mb-20px px-28px bg-#FAFBFC">
            <div class="flex-1">
              <div class="label">{{ $t('replenish.startTime') }}</div>
              <div class="value">{{ data.startTime ?? '-' }}</div>
            </div>
            <div class="flex-1">
              <div class="label">{{ $t('replenish.endTime') }}</div>
              <div class="value">{{ data.endTime ?? '-' }}</div>
            </div>
            <div class="flex-1">
              <div class="label">{{ $t('replenish.inspectionDuration') }}</div>
              <div class="value">{{ data.inspectionTime >= 0 ? data.inspectionTime + ' mins' : '-' }}</div>
            </div>
            <div class="flex-1">
              <div class="label">{{ $t('replenish.inspectionItems') }}</div>
              <div class="value">{{ data.inspectionTotalNum ?? '-' }}</div>
            </div>
          </div>
          <div v-if="data.platformItems?.length > 0">
            <TheTitle class="inline-block mr-12px" :content="$t('replenish.platformBasicEnvironment')" />
            <div v-for="(item, index) in data.platformItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </div>
          <div v-if="data.clusterItems?.length > 0">
            <TheTitle class="inline-block mr-12px" :content="$t('replenish.clusterInspection')" />
            <div v-for="(item, index) in data.clusterItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </div>
          <div v-if="data.componentItems?.length > 0">
            <TheTitle class="inline-block mr-12px" :content="$t('replenish.componentServiceInspection')" />
            <div v-for="(item, index) in data.componentItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </div>
          <div v-if="data.hostItems?.length > 0">
            <TheTitle class="inline-block mr-12px" :content="$t('replenish.hostInspection')" />
            <div v-for="(item, index) in data.hostItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </div>
          <div v-if="data.taskItems?.length > 0">
            <TheTitle class="inline-block mr-12px" :content="$t('replenish.disasterRecoveryTaskCheck')" />
            <div v-for="(item, index) in data.taskItems" :key="index">
              <ViewTable :data="item" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import htmlToPdf from '@/utils/htmlToPdf'
import ViewTable from './components/ViewTable.vue'
const { route, t, setBreadList, store, $has } = useBasicTool()
const data = ref()
const alertTypeClass = new Map()
  .set(2, 'grade-label grade-label--success')
  .set(3, 'grade-label grade-label--danger')
  .set(1, 'grade-label grade-label--ordinary')
const stateText = new Map().set(1, t('replenish.inProgress')).set(3, t('replenish.abnormal')).set(2, t('replenish.complete'))

const activeNames = ref<string[]>([])

const dataLength = ref(0)
const isNotAllCollapse = computed(() => {
  return activeNames.value.length === dataLength.value
})

function toggleCollapse(e: Event) {
  e.stopPropagation()

  activeNames.value = []
}
function down() {
  htmlToPdf.getPdf(data.value?.inspectionReportName, 'pdf-mark')
}

onMounted(() => {
  getInspectionReport(route.query.id)
  setBreadList([
    {
      name: t('replenish.platformInspection'),
      to: {
        name: 'inspection'
      }
    },
    {
      name: t('replenish.reportDetails')
    }
  ])
})
function getInspectionReport(id: any) {
  store.dispatch('inspection/inspectionReport', { id }).then((res) => {
    data.value = res.data
    const { platformItems, hostItems, clusterItems, componentItems, taskItems } = res.data
    if (platformItems?.length) {
      dataLength.value += 1
      activeNames.value.push(t('replenish.platformBasicEnvironment'))
    }
    if (hostItems?.length) {
      dataLength.value += 1
      activeNames.value.push(t('replenish.clusterInspection'))
    }
    if (clusterItems?.length) {
      dataLength.value += 1
      activeNames.value.push(t('replenish.componentServiceInspection'))
    }
    if (componentItems?.length) {
      dataLength.value += 1
      activeNames.value.push(t('replenish.hostInspection'))
    }
    if (taskItems?.length) {
      dataLength.value += 1
      activeNames.value.push(t('replenish.disasterRecoveryTaskCheck'))
    }
  })
}
</script>
<style lang="scss" scoped>
.minHeight {
  min-height: calc(100vh - 96px);
}
.grade-label {
  width: auto;
  padding: 0px 10px;
}
#pdf-mark {
  :deep(.title-box .title-box__vertical) {
    position: relative !important;
    top: 9px !important;
  }
  .grade-label {
    position: relative !important;
    top: 1px !important;
    line-height: 10px !important;
  }
}
.label {
  margin-right: 10px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  color: #99a0b5;
}
.value {
  margin-top: 2px;
  margin-right: 10px;
  font-size: 14px;
  line-height: 22px;
}
.report-collapse {
  border: none;
  .title-box {
    margin-bottom: 0;
  }
  :deep(.el-collapse-item) {
    border: 1px solid rgba(230, 233, 244, 1);
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:last-child {
      border-bottom: 1px solid rgba(230, 233, 244, 1);
    }
  }
  :deep(.el-collapse-item__header) {
    padding: 0 20px;
    background: #fafbfc;
  }
  :deep(.el-collapse-item__wrap) {
    padding: 20px 30px 0px;
  }
  .toggle-collapse {
    position: absolute;
    right: 120px;
  }
}
</style>
