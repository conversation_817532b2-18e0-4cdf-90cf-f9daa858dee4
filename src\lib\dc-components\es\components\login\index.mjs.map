{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/login/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Login from \"./src/index.vue\";\r\n\r\nexport const DcLogin = withInstall(Login);\r\n\r\nexport default DcLogin;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}