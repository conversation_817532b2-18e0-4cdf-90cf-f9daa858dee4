import { useTitle } from '@vueuse/core'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
const VITE_GLOB_APP_TITLE = import.meta.env.VITE_GLOB_APP_TITLE
export default function useBrowserLabel(titleAfter = VITE_GLOB_APP_TITLE) {
  const route = useRoute()
  const { t } = useI18n()
  const key: string = route.meta.title ? `${route.meta.title}` : 'router.EngineManager'
  const title = useTitle(`${t(key)} | ${titleAfter}`)
  watch(
    () => route.meta.title,
    (newValue) => {
      const key: string = newValue ? `${newValue}` : ''
      title.value = newValue ? `${t(key)} | ${titleAfter}` : VITE_GLOB_APP_TITLE
    }
  )
  return { title }
}
