{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/utils/install/index.ts"], "sourcesContent": ["import type { SFCWithInstall } from \"./types\";\r\n\r\nexport const withInstall = <T, E extends Record<string, any>>(\r\n  main: T,\r\n  extra?: E\r\n) => {\r\n  (main as SFCWithInstall<T>).install = (app): void => {\r\n    for (const comp of [main, ...Object.values(extra ?? {})]) {\r\n      app.component(comp.name, comp);\r\n    }\r\n  };\r\n\r\n  if (extra) {\r\n    for (const [key, comp] of Object.entries(extra)) {\r\n      (main as any)[key] = comp;\r\n    }\r\n  }\r\n  return main as SFCWithInstall<T> & E;\r\n};\r\n\r\nexport * from \"./types\";\r\n"], "names": [], "mappings": ";;AAAY,MAAC,WAAW,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK;AAC5C,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC1B,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE;AAC9D,MAAM,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACrC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACrD,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACvB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd;;;;"}