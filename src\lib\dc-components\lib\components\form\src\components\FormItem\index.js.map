{"version": 3, "file": "index.js", "sources": ["../../../../../../../../packages/components/form/src/components/FormItem/index.vue"], "sourcesContent": ["<template>\r\n  <el-form-item\r\n    v-show=\"props.visible\"\r\n    ref=\"formItemRef\"\r\n    :prop=\"props.config.model\"\r\n    :label=\"props.config.label\"\r\n    :label-width=\"props.config.labelWidth\"\r\n    :rules=\"props.visible ? props.config.rules : []\"\r\n    :inline-message=\"props.config.inlineMessage\"\r\n    :class=\"`dc-form-item ${props.config.class || ''}`\"\r\n    :style=\"{\r\n      marginRight,\r\n      marginBottom,\r\n      marginTop,\r\n      marginLeft,\r\n    }\"\r\n  >\r\n    <template v-if=\"props.config.labelSlot\" #label=\"scope\">\r\n      <DcRenderVNode :vnode=\"props.config.labelSlot\" :scope=\"scope\" />\r\n    </template>\r\n\r\n    <component\r\n      v-bind=\"{ ...(props.config.props || {}) }\"\r\n      :is=\"Component[props.config.component]\"\r\n      :model-value=\"props.modelValue\"\r\n      :file-list=\"props.modelValue\"\r\n      :row-index=\"props.rowIndex\"\r\n      :disabled=\"props.config.disabled\"\r\n      @update:modelValue=\"itemChange\"\r\n      @blur=\"handleBlur\"\r\n      @triggerEffect=\"triggerEffect\"\r\n    >\r\n      <template\r\n        v-for=\"slotI in Object.keys(props.config.componentSlot || {})\"\r\n        #[slotI]=\"scope\"\r\n        :key=\"slotI\"\r\n      >\r\n        <DcRenderVNode\r\n          :vnode=\"(props.config.componentSlot || {})[slotI]\"\r\n          :scope=\"scope\"\r\n        />\r\n      </template>\r\n    </component>\r\n    <template v-if=\"props.config.rightSlot\">\r\n      <DcRenderVNode :vnode=\"props.config.rightSlot\" />\r\n    </template>\r\n  </el-form-item>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, ref, watch } from \"vue\";\r\nimport { ElFormItem } from \"element-plus\";\r\n\r\nimport DcRenderVNode from \"../../../../render-vnode/src/index\";\r\n\r\nimport { Component } from \"./constants\";\r\nimport type { PropType, Ref } from \"vue\";\r\nimport type {\r\n  DcFormItem,\r\n  DcFormItemEffect,\r\n  DcFormItemMargin,\r\n} from \"../../types\";\r\n\r\ndefineOptions({\r\n  name: \"DcFormItem\",\r\n});\r\n\r\nconst props = defineProps({\r\n  visible: {\r\n    type: Boolean as PropType<boolean | Ref<boolean>>,\r\n    default: true,\r\n  },\r\n  config: {\r\n    type: Object as PropType<DcFormItem>,\r\n    default: () => ({}),\r\n  },\r\n  modelValue: {\r\n    type: [String, Number, Boolean, Array, Object] as PropType<any>,\r\n  },\r\n  margin: {\r\n    type: Object as PropType<DcFormItemMargin>,\r\n    default: () => ({}),\r\n  },\r\n  isInline: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  rowIndex: {\r\n    type: Number,\r\n    default: 0,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\", \"triggerEffect\"]);\r\n\r\nconst formItemRef = ref();\r\n\r\nconst marginBottom = computed(() =>\r\n  props.margin.bottom ? props.margin.bottom : \"18px\"\r\n);\r\n\r\nconst marginRight = computed(() =>\r\n  props.margin.right ? props.margin.right : props.isInline ? \"32px\" : \"0px\"\r\n);\r\n\r\nconst marginLeft = computed(() =>\r\n  props.margin.left ? props.margin.left : \"unset\"\r\n);\r\n\r\nconst marginTop = computed(() =>\r\n  props.margin.top ? props.margin.top : \"unset\"\r\n);\r\n\r\nconst ruleTrigger = computed(() => {\r\n  return (props.config.rules || []).map((item) => item.trigger);\r\n});\r\nconst handleBlur = () => {\r\n  if (ruleTrigger.value.includes(\"blur\")) {\r\n    formItemRef.value?.validate(\"blur\", () => {});\r\n  }\r\n};\r\n\r\nconst triggerEffect = (effect: DcFormItemEffect, rowIndex?: number) => {\r\n  emits(\"triggerEffect\", effect, rowIndex);\r\n};\r\n\r\nconst itemChange = (val: any) => {\r\n  emits(\"update:modelValue\", val, props.config);\r\n  if (props.config.effect && typeof props.config.effect === \"function\") {\r\n    triggerEffect(props.config.effect, props.rowIndex);\r\n  }\r\n};\r\n\r\nwatch(\r\n  () => props.modelValue,\r\n  () => {\r\n    if (ruleTrigger.value.includes(\"change\")) {\r\n      formItemRef.value?.validate(\"change\", () => {});\r\n    }\r\n  }\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["DO_defineComponent", "ref", "computed", "watch"], "mappings": ";;;;;;;;;;;AA+Dc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AA0Bd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAA,MAAM,cAAcC,OAAI,EAAA,CAAA;AAElB,IAAA,MAAA,YAAA,GAAeC,aAAS,MAC5B,KAAA,CAAM,OAAO,MAAS,GAAA,KAAA,CAAM,MAAO,CAAA,MAAA,GAAS,MAC9C,CAAA,CAAA;AAEA,IAAA,MAAM,WAAc,GAAAA,YAAA,CAAS,MAC3B,KAAA,CAAM,MAAO,CAAA,KAAA,GAAQ,KAAM,CAAA,MAAA,CAAO,KAAQ,GAAA,KAAA,CAAM,QAAW,GAAA,MAAA,GAAS,KACtE,CAAA,CAAA;AAEM,IAAA,MAAA,UAAA,GAAaA,aAAS,MAC1B,KAAA,CAAM,OAAO,IAAO,GAAA,KAAA,CAAM,MAAO,CAAA,IAAA,GAAO,OAC1C,CAAA,CAAA;AAEM,IAAA,MAAA,SAAA,GAAYA,aAAS,MACzB,KAAA,CAAM,OAAO,GAAM,GAAA,KAAA,CAAM,MAAO,CAAA,GAAA,GAAM,OACxC,CAAA,CAAA;AAEM,IAAA,MAAA,WAAA,GAAcA,aAAS,MAAM;AACzB,MAAA,OAAA,CAAA,KAAA,CAAM,OAAO,KAAS,IAAA,IAAI,GAAI,CAAA,CAAC,IAAS,KAAA,IAAA,CAAK,OAAO,CAAA,CAAA;AAAA,KAC7D,CAAA,CAAA;AACD,IAAA,MAAM,aAAa,MAAM;AACvB,MAAA,IAAI,WAAY,CAAA,KAAA,CAAM,QAAS,CAAA,MAAM,CAAG,EAAA;AAC1B,QAAA,WAAA,CAAA,KAAA,EAAO,QAAS,CAAA,MAAA,EAAQ,MAAM;AAAA,SAAE,CAAA,CAAA;AAAA,OAC9C;AAAA,KACF,CAAA;AAEM,IAAA,MAAA,aAAA,GAAgB,CAAC,MAAA,EAA0B,QAAsB,KAAA;AAC/D,MAAA,KAAA,CAAA,eAAA,EAAiB,QAAQ,QAAQ,CAAA,CAAA;AAAA,KACzC,CAAA;AAEM,IAAA,MAAA,UAAA,GAAa,CAAC,GAAa,KAAA;AACzB,MAAA,KAAA,CAAA,mBAAA,EAAqB,GAAK,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAC5C,MAAA,IAAI,MAAM,MAAO,CAAA,MAAA,IAAU,OAAO,KAAM,CAAA,MAAA,CAAO,WAAW,UAAY,EAAA;AACpE,QAAA,aAAA,CAAc,KAAM,CAAA,MAAA,CAAO,MAAQ,EAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AAAA,OACnD;AAAA,KACF,CAAA;AAGE,IAAAC,SAAA,CAAA,MAAM,KAAM,CAAA,UAAA,EACZ,MAAM;AACJ,MAAA,IAAI,WAAY,CAAA,KAAA,CAAM,QAAS,CAAA,QAAQ,CAAG,EAAA;AAC5B,QAAA,WAAA,CAAA,KAAA,EAAO,QAAS,CAAA,QAAA,EAAU,MAAM;AAAA,SAAE,CAAA,CAAA;AAAA,OAChD;AAAA,KAEJ,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}