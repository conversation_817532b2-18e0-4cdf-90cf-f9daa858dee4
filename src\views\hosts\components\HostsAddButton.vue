<template>
  <div class="add-button cursor">
    <el-icon class="icons"><CirclePlusFilled /></el-icon>
    <span>{{ $t('replenish.addNewHost') }}</span>
  </div>
</template>
<script lang="ts" setup>
import { CirclePlusFilled } from '@element-plus/icons-vue'
</script>
<style lang="scss" scoped>
.add-button {
  height: 40px;
  border: 1px solid var(--ops-border-color);
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  color: var(--ops-primary-color);
  background-color: rgba(71, 119, 255, 0.06);
}
.icons {
  position: relative;
  top: 3px;
  left: -2px;
  font-size: 16px;
}
</style>
