export function useHostListConfig() {
  const { t, $has, store } = useBasicTool()
  // 搜索配置项
  const searchItemData = reactive<Array<BaseSearch.SearchItemData>>([
    {
      label: computed(() => t('form.state') + '：'),
      type: 'select',
      selectOptions: [
        { name: t('state.online'), value: 1 },
        { name: t('state.offline'), value: 0 }
      ],
      param: 'onlineState',
      defaultValue: '',
      placeholder: computed(() => t('form.pleaseSelectAStatus'))
    },
    {
      label: computed(() => t('form.belongingCluster') + '：'),
      type: 'select',
      selectOptions: [],
      param: 'namespaceId',
      defaultValue: '',
      placeholder: computed(() => t('form.selectTheOwningCluster'))
    },
    {
      label: t('replenish.tenant') + '：',
      type: 'select',
      selectOptions: [],
      param: 'requestTenantId',
      defaultValue: '',
      placeholder: computed(() => t('form.pleaseChoose'))
    },
    {
      label: t('replenish.rack') + '：',
      type: 'input',
      param: 'rackNumber',
      defaultValue: '',
      placeholder: computed(() => t('form.pleaseEnter'))
    },
    {
      label: 'IP：',
      type: 'input',
      param: 'ip',
      defaultValue: '',
      placeholder: computed(() => t('form.pleaseEnterIPAddress'))
    },
    {
      label: t('replenish.hostAlias') + '：',
      type: 'input',
      param: 'alias',
      defaultValue: '',
      placeholder: computed(() => t('form.pleaseEnter'))
    }
  ])
  const language = computed(() => store.state.app.language)
  // 列表配置项
  const tableData = reactive<any>({
    column: [
      { prop: 'onlineState', label: computed(() => t('table.state')), slot: true, width: 110 },
      { prop: 'name', label: computed(() => t('table.hostName')), 'min-width': 200, slot: true },
      { prop: 'alias', label: t('replenish.hostAlias'), 'min-width': 200, slot: true },
      { prop: 'namespaceName', label: computed(() => t('table.belongingCluster')), 'min-width': 200 },
      { prop: 'ip', label: 'IP', 'min-width': 120 },
      { prop: 'rackNumber', label: t('replenish.rack'), slot: true, 'min-width': 200 },
      { prop: 'tenantName', label: t('replenish.tenantBelonging') },
      { prop: 'rolesNum', label: t('replenish.role'), slot: true },
      { prop: 'cpuTotal', label: 'CPU', slot: true, width: 140 },
      { prop: 'memoryTotal', label: computed(() => t('table.memory')), slot: true, width: 140 },
      { prop: 'diskTotal', label: t('replenish.disk'), slot: true, width: 140 },
      {
        prop: 'maintenanceStatus',
        label: t('replenish.maintenanceMode'),
        slot: true,
        slotHeader: true,
        'min-width': language.value === 'zh - CN' ? 100 : 190,
        vIf: $has('host-maintenance')
      },
      { prop: 'manageState', label: computed(() => t('table.operate')), slot: true, fixed: 'right', width: 140 },
      { prop: 'addState', label: t('replenish.addStatus'), slot: true, fixed: 'right', 'min-width': language.value === 'zh-CN' ? 100 : 160 }
    ],
    data: []
  })
  const gatewayTableData = reactive<any>({
    column: [
      { prop: 'onlineState', label: computed(() => t('table.state')), slot: true, width: 100 },
      { prop: 'name', label: computed(() => t('table.hostName')), 'min-width': 200, slot: true },
      { prop: 'alias', label: t('replenish.hostAlias'), 'min-width': 200, slot: true },
      { prop: 'ip', label: 'IP', 'min-width': 120 },
      { prop: 'rackNumber', label: t('replenish.rack'), slot: true, 'min-width': 200 },
      { prop: 'tenantName', label: t('replenish.tenantBelonging') },
      // { prop: 'rolesNum', label: t('replenish.role'), slot: true },
      { prop: 'cpuTotal', label: 'CPU', slot: true, width: 140 },
      { prop: 'memoryTotal', label: computed(() => t('table.memory')), slot: true, width: 140 },
      { prop: 'diskTotal', label: t('replenish.disk'), slot: true, width: 140 },
      {
        prop: 'maintenanceStatus',
        label: t('replenish.maintenanceMode'),
        slot: true,
        slotHeader: true,
        width: language.value === 'zh-CN' ? 100 : 190,
        vIf: $has('host-maintenance')
      },

      { prop: 'addState', label: t('replenish.addStatus'), slot: true, fixed: 'right', 'min-width': language.value === 'zh-CN' ? 100 : 160 },
      {
        prop: 'manageState',
        label: computed(() => t('table.operate')),
        slot: true,
        fixed: 'right',
        'min-width': language.value === 'zh-CN' ? 200 : 260
      }
    ],
    data: []
  })
  return { searchItemData, tableData, gatewayTableData }
}
