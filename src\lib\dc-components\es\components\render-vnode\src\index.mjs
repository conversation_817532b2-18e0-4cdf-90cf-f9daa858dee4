import { defineComponent } from 'vue';

var RenderVNode = defineComponent({
  name: "DcRenderVNode",
  props: {
    vnode: {
      type: Function,
      default: () => null
    },
    scope: {
      type: Object,
      default: () => ({})
    },
    extraScope: {
      type: Object,
      default: () => ({})
    }
  },
  render() {
    return typeof this.$props.vnode === "function" ? this.$props.vnode(this.$props.scope, this.$props.extraScope) : null;
  }
});

export { RenderVNode as default };
//# sourceMappingURL=index.mjs.map
