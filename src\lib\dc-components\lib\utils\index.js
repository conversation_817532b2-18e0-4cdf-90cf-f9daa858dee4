'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./common/index.js');
var index$1 = require('./install/index.js');
require('./hooks/index.js');
var dialog = require('./hooks/dialog.js');
var drawer = require('./hooks/drawer.js');
var form = require('./hooks/form.js');



exports.fromPairs = index.fromPairs;
exports.hasOwn = index.hasOwn;
exports.isObject = index.isObject;
exports.withInstall = index$1.withInstall;
exports.useDialog = dialog.useDialog;
exports.useDrawer = drawer.useDrawer;
exports.useDependModel = form.useDependModel;
//# sourceMappingURL=index.js.map
