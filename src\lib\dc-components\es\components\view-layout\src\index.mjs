import { pushScopeId, popScopeId, defineComponent, useCssVars, ref, useSlots, computed, openBlock, createElementBlock, createElementVNode, unref, renderSlot, createBlock, withCtx, createVNode, createCommentVNode, createTextVNode, toDisplayString } from 'vue';
import { ElIcon } from 'element-plus';
import 'element-plus/es/components/icon/style/css';
import { ArrowLeftBold } from '@element-plus/icons-vue';
import { useElementSize } from './hooks.mjs';
import './index.vue_vue_type_style_index_0_scoped_true_lang.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const _withScopeId = (n) => (pushScopeId("data-v-27aa1df5"), n = n(), popScopeId(), n);
const _hoisted_1 = { class: "view-layout" };
const _hoisted_2 = { class: "view-header" };
const _hoisted_3 = { key: 0 };
const _hoisted_4 = {
  key: 1,
  class: "view-header--name"
};
const _hoisted_5 = { key: 2 };
const _hoisted_6 = {
  key: 0,
  class: "view-search"
};
const _hoisted_7 = {
  key: 1,
  class: "view-pagination"
};
const __default__ = defineComponent({
  name: "DcViewLayout"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    showBack: {
      type: Boolean,
      default: false
    },
    headerName: {
      type: String,
      default: ""
    }
  },
  setup(__props) {
    useCssVars((_ctx) => ({
      "27aa1df5-contentPadding": contentPadding.value
    }));
    const tableRef = ref();
    const { height } = useElementSize(tableRef);
    const slots = useSlots();
    console.log(slots, "======slots");
    const contentPadding = computed(() => {
      if (slots.pagination)
        return "60px";
      return "0px";
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createElementVNode("div", _hoisted_2, [
          unref(slots).headerLeft ? (openBlock(), createElementBlock("div", _hoisted_3, [
            renderSlot(_ctx.$slots, "headerLeft", {}, void 0, true)
          ])) : (openBlock(), createElementBlock("span", _hoisted_4, [
            __props.showBack ? (openBlock(), createBlock(unref(ElIcon), {
              key: 0,
              onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$router.back())
            }, {
              default: withCtx(() => [
                createVNode(unref(ArrowLeftBold))
              ]),
              _: 1
            })) : createCommentVNode("v-if", true),
            createTextVNode(" " + toDisplayString(__props.headerName), 1)
          ])),
          unref(slots).headerRight ? (openBlock(), createElementBlock("div", _hoisted_5, [
            renderSlot(_ctx.$slots, "headerRight", {}, void 0, true)
          ])) : createCommentVNode("v-if", true)
        ]),
        unref(slots).search ? (openBlock(), createElementBlock("div", _hoisted_6, [
          renderSlot(_ctx.$slots, "search", {}, void 0, true)
        ])) : createCommentVNode("v-if", true),
        createElementVNode("div", {
          ref_key: "tableRef",
          ref: tableRef,
          class: "view-table"
        }, [
          renderSlot(_ctx.$slots, "table", { tableHeight: unref(height) }, void 0, true)
        ], 512),
        unref(slots).pagination ? (openBlock(), createElementBlock("div", _hoisted_7, [
          renderSlot(_ctx.$slots, "pagination", {}, void 0, true)
        ])) : createCommentVNode("v-if", true)
      ]);
    };
  }
});
var ViewLayout = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-27aa1df5"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\view-layout\\src\\index.vue"]]);

export { ViewLayout as default };
//# sourceMappingURL=index.mjs.map
