import { defineComponent, openBlock, createBlock, unref, mergeProps, createSlots, withCtx, renderSlot } from 'vue';
import { ElButton } from 'element-plus';
import 'element-plus/es/components/button/style/css';
import { debounce } from 'lodash';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcButton"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    debounce: {
      type: Boolean,
      default: false
    },
    debounceInterval: {
      type: Number,
      default: 300
    }
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const handleClick = (e) => {
      emits("click", e);
    };
    const onClick = props.debounce ? debounce(handleClick, props.debounceInterval) : handleClick;
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElButton), mergeProps(_ctx.$attrs, { onClick: unref(onClick) }), createSlots({
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default")
        ]),
        _: 2
      }, [
        _ctx.$slots.loading ? {
          name: "loading",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "loading")
          ]),
          key: "0"
        } : void 0,
        _ctx.$slots.icon ? {
          name: "icon",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "icon")
          ]),
          key: "1"
        } : void 0
      ]), 1040, ["onClick"]);
    };
  }
});
var DcButton = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\button\\src\\index.vue"]]);

export { DcButton as default };
//# sourceMappingURL=index.mjs.map
