{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/select/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Select from \"./src/index.vue\";\r\n\r\nexport const DcSelect = withInstall(Select);\r\n\r\nexport default DcSelect;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}