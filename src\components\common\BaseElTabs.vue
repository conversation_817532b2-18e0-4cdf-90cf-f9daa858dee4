<template>
  <div :class="`base-el-tabs ${props.activeBarTransAni ? '' : 'active-no-tran'}`">
    <el-tabs v-bind="$attrs">
      <slot />
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  activeBarTransAni: {
    type: Boolean,
    default: true
  }
})
</script>
<style lang="scss" scoped>
.base-el-tabs {
  :deep(.el-tabs.el-tabs--top) {
    border: none;
    .el-tabs__header {
      margin-bottom: 0px;
    }
  }
  &.active-no-tran {
    :deep(.el-tabs__active-bar) {
      transition: none;
    }
  }
  :deep(.el-tabs__nav-scroll) {
    height: 48px;
    padding: 0px 20px;
  }
  :deep(.el-tabs__nav-wrap.is-top::after) {
    height: 1px;
    background-color: var(--ops-border-color) !important;
  }
  :deep(.el-tabs__content) {
    padding: 0 20px;
  }
  :deep(.el-tabs__item) {
    height: 48px !important;
    padding: 0 16px;
    font-family: PingFangSC, PingFang SC;
    font-size: 16px !important;
  }
  :deep(.el-tabs__item.is-active) {
    font-weight: 600;
  }
}
</style>
