{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../../../packages/components/form/src/components/FormItem/constants.ts"], "sourcesContent": ["import {\r\n  ElAutocomplete,\r\n  ElCascader,\r\n  ElCascaderPanel,\r\n  ElColorPicker,\r\n  ElDatePicker,\r\n  ElInput,\r\n  ElInputNumber,\r\n  ElRate,\r\n  ElSelectV2,\r\n  ElSlider,\r\n  ElSwitch,\r\n  ElTimePicker,\r\n  ElTimeSelect,\r\n  ElTransfer,\r\n  ElUpload,\r\n} from \"element-plus\";\r\nimport DcCheckbox from \"../../../../checkbox/src/index.vue\";\r\nimport DcRadio from \"../../../../radio/src/index.vue\";\r\nimport DcSelect from \"../../../../select/src/index.vue\";\r\nimport { DcFormComponentEnum } from \"../../types\";\r\nimport DcFormCustom from \"../Custom/index\";\r\nimport DcFormTable from \"../Table/index.vue\";\r\nimport DcFormText from \"../Text/index\";\r\n\r\nexport const Component: any = {\r\n  [DcFormComponentEnum.INPUT]: ElInput,\r\n  [DcFormComponentEnum.TEXT]: DcFormText,\r\n  [DcFormComponentEnum.SELECT]: DcSelect,\r\n  [DcFormComponentEnum.RADIO]: DcRadio,\r\n  [DcFormComponentEnum.AUTOCOMPLETE]: ElAutocomplete,\r\n  [DcFormComponentEnum.CHECKBOX]: DcCheckbox,\r\n  [DcFormComponentEnum.CASCADER]: ElCascader,\r\n  [DcFormComponentEnum.COLORPICKER]: ElColorPicker,\r\n  [DcFormComponentEnum.CASCADERPANEL]: ElCascaderPanel,\r\n  [DcFormComponentEnum.DATEPICKER]: ElDatePicker,\r\n  [DcFormComponentEnum.INPUTNUMBER]: ElInputNumber,\r\n  [DcFormComponentEnum.RATE]: ElRate,\r\n  [DcFormComponentEnum.SELECTV2]: ElSelectV2,\r\n  [DcFormComponentEnum.SLIDER]: ElSlider,\r\n  [DcFormComponentEnum.SWITCH]: ElSwitch,\r\n  [DcFormComponentEnum.TIMEPICKER]: ElTimePicker,\r\n  [DcFormComponentEnum.TIMESELECT]: ElTimeSelect,\r\n  [DcFormComponentEnum.TRANSFER]: ElTransfer,\r\n  [DcFormComponentEnum.UPLOAD]: ElUpload,\r\n  [DcFormComponentEnum.CUSTOM]: DcFormCustom,\r\n  [DcFormComponentEnum.TABLE]: DcFormTable,\r\n};\r\n"], "names": ["DcSelect", "DcRadio"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAuBY,MAAC,SAAS,GAAG;AACzB,EAAC,CAAA,mBAAA,CAAA,KAAA,GAAA,OAAA;AACD,EAAE,CAAC,mBAAmB,CAAC,IAAI,GAAG,UAAE;AAChC,EAAE,CAAC,mBAAmB,CAAC,MAAM,GAAGA,MAAQ;AACxC,EAAE,CAAC,mBAAmB,CAAC,KAAK,GAAGC,KAAO;AACtC,EAAE,CAAC,mBAAmB,CAAC,YAAY,GAAG,cAAI;AAC1C,EAAE,CAAC,mBAAmB,CAAC,QAAQ,GAAG,UAAM;AACxC,EAAE,CAAC,mBAAmB,CAAC,QAAQ,GAAG,UAAU;AAC5C,EAAE,CAAC,mBAAmB,CAAC,WAAW,GAAG,aAAS;AAC9C,EAAE,CAAC,mBAAmB,CAAC,aAAa,GAAG,eAAO;AAC9C,EAAE,CAAC,mBAAmB,CAAC,UAAU,GAAG,YAAY;AAChD,EAAE,CAAC,mBAAmB,CAAC,WAAW,GAAG,aAAa;AAClD,EAAE,CAAC,mBAAmB,CAAC,IAAI,GAAG,MAAM;AACpC,EAAE,CAAC,mBAAmB,CAAC,QAAQ,GAAG,UAAU;AAC5C,EAAE,CAAC,mBAAmB,CAAC,MAAM,GAAG,QAAM;AACtC,EAAE,CAAC,mBAAmB,CAAC,MAAM,GAAG,QAAQ;AACxC,EAAE,CAAC,mBAAmB,CAAC,UAAU,GAAG,YAAM;AAC1C,EAAE,CAAC,mBAAmB,CAAC,UAAU,GAAG,YAAM;AAC1C,EAAE,CAAC,mBAAmB,CAAC,QAAQ,GAAG,UAAU;AAC5C,EAAE,CAAC,mBAAmB,CAAC,MAAM,GAAG,QAAQ;AACxC,EAAE,CAAC,mBAAmB,CAAC,MAAM,GAAG,YAAY;AAC5C,EAAE,CAAC,mBAAmB,CAAC,KAAK,GAAG,WAAW;AAC1C;;;;"}