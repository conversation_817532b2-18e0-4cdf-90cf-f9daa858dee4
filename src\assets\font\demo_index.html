<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2832950" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe810;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe810;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe77a;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe77a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76c;</span>
                <div class="name">icon-kfckfc</div>
                <div class="code-name">&amp;#xe76c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76d;</span>
                <div class="name">icon-kfckfc</div>
                <div class="code-name">&amp;#xe76d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe769;</span>
                <div class="name">中文</div>
                <div class="code-name">&amp;#xe769;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c8;</span>
                <div class="name">英文</div>
                <div class="code-name">&amp;#xe7c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75c;</span>
                <div class="name">已选</div>
                <div class="code-name">&amp;#xe75c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75d;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe75d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75e;</span>
                <div class="name">icon_提示</div>
                <div class="code-name">&amp;#xe75e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75a;</span>
                <div class="name">icon绑定</div>
                <div class="code-name">&amp;#xe75a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75b;</span>
                <div class="name">icon解绑</div>
                <div class="code-name">&amp;#xe75b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe740;</span>
                <div class="name">监控-组件监控</div>
                <div class="code-name">&amp;#xe740;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe741;</span>
                <div class="name">监控-主机监控（高亮）</div>
                <div class="code-name">&amp;#xe741;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe742;</span>
                <div class="name">监控-组件监控（高亮）</div>
                <div class="code-name">&amp;#xe742;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe743;</span>
                <div class="name">监控-主机监控</div>
                <div class="code-name">&amp;#xe743;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe744;</span>
                <div class="name">监控-集群监控（高亮）</div>
                <div class="code-name">&amp;#xe744;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe745;</span>
                <div class="name">监控-集群监控</div>
                <div class="code-name">&amp;#xe745;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe727;</span>
                <div class="name">方形选中-fill</div>
                <div class="code-name">&amp;#xe727;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe725;</span>
                <div class="name">侧导航-主机列表（选中）选中</div>
                <div class="code-name">&amp;#xe725;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe726;</span>
                <div class="name">侧导航-主机列表（未选）.svg未选</div>
                <div class="code-name">&amp;#xe726;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71b;</span>
                <div class="name">侧导航-集群概览（选中）</div>
                <div class="code-name">&amp;#xe71b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">侧导航-服务组件</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71d;</span>
                <div class="name">侧导航-服务组件（选中）</div>
                <div class="code-name">&amp;#xe71d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe720;</span>
                <div class="name">侧导航-集群概览</div>
                <div class="code-name">&amp;#xe720;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">配置文件-显示</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">配置文件-隐藏</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">日志-筛选</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71a;</span>
                <div class="name">集群概览-集群</div>
                <div class="code-name">&amp;#xe71a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">icon_更多</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e7;</span>
                <div class="name">icon_撤销</div>
                <div class="code-name">&amp;#xe6e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e2;</span>
                <div class="name">icon_复制</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e3;</span>
                <div class="name">icon_配置</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e4;</span>
                <div class="name">icon_web</div>
                <div class="code-name">&amp;#xe6e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e6;</span>
                <div class="name">icon_恢复默认值</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e0;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe6e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dd;</span>
                <div class="name">icon_集群监控</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6de;</span>
                <div class="name">icon_组件监控</div>
                <div class="code-name">&amp;#xe6de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6df;</span>
                <div class="name">icon_主机监控</div>
                <div class="code-name">&amp;#xe6df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dc;</span>
                <div class="name">icon_导航收起</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d2;</span>
                <div class="name">icon_权限</div>
                <div class="code-name">&amp;#xe6d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cd;</span>
                <div class="name">icon_停止</div>
                <div class="code-name">&amp;#xe6cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ce;</span>
                <div class="name">icon_查看日志</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cf;</span>
                <div class="name">icon_重启</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d0;</span>
                <div class="name">icon_删除</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d1;</span>
                <div class="name">icon_重命名</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">icon_重置密码</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d4;</span>
                <div class="name">icon_解冻</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d5;</span>
                <div class="name">icon_启动</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d7;</span>
                <div class="name">icon_查看</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d8;</span>
                <div class="name">icon_绑定K8s</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d9;</span>
                <div class="name">icon_冻结</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6da;</span>
                <div class="name">icon_上传</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6db;</span>
                <div class="name">icon_编辑</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c4;</span>
                <div class="name">icon_安装成功</div>
                <div class="code-name">&amp;#xe6c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c5;</span>
                <div class="name">icon_安装失败</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c1;</span>
                <div class="name">icon_返回（导航）</div>
                <div class="code-name">&amp;#xe6c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6be;</span>
                <div class="name">icon_关闭</div>
                <div class="code-name">&amp;#xe6be;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ac;</span>
                <div class="name">icon_失败</div>
                <div class="code-name">&amp;#xe6ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ad;</span>
                <div class="name">icon_警告o</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6af;</span>
                <div class="name">icon_成功</div>
                <div class="code-name">&amp;#xe6af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b0;</span>
                <div class="name">icon_进行中</div>
                <div class="code-name">&amp;#xe6b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b3;</span>
                <div class="name">icon_设置</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b5;</span>
                <div class="name">icon_下拉</div>
                <div class="code-name">&amp;#xe6b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b7;</span>
                <div class="name">icon_注意</div>
                <div class="code-name">&amp;#xe6b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b8;</span>
                <div class="name">icon_提示</div>
                <div class="code-name">&amp;#xe6b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ba;</span>
                <div class="name">icon_搜索</div>
                <div class="code-name">&amp;#xe6ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a5;</span>
                <div class="name">icon_登陆页_密码</div>
                <div class="code-name">&amp;#xe6a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a6;</span>
                <div class="name">icon_登陆页_导航_用户名</div>
                <div class="code-name">&amp;#xe6a6;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1686910300326') format('woff2'),
       url('iconfont.woff?t=1686910300326') format('woff'),
       url('iconfont.ttf?t=1686910300326') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_gaojingfuwu-xuanzhong"></span>
            <div class="name">
              icon-kfckfc
            </div>
            <div class="code-name">.icon-icon_gaojingfuwu-xuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_gaojingfuwu"></span>
            <div class="name">
              icon-kfckfc
            </div>
            <div class="code-name">.icon-icon_gaojingfuwu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhongwen1"></span>
            <div class="name">
              中文
            </div>
            <div class="code-name">.icon-zhongwen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingwen"></span>
            <div class="name">
              英文
            </div>
            <div class="code-name">.icon-yingwen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yixuan"></span>
            <div class="name">
              已选
            </div>
            <div class="code-name">.icon-yixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjia1"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icon-tianjia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_tishi1"></span>
            <div class="name">
              icon_提示
            </div>
            <div class="code-name">.icon-icon_tishi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-iconbangding"></span>
            <div class="name">
              icon绑定
            </div>
            <div class="code-name">.icon-iconbangding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-iconjiebang"></span>
            <div class="name">
              icon解绑
            </div>
            <div class="code-name">.icon-iconjiebang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiankong-zujianjiankong"></span>
            <div class="name">
              监控-组件监控
            </div>
            <div class="code-name">.icon-jiankong-zujianjiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-jiankong-zhujijiankonggaoliang"></span>
            <div class="name">
              监控-主机监控（高亮）
            </div>
            <div class="code-name">.icon-a-jiankong-zhujijiankonggaoliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-jiankong-zujianjiankonggaoliang"></span>
            <div class="name">
              监控-组件监控（高亮）
            </div>
            <div class="code-name">.icon-a-jiankong-zujianjiankonggaoliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiankong-zhujijiankong"></span>
            <div class="name">
              监控-主机监控
            </div>
            <div class="code-name">.icon-jiankong-zhujijiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-jiankong-jiqunjiankonggaoliang"></span>
            <div class="name">
              监控-集群监控（高亮）
            </div>
            <div class="code-name">.icon-a-jiankong-jiqunjiankonggaoliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiankong-jiqunjiankong"></span>
            <div class="name">
              监控-集群监控
            </div>
            <div class="code-name">.icon-jiankong-jiqunjiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fangxingxuanzhong-fill"></span>
            <div class="name">
              方形选中-fill
            </div>
            <div class="code-name">.icon-fangxingxuanzhong-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-cedaohang-zhujiliebiaoxuanzhongxuanzhong"></span>
            <div class="name">
              侧导航-主机列表（选中）选中
            </div>
            <div class="code-name">.icon-a-cedaohang-zhujiliebiaoxuanzhongxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-cedaohang-zhujiliebiaoweixuansvgweixuan"></span>
            <div class="name">
              侧导航-主机列表（未选）.svg未选
            </div>
            <div class="code-name">.icon-a-cedaohang-zhujiliebiaoweixuansvgweixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-cedaohang-jiqungailanxuanzhong"></span>
            <div class="name">
              侧导航-集群概览（选中）
            </div>
            <div class="code-name">.icon-a-cedaohang-jiqungailanxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cedaohang-fuwuzujian"></span>
            <div class="name">
              侧导航-服务组件
            </div>
            <div class="code-name">.icon-cedaohang-fuwuzujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-cedaohang-fuwuzujianxuanzhong"></span>
            <div class="name">
              侧导航-服务组件（选中）
            </div>
            <div class="code-name">.icon-a-cedaohang-fuwuzujianxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cedaohang-jiqungailan"></span>
            <div class="name">
              侧导航-集群概览
            </div>
            <div class="code-name">.icon-cedaohang-jiqungailan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peizhiwenjian-xianshi"></span>
            <div class="name">
              配置文件-显示
            </div>
            <div class="code-name">.icon-peizhiwenjian-xianshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peizhiwenjian-yincang"></span>
            <div class="name">
              配置文件-隐藏
            </div>
            <div class="code-name">.icon-peizhiwenjian-yincang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rizhi-shaixuan"></span>
            <div class="name">
              日志-筛选
            </div>
            <div class="code-name">.icon-rizhi-shaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiqungailan-jiqun"></span>
            <div class="name">
              集群概览-集群
            </div>
            <div class="code-name">.icon-jiqungailan-jiqun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_gengduo1"></span>
            <div class="name">
              icon_更多
            </div>
            <div class="code-name">.icon-icon_gengduo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_chexiao1"></span>
            <div class="name">
              icon_撤销
            </div>
            <div class="code-name">.icon-icon_chexiao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_fuzhi"></span>
            <div class="name">
              icon_复制
            </div>
            <div class="code-name">.icon-icon_fuzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_peizhi"></span>
            <div class="name">
              icon_配置
            </div>
            <div class="code-name">.icon-icon_peizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_web"></span>
            <div class="name">
              icon_web
            </div>
            <div class="code-name">.icon-icon_web
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_huifumorenzhi"></span>
            <div class="name">
              icon_恢复默认值
            </div>
            <div class="code-name">.icon-icon_huifumorenzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjia"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icon-tianjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_jiqunjiankong"></span>
            <div class="name">
              icon_集群监控
            </div>
            <div class="code-name">.icon-icon_jiqunjiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zujianjiankong"></span>
            <div class="name">
              icon_组件监控
            </div>
            <div class="code-name">.icon-icon_zujianjiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zhujijiankong"></span>
            <div class="name">
              icon_主机监控
            </div>
            <div class="code-name">.icon-icon_zhujijiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_daohangshouqi"></span>
            <div class="name">
              icon_导航收起
            </div>
            <div class="code-name">.icon-icon_daohangshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_quanxian"></span>
            <div class="name">
              icon_权限
            </div>
            <div class="code-name">.icon-icon_quanxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_tingzhi1"></span>
            <div class="name">
              icon_停止
            </div>
            <div class="code-name">.icon-icon_tingzhi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_chakanrizhi"></span>
            <div class="name">
              icon_查看日志
            </div>
            <div class="code-name">.icon-icon_chakanrizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zhongqi1"></span>
            <div class="name">
              icon_重启
            </div>
            <div class="code-name">.icon-icon_zhongqi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shanchu1"></span>
            <div class="name">
              icon_删除
            </div>
            <div class="code-name">.icon-icon_shanchu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zhongmingming1"></span>
            <div class="name">
              icon_重命名
            </div>
            <div class="code-name">.icon-icon_zhongmingming1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zhongzhimima1"></span>
            <div class="name">
              icon_重置密码
            </div>
            <div class="code-name">.icon-icon_zhongzhimima1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_jiedong1"></span>
            <div class="name">
              icon_解冻
            </div>
            <div class="code-name">.icon-icon_jiedong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_qidong1"></span>
            <div class="name">
              icon_启动
            </div>
            <div class="code-name">.icon-icon_qidong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_chakan"></span>
            <div class="name">
              icon_查看
            </div>
            <div class="code-name">.icon-icon_chakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_bangdingK8s"></span>
            <div class="name">
              icon_绑定K8s
            </div>
            <div class="code-name">.icon-icon_bangdingK8s
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_dongjie"></span>
            <div class="name">
              icon_冻结
            </div>
            <div class="code-name">.icon-icon_dongjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shangchuan"></span>
            <div class="name">
              icon_上传
            </div>
            <div class="code-name">.icon-icon_shangchuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_bianji1"></span>
            <div class="name">
              icon_编辑
            </div>
            <div class="code-name">.icon-icon_bianji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_anzhuangchenggong1"></span>
            <div class="name">
              icon_安装成功
            </div>
            <div class="code-name">.icon-icon_anzhuangchenggong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_anzhuangshibai1"></span>
            <div class="name">
              icon_安装失败
            </div>
            <div class="code-name">.icon-icon_anzhuangshibai1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-icon_fanhuidaohang"></span>
            <div class="name">
              icon_返回（导航）
            </div>
            <div class="code-name">.icon-a-icon_fanhuidaohang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_guanbi"></span>
            <div class="name">
              icon_关闭
            </div>
            <div class="code-name">.icon-icon_guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shibai"></span>
            <div class="name">
              icon_失败
            </div>
            <div class="code-name">.icon-icon_shibai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_jinggaoo"></span>
            <div class="name">
              icon_警告o
            </div>
            <div class="code-name">.icon-icon_jinggaoo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_chenggong"></span>
            <div class="name">
              icon_成功
            </div>
            <div class="code-name">.icon-icon_chenggong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_jinhangzhong"></span>
            <div class="name">
              icon_进行中
            </div>
            <div class="code-name">.icon-icon_jinhangzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_shezhi"></span>
            <div class="name">
              icon_设置
            </div>
            <div class="code-name">.icon-icon_shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_xiala"></span>
            <div class="name">
              icon_下拉
            </div>
            <div class="code-name">.icon-icon_xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_zhuyi"></span>
            <div class="name">
              icon_注意
            </div>
            <div class="code-name">.icon-icon_zhuyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_tishi"></span>
            <div class="name">
              icon_提示
            </div>
            <div class="code-name">.icon-icon_tishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_sousuo"></span>
            <div class="name">
              icon_搜索
            </div>
            <div class="code-name">.icon-icon_sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_dengluye_mima"></span>
            <div class="name">
              icon_登陆页_密码
            </div>
            <div class="code-name">.icon-icon_dengluye_mima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-icon_dengluye_daohang_yonghuming"></span>
            <div class="name">
              icon_登陆页_导航_用户名
            </div>
            <div class="code-name">.icon-icon_dengluye_daohang_yonghuming
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_gaojingfuwu-xuanzhong"></use>
                </svg>
                <div class="name">icon-kfckfc</div>
                <div class="code-name">#icon-icon_gaojingfuwu-xuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_gaojingfuwu"></use>
                </svg>
                <div class="name">icon-kfckfc</div>
                <div class="code-name">#icon-icon_gaojingfuwu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhongwen1"></use>
                </svg>
                <div class="name">中文</div>
                <div class="code-name">#icon-zhongwen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingwen"></use>
                </svg>
                <div class="name">英文</div>
                <div class="code-name">#icon-yingwen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yixuan"></use>
                </svg>
                <div class="name">已选</div>
                <div class="code-name">#icon-yixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjia1"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icon-tianjia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_tishi1"></use>
                </svg>
                <div class="name">icon_提示</div>
                <div class="code-name">#icon-icon_tishi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-iconbangding"></use>
                </svg>
                <div class="name">icon绑定</div>
                <div class="code-name">#icon-iconbangding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-iconjiebang"></use>
                </svg>
                <div class="name">icon解绑</div>
                <div class="code-name">#icon-iconjiebang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiankong-zujianjiankong"></use>
                </svg>
                <div class="name">监控-组件监控</div>
                <div class="code-name">#icon-jiankong-zujianjiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-jiankong-zhujijiankonggaoliang"></use>
                </svg>
                <div class="name">监控-主机监控（高亮）</div>
                <div class="code-name">#icon-a-jiankong-zhujijiankonggaoliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-jiankong-zujianjiankonggaoliang"></use>
                </svg>
                <div class="name">监控-组件监控（高亮）</div>
                <div class="code-name">#icon-a-jiankong-zujianjiankonggaoliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiankong-zhujijiankong"></use>
                </svg>
                <div class="name">监控-主机监控</div>
                <div class="code-name">#icon-jiankong-zhujijiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-jiankong-jiqunjiankonggaoliang"></use>
                </svg>
                <div class="name">监控-集群监控（高亮）</div>
                <div class="code-name">#icon-a-jiankong-jiqunjiankonggaoliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiankong-jiqunjiankong"></use>
                </svg>
                <div class="name">监控-集群监控</div>
                <div class="code-name">#icon-jiankong-jiqunjiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fangxingxuanzhong-fill"></use>
                </svg>
                <div class="name">方形选中-fill</div>
                <div class="code-name">#icon-fangxingxuanzhong-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-cedaohang-zhujiliebiaoxuanzhongxuanzhong"></use>
                </svg>
                <div class="name">侧导航-主机列表（选中）选中</div>
                <div class="code-name">#icon-a-cedaohang-zhujiliebiaoxuanzhongxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-cedaohang-zhujiliebiaoweixuansvgweixuan"></use>
                </svg>
                <div class="name">侧导航-主机列表（未选）.svg未选</div>
                <div class="code-name">#icon-a-cedaohang-zhujiliebiaoweixuansvgweixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-cedaohang-jiqungailanxuanzhong"></use>
                </svg>
                <div class="name">侧导航-集群概览（选中）</div>
                <div class="code-name">#icon-a-cedaohang-jiqungailanxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cedaohang-fuwuzujian"></use>
                </svg>
                <div class="name">侧导航-服务组件</div>
                <div class="code-name">#icon-cedaohang-fuwuzujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-cedaohang-fuwuzujianxuanzhong"></use>
                </svg>
                <div class="name">侧导航-服务组件（选中）</div>
                <div class="code-name">#icon-a-cedaohang-fuwuzujianxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cedaohang-jiqungailan"></use>
                </svg>
                <div class="name">侧导航-集群概览</div>
                <div class="code-name">#icon-cedaohang-jiqungailan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peizhiwenjian-xianshi"></use>
                </svg>
                <div class="name">配置文件-显示</div>
                <div class="code-name">#icon-peizhiwenjian-xianshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peizhiwenjian-yincang"></use>
                </svg>
                <div class="name">配置文件-隐藏</div>
                <div class="code-name">#icon-peizhiwenjian-yincang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rizhi-shaixuan"></use>
                </svg>
                <div class="name">日志-筛选</div>
                <div class="code-name">#icon-rizhi-shaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiqungailan-jiqun"></use>
                </svg>
                <div class="name">集群概览-集群</div>
                <div class="code-name">#icon-jiqungailan-jiqun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_gengduo1"></use>
                </svg>
                <div class="name">icon_更多</div>
                <div class="code-name">#icon-icon_gengduo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_chexiao1"></use>
                </svg>
                <div class="name">icon_撤销</div>
                <div class="code-name">#icon-icon_chexiao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_fuzhi"></use>
                </svg>
                <div class="name">icon_复制</div>
                <div class="code-name">#icon-icon_fuzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_peizhi"></use>
                </svg>
                <div class="name">icon_配置</div>
                <div class="code-name">#icon-icon_peizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_web"></use>
                </svg>
                <div class="name">icon_web</div>
                <div class="code-name">#icon-icon_web</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_huifumorenzhi"></use>
                </svg>
                <div class="name">icon_恢复默认值</div>
                <div class="code-name">#icon-icon_huifumorenzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjia"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icon-tianjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_jiqunjiankong"></use>
                </svg>
                <div class="name">icon_集群监控</div>
                <div class="code-name">#icon-icon_jiqunjiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zujianjiankong"></use>
                </svg>
                <div class="name">icon_组件监控</div>
                <div class="code-name">#icon-icon_zujianjiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zhujijiankong"></use>
                </svg>
                <div class="name">icon_主机监控</div>
                <div class="code-name">#icon-icon_zhujijiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_daohangshouqi"></use>
                </svg>
                <div class="name">icon_导航收起</div>
                <div class="code-name">#icon-icon_daohangshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_quanxian"></use>
                </svg>
                <div class="name">icon_权限</div>
                <div class="code-name">#icon-icon_quanxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_tingzhi1"></use>
                </svg>
                <div class="name">icon_停止</div>
                <div class="code-name">#icon-icon_tingzhi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_chakanrizhi"></use>
                </svg>
                <div class="name">icon_查看日志</div>
                <div class="code-name">#icon-icon_chakanrizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zhongqi1"></use>
                </svg>
                <div class="name">icon_重启</div>
                <div class="code-name">#icon-icon_zhongqi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shanchu1"></use>
                </svg>
                <div class="name">icon_删除</div>
                <div class="code-name">#icon-icon_shanchu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zhongmingming1"></use>
                </svg>
                <div class="name">icon_重命名</div>
                <div class="code-name">#icon-icon_zhongmingming1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zhongzhimima1"></use>
                </svg>
                <div class="name">icon_重置密码</div>
                <div class="code-name">#icon-icon_zhongzhimima1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_jiedong1"></use>
                </svg>
                <div class="name">icon_解冻</div>
                <div class="code-name">#icon-icon_jiedong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_qidong1"></use>
                </svg>
                <div class="name">icon_启动</div>
                <div class="code-name">#icon-icon_qidong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_chakan"></use>
                </svg>
                <div class="name">icon_查看</div>
                <div class="code-name">#icon-icon_chakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_bangdingK8s"></use>
                </svg>
                <div class="name">icon_绑定K8s</div>
                <div class="code-name">#icon-icon_bangdingK8s</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_dongjie"></use>
                </svg>
                <div class="name">icon_冻结</div>
                <div class="code-name">#icon-icon_dongjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shangchuan"></use>
                </svg>
                <div class="name">icon_上传</div>
                <div class="code-name">#icon-icon_shangchuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_bianji1"></use>
                </svg>
                <div class="name">icon_编辑</div>
                <div class="code-name">#icon-icon_bianji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_anzhuangchenggong1"></use>
                </svg>
                <div class="name">icon_安装成功</div>
                <div class="code-name">#icon-icon_anzhuangchenggong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_anzhuangshibai1"></use>
                </svg>
                <div class="name">icon_安装失败</div>
                <div class="code-name">#icon-icon_anzhuangshibai1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-icon_fanhuidaohang"></use>
                </svg>
                <div class="name">icon_返回（导航）</div>
                <div class="code-name">#icon-a-icon_fanhuidaohang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_guanbi"></use>
                </svg>
                <div class="name">icon_关闭</div>
                <div class="code-name">#icon-icon_guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shibai"></use>
                </svg>
                <div class="name">icon_失败</div>
                <div class="code-name">#icon-icon_shibai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_jinggaoo"></use>
                </svg>
                <div class="name">icon_警告o</div>
                <div class="code-name">#icon-icon_jinggaoo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_chenggong"></use>
                </svg>
                <div class="name">icon_成功</div>
                <div class="code-name">#icon-icon_chenggong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_jinhangzhong"></use>
                </svg>
                <div class="name">icon_进行中</div>
                <div class="code-name">#icon-icon_jinhangzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_shezhi"></use>
                </svg>
                <div class="name">icon_设置</div>
                <div class="code-name">#icon-icon_shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_xiala"></use>
                </svg>
                <div class="name">icon_下拉</div>
                <div class="code-name">#icon-icon_xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_zhuyi"></use>
                </svg>
                <div class="name">icon_注意</div>
                <div class="code-name">#icon-icon_zhuyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_tishi"></use>
                </svg>
                <div class="name">icon_提示</div>
                <div class="code-name">#icon-icon_tishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_sousuo"></use>
                </svg>
                <div class="name">icon_搜索</div>
                <div class="code-name">#icon-icon_sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_dengluye_mima"></use>
                </svg>
                <div class="name">icon_登陆页_密码</div>
                <div class="code-name">#icon-icon_dengluye_mima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-icon_dengluye_daohang_yonghuming"></use>
                </svg>
                <div class="name">icon_登陆页_导航_用户名</div>
                <div class="code-name">#icon-icon_dengluye_daohang_yonghuming</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
