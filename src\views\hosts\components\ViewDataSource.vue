<template>
  <el-drawer :modal="false" class="view-ds-drawer" :model-value="$props.visible" :title="$t('replenish.viewDataSource')" @close="onClose">
    <div class="view-ds">
      <div class="view-ds-title">{{ $t('replenish.hostName') }}: {{ props.hostInfo.name }}</div>
      <div class="choose-ds">
        <el-form ref="ruleFormRef" :model="configInfo" :rules="rules">
          <el-form-item prop="namespaceId" :label="$t('replenish.selectCluster') + ':'">
            <el-select :disabled="!isEdit" v-model="configInfo.namespaceId" @change="getClusterService($event)">
              <el-option v-for="c in clusterList" :key="c.id" :value="c.id" :label="c.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="components" :label="$t('replenish.selectDataSource') + ':'">
            <div class="choose-ds-components">
              <div class="choose-ds-component" v-for="c in componentList" :key="c.value">
                <div>
                  <el-checkbox
                    :disabled="!isEdit"
                    :modelValue="configInfo.components.includes(c.value)"
                    @change="checkComponent(c.value, c.dsList)"
                    >{{ c.label }}</el-checkbox
                  >
                </div>
                <div>
                  <el-radio-group :disabled="!isEdit" :modelValue="configInfo.dsList[c.value]" @change="checkDs(c.value, $event)">
                    <el-radio :disabled="!isEdit" v-for="d in c.dsList" :key="d.value" :label="d.value">{{ d.label }}</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <BaseButton v-if="!isEdit" type="primary" @click="edit"> {{ t('replenish.edit') }} </BaseButton>
      <BaseButton v-if="isEdit" type="info" @click="cancelEdit"> {{ t('replenish.cancel') }} </BaseButton>
      <BaseButton v-if="isEdit" :loading="loading" :disabled="loading" type="primary" @click="save"> {{ t('replenish.save') }} </BaseButton>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { handleCofirm } from '@/utils/The/TheConfirmDelete'
import { ElMessage } from 'element-plus'
import { PropType } from 'vue'
const { route, t, router, store, $has, setBreadList } = useBasicTool()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  hostInfo: {
    type: Object as PropType<{
      id: string
      name: string
    }>,
    default: () => ({
      id: '',
      name: ''
    })
  }
})

const emits = defineEmits(['close'])

const isEdit = ref(false)
const ruleFormRef = ref()
const loading = ref(false)
const configInfo = ref<{
  namespaceId: string
  components: string[]
  dsList: Record<string, string>
}>({
  namespaceId: '',
  components: [],
  dsList: {}
})

const rules = {
  namespaceId: [{ required: true, message: t('message.requiredSelect'), trigger: 'change' }],
  components: [
    {
      required: true,
      message: t('replenish.pleaseSelectDataSource'),
      type: 'array'
    }
  ]
}
const clusterList = ref<any[]>([])

const componentList = ref<any[]>([])

const getNamespaceList = () => {
  store.dispatch('namespace/namespaceGatewayList', { name: '', searchAudit: true }).then((res) => {
    clusterList.value = res.data
  })
}

const getClusterService = (id) => {
  configInfo.value.components = []
  configInfo.value.dsList = {}
  store.dispatch('cluster/getClusterServiceNode', { id }).then((res) => {
    componentList.value = Object.keys(res.data).map((item) => {
      return {
        value: item,
        label: item,
        dsList: res.data[item].map((s) => {
          return {
            value: s.clusterServiceId,
            label: s.clusterServiceName
          }
        })
      }
    })
  })
}

const checkComponent = (val: any, dsList: any) => {
  const index = configInfo.value.components.indexOf(val)
  if (index === -1) {
    configInfo.value.components.push(val)
    configInfo.value.dsList[val] = dsList[0].value
  } else {
    configInfo.value.components.splice(index, 1)
    configInfo.value.dsList[val] = ''
  }
}

const checkDs = (componentVal: any, val: any) => {
  const index = configInfo.value.components.indexOf(componentVal)
  if (index === -1) {
    configInfo.value.components.push(componentVal)
  }
  configInfo.value.dsList[componentVal] = val
}

function getConfig() {
  store.dispatch('hosts/hostClusterConfig', { id: props.hostInfo.id }).then((res) => {
    const { namespaceId, list } = res.data
    const list2 = list?.map((item) => item.clusterServiceId) || []
    store.dispatch('cluster/getClusterServiceNode', { id: namespaceId }).then((res2) => {
      const checkedComponents: string[] = []
      const dsList: Record<string, string> = {}
      componentList.value = Object.keys(res2.data).map((item) => {
        if (list2 && res2.data[item].some((c) => list2.includes(c.clusterServiceId))) {
          checkedComponents.push(item)
          dsList[item] = res2.data[item].filter((c) => list2.includes(c.clusterServiceId)).map((c) => c.clusterServiceId)[0] || ''
        }

        return {
          value: item,
          label: item,
          dsList: res2.data[item].map((s) => {
            return {
              value: s.clusterServiceId,
              label: s.clusterServiceName
            }
          })
        }
      })
      configInfo.value = {
        namespaceId,
        components: checkedComponents,
        dsList
      }
      nextTick(() => {
        ruleFormRef.value.clearValidate()
      })
    })
  })
}
function edit() {
  isEdit.value = true
}

function cancelEdit() {
  const configs = {
    message: t('replenish.confirmExitEditing'),
    desc: t('replenish.exitWithoutSaving'),
    showRemind: false,
    showInput: false
  }
  handleCofirm(configs).then(() => {
    getConfig()
    isEdit.value = false
  })
}

function save() {
  ruleFormRef.value.validate((isValid: boolean) => {
    if (!isValid) {
      return
    }
    const clusterIds: string[] = []
    Object.keys(configInfo.value.dsList).forEach((c) => {
      if (configInfo.value.dsList[c]) {
        clusterIds.push(configInfo.value.dsList[c])
      }
    })
    loading.value = true
    store
      .dispatch('hosts/updateHostClusterConfig', {
        clusterServiceIds: clusterIds,
        namespaceId: configInfo.value.namespaceId,
        hostId: props.hostInfo.id
      })
      .then((res) => {
        if (res.data) {
          ElMessage.success(t('replenish.saveSuccess'))
          isEdit.value = false
        } else {
          ElMessage.error(res.msg)
        }
      })
      .finally(() => {
        loading.value = false
      })
  })
}

const onClose = () => {
  emits('close')
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      getConfig()
      getNamespaceList()
    }
  }
)
</script>

<style lang="scss">
.view-ds {
  &-drawer {
    width: 600px !important;
    .el-drawer__header {
      padding: 16px 20px;
      margin: 0 !important;
    }
    .el-drawer__title {
      font-size: 16px;
      line-height: 22px;
      font-weight: 600;
      color: rgba(51, 57, 76, 1);
    }
    .el-drawer__footer {
      padding: 16px 20px;
    }
  }
  &-title {
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 600;
    color: rgba(33, 51, 68, 1);
  }
  .choose-ds {
    padding: 20px 20px 0;
    background: #fafbfc;
    .el-form-item {
      align-items: flex-start;
      width: 100%;
      .el-select {
        width: 260px;
        margin-right: 20px;
      }
    }
  }
}
</style>
