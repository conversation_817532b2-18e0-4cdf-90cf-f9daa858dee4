import { ref, onMounted, onUnmounted } from 'vue';

function useElementSize(target, options = {}) {
  const width = ref(options.initialWidth || 0);
  const height = ref(options.initialHeight || 0);
  let observer = null;
  let timeoutId = null;
  const updateSize = (entry) => {
    if (options.debounce) {
      if (timeoutId) {
        window.clearTimeout(timeoutId);
      }
      timeoutId = window.setTimeout(() => {
        width.value = entry.contentRect.width;
        height.value = entry.contentRect.height;
      }, options.debounce);
    } else {
      width.value = entry.contentRect.width;
      height.value = entry.contentRect.height;
    }
  };
  const observeElement = (element) => {
    observer = new ResizeObserver((entries) => {
      if (entries[0]) {
        updateSize(entries[0]);
      }
    });
    observer.observe(element);
  };
  onMounted(() => {
    const element = target instanceof HTMLElement ? target : target?.value;
    if (element) {
      width.value = element.clientWidth;
      height.value = element.clientHeight;
      observeElement(element);
    }
  });
  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
    }
    if (timeoutId) {
      window.clearTimeout(timeoutId);
    }
  });
  return {
    width,
    height
  };
}

export { useElementSize };
//# sourceMappingURL=hooks.mjs.map
