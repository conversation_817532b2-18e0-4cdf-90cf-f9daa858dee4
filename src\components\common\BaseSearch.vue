<template>
  <div class="base-search">
    <el-form :inline="true" :model="formInline" v-bind="$attrs">
      <!-- 插槽before -->
      <slot name="before" />
      <template v-for="(item, index) in searchItemData">
        <el-form-item v-if="!item.slotName" :key="index" :label="item.label">
          <el-select
            v-if="item.type == 'select'"
            v-model="formInline[item.param]"
            class="base-search__input"
            :placeholder="item.placeholder"
            clearable
            v-bind="item.props"
          >
            <el-option
              v-for="(optionItem, optionIndex) in item.selectOptions"
              :key="optionIndex"
              :label="optionItem.name"
              :value="optionItem.value"
              :disabled="optionItem?.disabled"
            />
          </el-select>
          <el-input
            v-if="item.type == 'input'"
            v-model="formInline[item.param]"
            class="base-search__input"
            :placeholder="item.placeholder"
            v-bind="item.props"
            @keyup.enter="onSubmit"
          />
          <el-date-picker
            v-if="item.type == 'datetime'"
            v-model="formInline[item.param]"
            type="datetimerange"
            range-separator="-"
            :start-placeholder="$t('replenish.startDate')"
            :end-placeholder="$t('replenish.endDate')"
            v-bind="item.props"
          />
        </el-form-item>
        <slot v-if="!item.slotName" :name="item.slotName" />
      </template>

      <!-- 插槽after -->
      <slot name="after" />
      <el-form-item class="search-button">
        <BaseButton type="primary" debounce @click="onSubmit">{{ submitButton ?? $t('button.inquire') }}</BaseButton>
        <BaseButton type="info" debounce @click="resetSearch">{{ resetButton ?? $t('button.reset') }}</BaseButton>
        <!-- 可用于显示其他按钮 -->
        <slot name="button" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
interface Props {
  searchItemData: Array<BaseSearch.SearchItemData>
  submitButton?: string // 提交按钮
  resetButton?: string // 重置按钮
}
const props = withDefaults(defineProps<Props>(), {
  searchItemData: () => [
    {
      label: '选择框',
      type: 'select',
      selectOptions: [
        { name: '', value: 111 },
        { name: '', value: 222 }
      ],
      param: 'company',
      defaultValue: 222,
      placeholder: '提示语'
    },
    {
      label: '输入框',
      type: 'input',
      param: 'name',
      defaultValue: '',
      placeholder: '提示语'
    }
  ]
})
// onSubmit提交事件  resetSearch重置事件
const emit = defineEmits(['onSubmit', 'resetSearch', 'selectDepart', 'deptOption'])
// 数据对象
const formInline = ref<any>({})
onMounted(() => {
  getInitialData()
})
watch(
  () => props.searchItemData,
  () => {
    getInitialData()
  },
  { deep: true }
)
// 获取初始数据
function getInitialData() {
  props.searchItemData.forEach((item: any) => {
    formInline.value[item.param] = item?.defaultValue ?? ''
  })
}
// 搜索
function onSubmit() {
  emit('onSubmit', formInline.value, true)
}
function submit() {
  emit('onSubmit', formInline.value, false)
}
// 重置
function resetSearch() {
  getInitialData()
  emit('resetSearch', formInline.value)
}
defineExpose({
  onSubmit,
  submit,
  resetSearch,
  formInline
})
</script>
<style lang="scss" scoped>
.base-search {
  :deep(.el-form--inline .el-form-item) {
    margin-right: 20px;
  }
  :deep(.el-form) {
    // height: 32px;
    margin-top: 18px;
  }
  .base-search__input {
    min-width: 240px;
  }
}
</style>
