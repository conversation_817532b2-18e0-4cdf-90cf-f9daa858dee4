export default [
    {
        url: '/api/ops/namespace/pageByName',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: {
                total: 1,
                records: [{
                    id: 9,
                    name: '集群001',
                    hostCount: 1,
                    componentCount: 2,
                    k8sCount: 3
                }]
            }
        })
    },
    {
        url: '/api/ops/hosts/page',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: {
                total: 1,
                records: [{
                    onlineState: '1',
                    name: 'host001',
                    namespaceName: 'namespace001',
                    ip: '************',
                }]
            }
        })
    },
]