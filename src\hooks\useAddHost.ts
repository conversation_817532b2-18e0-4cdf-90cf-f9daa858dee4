import type { FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
export interface HostAddForm {
  [key: string]: any
  sshPort: string
  sudoUser: string
  userPwd: string
  rackNumber: string
  ips: string
  loginType: number
}
export function useHostAddFrom() {
  const { t } = useI18n()
  const hostAddForm = reactive<HostAddForm>({
    sshPort: '22',
    sudoUser: '',
    userPwd: '',
    rackNumber: '',
    ips: '',
    loginType: 1,
    namespaceId: '',
    components: [],
    dsList: {}
  })

  const rules = reactive<FormRules>({
    namespaceId: [{ required: true, message: t('message.requiredSelect'), trigger: 'change' }],
    sshPort: [
      { required: true, message: t('message.requiredinput'), trigger: 'blur' },
      { pattern: /^([1-9]?|-?[1-9]\d*)$/, message: t('message.SSHPortNumber'), trigger: 'blur' }
    ],
    sudoUser: [{ required: true, message: t('message.requiredinput'), trigger: 'blur' }],
    userPwd: [{ required: true, message: t('message.requiredinput'), trigger: 'blur' }],
    loginType: [{ required: true, message: t('message.requiredinput'), trigger: 'blur' }],
    ips: [
      { required: true, message: t('message.requiredinput'), trigger: 'blur' },
      {
        validator: validatePass,
        trigger: 'blur'
      }
    ],
    components: [
      {
        required: true,
        message: t('replenish.pleaseSelectDataSource'),
        type: 'array'
      }
    ]
  })
  function validatePass(rule: any, value: any, callback: any) {
    const ipsRegex =
      /^([1-9]|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])(\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])){2}.\[(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])-(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\]$/
    const ipRegex = /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})((.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2}))){3}$/
    if (ipRegex.test(value) || ipsRegex.test(value)) {
      callback()
    } else {
      callback(new Error(t('replenish.pleaseEnterTheCorrectIPAddress')))
    }
  }
  return { hostAddForm, rules }
}
