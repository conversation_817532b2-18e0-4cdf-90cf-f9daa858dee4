<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monaco Editor Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .code {
            background: #f1f3f4;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <h1>Monaco Editor 调试信息</h1>
    
    <div class="debug-info error">
        <h3>🚨 错误分析：methodNames is not iterable</h3>
        <p>这个错误通常出现在以下情况：</p>
        <ul>
            <li><strong>Monaco Editor 版本兼容性问题</strong>：某些配置选项在不同版本中可能不支持</li>
            <li><strong>代理对象创建失败</strong>：Vue 3 的响应式系统与 Monaco Editor 的某些内部对象冲突</li>
            <li><strong>配置选项错误</strong>：传递了不正确的配置参数</li>
        </ul>
    </div>

    <div class="debug-info warning">
        <h3>⚠️ 已移除的配置选项</h3>
        <p>为了解决兼容性问题，我们已经移除了以下可能导致问题的配置：</p>
        <div class="code">
// 已注释掉的配置
// diffCodeLens: true,
// diffAlgorithm: 'advanced'
        </div>
    </div>

    <div class="debug-info success">
        <h3>✅ 修复措施</h3>
        <ol>
            <li><strong>添加错误处理</strong>：在 init() 函数中添加了 try-catch 块</li>
            <li><strong>Monaco Editor 检查</strong>：确保 Monaco Editor 已正确加载</li>
            <li><strong>简化配置</strong>：移除了可能导致兼容性问题的高级配置</li>
            <li><strong>初始化顺序</strong>：在 onBeforeMount 中调用 initEditor()</li>
        </ol>
    </div>

    <div class="debug-info">
        <h3>🔧 当前配置</h3>
        <div class="code">
diffEditor = monaco.editor.createDiffEditor(container, {
  enableSplitViewResizing: false,
  renderSideBySide: true,
  renderOverviewRuler: true,
  ignoreTrimWhitespace: false,
  minimap: { enabled: props.miniMap },
  lineNumbers: props.showLine ? 'on' : 'off',
  wordWrap: props.lineWrapping ? 'on' : 'off',
  folding: props.folding,
  lineDecorationsWidth: props.lineDecorationsWidth,
  readOnly: props.readOnly,
  automaticLayout: false,
  theme: 'vs'
})
        </div>
    </div>

    <div class="debug-info">
        <h3>🔍 调试步骤</h3>
        <ol>
            <li>打开浏览器开发者工具 (F12)</li>
            <li>切换到 Console 标签页</li>
            <li>刷新页面并查看是否还有 "methodNames is not iterable" 错误</li>
            <li>如果错误仍然存在，检查 Monaco Editor 的版本和导入方式</li>
            <li>确认 CodeDiffEditor 组件是否正确显示</li>
        </ol>
    </div>

    <div class="debug-info warning">
        <h3>🔄 如果问题仍然存在</h3>
        <p>可能需要考虑以下解决方案：</p>
        <ul>
            <li><strong>降级 Monaco Editor</strong>：使用更稳定的版本</li>
            <li><strong>使用 CDN 版本</strong>：避免打包工具的兼容性问题</li>
            <li><strong>延迟初始化</strong>：在组件完全挂载后再初始化编辑器</li>
            <li><strong>替代方案</strong>：考虑使用其他 diff 显示组件</li>
        </ul>
    </div>

    <script>
        // 检查 Monaco Editor 是否可用
        console.log('Monaco Editor 检查:');
        console.log('- window.monaco:', typeof window.monaco);
        console.log('- 当前页面 URL:', window.location.href);
        
        // 模拟检查
        setTimeout(() => {
            const status = document.createElement('div');
            status.className = 'debug-info';
            
            if (typeof window.monaco !== 'undefined') {
                status.className += ' success';
                status.innerHTML = '<h3>✅ Monaco Editor 已加载</h3><p>Monaco Editor 在当前环境中可用。</p>';
            } else {
                status.className += ' error';
                status.innerHTML = '<h3>❌ Monaco Editor 未加载</h3><p>Monaco Editor 在当前环境中不可用，这可能是导致错误的原因。</p>';
            }
            
            document.body.appendChild(status);
        }, 1000);
    </script>
</body>
</html>
