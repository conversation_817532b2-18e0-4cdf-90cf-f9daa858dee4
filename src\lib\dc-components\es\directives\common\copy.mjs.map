{"version": 3, "file": "copy.mjs", "sources": ["../../../../../packages/directives/common/copy.ts"], "sourcesContent": ["/**\r\n * v-copy\r\n * 复制某个值至剪贴板\r\n * 接收参数：string类型/Ref<string>类型/Reactive<string>类型\r\n */\r\nimport { ElMessage } from \"element-plus\";\r\nimport type { Directive, DirectiveBinding } from \"vue\";\r\n\r\ninterface DirectiveBindingValue {\r\n  text: string;\r\n  showTip?: boolean;\r\n  tips?: string;\r\n}\r\ninterface ElType extends HTMLElement {\r\n  copyValue: DirectiveBindingValue;\r\n  __handleClick__: any;\r\n}\r\n\r\nconst defaultCopyValue = {\r\n  showTip: true,\r\n  text: \"\",\r\n  tips: \"复制成功\",\r\n};\r\n\r\nconst copy: Directive = {\r\n  mounted(el: ElType, binding: DirectiveBinding<DirectiveBindingValue>) {\r\n    el.copyValue = { ...defaultCopyValue, ...binding.value };\r\n    el.addEventListener(\"click\", handleClick);\r\n  },\r\n  updated(el: ElType, binding: DirectiveBinding<DirectiveBindingValue>) {\r\n    el.copyValue = { ...defaultCopyValue, ...binding.value };\r\n  },\r\n  beforeUnmount(el: ElType) {\r\n    el.removeEventListener(\"click\", el.__handleClick__);\r\n  },\r\n};\r\n\r\nfunction handleClick(this: any) {\r\n  const input = document.createElement(\"input\");\r\n  input.value = this.copyValue.text.toLocaleString();\r\n  document.body.appendChild(input);\r\n  input.select();\r\n  document.execCommand(\"Copy\");\r\n  document.body.removeChild(input);\r\n  if (this.copyValue.showTip) {\r\n    ElMessage({\r\n      type: \"success\",\r\n      message: this.copyValue.tips,\r\n    });\r\n  }\r\n}\r\n\r\nexport default copy;\r\n"], "names": [], "mappings": ";;;AAEA,MAAM,gBAAO,GAAA;AACb,EAAE,OAAO,EAAE,IAAI;AACf,EAAE,IAAE,EAAA,EAAA;AACJ,EAAE,IAAI,EAAE,0BAA0B;;AAAQ,MAAA,IAAA,GAAA;AAC1C,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AACvB,IAAC,EAAA,CAAA,SAAA,GAAA,EAAA,GAAA,gBAAA,EAAA,GAAA,OAAA,CAAA,KAAA,EAAA,CAAA;AACD,IAAI,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAC,CAAA,CAAA;AAClC,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAA;AACrB,IAAI,EAAE,CAAC,SAAS,GAAC,EAAA,GAAA,gBAAA,EAAA,GAAA,OAAA,CAAA,KAAA,EAAA,CAAA;AACjB,GAAE;AACF,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAA,CAAA,eAAA,CAAA,CAAA;AACpC,GAAG;AACH,EAAE;AACF,SAAC,WAAA,GAAA;AACD,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAE,CAAA,OAAA,CAAA,CAAA;AAC3B,EAAE,KAAK,CAAC,KAAK,GAAG,IAAC,CAAA,SAAA,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA;AACjB,EAAE,QAAQ,CAAC,IAAC,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACZ,EAAE,KAAK,CAAC,MAAM,EAAE,CAAA;AAChB,EAAE,QAAC,CAAA,WAAA,CAAA,MAAA,CAAA,CAAA;AACH,EAAC,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACD,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,OAAK,EAAA;AAC1B,IAAI,SAAS,CAAC;AACd,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;AAClC,KAAK,CAAA,CAAA;AACL,GAAG;AACH;;;;"}