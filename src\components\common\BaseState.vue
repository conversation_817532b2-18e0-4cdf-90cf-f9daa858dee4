<template>
  <div v-if="stateCode === '1'">
    <span class="status-spot status-spot--on-line" />
    <span>{{ $t('state.online') }}</span>
  </div>
  <div v-if="stateCode === '0'">
    <span class="status-spot status-spot--off-line" />
    <span>{{ $t('state.offline') }}</span>
  </div>
  <div v-if="stateCode === '2'">
    <!-- is-loading 为element-plus 转动样式 -->
    <el-icon class="is-loading" style="position: relative; top: 2px; left: -3px">
      <Loading />
    </el-icon>
    <span>{{ $t('state.deleting') }}</span>
  </div>
  <div v-if="stateCode === '3'">
    <span class="status-spot status-spot--off-line" />
    <span>{{ $t('state.deletionFailed') }}</span>
  </div>
  <div v-if="stateCode === '4'">
    <span class="status-spot status-spot--off-line" />
    <span>添加失败</span>
  </div>
  <div v-if="stateCode === '5'">
    <el-icon class="is-loading" style="position: relative; top: 2px; left: -3px">
      <Loading />
    </el-icon>
    <span>添加中</span>
  </div>
  <!-- 后端返回状态类型不同 -->
  <div v-if="stateCode === 0" key="online">
    <span class="status-spot status-spot--on-line" />
    <span>{{ $t('state.normal') }}</span>
  </div>
  <div v-if="stateCode === 1" key="offline">
    <span class="status-spot status-spot--off-line" />
    <span>{{ $t('state.freeze') }}</span>
  </div>
</template>
<script lang="ts" setup>
import { Loading } from '@element-plus/icons-vue'
interface Props {
  stateCode: string | number
}
withDefaults(defineProps<Props>(), {
  stateCode: ''
})
</script>
<style lang="scss" scoped></style>
