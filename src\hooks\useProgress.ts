import { useI18n } from 'vue-i18n'

export function useProgress() {
  const { t } = useI18n()
  const store = useStore()
  const progressDialog = ref(false)
  const progressDialogTitle = ref('')
  const progressData = ref<any>()
  const itemState = ref<number>()
  const itemDataIndex = ref(0)
  let progressLoop: any
  const progressLoopLock = ref(false)
  const TitleMap = new Map([
    [1, t('replenish.deploymentProgressDetails')],
    [2, t('replenish.deploymentProgressDetails')],
    [10, t('replenish.startupProgressDetails')],
    [11, t('replenish.restartProgressDetails')],
    [12, t('replenish.stopProgressDetails')],
    [13, t('replenish.deleteProgressDetails')],
    [14, t('replenish.startupProgressDetails')],
    [15, t('replenish.restartProgressDetails')],
    [16, t('replenish.stopProgressDetails')],
    [17, t('replenish.deleteProgressDetails')],
    [18, t('replenish.scaleUpOrDownProgressDetails')],
    [19, t('replenish.scaleUpOrDownProgressDetails')],
    [20, t('replenish.schedulingProgressDetails')],
    [21, t('replenish.schedulingProgressDetails')],
    [22, t('replenish.updateProgressDetails')],
    [23, t('replenish.updateProgressDetails')],
    [26, t('replenish.rollingRestartProgressDetails')],
    [27, t('replenish.rollingRestartProgressDetails')]
  ])

  function openLoop(data: clusterApi.OperateProgress) {
    progressDialog.value = true
    operateProgress(data)
    progressLoop = setInterval(() => {
      if (progressLoopLock.value) return
      operateProgress(data)
    }, 1200)
  }

  function closeProgresDialog() {
    if (progressLoop) clearInterval(progressLoop)
  }
  function operateProgress(data: clusterApi.OperateProgress) {
    store.dispatch('cluster/operateProgress', { ...data }).then((res) => {
      progressData.value = res.data
      progressLoopLock.value = false
    })
  }
  function rerun(groupNumber: string | undefined, instanceId: stringOrNumber, clusterId: stringOrNumber) {
    return store.dispatch('cluster/clusterRestartRerun', { groupNumber, instanceId, clusterId })
  }
  return {
    progressDialog,
    progressDialogTitle,
    itemState,
    itemDataIndex,
    progressData,
    TitleMap,
    closeProgresDialog,
    operateProgress,
    openLoop,
    rerun
  }
}
