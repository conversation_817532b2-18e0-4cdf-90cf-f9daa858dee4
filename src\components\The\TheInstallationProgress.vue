<template>
  <div class="baseDialog">
    <el-dialog
      :model-value="modelValue"
      center
      :open-delay="1000"
      :close-delay="1500"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      @opened="handleOpened"
      @closed="handleClosed"
    >
      <el-progress class="progress" type="circle" :indeterminate="true" :percentage="percentage" :color="getProgressStateMap" :format="formatter" />
      <template #footer>
        <span class="dialog-footer">{{ DeployInformation.stepMsg }}</span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, reactive, computed, watch } from 'vue'
import { useStore } from 'vuex'
import Loop from '@/utils/Loop'
import { useI18n } from 'vue-i18n'
enum DeployState {
  LOADING = 'LOADING',
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL'
}

const { t } = useI18n()

function getProgressStateMap() {
  const progressStateMap = new Map().set('LOADING', 'var(--ops-primary-color)').set('SUCCESS', '#09BA76').set('FAIL', '#EE5E5E')
  return progressStateMap.get(DeployInformation.stete)
}

const store = useStore()
const loop = ref()
const lock = ref<boolean>(false)
const DeployInformation = reactive({
  allStepNum: 1,
  currentStepNum: 0,
  stepMsg: t('message.gettingInformation'),
  stete: DeployState.LOADING
})
const percentage = computed(() => (DeployInformation.currentStepNum / DeployInformation.allStepNum) * 100)

watch(
  () => DeployInformation.stete,
  (newValue, oldValue) => {
    emits('update:modelValue', false)
  }
)

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  serialNumber: {
    type: String,
    default: ''
  },
  dispatch: {
    type: String,
    default: ''
  },
  showPercentage: {
    required: false,
    type: Boolean,
    default: true
  }
})
const { dispatch, serialNumber, showPercentage } = toRefs(props)
const emits = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'close', value: any): void
}>()

function lookDeployProgress(dispatch: string, serialNumber: string) {
  if (!serialNumber) {
    return console.error('Lookdeployprogress is missing the required "serialNumber" parameter')
  }
  if (!dispatch) {
    return console.error('Lookdeployprogress is missing the required "dispatch" parameter')
  }
  loop.value = new Loop(() => {
    if (lock.value) return
    lock.value = true
    store
      .dispatch(dispatch, { serialNumber })
      .then((response) => {
        if (!response.data) return
        const { allStepNum, currentStepNum, stepMsg, stete } = response.data
        DeployInformation.allStepNum = allStepNum
        DeployInformation.currentStepNum = currentStepNum
        DeployInformation.stepMsg = stepMsg
        DeployInformation.stete = stete
      })
      .finally(() => {
        lock.value = false
      })
  }, 1200)
}

function handleOpened() {
  //
  // alert('Opened');
  lookDeployProgress(dispatch.value, serialNumber.value)
}

function handleClosed() {
  // alert('Closed');
  loop.value.clearLoop()
  emits('close', DeployInformation)
  resetDeployInformation()
}

function resetDeployInformation() {
  DeployInformation.stete = DeployState.LOADING
  DeployInformation.allStepNum = 1
  DeployInformation.currentStepNum = 0
  DeployInformation.stepMsg = t('message.gettingInformation')
}

function formatter(percentage: any) {
  if (showPercentage.value) {
    return percentage + '%'
  }
  return `${DeployInformation.currentStepNum} / ${DeployInformation.allStepNum}`
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
  top: 120px;
  width: 440px !important;
  height: 340px !important;
  .el-dialog__body {
    border: none !important;
    text-align: center;
  }
}
:deep(.el-progress) {
  animation-name: progress;
  animation-duration: 3s;
  animation-iteration-count: infinite;
}

@keyframes progress {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
</style>
