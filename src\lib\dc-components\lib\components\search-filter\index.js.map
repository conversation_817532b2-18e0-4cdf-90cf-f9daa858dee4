{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/search-filter/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport SearchFilter from \"./src/index.vue\";\r\n\r\nexport const DcSearchFilter = withInstall(SearchFilter);\r\n\r\nexport default DcSearchFilter;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "SearchFilter"], "mappings": ";;;;;;;;AAIa,MAAA,cAAA,GAAiBA,qBAAYC,gBAAY;;;;;"}