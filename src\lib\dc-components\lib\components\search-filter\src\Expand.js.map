{"version": 3, "file": "Expand.js", "sources": ["../../../../../../packages/components/search-filter/src/Expand.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"`${clsPrefix}`\" @click=\"toggle\">\r\n    <ElIcon>\r\n      <ArrowDown v-if=\"isExpand\" />\r\n      <ArrowUp v-else />\r\n    </ElIcon>\r\n    <span :class=\"`${clsPrefix}_text`\">\r\n      {{ isExpand ? props.expandText : props.collapseText }}\r\n    </span>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref } from \"vue\";\r\nimport { ElIcon } from \"element-plus\";\r\nimport { ArrowDown, ArrowUp } from \"@element-plus/icons-vue\";\r\n\r\nconst clsPrefix = \"dc-search-filter-expand\";\r\n\r\nconst props = defineProps({\r\n  expandText: {\r\n    type: String,\r\n    default: \"展开\",\r\n  },\r\n  collapseText: {\r\n    type: String,\r\n    default: \"收起\",\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"toggleExpand\"]);\r\n\r\nconst isExpand = ref(false);\r\n\r\nconst toggle = () => {\r\n  isExpand.value = !isExpand.value;\r\n  emits(\"toggleExpand\", isExpand.value);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dc-search-filter-expand {\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  color: var(--el-color-primary);\r\n\r\n  &_text {\r\n    margin-left: 5px;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["ref"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAWd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAER,IAAA,MAAA,QAAA,GAAWA,QAAI,KAAK,CAAA,CAAA;AAE1B,IAAA,MAAM,SAAS,MAAM;AACV,MAAA,QAAA,CAAA,KAAA,GAAQ,CAAC,QAAS,CAAA,KAAA,CAAA;AACrB,MAAA,KAAA,CAAA,cAAA,EAAgB,SAAS,KAAK,CAAA,CAAA;AAAA,KACtC,CAAA;;;;;;;;;;;;;;;;;;;;;;;"}