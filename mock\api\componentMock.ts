export default [
    {
        url: '/api/ops/component/list/all',
        method: 'GET',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: [{
                clusterName: '自定义名称',
                id: 66,
                name: '组件001',
                deployWay: 1,
                version: '1.3.6',
                state: 1,
                description: '组件说明'
            }]
        })
    },
    {
        url: '/api/ops/component/list/module/66/2',
        method: 'GET',
        response: (options) => {
            console.log(options);
            return ({
                code: 0,
                msg: 'ok',
                data: [{
                    clusterName:'自定义名称',
                    id: 67,
                    name: '组件001',
                    deployWay: 1,
                    version: '1.3.6',
                    state: 1,
                    description: '组件说明'
                }]
            })
        }
    },
    {
        url: '/api/ops/component/list/module/role/67',
        method: 'GET',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: [{
                clusterName: '自定义名称',
                id: 12,
                roleName: 'VNODE',
                maxNumber: 3,
                minNumber: 1,
                oddEvenType: 1
            }]
        })
    },
    {
        url: '/api/ops/component/list/kubernetes/9',
        method: 'GET',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: [{
                id: 68,
                k8sName: '集群002',
            }]
        })
    },
]