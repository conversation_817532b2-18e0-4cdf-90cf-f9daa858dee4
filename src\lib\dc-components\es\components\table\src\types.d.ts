import type { VNode } from "vue";
import type DcTable from "./index.vue";
declare type Filters = {
    text: string;
    value: string;
}[];
export interface DcTableColumnItem {
    type?: string;
    label?: string;
    className?: string;
    labelClassName?: string;
    prop: string;
    property?: string;
    width?: string | number;
    minWidth?: string | number;
    renderHeader?: (data: {
        column: DcTableColumnItem;
        $index: number;
    }) => VNode;
    sortable?: boolean | string;
    sortMethod?: (a: any, b: any) => number;
    sortBy?: string | ((row: any, index: number) => string) | string[];
    resizable?: boolean;
    columnKey?: string;
    align?: string;
    headerAlign?: string;
    showOverflowTooltip?: boolean | Record<string, any>;
    fixed?: boolean | string;
    formatter?: (row: any, column: DcTableColumnItem, cellValue: any, index: number) => VNode | string;
    selectable?: (row: any, index: number) => boolean;
    reserveSelection?: boolean;
    filterMethod?: (value: any, row: any, column: DcTableColumnItem) => void;
    filteredValue?: string[];
    filters?: Filters;
    filterPlacement?: string;
    filterMultiple?: boolean;
    index?: number | ((index: number) => number);
    sortOrders?: ("ascending" | "descending" | null)[];
    slot?: boolean;
    columnRequired?: boolean;
    columnDefault?: boolean;
}
export declare type DcTableInstance = InstanceType<typeof DcTable>;
export {};
