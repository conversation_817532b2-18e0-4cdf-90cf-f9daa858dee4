{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/view-layout/index.ts"], "sourcesContent": ["/*\r\n * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>\r\n * @Date: 2025-02-28 15:17:46\r\n * @LastEditTime: 2025-02-28 15:17:49\r\n * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>\r\n * @Description:\r\n */\r\nimport { withInstall } from \"@dc-components/utils\";\r\n\r\nimport ViewLayout from \"./src/index.vue\";\r\n\r\nexport const DcViewLayout = withInstall(ViewLayout);\r\n\r\nexport default DcViewLayout;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "ViewLayout"], "mappings": ";;;;;;;;AAWa,MAAA,YAAA,GAAeA,qBAAYC,gBAAU;;;;;"}