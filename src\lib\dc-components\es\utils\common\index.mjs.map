{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/utils/common/index.ts"], "sourcesContent": ["export const isObject = (val: unknown): val is Record<any, any> =>\r\n  val !== null && typeof val === \"object\";\r\n\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nexport const hasOwn = (\r\n  val: object,\r\n  key: string | symbol\r\n): key is keyof typeof val => hasOwnProperty.call(val, key);\r\n\r\nexport const fromPairs = (pairs: any[]) => {\r\n  const result: Record<string, any> = {};\r\n  if (pairs == null) {\r\n    return result;\r\n  }\r\n  for (const pair of pairs) {\r\n    result[pair[0]] = pair[1];\r\n  }\r\n  return result;\r\n};\r\n"], "names": [], "mappings": "AAAY,MAAC,QAAQ,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,SAAS;AACzE,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC3C,MAAC,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE;AACtD,MAAC,SAAS,GAAG,CAAC,KAAK,KAAK;AACpC,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AACrB,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC5B,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}