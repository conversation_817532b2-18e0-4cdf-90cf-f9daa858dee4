import jiankongac from '../../assets/icons/icon-jiankong-ac.svg'
import jiankong from '../../assets/icons/icon-jiankong.svg'
import jiqunac from '../../assets/icons/icon-jiqun-ac.svg'
import jiqun from '../../assets/icons/icon-jiqun.svg'
import qianyibeifenac from '../../assets/icons/icon-qianyibeifen-ac.svg'
import qianyibeifen from '../../assets/icons/icon-qianyibeifen.svg'
import rizhiac from '../../assets/icons/icon-rizhi-ac.svg'
import rizhi from '../../assets/icons/icon-rizhi.svg'
import shezhiac from '../../assets/icons/icon-shezhi-ac.svg'
import shezhi from '../../assets/icons/icon-shezhi.svg'
import shouqi from '../../assets/icons/icon-shouqi.svg'
import userac from '../../assets/icons/icon-user-ac.svg'
import user from '../../assets/icons/icon-user.svg'
import xunjianac from '../../assets/icons/icon-xujian-ac.svg'
import xunjian from '../../assets/icons/icon-xujian.svg'
import zhankai from '../../assets/icons/icon-zhankai.svg'
import zhujiac from '../../assets/icons/icon-zhuji-ac.svg'
import zhuji from '../../assets/icons/icon-zhuji.svg'
import zujianac from '../../assets/icons/icon-zujian-ac.svg'
import zujian from '../../assets/icons/icon-zujian.svg'
import document from '../../assets/icons/icon_document.svg'
import documentac from '../../assets/icons/icon_document_activate.svg'
export function useMeunImportIcon() {
  const { router, route } = useBasicTool()
  const meunIconMap = new Map()
    .set('jiqunac', jiqunac)
    .set('jiqun', jiqun)
    .set('zhujiac', zhujiac)
    .set('zhuji', zhuji)
    .set('jiankongac', jiankongac)
    .set('jiankong', jiankong)
    .set('zujianac', zujianac)
    .set('zujian', zujian)
    .set('rizhiac', rizhiac)
    .set('rizhi', rizhi)
    .set('qianyibeifenac', qianyibeifenac)
    .set('qianyibeifen', qianyibeifen)
    .set('zhankai', zhankai)
    .set('shouqi', shouqi)
    .set('shezhi', shezhi)
    .set('shezhiac', shezhiac)
    .set('shezhi', shezhi)
    .set('shezhiac', shezhiac)
    .set('xunjian', xunjian)
    .set('xunjianac', xunjianac)
    .set('document', document)
    .set('documentac', documentac)
    .set('user', user)
    .set('userac', userac)
  const routeRootPath = computed(() => `/${router.currentRoute.value.path.split('/')[1]}`)
  function menuIconSrc(path: string[], acIconName: string, iconName: string) {
    return path.includes(routeRootPath.value) ? meunIconMap.get(acIconName) : meunIconMap.get(iconName)
  }
  const isNamespace = computed(() => routeRootPath.value === '/namespace' && route.query?.namespaceId)
  return { meunIconMap, routeRootPath, menuIconSrc, isNamespace }
}
