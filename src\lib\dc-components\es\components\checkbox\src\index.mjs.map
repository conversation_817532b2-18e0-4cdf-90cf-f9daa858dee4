{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/checkbox/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-checkbox-group\r\n    v-bind=\"{ ...$attrs }\"\r\n    :model-value=\"props.modelValue\"\r\n    @change=\"handleChange\"\r\n  >\r\n    <el-checkbox\r\n      v-for=\"(item, index) in options\"\r\n      :key=\"index\"\r\n      :label=\"item.value\"\r\n    >\r\n      {{ item.label }}\r\n    </el-checkbox>\r\n  </el-checkbox-group>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, isRef, useAttrs } from \"vue\";\r\nimport { ElCheckbox, ElCheckboxGroup } from \"element-plus\";\r\nimport type { CheckboxValueType } from \"element-plus\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcCheckboxOptions } from \"./types\";\r\n\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: Array as PropType<string[] | number[]>,\r\n  },\r\n  options: {\r\n    type: [Array, Object] as PropType<DcCheckboxOptions>,\r\n    default: () => [],\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\"]);\r\nconst attrs = useAttrs();\r\n\r\nconst options = computed(() => {\r\n  return isRef(props.options) ? props.options.value : props.options;\r\n});\r\n\r\nconst handleChange = (val: CheckboxValueType[]) => {\r\n  emits(\"update:modelValue\", val);\r\n  if (typeof attrs.onChange === \"function\") {\r\n    attrs.onChange(val);\r\n  }\r\n};\r\n\r\ndefineOptions({\r\n  name: \"DcCheckbox\",\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;AA+Cc,EAAA,IAAA,EAAA,YAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;AA1BA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAUd,IAAA,MAAM,KAAQ,GAAA,QAAA,EAAA,CAAA;AACd,IAAA,MAAM,UAAiB,QAAA,CAAA,MAAA;AAEvB,MAAM,OAAA,KAAA,CAAA,aAAyB,CAAA,GAAA,KAAA,CAAA,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA;AAC7B,KAAA,CAAA,CAAA;AAA0D,IAC5D,MAAC,YAAA,GAAA,CAAA,GAAA,KAAA;AAED,MAAM,KAAA,CAAA,mBAA6C,EAAA,GAAA,CAAA,CAAA;AACjD,MAAA,IAAA,0BAA8B,UAAA,EAAA;AAC9B,QAAI,KAAA,CAAA,QAAa,CAAA,GAAA,CAAA,CAAA;AACf,OAAA;AAAkB,KACpB,CAAA;AAAA,IACF,CAAA;AAIE,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;"}