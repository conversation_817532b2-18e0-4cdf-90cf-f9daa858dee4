{"version": 3, "file": "constants.js", "sources": ["../../../../../../../../packages/components/form/src/components/FormItem/constants.ts"], "sourcesContent": ["import {\r\n  ElAutocomplete,\r\n  ElCascader,\r\n  ElCascaderPanel,\r\n  ElColorPicker,\r\n  ElDatePicker,\r\n  ElInput,\r\n  ElInputNumber,\r\n  ElRate,\r\n  ElSelectV2,\r\n  ElSlider,\r\n  ElSwitch,\r\n  ElTimePicker,\r\n  ElTimeSelect,\r\n  ElTransfer,\r\n  ElUpload,\r\n} from \"element-plus\";\r\nimport DcCheckbox from \"../../../../checkbox/src/index.vue\";\r\nimport DcRadio from \"../../../../radio/src/index.vue\";\r\nimport DcSelect from \"../../../../select/src/index.vue\";\r\nimport { DcFormComponentEnum } from \"../../types\";\r\nimport DcFormCustom from \"../Custom/index\";\r\nimport DcFormTable from \"../Table/index.vue\";\r\nimport DcFormText from \"../Text/index\";\r\n\r\nexport const Component: any = {\r\n  [DcFormComponentEnum.INPUT]: ElInput,\r\n  [DcFormComponentEnum.TEXT]: DcFormText,\r\n  [DcFormComponentEnum.SELECT]: DcSelect,\r\n  [DcFormComponentEnum.RADIO]: DcRadio,\r\n  [DcFormComponentEnum.AUTOCOMPLETE]: ElAutocomplete,\r\n  [DcFormComponentEnum.CHECKBOX]: DcCheckbox,\r\n  [DcFormComponentEnum.CASCADER]: ElCascader,\r\n  [DcFormComponentEnum.COLORPICKER]: ElColorPicker,\r\n  [DcFormComponentEnum.CASCADERPANEL]: ElCascaderPanel,\r\n  [DcFormComponentEnum.DATEPICKER]: ElDatePicker,\r\n  [DcFormComponentEnum.INPUTNUMBER]: ElInputNumber,\r\n  [DcFormComponentEnum.RATE]: ElRate,\r\n  [DcFormComponentEnum.SELECTV2]: ElSelectV2,\r\n  [DcFormComponentEnum.SLIDER]: ElSlider,\r\n  [DcFormComponentEnum.SWITCH]: ElSwitch,\r\n  [DcFormComponentEnum.TIMEPICKER]: ElTimePicker,\r\n  [DcFormComponentEnum.TIMESELECT]: ElTimeSelect,\r\n  [DcFormComponentEnum.TRANSFER]: ElTransfer,\r\n  [DcFormComponentEnum.UPLOAD]: ElUpload,\r\n  [DcFormComponentEnum.CUSTOM]: DcFormCustom,\r\n  [DcFormComponentEnum.TABLE]: DcFormTable,\r\n};\r\n"], "names": ["DcFormComponentEnum", "ElInput", "DcFormText", "DcSelect", "DcRadio", "ElAutocomplete", "DcCheckbox", "ElCascader", "ElColorPicker", "ElCascaderPanel", "ElDatePicker", "ElInputNumber", "ElRate", "ElSelectV2", "ElSlider", "ElSwitch", "ElTimePicker", "ElTimeSelect", "ElTransfer", "ElUpload", "DcFormCustom", "DcFormTable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBO,MAAM,SAAiB,GAAA;AAAA,EAC5B,CAACA,0BAAoB,KAAQ,GAAAC,mBAAA;AAAA,EAC7B,CAACD,0BAAoB,IAAO,GAAAE,gBAAA;AAAA,EAC5B,CAACF,0BAAoB,MAAS,GAAAG,kBAAA;AAAA,EAC9B,CAACH,0BAAoB,KAAQ,GAAAI,kBAAA;AAAA,EAC7B,CAACJ,0BAAoB,YAAe,GAAAK,0BAAA;AAAA,EACpC,CAACL,0BAAoB,QAAW,GAAAM,kBAAA;AAAA,EAChC,CAACN,0BAAoB,QAAW,GAAAO,sBAAA;AAAA,EAChC,CAACP,0BAAoB,WAAc,GAAAQ,yBAAA;AAAA,EACnC,CAACR,0BAAoB,aAAgB,GAAAS,2BAAA;AAAA,EACrC,CAACT,0BAAoB,UAAa,GAAAU,wBAAA;AAAA,EAClC,CAACV,0BAAoB,WAAc,GAAAW,yBAAA;AAAA,EACnC,CAACX,0BAAoB,IAAO,GAAAY,kBAAA;AAAA,EAC5B,CAACZ,0BAAoB,QAAW,GAAAa,sBAAA;AAAA,EAChC,CAACb,0BAAoB,MAAS,GAAAc,oBAAA;AAAA,EAC9B,CAACd,0BAAoB,MAAS,GAAAe,oBAAA;AAAA,EAC9B,CAACf,0BAAoB,UAAa,GAAAgB,wBAAA;AAAA,EAClC,CAAChB,0BAAoB,UAAa,GAAAiB,wBAAA;AAAA,EAClC,CAACjB,0BAAoB,QAAW,GAAAkB,sBAAA;AAAA,EAChC,CAAClB,0BAAoB,MAAS,GAAAmB,oBAAA;AAAA,EAC9B,CAACnB,0BAAoB,MAAS,GAAAoB,kBAAA;AAAA,EAC9B,CAACpB,0BAAoB,KAAQ,GAAAqB,kBAAA;AAC/B;;;;"}