<template>
  <div class="menu-namespace-box">
    <el-menu class="menu-namespace" :default-active="routeRootPath" active-text-color="var(--ops-primary-color)" :collapse="isCollapse" router>
      <div class="h-40px lh-40px f-14 font-600 pl-12px">{{ $t('replenish.cluster') }}</div>
      <el-popover
        popper-class="k8s-content-popover"
        placement="right-start"
        :width="280"
        trigger="hover"
        :show-arrow="false"
        :disabled="store.state.component.deployMode !== 2"
      >
        <template #reference>
          <div class="namespace-box">
            <div class="namespace-name">
              <div class="namespace-name-label">
                {{ $t('menu.currentCluster') }}
              </div>
              <TheTooltipOverflow :width="165" :content="namespaceNameText || namespaceTitle" />
            </div>
            <div class="namespace-name flex">
              <div class="namespace-name-label">{{ `${$t('table.deploymentMode')}:` }}</div>
              <div class="flex-1"><TheTooltipOverflow width="100%" :content="deployMode" /></div>
            </div>
          </div>
        </template>
        <template #default>
          <div class="k8s-content">
            <div class="k8s-content-label">{{ $t('replenish.k8sName') }}</div>
            <div class="k8s-content-value">{{ k8sInfo.k8sName || ' -' }} <BaseCopy :text="k8sInfo.k8sName" color="RGBA(177, 179, 185, 1)" /></div>
            <div class="k8s-content-label">{{ $t('table.k8sNamespace') }}</div>
            <div class="k8s-content-value">
              {{ k8sInfo.kubeNamespaceName || ' -' }}
              <BaseCopy :text="k8sInfo.kubeNamespaceName" color="RGBA(177, 179, 185, 1)" />
            </div>
            <div class="k8s-content-label">ServiceAccount</div>
            <div class="k8s-content-value">
              {{ k8sInfo.serviceAccount || ' -' }}
              <BaseCopy :text="k8sInfo.serviceAccount" color="RGBA(177, 179, 185, 1)" />
            </div>
          </div>
        </template>
      </el-popover>
      <el-menu-item
        v-if="$has('namespace-overview')"
        index="/overview"
        :route="{
          name: 'namespaceOverview',
          query: {
            namespaceId,
            namespaceTitle
          }
        }"
      >
        <template #title>{{ $t('menu.clusterOverview') }}</template>
      </el-menu-item>
      <el-menu-item
        v-if="$has('namespace-hosts')"
        index="/hosts"
        :route="{
          name: 'namespaceHosts',
          query: {
            namespaceId,
            namespaceTitle
          }
        }"
      >
        <template #title>{{ $t('menu.resourceManagement') }}</template>
      </el-menu-item>
      <el-menu-item
        v-if="$has('namespace-components')"
        index="/components"
        :route="{
          name: 'namespaceComponents',
          query: {
            namespaceId,
            namespaceTitle
          }
        }"
      >
        <template #title>{{ $t('menu.serviceComponent') }}</template>
      </el-menu-item>
      <el-menu-item
        v-if="$has('namespace-give-larm')"
        index="/giveAlarm"
        :route="{
          name: 'namespaceGiveAlarmRecord',
          query: {
            namespaceId,
            namespaceTitle
          }
        }"
      >
        <template #title>{{ $t('replenish.alarmService') }}</template>
      </el-menu-item>
      <el-menu-item
        v-if="$has('namespace-custom-graph')"
        index="/customGraph"
        :route="{
          name: 'namespaceCustomGraphRecord',
          query: {
            namespaceId,
            namespaceTitle
          }
        }"
      >
        <template #title>{{ $t('replenish.customChart') }}</template>
      </el-menu-item>
      <el-menu-item
        v-if="$has('namespace-log')"
        index="/namespaceLog"
        :route="{
          name: 'namespaceLog',
          query: {
            instanceId: namespaceId,
            namespaceId,
            msgSource: 'docker'
          }
        }"
      >
        <template #title>{{ $t('menu.logList') }}</template>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { getClusterInfo } from '@/api/clusterApi'
import { bus } from '@/utils/mitt'
const { route, router, store, $has, t } = useBasicTool()

const namespaceNameText = computed(() => store.state.namespace.namespaceName)

const isCollapse = ref(false)

const k8sInfo = ref({
  k8sName: '',
  serviceAccount: '',
  kubeNamespaceName: ''
})

const namespaceId = computed(() => {
  return route.query.namespaceId
})

const deployModeEnum = {
  1: t('form.host'),
  2: 'Kubernetes'
}

const deployMode = computed(() => {
  const mode = store.state.component.deployMode
  console.log('deployMode', mode)
  if (typeof mode === 'number') {
    return deployModeEnum[mode as 1 | 2] || '-'
  }
  return '-'
})

const namespaceTitle = computed(() => {
  return route.query.namespaceTitle || route.query.namespaceName
})

onMounted(() => {
  if (namespaceTitle.value) {
    store.commit('namespace/setNamespaceName', namespaceTitle.value)
  }
  bus.on('updateK8s', () => {
    isEnterCluster()
  })
})

onBeforeUnmount(() => {
  bus.off('updateK8s')
})
function isEnterCluster() {
  if (namespaceId.value) {
    getClusterInfo({ id: namespaceId.value, _t: Date.now() }).then((res) => {
      if (res.data.data === null) {
        router.push({ name: 'namespace' })
      } else {
        k8sInfo.value = res.data.data
      }
    })
  }
}

const routePathList = computed(() => router.currentRoute.value.path.split('/'))
const routeRootPath = computed(() => `/${routePathList.value[2]}`)
// onBeforeRouteLeave((to, from) => {
//   store.commit('namespace/setMeunCurrentNamespaceTitle', '')
// })

watch(
  () => namespaceId.value,
  () => {
    isEnterCluster()
  },
  {
    immediate: true
  }
)

onUnmounted(() => {
  store.commit('namespace/setMeunCurrentNamespaceTitle', '')
})
</script>

<style lang="scss" scoped>
.menu-namespace-box {
  background-color: var(--ops-bg-white-color);
  :deep(.el-menu) {
    border-right: none;
  }
  :deep(.box span) {
    font-size: 14px;
    font-weight: 600;
  }
  .menu-namespace {
    position: sticky;
    top: 60px;
    width: 192px;
    height: calc(100vh - 56px);
    padding-left: 8px;
    background-color: var(--ops-bg-white-color);
    box-shadow: inset -1px 0px 0px 0px var(--ops-border-color);
    .icon {
      width: 18px;
      height: 18px;
      margin-right: 10px;
    }
    .el-menu-item {
      height: 40px;
      padding: 0;
      padding-left: 15px;
      margin-top: 4px;
      margin-right: 8px;
      font-size: 14px;
      line-height: 40px;
      @include flex(none, center);
    }
    .el-menu-item.is-active {
      font-weight: 600;
    }
    .el-menu-item:hover {
      background-color: var(--ops-menu-hover-color-1) !important;
    }
    .iconfont {
      margin-right: 10px;
      vertical-align: baseline;
    }
  }
  .namespace-box {
    position: relative;
    box-sizing: border-box;
    width: 176px;
    height: 88px;
    padding: 10px 12px;
    border-radius: var(--radius-m);
    color: var(--ops-text-white-color);
    background-image: url('../../assets/img/bg-current-namespace.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &::after {
      content: '';
      position: absolute;
      top: 58px;
      left: 0;
      width: 100%;
      height: 100%;
      height: 1px;
      background: #ffffff;
      opacity: 0.5;
    }
    .iconfont {
      position: relative;
      top: -4px;
      padding: 0 0 15px 15px;
      font-size: 17px;
    }
    .namespace-name {
      font-size: 14px;
      line-height: 22px;
      font-weight: 600;
      color: var(--ops-text-white-color);
      &.flex {
        display: flex;
        align-items: center;
        // margin-top: 2px;
        .namespace-name-label {
          width: max-content;
          margin-top: -2px;
          margin-right: 8px;
        }
        .flex-1 {
          overflow: hidden;
          flex: 1;
          height: max-content;
        }
      }
      &-label {
        display: inline-flex;
        align-items: center;
        width: 100%;
        font-size: 12px;
        line-height: 20px;
        font-weight: 400;
      }
      &-k8s {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 42px;
        height: 18px;
        border: 0.5px solid rgba(76, 255, 246, 1);
        border-radius: 2px;
        margin-left: 8px;
        font-size: 12px;
        vertical-align: middle;
        color: rgba(76, 255, 246, 1);
        span {
          border-top: 4px transparent solid;
          border-right: 4px transparent solid;
          border-bottom: 4px transparent solid;
          border-left: 4px solid rgba(76, 255, 246, 1);
          margin-left: 6px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.k8s-content-popover {
  .k8s-content {
    &-label {
      font-size: 12px;
      line-height: 20px;
      color: RGBA(121, 125, 134, 1);
    }
    &-value {
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 22px;
      font-weight: 600;
      color: RGBA(46, 52, 66, 1);
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
