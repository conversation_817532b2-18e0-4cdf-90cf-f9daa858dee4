import { defineComponent, computed, openBlock, createElementBlock, createBlock, unref, createCommentVNode, mergeProps, createSlots, withCtx, createTextVNode, toDisplayString, renderList, createVNode } from 'vue';
import DcButton from '../../button/src/index.mjs';
import RenderVNode from '../../render-vnode/src/index.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const _hoisted_1 = { class: "dc-dialog-footer" };
const _sfc_main = defineComponent({
  __name: "Footer",
  props: {
    confirmAction: {
      type: Object,
      default: () => ({})
    },
    cancelAction: {
      type: Object,
      default: () => ({})
    },
    footerLeftSlot: {
      type: Object
    },
    footerRightSlot: {
      type: Object
    }
  },
  emits: ["cancel", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const defaultConfirmAction = {
      innerText: "\u786E\u5B9A",
      props: {
        type: "primary"
      },
      config: {
        debounce: true
      }
    };
    const confirmAction = computed(() => props.confirmAction ? {
      ...defaultConfirmAction,
      ...props.confirmAction,
      props: {
        ...defaultConfirmAction.props,
        ...props.confirmAction.props || {}
      },
      config: {
        ...defaultConfirmAction.config,
        ...props.confirmAction.config || {}
      }
    } : defaultConfirmAction);
    const defaultCancelAction = {
      innerText: "\u53D6\u6D88"
    };
    const cancelAction = computed(() => props.cancelAction ? { ...defaultCancelAction, ...props.cancelAction } : defaultCancelAction);
    const onConfirm = () => {
      emits("confirm");
    };
    const onCancel = () => {
      emits("cancel");
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        props.footerLeftSlot ? (openBlock(), createBlock(unref(RenderVNode), {
          key: 0,
          vnode: props.footerLeftSlot
        }, null, 8, ["vnode"])) : createCommentVNode("v-if", true),
        confirmAction.value.visible !== false ? (openBlock(), createBlock(DcButton, mergeProps({ key: 1 }, { ...confirmAction.value.props }, {
          config: confirmAction.value.config,
          onClick: onConfirm
        }), createSlots({
          default: withCtx(() => [
            createTextVNode(" " + toDisplayString(confirmAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          renderList(Object.keys(confirmAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: withCtx((scope) => [
                createVNode(unref(RenderVNode), {
                  vnode: (confirmAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : createCommentVNode("v-if", true),
        cancelAction.value.visible !== false ? (openBlock(), createBlock(DcButton, mergeProps({ key: 2 }, { ...cancelAction.value.props }, {
          config: cancelAction.value.config,
          onClick: onCancel
        }), createSlots({
          default: withCtx(() => [
            createTextVNode(" " + toDisplayString(cancelAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          renderList(Object.keys(cancelAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: withCtx((scope) => [
                createVNode(unref(RenderVNode), {
                  vnode: (cancelAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : createCommentVNode("v-if", true),
        props.footerRightSlot ? (openBlock(), createBlock(unref(RenderVNode), {
          key: 3,
          vnode: props.footerRightSlot
        }, null, 8, ["vnode"])) : createCommentVNode("v-if", true)
      ]);
    };
  }
});
var Footer = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\dialog\\src\\Footer.vue"]]);

export { Footer as default };
//# sourceMappingURL=Footer.mjs.map
