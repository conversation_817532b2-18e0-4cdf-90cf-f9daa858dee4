<template>
  <div
    class="menu"
    :class="{
      'px-8px': !collapse
    }"
  >
    <el-scrollbar class="menu-scrollbar">
      <el-menu
        router
        :default-active="routeRootPath"
        class="w-160px collapse-class"
        :collapse="collapse"
        background-color="transparent"
        active-text-color="var(--ops-primary-color)"
      >
        <el-menu
          router
          :default-active="routeRootPath"
          class="w-160px"
          :class="{ 'collapse-class': collapse }"
          :collapse="true"
          background-color="transparent"
          active-text-color="var(--ops-primary-color)"
        >
          <el-sub-menu :hide-timeout="1000000000000" :class="{ 'w-160px': !collapse }" v-if="$has('namespace')" index="/namespace">
            <template #title>
              <img class="mr-8px" :src="menuIconSrc(['/namespace'], 'jiqunac', 'jiqun')" alt="icon" />
              <template v-if="!collapse">{{ $t('menu.colony') }}</template>
            </template>
            <template #default>
              <div class="currentCluster-box">
                <el-menu-item
                  v-for="item in menuNamespaceList"
                  :key="item.clusterId"
                  :class="{
                    'currentCluster-item': true,
                    isItem: meunCurrentNamespaceTitle == item.clusterName
                  }"
                  :label="item.clusterName"
                  :value="item.clusterName"
                  index="/namespace/overview"
                  :route="{
                    name: 'namespaceOverview',
                    query: { namespaceId: item.clusterId, namespaceTitle: item.clusterName, deployMode: item.deployMode }
                  }"
                >
                  <div @click="handleNamespaceView(item)" class="w-100% h-100% flex justify-between pl-10px items-center">
                    <TheTooltipOverflow class="z-999999 relative cursor-pointer" :width="item?.lastRecord ? 128 : 200" :content="item.clusterName" />
                    <div class="flex-1 ml-8px flex h-20px">
                      <RecentUseTag v-if="item?.lastRecord" />
                    </div>
                  </div>
                </el-menu-item>
                <br />
                <el-menu-item class="currentCluster-text" index="/namespace">
                  <div class="ml-8px c-primary" @click="more">{{ $t('menu.goToClusterList') }}&gt;&gt;</div>
                </el-menu-item>
              </div>
            </template>
          </el-sub-menu>
        </el-menu>
        <el-sub-menu v-if="$has('hosts') || $has('rack') || $has('kubernetes')" index="hosts">
          <template #title>
            <img :src="menuIconSrc(['/hosts', '/shelf', '/k8s'], 'zhujiac', 'zhuji')" alt="icon" />
            <span class="ml-6px"> {{ $t('replenish.resources') }}</span>
          </template>
          <el-menu-item index="/hosts" v-if="$has('hosts') && systemVersion === 1"> {{ $t('router.hostManagement') }} </el-menu-item>
          <!-- <el-menu-item index="/shelf" v-if="$has('rack')"> {{ $t('menu.rackManagement') }} </el-menu-item> -->
          <el-menu-item index="/k8s" v-if="$has('kubernetes')"> {{ $t('router.kubernetesManagement') }} </el-menu-item>
        </el-sub-menu>
        <!-- <el-menu-item v-if="$has('kubernetes')" index="/k8s">
          <img :src="menuIconSrc(['/k8s'], 'zujianac', 'zujian')" alt="icon" />
          <template #title> <span class="ml-6px"> Kubernetes </span> </template>
        </el-menu-item> -->
        <el-sub-menu v-if="$has('audit')" index="logs">
          <template #title>
            <img :src="menuIconSrc(['/audit'], 'rizhiac', 'rizhi')" alt="icon" />
            <span class="ml-6px">{{ $t('menu.diagnosis') }}</span>
          </template>
          <el-menu-item v-if="$has('audit')" index="/audit"> {{ $t('menu.auditList') }} </el-menu-item>
        </el-sub-menu>
        <el-sub-menu v-if="$has('migrationBackup')" index="migrationBackup">
          <template #title>
            <img :src="menuIconSrc(['/migrationBackup', '/sczTaskManagement'], 'qianyibeifenac', 'qianyibeifen')" alt="icon" />
            <span class="ml-6px">
              <BaseTooltip :content="$t('replenish.migrationBackup')" :line="1" placement="right" />
            </span>
          </template>
          <el-menu-item v-if="$has('coldAndHotDataClassification')" index="/migrationBackup">
            <BaseTooltip :content="$t('replenish.hotColdDataGrading')" :line="1" placement="right" />
          </el-menu-item>
          <el-menu-item v-if="$has('synchronizeTask')" index="/sczTaskManagement">
            <BaseTooltip :content="$t('replenish.syncTaskManagement')" :line="1" placement="right" />
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-scrollbar>
    <div class="h-1px mb-7px"></div>
    <div class="h-40px lh-40px mb-8px relative pl-8px hover:bg-menuHover cursor-pointer" :class="{ 'mx-8px': collapse }" @click="switchCollapse">
      <BaseElTooltip :disabled="!collapse" :content="`${$t('replenish.expand')}/${$t('replenish.collapse')}`" placement="right">
        <img class="inline-block" :src="collapse ? meunIconMap.get('zhankai') : meunIconMap.get('shouqi')" alt="icon" />
      </BaseElTooltip>
      <div v-show="!collapse" class="f-14 ml-6px absolute h-40px left-30px top-2px">{{ $t('replenish.collapse') }}</div>
    </div>
  </div>
  <MenuNamespace v-if="isNamespace" />
</template>
<script lang="ts" setup>
import { useMeunImportIcon } from '../hooks/useMeunImportIcon'
import MenuNamespace from './MenuNamespace.vue'
import RecentUseTag from './MenuRecentUseTag.vue'
const { store, $has } = useBasicTool()
const menuNamespaceList = computed(() => store.state.namespace.menuNamespaceList)
const meunCurrentNamespaceTitle = computed(() => store.state.namespace.meunCurrentNamespaceTitle)
const systemVersion = computed(() => store.state.app.systemVersion)
const collapse = ref(true)
const { meunIconMap, routeRootPath, menuIconSrc, isNamespace } = useMeunImportIcon()
function handleNamespaceView(item: any): object {
  const id = item?.clusterId
  const namespaceTitle = item?.clusterName
  const deployMode = item?.deployMode
  store.commit('namespace/setMeunCurrentNamespaceTitle', namespaceTitle)
  store.commit('namespace/setNamespaceName', namespaceTitle)
  store.commit('component/setNamespaceId', id)
  store.commit('component/setDeployMode', deployMode)
  store.dispatch('namespace/getMenuNamespaceList')
  return {
    name: 'namespaceOverview',
    query: { namespaceId: id, namespaceTitle, deployMode }
  }
}
function more() {
  store.commit('namespace/setMeunCurrentNamespaceTitle', '')
}

function switchCollapse() {
  collapse.value = !collapse.value
}
</script>
<style lang="scss" scoped>
.menu {
  position: relative;
  height: 100%;
  transition: all 0.4s;
  img {
    width: 24px;
    max-width: none;
    height: 24px;
  }
  .menu-scrollbar {
    height: calc(100vh - 113px);
    padding-top: 5px;
  }
  :deep(.el-menu) {
    border-right: none;
    .el-menu-item,
    .el-sub-menu__title {
      height: 40px;
      padding-left: 5px;
      margin-bottom: 4px;
      font-weight: 500;
    }
    .el-menu-item:hover {
      background-color: var(--ops-menu-hover-color) !important;
    }
    .el-sub-menu.is-active .el-sub-menu__title {
      color: var(--ops-primary-color);
    }
    .el-sub-menu__title:hover {
      background-color: var(--ops-menu-hover-color) !important;
    }
    .el-sub-menu {
      .el-menu-item {
        height: 40px;
        padding-left: 34px;
        margin-bottom: 4px;
      }
      .el-sub-menu__icon-arrow {
        right: 8px;
      }
    }
  }
  :deep(.el-menu--collapse.collapse-class) {
    width: 56px !important;
    .el-menu-item,
    .el-sub-menu__title {
      width: 40px;
      margin-left: 8px;
      .el-menu-tooltip__trigger {
        padding-left: 8px;
      }
    }
    .el-sub-menu__title {
      width: 40px;
      padding-right: 0px;
      padding-left: 8px;
    }
  }
}
.el-menu--vertical {
  .currentCluster-box {
    box-sizing: border-box;
    width: 482px;
    padding: 10px 0 10px 8px;
    font-size: 14px;
    .currentCluster-item,
    .currentCluster-text {
      display: inline-block;
      box-sizing: border-box;
      width: 220px;
      height: 40px;
      padding: 0;
      margin-right: 16px;
      line-height: 40px;
      color: var(--ops-text-color);
      cursor: pointer;
    }
    .currentCluster-item:nth-child(2n) {
      margin-right: 0px;
    }
    .currentCluster-text:hover {
      background-color: transparent;
    }
    .currentCluster-item:hover {
      background-color: var(--ops-menu-hover-color-1);
    }
    .isItem {
      color: var(--ops-primary-color);
    }
  }
}
:deep(.el-menu--collapse .el-menu .el-sub-menu) {
  min-width: auto;
}
</style>
