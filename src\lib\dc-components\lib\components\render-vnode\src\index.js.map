{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/render-vnode/src/index.tsx"], "sourcesContent": ["import { defineComponent } from \"vue\";\r\nimport type { PropType } from \"vue\";\r\nimport type { RenderVNodeFn } from \"./types\";\r\n\r\nexport default defineComponent({\r\n  name: \"DcRenderVNode\",\r\n  props: {\r\n    vnode: {\r\n      type: Function as PropType<RenderVNodeFn>,\r\n      default: () => null,\r\n    },\r\n    scope: {\r\n      type: Object as PropType<Record<string, any>>,\r\n      default: () => ({}),\r\n    },\r\n    extraScope: {\r\n      type: Object as PropType<Record<string, any>>,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n\r\n  render() {\r\n    return typeof this.$props.vnode === \"function\"\r\n      ? this.$props.vnode(this.$props.scope, this.$props.extraScope)\r\n      : null;\r\n  },\r\n});\r\n"], "names": ["defineComponent", "name", "props", "vnode", "type", "Function", "default", "scope", "Object", "extraScope", "render", "$props"], "mappings": ";;;;;;AAIA,kBAAeA,mBAAgB,CAAA;AAAA,EAC7BC,IAAM,EAAA,eAAA;AAAA,EACNC,KAAO,EAAA;AAAA,IACLC,KAAO,EAAA;AAAA,MACLC,IAAMC,EAAAA,QAAAA;AAAAA,MACNC,SAASA,MAAM,IAAA;AAAA,KACjB;AAAA,IACAC,KAAO,EAAA;AAAA,MACLH,IAAMI,EAAAA,MAAAA;AAAAA,MACNF,OAAAA,EAASA,OAAQ,EAAA,CAAA;AAAA,KACnB;AAAA,IACAG,UAAY,EAAA;AAAA,MACVL,IAAMI,EAAAA,MAAAA;AAAAA,MACNF,OAAAA,EAASA,OAAQ,EAAA,CAAA;AAAA,KACnB;AAAA,GACF;AAAA,EAEAI,MAAS,GAAA;AACP,IAAA,OAAO,OAAO,IAAA,CAAKC,MAAOR,CAAAA,KAAAA,KAAU,aAChC,IAAKQ,CAAAA,MAAAA,CAAOR,KAAM,CAAA,IAAA,CAAKQ,MAAOJ,CAAAA,KAAAA,EAAO,IAAKI,CAAAA,MAAAA,CAAOF,UAAU,CAC3D,GAAA,IAAA,CAAA;AAAA,GACN;AACF,CAAC,CAAA;;;;"}