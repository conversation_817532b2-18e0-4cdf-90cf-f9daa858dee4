import { useI18n } from 'vue-i18n'
export function useSwitchTime(host: Array<number> | stringOrNumber, time: string, label: string, homePageDisplay = false) {
  // 搜索数据
  const searchModelObj = reactive({
    host,
    time
  })
  const { t } = useI18n()
  // 视图数据
  const panelHost = ref<any>([])
  const store = useStore()
  const pickerDate = ref<never[]>([])
  // element shortcuts的数据
  const shortcuts = [
    {
      text: '5' + t('mixed.minutes'),
      value: () => {
        searchModelObj.time = '5m'
        if (Array.isArray(searchModelObj.host)) getMonitorDetail(searchModelObj.host[2], '5m')
        else getMonitorDetail(searchModelObj.host, '5m')
        return ['no', 'no']
      }
    },
    {
      text: '30' + t('mixed.minutes'),
      value: () => {
        searchModelObj.time = '30m'
        if (Array.isArray(searchModelObj.host)) getMonitorDetail(searchModelObj.host[2], '30m')
        else getMonitorDetail(searchModelObj.host, '30m')
        return ['no', 'no']
      }
    },
    {
      text: '1' + t('mixed.hour'),
      value: () => {
        searchModelObj.time = '1h'
        if (Array.isArray(searchModelObj.host)) getMonitorDetail(searchModelObj.host[2], '1h')
        else getMonitorDetail(searchModelObj.host, '1h')
        return ['no', 'no']
      }
    },
    {
      text: '2' + t('mixed.hour'),
      value: () => {
        searchModelObj.time = '2h'
        if (Array.isArray(searchModelObj.host)) getMonitorDetail(searchModelObj.host[2], '2h')
        else getMonitorDetail(searchModelObj.host, '2h')
        return ['no', 'no']
      }
    },
    {
      text: '6' + t('mixed.hour'),
      value: () => {
        searchModelObj.time = '6h'
        if (Array.isArray(searchModelObj.host)) getMonitorDetail(searchModelObj.host[2], '6h')
        else getMonitorDetail(searchModelObj.host, '6h')
        return ['no', 'no']
      }
    },
    {
      text: '12' + t('mixed.hour'),
      value: () => {
        searchModelObj.time = '12h'
        if (Array.isArray(searchModelObj.host)) getMonitorDetail(searchModelObj.host[2], '12h')
        else getMonitorDetail(searchModelObj.host, '12h')
        return ['no', 'no']
      }
    }
  ]
  // 切换是获取数据
  function getMonitorDetail(id: number | string, val: string) {
    store.dispatch('panel/getPanelQueryByLabel', { id, label, homePageDisplay }).then((res) => {
      panelHost.value = res.data
      for (const item of panelHost.value) {
        const query = item.panelUrl.split('&')
        const newItem = `from=now-${val}&to=now`
        query.splice(1, 0, newItem)
        item.panelUrl = query.join('&')
      }
    })
  }
  // 日期切换时获取数据
  function pickerChange() {
    if (pickerDate.value[0] !== 'Invalid Date') {
      store
        .dispatch('panel/getPanelQueryByLabel', {
          id: Array.isArray(searchModelObj.host) ? searchModelObj.host[2] : searchModelObj.host,
          label,
          homePageDisplay
        })
        .then((res) => {
          panelHost.value = res.data
          for (const item of panelHost.value) {
            const fromDate = new Date(pickerDate.value[0]).getTime()
            const toDate = new Date(pickerDate.value[1]).getTime()
            const query = item.panelUrl.split('&')
            const newItem = `from=${fromDate}&to=${toDate}`
            query.splice(1, 0, newItem)
            item.panelUrl = query.join('&')
          }
        })
    }
  }
  function monitorSubjectSwitch(value: string | number) {
    if (pickerDate.value.length && pickerDate.value[0] !== 'Invalid Date') {
      pickerChange()
    } else {
      getMonitorDetail(value, searchModelObj.time)
    }
  }

  return { searchModelObj, shortcuts, panelHost, pickerDate, getMonitorDetail, pickerChange, monitorSubjectSwitch }
}
