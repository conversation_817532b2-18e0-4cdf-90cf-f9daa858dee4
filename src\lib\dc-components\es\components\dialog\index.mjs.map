{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/dialog/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Dialog from \"./src/index.vue\";\r\n\r\nexport const DcDialog = withInstall(Dialog);\r\n\r\nexport default DcDialog;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}