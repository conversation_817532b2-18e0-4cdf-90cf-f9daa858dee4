'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/icon/style/css');
var iconsVue = require('@element-plus/icons-vue');
var hooks = require('./hooks.js');
require('./index.vue_vue_type_style_index_0_scoped_true_lang.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const _withScopeId = (n) => (vue.pushScopeId("data-v-27aa1df5"), n = n(), vue.popScopeId(), n);
const _hoisted_1 = { class: "view-layout" };
const _hoisted_2 = { class: "view-header" };
const _hoisted_3 = { key: 0 };
const _hoisted_4 = {
  key: 1,
  class: "view-header--name"
};
const _hoisted_5 = { key: 2 };
const _hoisted_6 = {
  key: 0,
  class: "view-search"
};
const _hoisted_7 = {
  key: 1,
  class: "view-pagination"
};
const __default__ = vue.defineComponent({
  name: "DcViewLayout"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    showBack: {
      type: Boolean,
      default: false
    },
    headerName: {
      type: String,
      default: ""
    }
  },
  setup(__props) {
    vue.useCssVars((_ctx) => ({
      "27aa1df5-contentPadding": contentPadding.value
    }));
    const tableRef = vue.ref();
    const { height } = hooks.useElementSize(tableRef);
    const slots = vue.useSlots();
    console.log(slots, "======slots");
    const contentPadding = vue.computed(() => {
      if (slots.pagination)
        return "60px";
      return "0px";
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", _hoisted_1, [
        vue.createElementVNode("div", _hoisted_2, [
          vue.unref(slots).headerLeft ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_3, [
            vue.renderSlot(_ctx.$slots, "headerLeft", {}, void 0, true)
          ])) : (vue.openBlock(), vue.createElementBlock("span", _hoisted_4, [
            __props.showBack ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElIcon), {
              key: 0,
              onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$router.back())
            }, {
              default: vue.withCtx(() => [
                vue.createVNode(vue.unref(iconsVue.ArrowLeftBold))
              ]),
              _: 1
            })) : vue.createCommentVNode("v-if", true),
            vue.createTextVNode(" " + vue.toDisplayString(__props.headerName), 1)
          ])),
          vue.unref(slots).headerRight ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_5, [
            vue.renderSlot(_ctx.$slots, "headerRight", {}, void 0, true)
          ])) : vue.createCommentVNode("v-if", true)
        ]),
        vue.unref(slots).search ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_6, [
          vue.renderSlot(_ctx.$slots, "search", {}, void 0, true)
        ])) : vue.createCommentVNode("v-if", true),
        vue.createElementVNode("div", {
          ref_key: "tableRef",
          ref: tableRef,
          class: "view-table"
        }, [
          vue.renderSlot(_ctx.$slots, "table", { tableHeight: vue.unref(height) }, void 0, true)
        ], 512),
        vue.unref(slots).pagination ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_7, [
          vue.renderSlot(_ctx.$slots, "pagination", {}, void 0, true)
        ])) : vue.createCommentVNode("v-if", true)
      ]);
    };
  }
});
var ViewLayout = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__scopeId", "data-v-27aa1df5"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\view-layout\\src\\index.vue"]]);

exports["default"] = ViewLayout;
//# sourceMappingURL=index.js.map
