<template>
  <div class="config-record-box">
    <div style="height: calc(100% - 52px)">
      <BaseTable v-loading="tableLoading" :columns="tableOptions.columns" :data="tableOptions.dataList">
        <template #id="scope">
          {{ scope.row.versionNo }}
          <BaseElTooltip v-if="scope.row.currentUse === 1" :content="$t('mixed.configurationTip')" placement="top">
            <span style="font-size: 14px; color: var(--ops-primary-color)">（{{ $t('title.currentConfiguration') }}）</span>
          </BaseElTooltip>
          <BaseElTooltip v-else-if="scope.row.currentVersion" :content="$t('mixed.latestConfigurationTip')" placement="top">
            <span style="font-size: 14px; color: var(--ops-primary-color)">（{{ $t('title.latestConfiguration') }}）</span>
          </BaseElTooltip>
        </template>
        <template #confStatus="{ row }">{{ confStatuEnum?.[row.confStatus] || row.confStatus }}</template>
        <template #confType="{ row }">{{ cconfTypeEnum?.[row.confType] || row.confType }}</template>
        <template #after>
          <el-table-column :label="$t('table.operate')" :width="language === 'zh-CN' ? 220 : 270" fixed="right">
            <template #default="scope">
              <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" />
            </template>
          </el-table-column>
        </template>
      </BaseTable>
    </div>

    <div class="pager">
      <BasePagination v-model="pageInfo" @page-change="handlePageChange" />
    </div>
    <OperateConfigRecord
      :is-open-see-drawer="isOpenSeeDrawer"
      :is-version-rollback="isVersionRollback"
      :title="ViewModificationsTitle"
      :type="type"
      :version-no="versionNo"
      :record-id="recordId"
      @close-refresh="closeRefresh"
      @close="close"
    />
    <CheckDetail v-model="detailVisible" :isBatch="isBatch" :clusterServiceId="clusterServiceId" :recordId="recordId" />
  </div>
</template>
<script lang="ts" setup>
import OperateConfigRecord from './components/OperateConfigRecord.vue'
import CheckDetail from './components/CheckDetail.vue'
const { t, store, $has } = useBasicTool()
const { pageInfo } = usePage()
const tableLoading = ref(true)
const language = computed(() => store.state.app.language)
const props = defineProps({
  clusterServiceId: {
    type: String,
    default: ''
  },
  clusterServiceInstanceId: {
    type: String,
    default: ''
  }
})

const confStatuEnum = {
  0: t('replenish.notEffect'),
  1: t('replenish.effective')
}

const cconfTypeEnum = {
  1: t('title.configParameters'),
  2: t('replenish.batchConfiguration')
}

const tableOptions = reactive({
  columns: [
    { prop: 'id', label: computed(() => t('table.versionNumber')), slot: true, width: '210' },
    { prop: 'confStatus', label: computed(() => t('table.state')), slot: true, width: '210' },
    { prop: 'createUserName', label: computed(() => t('table.operator')), width: '120' },
    { prop: 'confType', label: computed(() => t('form.type')), slot: true, width: '210' },
    { prop: 'modifyReason', label: computed(() => t('table.modifyReason')) },
    { prop: 'createTime', label: computed(() => t('table.operatingTime')) }
  ],
  dataList: []
})
const updateRecordData = computed(() => store.state.clusterServiceConfig.updateRecordData)
const isOpenSeeDrawer = ref(false)
const ViewModificationsTitle = ref('')
const recordId: any = ref(0)
const type = ref(1) // 查看修改 1 配置快照 2
const isVersionRollback = ref(false) // 判断是是否是回滚
const versionNo = ref(1)

const detailVisible = ref(false)
const isBatch = ref(false)

onMounted(() => {
  getUpdateRecordList()
})
// 分页
function handlePageChange() {
  getUpdateRecordList()
}
// 打开抽屉函数
function openSee(id: string | number, value: number, title: string, faly: boolean, version: number) {
  isOpenSeeDrawer.value = true
  recordId.value = id
  type.value = value
  ViewModificationsTitle.value = title
  isVersionRollback.value = faly
  versionNo.value = version
}
// 获取列表
function getUpdateRecordList() {
  store
    .dispatch('clusterServiceConfig/getUpdateRecordList', {
      ...pageInfo,
      clusterServiceId: props.clusterServiceId,
      clusterServiceInstanceId: props.clusterServiceInstanceId
    })
    .then(() => {
      pageInfo.total = updateRecordData.value.total
      tableOptions.dataList = updateRecordData.value.records
      tableLoading.value = false
    })
}
// 关闭抽屉
function close() {
  isVersionRollback.value = false
  isOpenSeeDrawer.value = false
}
// 关闭时刷新列表
function closeRefresh() {
  close()
  getUpdateRecordList()
}

const openDetail = (id: string | number, confType: string) => {
  recordId.value = id
  isBatch.value = confType === '2'
  detailVisible.value = true
}

// 表格操作栏数据
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('table.viewModification'),
        disabled: false,
        click: () => {
          openDetail(scope.row.id, scope.row.confType)
          // openSee(scope.row.id, 1, t('table.viewModification'), false, scope.row.versionNo)
        },
        isButton: true
      }
      // {
      //   buttonName: t('table.configurationSnapshot'),
      //   disabled: false,
      //   click: () => {
      //     openSee(scope.row.id, 2, t('table.configurationSnapshot'), false, scope.row.versionNo)
      //   },
      //   isButton: true
      // },
      // {
      //   buttonName: t('table.versionRollback'),
      //   disabled: false,
      //   click: () => {
      //     openSee(scope.row.id, 2, t('table.versionRollback'), true, scope.row.versionNo)
      //   },
      //   isButton: scope.row.rollbackFlag !== 0 && !scope.row.currentVersion && $has('namespace-componentsConfiguraRollback')
      // }
    ]
  }
}
</script>
<style lang="scss" scoped>
.config-record-box {
  box-sizing: border-box;
  height: 100%;
  padding: 20px 20px 12px;
  :deep(.title-box) {
    margin-bottom: 0;
  }
}
.pager {
  margin-top: 15px;
  @include flex(flex-end);
}
</style>
