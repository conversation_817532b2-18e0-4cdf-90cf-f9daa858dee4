import service from '@/utils/Http'
/**
 * @description 历史巡检报告列表接口
 * @param {inspectionApi.InspectionPage} data
 * @return {*}
 */

export const inspectionPage = (data: inspectionApi.InspectionPage) => {
  return service.post<ApiResponse<any>>('/ops/inspection/page', data)
}

/**
 * @description 最后一次巡检信息
 * @return {*}
 */
export const inspectionLatestInfo = () => {
  return service.get<ApiResponse<any>>('/ops/inspection/latest/info')
}

/**
 * @description 手动巡检
 * @param {inspectionApi.InspectionManual} data
 * @return {*}
 */
export const inspectionManual = (data: inspectionApi.InspectionManual) => {
  return service.post<ApiResponse<any>>('/ops/inspection/manual', data)
}

/**
 * @description 巡检配置
 * @return {*}
 */
export const inspectionConfig = () => {
  return service.get<ApiResponse<any>>('/ops/inspection/config')
}

/**
 * @description 保存巡检配置
 * @param {inspectionApi.InspectionConfigSave} data
 * @return {*}
 */
export const inspectionConfigSave = (data: inspectionApi.InspectionConfigSave) => {
  return service.post<ApiResponse<any>>('/ops/inspection/config/save', data)
}

/**
 * @description 巡检报告
 * @param {inspectionApi.InspectionReport} data
 * @return {*}
 */
export const inspectionReport = (data: inspectionApi.InspectionReport) => {
  return service.post<ApiResponse<any>>('/ops/inspection/report', data)
}
