export default [
    {
        url: '/api/authentication/auth/login',
        method: 'POST',
        response: () => ({
            code: '200',
            msg: 'ok',
            data: {
                token: 'token'
            }
        })
    },
    {
        url: '/api/authentication/user/getUserInfo',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: {
                componentList: [
                    'userList', // 用户列表
                    'hosts-add', // 添加主机
                    'hosts-Unbind', //解绑主机
                    'namespace-add',// 添加集群
                    'namespace-rename', // 重命名集群
                    'namespace-addComponents', // 添加组件
                    'namespace-startUpComponents', // 启动
                    'namespace-stopComponents', // 停止
                    'namespace-restartComponents', // 重启
                    'namespace-deleteComponents', // 删除s
                ]
            }
        })
    },
]