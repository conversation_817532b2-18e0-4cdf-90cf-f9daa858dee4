{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/render-vnode/src/index.tsx"], "sourcesContent": ["import { defineComponent } from \"vue\";\r\nimport type { PropType } from \"vue\";\r\nimport type { RenderVNodeFn } from \"./types\";\r\n\r\nexport default defineComponent({\r\n  name: \"DcRenderVNode\",\r\n  props: {\r\n    vnode: {\r\n      type: Function as PropType<RenderVNodeFn>,\r\n      default: () => null,\r\n    },\r\n    scope: {\r\n      type: Object as PropType<Record<string, any>>,\r\n      default: () => ({}),\r\n    },\r\n    extraScope: {\r\n      type: Object as PropType<Record<string, any>>,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n\r\n  render() {\r\n    return typeof this.$props.vnode === \"function\"\r\n      ? this.$props.vnode(this.$props.scope, this.$props.extraScope)\r\n      : null;\r\n  },\r\n});\r\n"], "names": ["defineComponent", "name", "props", "vnode", "type", "Function", "default", "scope", "Object", "extraScope", "render", "$props"], "mappings": ";;AAIA,kBAAeA,eAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,eAAe;AACrBC,EAAAA,KAAK,EAAE;AACLC,IAAAA,KAAK,EAAE;AACLC,MAAAA,IAAI,EAAEC,QAAmC;MACzCC,OAAO,EAAEA,MAAM,IAAA;KAChB;AACDC,IAAAA,KAAK,EAAE;AACLH,MAAAA,IAAI,EAAEI,MAAuC;AAC7CF,MAAAA,OAAO,EAAEA,OAAO,EAAE,CAAA;KACnB;AACDG,IAAAA,UAAU,EAAE;AACVL,MAAAA,IAAI,EAAEI,MAAuC;AAC7CF,MAAAA,OAAO,EAAEA,OAAO,EAAE,CAAA;AACpB,KAAA;GACD;AAEDI,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,OAAO,IAAI,CAACC,MAAM,CAACR,KAAK,KAAK,UAAU,GAC1C,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAC,IAAI,CAACQ,MAAM,CAACJ,KAAK,EAAE,IAAI,CAACI,MAAM,CAACF,UAAU,CAAC,GAC5D,IAAI,CAAA;AACV,GAAA;AACF,CAAC,CAAC;;;;"}