// userpi.ts
import service, { uploadService } from '@/utils/Http'

/**
 * @description 用户登录
 * @param {UserApi.ILogin} data
 * @return {*}
 */
export const login = async (data: UserApi.ILogin) => {
  return await service.post<ApiResponse<UserApi.LoginData>>('/auth/login', data)
}

/**
 * @description 退出登录
 * @return {*}
 */
export const logout = async () => {
  return await service.post<ApiResponse<any>>('/auth/logout')
}

/**
 * @description 新增用户
 * @param {UserApi.IAddUser} data
 * @return {*}
 */
export const addUser = async (data: UserApi.IAddUser) => {
  return await service.post<ApiResponse<boolean>>('/user/add', data)
}

/**
 * @description 根据ID删除用户
 * @param {UserApi.IDeteleUserById} data
 * @return {*}
 */
export const deteleUserById = async (data: UserApi.IDeteleUserById) => {
  return await service.post<ApiResponse<boolean>>('/user/delete', data)
}

/**
 * @description 根据ID获取用户详细信息
 * @return {*}
 */
export const getUserDetails = async (data: UserApi.IGetUserDetails) => {
  return await service.post<ApiResponse<UserApi.UserDetailData>>('/user/detail', data)
}

/**
 * @description 获取用户列表
 * @param {UserApi.IGetUserList} data
 * @return {*}
 */
export const getUserList = async (data: UserApi.IGetUserList) => {
  return await service.post<ApiResponse<UserApi.UserListData>>('/user/getUserPageByRoleId', data)
}

/**
 * @description 根据ID重置用户密码
 * @param {UserApi.IResetPassword} data
 * @return {*}
 */
export const resetPassword = async (data: UserApi.IResetPassword) => {
  return await service.post<ApiResponse<boolean>>('/user/resetPassword', data)
}

/**
 * @description 修改某个用户信息
 * @param {UserApi.IUpdateUser} data
 * @return {*}
 */
export const updateUser = async (data: UserApi.IUpdateUser) => {
  return await service.post<ApiResponse<boolean>>('/user/updateUserInfo', data)
}

/**
 * @description 用户账号冻结/解冻
 * @param {UserApi.IUpdateUserStatus} data
 * @return {*}
 */
export const updateUserStatus = async (data: UserApi.IUpdateUserStatus) => {
  return await service.post<ApiResponse<boolean>>('/user/update/status', data)
}

/**
 * @description 修改当前用户的邮箱或手机号
 * @param {UserApi.IUpdateCurrentUserInfo} data
 * @return {*}
 */
export const updateCurrentUserInfo = async (data: UserApi.IUpdateCurrentUserInfo) => {
  return await service.post<ApiResponse<boolean>>('/user/updatePhoneOrEmail', data)
}

/**
 * @description 当前用户修改密码
 * @param {UserApi.IUpdatePassword} data
 * @return {*}
 */
export const updatePassword = async (data: UserApi.IUpdatePassword) => {
  return await service.post<ApiResponse<boolean>>('/user/updatePassword', data)
}

/**
 * @description 修改用户角色信息
 * @param {UserApi.IUpdateUserRole} data
 * @return {*}
 */
export const updateUserRole = async (data: UserApi.IUpdateUserRole) => {
  return await service.post<ApiResponse<boolean>>('/user/updateRole', data)
}

/**
 * @description 获取当前用户信息
 * @return {*}
 */
export const getUserInfo = async () => {
  return await service.post<ApiResponse<UserApi.UserDetailData>>('/user/getUserInfo')
}

/**
 * @description 获取角色列表
 * @return {*}
 */
export const getRoleList = async () => {
  return await service.post<ApiResponse<UserApi.RoleInfoListData>>('/user/getCurrentSysRoleList')
}

/**
 * @description 获取用户最后一次访问的集群地址
 * @return {*}
 */
export const getRecords = async () => {
  return await service.get<
    ApiResponse<{
      url: string
      namespaceId: stringOrNumber
      namespaceTitle: string
      deployMode: stringOrNumber
    }>
  >('/access/records')
}

/**
 * @description 保存用户最后一次进入的集群地址
 * @param {UserApi.SaveRecords} data
 * @return {*}
 */
export const saveRecords = async (data: UserApi.SaveRecords) => {
  return await service.post<ApiResponse<any>>('/access/genRecords', data)
}

/**
 * @description 获取所有租户信息
 * @return {*}
 */
export const getAllTenantInfo = async () => {
  return await service.post<ApiResponse<any>>('/tenant/getAllTenantInfo')
}

/**
 * @description 获取所有租户信息
 * @return {*}
 */
export const getTenantList = async (data: UserApi.GetTenantList) => {
  return await service.post<ApiResponse<any>>('/tenant/list', data)
}

/**
 * @description 添加租户
 * @return {*}
 */
export const addTenant = async (data: UserApi.AddTenant) => {
  return await service.post<ApiResponse<any>>('/authentication/tenant/add', data)
}

/**
 * @description 编辑租户
 * @return {*}
 */
export const updateTenant = async (data: UserApi.UpdateTenant) => {
  return await service.post<ApiResponse<any>>('/authentication/tenant/update', data)
}

/**
 * @description 变更租户状态
 * @return {*}
 */
export const updateTenantState = async (data: UserApi.UpdateTenantState) => {
  return await service.get<ApiResponse<any>>(`/authentication/tenant/updateState?state=${data.state}&tenantId=${data.tenantId}`)
}

/**
 * @description 重置租户密码
 * @return {*}
 */
export const resetTenantPassword = async (data: UserApi.ResetTenantPassword) => {
  return await service.get<ApiResponse<any>>(`/authentication/tenant/resetPassword?loginName=${data.loginName}`)
}

/**
 * @description 删除租户
 * @return {*}
 */
export const deleteTenant = async (data: UserApi.DeleteTenant) => {
  return await service.get<ApiResponse<any>>(`/authentication/tenant/delete?tenantId=${data.tenantId}`)
}

/**
 * @description 获取CE所有租户
 * @return {*}
 */
export const tenantAll = async () => {
  return await service.post<ApiResponse<any>>(`/tenant/listAll`, {})
}

/**
 * @description 获取EngineManager的版本信息
 * @return {*}
 */
export const getVersion = async () => {
  return await service.get<ApiResponse<any>>(`/enum/version`)
}

export const getTenatPermission = async (data: UserApi.GetTenatPermission) => {
  return await service.post<ApiResponse<any>>(`/tenant/selectPermissionsByTenant`, data)
}

export const updateTenatPermission = async (data: UserApi.UpdateTenatPermission) => {
  return await service.post<ApiResponse<any>>(`/tenant/updatePermissionsByTenant`, data)
}

export const getTerminalVisible = () => {
  return service.get<ApiResponse<any>>(`/ops/globalConf/terminal/visible`)
}

export const setTerminalVisible = (data: { visible: boolean }) => {
  return service.post<ApiResponse<any>>(`/ops/globalConf/terminal/visible`, data)
}

export const getLogoInfo = () => {
  return service.get<ApiResponse<any>>(`/ops/globalConf/query/logoInfo`)
}

export const updateLogoInfo = (data: {
  browserTab: File
  companyName: string
  logo: File
  logoInfo: number
  logoStatus: number
  platformDescribe: string
  platformName: string
  versionStatus: number
}) => {
  return uploadService().post<ApiResponse<any>>(`/ops/ops/globalConf/update/logoInfo`, data)
}

/**
 * @description 获取License详情
 * @return {Promise<ApiResponse<UserApi.LicenseDetail>>}
 */
export const getLicenseDetail = async () => {
  return service.get<ApiResponse<UserApi.LicenseDetail>>('/license/detail')
}
