<template>
  <div class="columns-filter">
    <el-dropdown
      ref="dropdownRef"
      trigger="click"
      :hide-on-click="false"
      min-height="380px"
      placement="bottom"
      popper-class="columns-filter-popper"
      @visible-change="visibleChange"
    >
      <span class="columns-filter"><i class="iconfont icon-rizhi-shaixuan" /></span>
      <template #dropdown>
        <el-dropdown-menu>
          <div class="check-all">
            <el-checkbox v-model="checkAll" :label="$t('button.selectAll')" :indeterminate="isIndeterminate" @change="handleCheckAllChange" />
            <span class="reset" @click="onReset">{{ $t('button.reset') }}</span>
          </div>
          <el-checkbox-group v-model="checkList" @change="handleCheckedChange">
            <el-dropdown-item v-for="item in props.columns" :key="item.label">
              <el-checkbox :label="item.label" :checked="item.default" :disabled="item.default" />
            </el-dropdown-item>
          </el-checkbox-group>
        </el-dropdown-menu>
        <!-- <el-dropdown-item> -->
        <div class="btn-list">
          <BaseButton type="primary" @click="onChange">{{ $t('button.sure') }}</BaseButton>
          <BaseButton type="info" @click="clone">{{ $t('button.cancel') }}</BaseButton>
        </div>
        <!-- </el-dropdown-item> -->
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import type { CheckboxValueType } from 'element-plus'
interface Props {
  columns: Array<{
    label: string
    default?: boolean
  }>
}
const props = withDefaults(defineProps<Props>(), {
  columns: () => [
    {
      label: t('replenish.time'),
      default: true
    },
    {
      label: t('replenish.message'),
      default: true
    }
  ]
})
const emits = defineEmits<{
  (e: 'change', value: Array<string>): void
}>()

const dropdownRef = ref()
const isChanged = ref(false)
const initCheckList = ref<any>([])
const checkAll = ref(false)
const isIndeterminate = ref(true)
const checkList = ref<any>([])
// 多选浮窗显隐性改变时触发
function visibleChange(visible: boolean) {
  if (visible) {
    isChanged.value = false
    initCheckList.value = checkList.value
    handleCheckedChange(checkList.value)
  } else {
    !isChanged.value && (checkList.value = initCheckList.value)
  }
}
// 全选切换逻辑
function handleCheckAllChange(val: CheckboxValueType) {
  checkList.value = val ? props.columns.map((item) => item.label) : props.columns.filter((item) => item.default).map((item) => item.label)
  isIndeterminate.value = false
}
// 全选判断逻辑
function handleCheckedChange(value: CheckboxValueType[]): any {
  const checkedCount = value.length
  checkAll.value = checkedCount === props.columns.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < props.columns.length
}
// 确认时触发
function onChange() {
  dropdownRef.value.handleClose()
  isChanged.value = true
  emits('change', checkList.value)
}
// 取消是触发
function clone() {
  dropdownRef.value.handleClose()
}
// 重置时触发
function onReset() {
  checkList.value = props.columns.filter((item) => item.default).map((item) => item.label)
  onChange()
}
</script>

<style lang="scss" scoped>
.columns-filter {
  cursor: pointer;
}
</style>
<style lang="scss">
.columns-filter-popper {
  position: relative;
  width: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 0;
  background-color: var(--ops-bg-white-color);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 6%);
  .reset {
    position: absolute;
    top: 16px;
    // top:0px;
    right: 0;
    z-index: 2;
    font-size: 14px;
    color: var(--ops-primary-color);
    cursor: pointer;
  }
  .el-dropdown-menu {
    padding: 0 12px;
    font-size: 18px;
    .check-all {
      position: relative;
      display: flex;
      justify-content: space-between;
      padding-top: 10px;
      font-size: 14px;
      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        width: 100%;
        height: 1px;
        background-color: #e6e9f4;
      }
    }
    .el-checkbox-group {
      margin-top: 10px;
    }
    .el-dropdown-menu__item {
      display: flex;
      padding: 0;
    }
    .el-dropdown-menu__item.el-dropdown-menu__item--divided {
      &::before {
        margin: 0;
      }
    }
  }
  .btn-list {
    @include flex(flex-end);
    padding: 8px 0;
    border-top: 1px solid var(--ops-border-color);
    margin: 0 12px;
  }
}
</style>
