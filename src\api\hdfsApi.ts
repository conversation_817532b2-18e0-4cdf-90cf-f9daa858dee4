import service from '@/utils/Http'

export const getLitterFileConfigSelect = (data: HdfsApi.getLitterFileConfigSelect) => {
  return service.post<ApiResponse<any>>('/component/hdfs/tinyFile/config/select', data)
}

export const litterFileConfigUpdateOrSave = (data: HdfsApi.litterFileConfigUpdateOrSave) => {
  return service.post<ApiResponse<any>>('/component/hdfs/tinyFile/config/updateOrSave', data)
}

export const getLitterFileList = (data: HdfsApi.getLitterFileList) => {
  return service.post<ApiResponse<any>>('/component/hdfs/tinyFile/page', data)
}
