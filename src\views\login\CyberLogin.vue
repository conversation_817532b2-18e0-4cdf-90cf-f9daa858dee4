<template>
  <DcLogin
    productKey="CyberEngine"
    :useLocale="false"
    :baseUrl="baseUrl"
    :title="logoInfo.platformName || '  '"
    :productDesc="logoInfo.platformDescribe"
    :logo="logoImg"
    :logoDesc="logoInfo.versionStatus === 1 ? versionInfo : ''"
    :bottomDesc="logoInfo.companyName"
    :showLogo="logoInfo.logoStatus === 1"
    :autoLogin="false"
    :locale="language"
    :multipointLogin="multipointLogin"
    @loginSuccess="loginSuccess"
  />
</template>
<script lang="ts" setup>
import defaultLogoImg from '@/assets/img/logo.png'

import 'cyber-login/style.css'
import { getRecords, getVersion } from '@/api/userApi'
import { getClusterInfo } from '@/api/clusterApi'
import Base from '@/utils/Base'
import { bus } from '@/utils/mitt'
const { router, store } = useBasicTool()
const versionInfo = ref('')
import { LStorage } from '@/utils/storage'
import { DcLogin } from 'dc-components'
import 'dc-components/index.css'
const language = computed(() => store.state.app.language)
const baseUrl = computed(() => {
  return '/api/authentication'
  if (Base.IS_DEV) return import.meta.env.VITE_SERVER_PROXY_TARGET + '/api/authentication'
  else return '/api/authentication'
})

const logoInfo = computed(() => store.state.user.logoInfo)

const multipointLogin = ref(false)

const logoImg = computed(() => {
  if (logoInfo.value.logoInfo === 1 && logoInfo.value.logo) {
    return `data:image/png;base64,${logoInfo.value.logo}`
  } else {
    return defaultLogoImg
  }
})

function loginSuccess(data: any) {
  store.commit('user/setUserName', data.loginName)
  const { jwtToken } = data
  // Base.setCookie(Base.cookie, jwtToken, Base.default_exp_value)
  LStorage.set(Base.cookie, jwtToken)
  store.dispatch('user/getUserInfo').then(() => {
    store.dispatch('app/getNamespaceType')
    isLastCluster()
    bus.emit('connectWebSocket')
  })
}

function isLastCluster() {
  getRecords().then((res) => {
    if (res.data.data) {
      const { namespaceId, namespaceTitle, deployMode } = res.data.data
      if (!namespaceId) {
        router.push({ name: 'namespace' })
        return
      }
      getClusterInfo({ id: namespaceId }).then((resT) => {
        if (resT.data.data) {
          store.commit('namespace/setNamespaceName', namespaceTitle)
          store.commit('component/setNamespaceId', namespaceId)
          store.commit('namespace/setMeunCurrentNamespaceTitle', namespaceTitle)
          store.commit('component/setDeployMode', deployMode)
          router.push({
            name: 'namespaceOverview',
            query: { namespaceId, namespaceTitle }
          })
        } else {
          router.push({ name: 'namespace' })
        }
      })
    } else {
      router.push({ name: 'namespace' })
    }
  })
}

onMounted(() => {
  store.dispatch('user/getLogoInfo')
  getVersion().then((res) => {
    const { version, hash } = res.data.data
    versionInfo.value = `${version}`
    if ('multipointLogin' in res.data.data) {
      multipointLogin.value = res.data.data.multipointLogin
    }
  })

  console.log('multipointLogin', multipointLogin.value)
})
</script>
<style lang="scss" scoped></style>
