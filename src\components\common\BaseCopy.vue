<template>
  <SvgIcon
    :style="{
      width: props.size,
      height: props.size,
      fontSize: props.size
    }"
    class="base-copy"
    prefix="icon"
    name="icon_fuzhi"
    :color="props.color"
    @click="copyText"
  />
</template>

<script setup lang="ts">
import { copy } from '@/utils/text'
import { ElMessage } from 'element-plus'
const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: '14px'
  },
  color: {
    type: String,
    default: 'inherit'
  }
})
const { t } = useBasicTool()
const copyText = () => {
  copy(props.text)
  ElMessage.success(t('replenish.copySuccess'))
}
</script>
<style lang="scss">
.base-copy {
  display: inline-block;
  margin-left: 4px;
  vertical-align: middle;
  cursor: pointer;
}
</style>
