// esApi.ts
import service, { downService } from '@/utils/Http'
import { getPath } from '@/utils/Path'
/**
 * @description 根据条件查询日志
 * @param {EsApi.IGetAllEs} data
 * @return {*}
 */
export const getAllEs = async (data: EsApi.IGetAllEs) => {
  return await service.post<ApiResponse<PageList<EsApi.GetAllEsData>>>('/es/get/all', data)
}

/**
 * @description 根据条件审计列表
 * @param {EsApi.GetAuditList} data
 * @return {*}
 */
export const getAuditList = async (data: EsApi.GetAuditList) => {
  return await service.post<ApiResponse<PageList<EsApi.GetAuditListData>>>('/operationLog/logList', data)
}

/**
 * @description 根据条件审计列表
 * @param {EsApi.GetAuditList} data
 * @return {*}
 */
export const downloadAudit = async (data: EsApi.DownloadAudit) => {
  return await downService().post(`${getPath('/operationLog/export')}`, data)
}

/**
 * @description 根据条件日志列表
 * @param {EsApi.GetAuditList} data
 * @return {*}
 */
export const downloadEs = async (data: EsApi.IGetAllEs) => {
  return await downService().post(`${getPath('/es/logDownload')}`, data)
}
