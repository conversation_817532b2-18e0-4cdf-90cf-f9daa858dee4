import { pushScopeId, popScopeId, defineComponent, ref, onMounted, watch, openBlock, createBlock, unref, withCtx, createElementVNode, createVNode, createTextVNode, toDisplayString, createElementBlock, Fragment, renderList, createCommentVNode } from 'vue';
import { ElDropdown, ElDropdownMenu, ElCheckbox, ElButton, ElCheckboxGroup, ElDropdownItem, ElIcon } from 'element-plus';
import 'element-plus/es/components/button/style/css';
import 'element-plus/es/components/checkbox/style/css';
import 'element-plus/es/components/checkbox-group/style/css';
import 'element-plus/es/components/dropdown/style/css';
import 'element-plus/es/components/dropdown-item/style/css';
import 'element-plus/es/components/dropdown-menu/style/css';
import 'element-plus/es/components/icon/style/css';
import { Operation } from '@element-plus/icons-vue';
import './ColumnsFilter.vue_vue_type_style_index_0_scoped_true_lang.mjs';
import _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';

const _withScopeId = (n) => (pushScopeId("data-v-7570be4d"), n = n(), popScopeId(), n);
const _hoisted_1 = { class: "dc-table-columns-filter_icon" };
const _hoisted_2 = { class: "dc-table-columns-filter_dropdown-box" };
const _hoisted_3 = { class: "dc-table-columns-filter_operator" };
const _hoisted_4 = { class: "dc-table-columns-filter_button-box" };
const _sfc_main = defineComponent({
  __name: "ColumnsFilter",
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    filterSubmitText: {
      type: String
    },
    filterCancelText: {
      type: String
    },
    filterAllText: {
      type: String
    },
    filterResetText: {
      type: String
    }
  },
  emits: ["change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const dropdownRef = ref();
    const isChanged = ref(false);
    const initCheckData = ref([]);
    const checkAll = ref(false);
    const isIndeterminate = ref(true);
    const checkList = ref([]);
    function visibleChange(visible) {
      if (visible) {
        isChanged.value = false;
        initCheckData.value = checkList.value;
        handleCheckedChange(checkList.value);
      } else {
        !isChanged.value && (checkList.value = initCheckData.value);
      }
    }
    onMounted(() => {
      initCheckList();
    });
    function initCheckList() {
      checkList.value = props.columns.filter((item) => item.label && (item.columnDefault || item.columnRequired)).map((item) => item.label);
      onChange();
    }
    watch(() => props.columns, () => {
      initCheckList();
    });
    function handleCheckAllChange(val) {
      checkList.value = val ? props.columns.map((item) => item.label) : props.columns.filter((item) => item.label && item.columnRequired).map((item) => item.label);
      const length = props.columns.filter((item) => item.label).length;
      if (checkList.value.length < length && !val) {
        isIndeterminate.value = true;
      } else {
        isIndeterminate.value = false;
      }
    }
    function handleCheckedChange(value) {
      const checkedCount = value.length;
      const length = props.columns.filter((item) => item.label).length;
      checkAll.value = checkedCount === length;
      isIndeterminate.value = checkedCount > 0 && checkedCount < length;
    }
    function onChange() {
      dropdownRef.value.handleClose();
      isChanged.value = true;
      emits("change", checkList.value);
    }
    function clone() {
      dropdownRef.value.handleClose();
    }
    function onReset() {
      initCheckList();
    }
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElDropdown), {
        ref_key: "dropdownRef",
        ref: dropdownRef,
        trigger: "click",
        "hide-on-click": false,
        "min-height": "380px",
        placement: "right",
        onVisibleChange: visibleChange
      }, {
        dropdown: withCtx(() => [
          createElementVNode("div", _hoisted_2, [
            createVNode(unref(ElDropdownMenu), null, {
              default: withCtx(() => [
                createElementVNode("div", _hoisted_3, [
                  createVNode(unref(ElCheckbox), {
                    modelValue: checkAll.value,
                    "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => checkAll.value = $event),
                    label: __props.filterAllText ?? "\u5168\u9009",
                    indeterminate: isIndeterminate.value,
                    onChange: handleCheckAllChange
                  }, null, 8, ["modelValue", "label", "indeterminate"]),
                  createVNode(unref(ElButton), {
                    link: "",
                    onClick: onReset
                  }, {
                    default: withCtx(() => [
                      createTextVNode(toDisplayString(__props.filterResetText ?? "\u91CD\u7F6E"), 1)
                    ]),
                    _: 1
                  })
                ]),
                createVNode(unref(ElCheckboxGroup), {
                  modelValue: checkList.value,
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => checkList.value = $event),
                  onChange: handleCheckedChange
                }, {
                  default: withCtx(() => [
                    (openBlock(true), createElementBlock(Fragment, null, renderList(props.columns, (item) => {
                      return openBlock(), createElementBlock(Fragment, null, [
                        item.label ? (openBlock(), createBlock(unref(ElDropdownItem), {
                          key: item.label
                        }, {
                          default: withCtx(() => [
                            createVNode(unref(ElCheckbox), {
                              label: item.label,
                              checked: item.columnRequired || item.columnDefault,
                              disabled: item.columnRequired
                            }, null, 8, ["label", "checked", "disabled"])
                          ]),
                          _: 2
                        }, 1024)) : createCommentVNode("v-if", true)
                      ], 64);
                    }), 256))
                  ]),
                  _: 1
                }, 8, ["modelValue"])
              ]),
              _: 1
            }),
            createElementVNode("div", _hoisted_4, [
              createElementVNode("div", null, [
                createVNode(unref(ElButton), {
                  type: "primary",
                  size: "small",
                  onClick: onChange
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(__props.filterSubmitText ?? "\u786E\u5B9A"), 1)
                  ]),
                  _: 1
                }),
                createVNode(unref(ElButton), {
                  size: "small",
                  onClick: clone
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(__props.filterCancelText ?? "\u53D6\u6D88"), 1)
                  ]),
                  _: 1
                })
              ])
            ])
          ])
        ]),
        default: withCtx(() => [
          createElementVNode("span", _hoisted_1, [
            createVNode(unref(ElIcon), null, {
              default: withCtx(() => [
                createVNode(unref(Operation))
              ]),
              _: 1
            })
          ])
        ]),
        _: 1
      }, 512);
    };
  }
});
var ColumnsFilter = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-7570be4d"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\table\\src\\components\\ColumnsFilter.vue"]]);

export { ColumnsFilter as default };
//# sourceMappingURL=ColumnsFilter.mjs.map
