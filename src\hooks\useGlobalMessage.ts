import Base from '@/utils/Base'
import { LStorage } from '@/utils/storage'
export default function useGlobalMessage(tip: Function) {
  const socket = ref<WebSocket>()
  const reconnectInterval = 2000 // 重连间隔时间（毫秒）
  let reconnectTimer: any = null
  const IS_DEV = process.env.NODE_ENV === 'development'
  const MessageType: any = {
    ERROR: 'error',
    SUCCESS: 'success'
  }
  function connectWebSocket() {
    const token = LStorage.get(Base.cookie)
    let url = ''
    if (IS_DEV) {
      url =
        (window.location.protocol === 'https:' ? 'wss://' : 'ws://') +
        import.meta.env.VITE_SERVER_PROXY_TARGET?.split('//')[1] +
        'api/ops/webssh/em-ws'
    } else {
      url = (window.location.protocol === 'https:' ? 'wss://' : 'ws://') + window.location.host + '/api/ops/webssh/em-ws'
    }

    socket.value = new WebSocket(url, [token])

    socket.value.onopen = () => {
      console.log('WebSocket连接已建立')
      if (reconnectTimer) {
        // 清除之前的重连定时器
        clearInterval(reconnectTimer)
      }
    }
    socket.value.onmessage = (event) => {
      const data = JSON.parse(event.data)
      onMessage(data)
    }

    socket.value.onclose = () => {
      console.log('WebSocket连接已关闭')
      // reconnect(); // 连接关闭时触发重连
    }
    socket.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
      reconnect() // 连接错误时触发重连
    }
  }
  function reconnect() {
    if (reconnectTimer) {
      // 清除之前的重连定时器
      clearInterval(reconnectTimer)
    }
    if (socket.value?.readyState === 1) {
      clearInterval(reconnectTimer)
      return
    }
    reconnectTimer = setInterval(() => {
      console.log('尝试重新连接...')
      connectWebSocket()
    }, reconnectInterval)
  }
  // 关闭事件
  function close() {
    socket.value?.close()
  }

  function onMessage(data: { msgState: string; msg: string; timeStamp: number }) {
    data.msgState = MessageType[data.msgState]
    tip(data)
  }
  return { connectWebSocket, close }
}
