<template>
  <div class="base-collapse">
    <el-collapse v-bind="$attrs">
      <slot></slot>
    </el-collapse>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="scss" scoped>
.base-collapse {
  :deep(.el-collapse) {
    --el-collapse-border-color: var(--ops-border-color);
    --el-collapse-header-bg-color: #fafbfc;
    border-top: 1px solid var(--ops-border-color);
    border-bottom: 1px solid var(--ops-border-color);
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 0px;
  }
  :deep(.el-collapse-item__arrow) {
    margin: 0 20px 0 auto;
  }
}
</style>
