{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/render-vnode/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport RenderVNode from \"./src/index\";\r\n\r\nexport const DcRenderVNode = withInstall(RenderVNode);\r\n\r\nexport default DcRenderVNode;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": ["withInstall", "RenderVNode"], "mappings": ";;;;;;;;AAIa,MAAA,aAAA,GAAgBA,qBAAYC,gBAAW;;;;;"}