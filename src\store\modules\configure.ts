import { Module } from 'vuex'
import { FuzzyQuery } from '@/utils/FuzzyQuery'
import { download } from '@/utils/Download'
import { downloadConfigStreamByServiceInstanceId } from '@/api/configureApi'
import { newgetMergeConfigsByClusterId, newUpdateAllConfigsByClusterId } from '@/api/clusterServiceConfigApi'
interface StoreUser {
  configureList: Array<object>
  viewData: Array<object>
  searchConfigureList: Array<object>
  total: number
  editRecords: Array<object>
}

const store: Module<StoreUser, unknown> = {
  namespaced: true,
  state() {
    return {
      configureList: [],
      viewData: [],
      searchConfigureList: [],
      total: 0,
      editRecords: [] // 上次保存的数据
    }
  },
  mutations: {
    SET_CONFIGURE_LIST(state, value) {
      state.configureList = value
    },
    SET_VIEW_DATA(state, value) {
      state.viewData = value
    },
    SET_SEARCH_CONFIGURE_LIST(state, value) {
      state.searchConfigureList = value
    },
    SET_TOTAL(state, value) {
      state.total = value
    },
    SET_EDIT_RECORDS(state, value) {
      state.editRecords = value
    },
    SET_UNSHIFT_CONFIGURE_LIST(state, value) {
      state.searchConfigureList.unshift(...value)
      state.configureList.unshift(...value)
    }
  },
  actions: {
    // 获取configure列表
    getConfigureList({ commit }, value) {
      const parameter: clusterServiceConfigApi.newgetMergeConfigsByClusterId = {
        clusterServiceId: value.clusterServiceId,
        clusterServiceInstanceId: value.clusterServiceInstanceId
      }
      return new Promise((resolve, reject) => {
        newgetMergeConfigsByClusterId(parameter)
          .then((response) => {
            const result = response.data
            commit('SET_EDIT_RECORDS', result.data.editRecords)
            commit('SET_CONFIGURE_LIST', result.data.mergeConfigs)
            commit('SET_SEARCH_CONFIGURE_LIST', result.data.mergeConfigs)
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    reverseGetConfigureList({ commit }, value) {
      const parameter: clusterServiceConfigApi.newgetMergeConfigsByClusterId = {
        clusterServiceId: value.clusterServiceId,
        clusterServiceInstanceId: value.clusterServiceInstanceId
      }
      return new Promise((resolve, reject) => {
        newgetMergeConfigsByClusterId(parameter)
          .then((response) => {
            const result = response.data
            commit('SET_EDIT_RECORDS', result.data.editRecords)
            commit('SET_CONFIGURE_LIST', result.data.mergeConfigs?.reverse())
            commit('SET_SEARCH_CONFIGURE_LIST', result.data.mergeConfigs?.reverse())
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    // 前端模拟分页查询
    getConfigureListByPage({ commit, state }, value) {
      commit('SET_VIEW_DATA', [])
      const parameter: ConfigureApi.IGetConfigureListByPage = {
        pageNo: value.pageNo,
        pageSize: value.pageSize,
        confKey: value.searchValue,
        fileName: value.fileName,
        roleName: value.roleName,
        labelType: value.labelType
      }
      return new Promise((resolve) => {
        const start = (Number(parameter.pageNo) - 1) * Number(parameter.pageSize)
        const end = start + Number(parameter.pageSize) - 1
        const confKey = parameter.confKey || ''
        const fileName = parameter.fileName || ''
        const roleName = parameter.roleName || ''
        const labelType = parameter.labelType ?? ''
        let result: any = []
        if (confKey || fileName || roleName || labelType) {
          const searchList = state.configureList.filter((item: any, index: number) => {
            return FuzzyQuery(confKey, item.confKey)
          })
          const againSearchList = searchList.filter((item: any, index: number) => {
            return FuzzyQuery(fileName, item.confName)
          })
          const roleNameSearchList = againSearchList.filter((item: any, index: number) => {
            return roleName === item.roleNameSelect || roleName === '' || !item.roleNameSelect
          })
          const labelTypeSearchList = roleNameSearchList.filter((item: any, index: number) => {
            return labelType === item.labelType || labelType === ''
          })
          commit('SET_TOTAL', labelTypeSearchList.length)
          commit('SET_SEARCH_CONFIGURE_LIST', labelTypeSearchList)
        } else {
          commit('SET_TOTAL', state.configureList.length)
          commit('SET_SEARCH_CONFIGURE_LIST', state.configureList)
        }
        result = state.searchConfigureList.filter((item: any, index: number) => index >= start && index <= end)
        commit('SET_VIEW_DATA', result)
        resolve(result)
      })
    },
    // 修改配置文件信息
    updateAllConfigsByClusterId(_, value) {
      const parameter: clusterServiceConfigApi.newUpdateAllConfigsByClusterId = {
        ...value,
        configRequestDtos: value.configRequestDtos,
        modifyReason: value.modifyReason,
        clusterServiceId: value.clusterServiceId,
        clusterServiceInstanceId: value.clusterServiceInstanceId,
        applied: value.applied,
        recordId: value.recordId,
        customDelete: value.customDelete
      }
      return new Promise((resolve, reject) => {
        newUpdateAllConfigsByClusterId(parameter)
          .then((response: any) => {
            resolve(response)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    // 组件实例配置下载
    configDownload(_, value) {
      const parameter: ConfigureApi.IDownloadConfigStreamByServiceInstanceId = {
        clusterServiceInstanceId: value.clusterServiceInstanceId
      }
      return new Promise((resolve, reject) => {
        downloadConfigStreamByServiceInstanceId(parameter)
          .then((response) => {
            const { data, headers } = response
            const filename = headers['content-disposition'].match(/fileName=(.*)$/)[1]
            download(data, filename)
            resolve(response)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    }
  }
}

export default store
