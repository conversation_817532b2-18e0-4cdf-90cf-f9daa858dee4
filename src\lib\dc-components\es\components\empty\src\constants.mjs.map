{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/empty/src/constants.ts"], "sourcesContent": ["import {\r\n  api,\r\n  bloodDark,\r\n  bloodLight,\r\n  dark404,\r\n  dark405,\r\n  echarts,\r\n  feature,\r\n  file,\r\n  image,\r\n  imagePlaceholder,\r\n  light404,\r\n  light405,\r\n  listDark,\r\n  listLight,\r\n  message,\r\n  model,\r\n  networkDark,\r\n  networkLight,\r\n  powerDark,\r\n  powerLight,\r\n  unListDark,\r\n  unListLight,\r\n} from \"./images/index\";\r\n\r\nexport const imgConfig = {\r\n  light: {\r\n    list: listLight,\r\n    unList: unListLight,\r\n    echarts,\r\n    file,\r\n    power: powerLight,\r\n    feature,\r\n    blood: bloodLight,\r\n    model,\r\n    network: networkLight,\r\n    \"404\": light404,\r\n    \"405\": light405,\r\n    message,\r\n    image,\r\n    imagePlaceholder,\r\n    api,\r\n  },\r\n  dark: {\r\n    list: listDark,\r\n    unList: unListDark,\r\n    echarts,\r\n    file,\r\n    power: powerDark,\r\n    feature,\r\n    blood: bloodDark,\r\n    model,\r\n    network: networkDark,\r\n    \"404\": dark404,\r\n    \"405\": dark405,\r\n    message,\r\n    image,\r\n    imagePlaceholder,\r\n    api,\r\n  },\r\n};\r\n\r\nexport const descConfig = {\r\n  \"zh-CN\": {\r\n    list: \"暂无数据\",\r\n    unList: \"暂无内容\",\r\n    echarts: \"暂无图表\",\r\n    file: \"暂未添加文件\",\r\n    power: \"暂无权限\",\r\n    feature: \"暂无该功能\",\r\n    blood: \"暂无血缘信息\",\r\n    model: \"暂无模型，请先新建\",\r\n    network: \"网络中断，请刷新重试\",\r\n    \"404\": \"404\",\r\n    \"405\": \"405\",\r\n    message: \"暂无消息\",\r\n    image: \"暂无图片，请先添加\",\r\n    imagePlaceholder: \"图片占位\",\r\n    api: \"暂无API，请先新建\",\r\n  },\r\n  \"en-US\": {\r\n    list: \"No data\",\r\n    unList: \"No content\",\r\n    echarts: \"No chart\",\r\n    file: \"No files added\",\r\n    power: \"No permission\",\r\n    feature: \"This function is not available\",\r\n    blood: \"No bloodline information\",\r\n    model: \"There is no model, please create a new one first\",\r\n    network: \"The network is interrupted, please refresh and try again.\",\r\n    \"404\": \"404\",\r\n    \"405\": \"405\",\r\n    message: \"No news\",\r\n    image: \"There are no pictures, please add them first\",\r\n    imagePlaceholder: \"Picture placeholder\",\r\n    api: \"There is no API, please create a new one first\",\r\n  },\r\n};\r\n"], "names": ["listLight", "unListLight", "echarts", "file", "powerLight", "feature", "bloodLight", "model", "networkLight", "light404", "light405", "message", "image", "imagePlaceholder", "api", "listDark", "unListDark", "powerDark", "bloodDark", "networkDark", "dark404", "dark405"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBY,MAAC,SAAS,GAAG;AACzB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,GAAS;AACnB,IAAI,MAAM,EAAEC,KAAW;AACvB,aAAIC,KAAO;AACX,UAAIC,KAAI;AACR,IAAI,KAAK,EAAEC,KAAU;AACrB,aAAIC,KAAO;AACX,IAAI,KAAK,EAAEC,KAAU;AACrB,WAAIC,KAAK;AACT,IAAI,OAAO,EAAEC,KAAY;AACzB,IAAI,KAAK,EAAEC,KAAQ;AACnB,IAAI,KAAK,EAAEC,KAAQ;AACnB,aAAIC,KAAO;AACX,WAAIC,KAAK;AACT,sBAAIC,KAAgB;AACpB,SAAIC,KAAG;AACP,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,KAAQ;AAClB,IAAI,MAAM,EAAEC,KAAU;AACtB,aAAId,KAAO;AACX,UAAIC,KAAI;AACR,IAAI,KAAK,EAAEc,KAAS;AACpB,aAAIZ,KAAO;AACX,IAAI,KAAK,EAAEa,KAAS;AACpB,WAAIX,KAAK;AACT,IAAI,OAAO,EAAEY,KAAW;AACxB,IAAI,KAAK,EAAEC,KAAO;AAClB,IAAI,KAAK,EAAEC,KAAO;AAClB,aAAIV,KAAO;AACX,WAAIC,KAAK;AACT,sBAAIC,KAAgB;AACpB,SAAIC,KAAG;AACP,GAAG;AACH,EAAE;AACU,MAAC,UAAU,GAAG;AAC1B,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,0BAA0B;AACpC,IAAI,MAAM,EAAE,0BAA0B;AACtC,IAAI,OAAO,EAAE,0BAA0B;AACvC,IAAI,IAAI,EAAE,sCAAsC;AAChD,IAAI,KAAK,EAAE,0BAA0B;AACrC,IAAI,OAAO,EAAE,gCAAgC;AAC7C,IAAI,KAAK,EAAE,sCAAsC;AACjD,IAAI,KAAK,EAAE,wDAAwD;AACnE,IAAI,OAAO,EAAE,8DAA8D;AAC3E,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,OAAO,EAAE,0BAA0B;AACvC,IAAI,KAAK,EAAE,wDAAwD;AACnE,IAAI,gBAAgB,EAAE,0BAA0B;AAChD,IAAI,GAAG,EAAE,+CAA+C;AACxD,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,MAAM,EAAE,YAAY;AACxB,IAAI,OAAO,EAAE,UAAU;AACvB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,KAAK,EAAE,eAAe;AAC1B,IAAI,OAAO,EAAE,gCAAgC;AAC7C,IAAI,KAAK,EAAE,0BAA0B;AACrC,IAAI,KAAK,EAAE,kDAAkD;AAC7D,IAAI,OAAO,EAAE,2DAA2D;AACxE,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,8CAA8C;AACzD,IAAI,gBAAgB,EAAE,qBAAqB;AAC3C,IAAI,GAAG,EAAE,gDAAgD;AACzD,GAAG;AACH;;;;"}