import { defineComponent, ref, openBlock, createElementBlock, normalizeClass, createVNode, unref, withCtx, createBlock, createElementVNode, toDisplayString } from 'vue';
import { ElIcon } from 'element-plus';
import 'element-plus/es/components/icon/style/css';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import './Expand.vue_vue_type_style_index_0_scoped_true_lang.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const clsPrefix = "dc-search-filter-expand";
const _sfc_main = defineComponent({
  __name: "Expand",
  props: {
    expandText: {
      type: String,
      default: "\u5C55\u5F00"
    },
    collapseText: {
      type: String,
      default: "\u6536\u8D77"
    }
  },
  emits: ["toggleExpand"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const isExpand = ref(false);
    const toggle = () => {
      isExpand.value = !isExpand.value;
      emits("toggleExpand", isExpand.value);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(`${clsPrefix}`),
        onClick: toggle
      }, [
        createVNode(unref(ElIcon), null, {
          default: withCtx(() => [
            isExpand.value ? (openBlock(), createBlock(unref(ArrowDown), { key: 0 })) : (openBlock(), createBlock(unref(ArrowUp), { key: 1 }))
          ]),
          _: 1
        }),
        createElementVNode("span", {
          class: normalizeClass(`${clsPrefix}_text`)
        }, toDisplayString(isExpand.value ? props.expandText : props.collapseText), 3)
      ], 2);
    };
  }
});
var Expand = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-e86dc0c8"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\search-filter\\src\\Expand.vue"]]);

export { Expand as default };
//# sourceMappingURL=Expand.mjs.map
