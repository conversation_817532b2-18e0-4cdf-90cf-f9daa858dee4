import type { Ref } from "vue";
import type { Dc<PERSON>uttonConfig } from "../../button";
import type { RenderVNodeFn } from "../../render-vnode/src/types";
import type DcDialog from "./index.vue";
export interface DcDialogAction {
    innerText?: string;
    visible?: boolean | Ref<boolean>;
    componentSlot?: Record<string, RenderVNodeFn>;
    props?: Record<string, any>;
    config?: DcButtonConfig;
}
export declare enum DcDialogTypes {
    HEADER_FOOTER = "header_footer",
    FOOTER = "footer",
    HEADER = "header",
    ONLYCONTENT = "ONLYCONTENT"
}
export declare type DcDialogInstance = InstanceType<typeof DcDialog>;
