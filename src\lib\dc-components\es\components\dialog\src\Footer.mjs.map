{"version": 3, "file": "Footer.mjs", "sources": ["../../../../../../packages/components/dialog/src/Footer.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dc-dialog-footer\">\r\n    <DcRenderVNode v-if=\"props.footerLeftSlot\" :vnode=\"props.footerLeftSlot\" />\r\n    <DcButton\r\n      v-if=\"confirmAction.visible !== false\"\r\n      v-bind=\"{ ...confirmAction.props }\"\r\n      :config=\"confirmAction.config\"\r\n      @click=\"onConfirm\"\r\n    >\r\n      <template\r\n        v-for=\"slotI in Object.keys(confirmAction.componentSlot || {})\"\r\n        #[slotI]=\"scope\"\r\n        :key=\"slotI\"\r\n      >\r\n        <DcRenderVNode\r\n          :vnode=\"(confirmAction.componentSlot || {})[slotI]\"\r\n          :scope=\"scope\"\r\n        />\r\n      </template>\r\n      {{ confirmAction.innerText }}\r\n    </DcButton>\r\n    <DcButton\r\n      v-if=\"cancelAction.visible !== false\"\r\n      v-bind=\"{ ...cancelAction.props }\"\r\n      :config=\"cancelAction.config\"\r\n      @click=\"onCancel\"\r\n    >\r\n      <template\r\n        v-for=\"slotI in Object.keys(cancelAction.componentSlot || {})\"\r\n        #[slotI]=\"scope\"\r\n        :key=\"slotI\"\r\n      >\r\n        <DcRenderVNode\r\n          :vnode=\"(cancelAction.componentSlot || {})[slotI]\"\r\n          :scope=\"scope\"\r\n        />\r\n      </template>\r\n      {{ cancelAction.innerText }}\r\n    </DcButton>\r\n    <DcRenderVNode\r\n      v-if=\"props.footerRightSlot\"\r\n      :vnode=\"props.footerRightSlot\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed } from \"vue\";\r\nimport DcButton from \"../../button/src/index.vue\";\r\nimport DcRenderVNode from \"../../render-vnode/src/index\";\r\nimport type { PropType } from \"vue\";\r\nimport type { RenderVNodeFn } from \"../../render-vnode/src/types\";\r\nimport type { DcDialogAction } from \"./types\";\r\n\r\nconst props = defineProps({\r\n  confirmAction: {\r\n    type: Object as PropType<DcDialogAction>,\r\n    default: () => ({}),\r\n  },\r\n  cancelAction: {\r\n    type: Object as PropType<DcDialogAction>,\r\n    default: () => ({}),\r\n  },\r\n  footerLeftSlot: {\r\n    type: Object as PropType<RenderVNodeFn>,\r\n  },\r\n  footerRightSlot: {\r\n    type: Object as PropType<RenderVNodeFn>,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"cancel\", \"confirm\"]);\r\n\r\nconst defaultConfirmAction: DcDialogAction = {\r\n  innerText: \"确定\",\r\n  props: {\r\n    type: \"primary\",\r\n  },\r\n  config: {\r\n    debounce: true,\r\n  },\r\n};\r\n\r\nconst confirmAction = computed(() =>\r\n  props.confirmAction\r\n    ? {\r\n        ...defaultConfirmAction,\r\n        ...props.confirmAction,\r\n        props: {\r\n          ...defaultConfirmAction.props,\r\n          ...(props.confirmAction.props || {}),\r\n        },\r\n        config: {\r\n          ...defaultConfirmAction.config,\r\n          ...(props.confirmAction.config || {}),\r\n        },\r\n      }\r\n    : defaultConfirmAction\r\n);\r\n\r\nconst defaultCancelAction: DcDialogAction = {\r\n  innerText: \"取消\",\r\n};\r\n\r\nconst cancelAction = computed(() =>\r\n  props.cancelAction\r\n    ? { ...defaultCancelAction, ...props.cancelAction }\r\n    : defaultCancelAction\r\n);\r\n\r\nconst onConfirm = () => {\r\n  emits(\"confirm\");\r\n};\r\n\r\nconst onCancel = () => {\r\n  emits(\"cancel\");\r\n};\r\n</script>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAiBd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAA,MAAM,oBAAuC,GAAA;AAAA,MAC3C,SAAW,EAAA,cAAA;AAAA,MACX,KAAO,EAAA;AAAA,QACL,IAAM,EAAA,SAAA;AAAA,OACR;AAAA,MACA,MAAQ,EAAA;AAAA,QACN,QAAU,EAAA,IAAA;AAAA,OACZ;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,aAAgB,GAAA,QAAA,CAAS,MAC7B,KAAA,CAAM,aACF,GAAA;AAAA,MACE,GAAG,oBAAA;AAAA,MACH,GAAG,KAAM,CAAA,aAAA;AAAA,MACT,KAAO,EAAA;AAAA,QACL,GAAG,oBAAqB,CAAA,KAAA;AAAA,QACxB,GAAI,KAAA,CAAM,aAAc,CAAA,KAAA,IAAS,EAAC;AAAA,OACpC;AAAA,MACA,MAAQ,EAAA;AAAA,QACN,GAAG,oBAAqB,CAAA,MAAA;AAAA,QACxB,GAAI,KAAA,CAAM,aAAc,CAAA,MAAA,IAAU,EAAC;AAAA,OACrC;AAAA,QAEF,oBACN,CAAA,CAAA;AAEA,IAAA,MAAM,mBAAsC,GAAA;AAAA,MAC1C,SAAW,EAAA,cAAA;AAAA,KACb,CAAA;AAEA,IAAA,MAAM,YAAe,GAAA,QAAA,CAAS,MAC5B,KAAA,CAAM,YACF,GAAA,EAAE,GAAG,mBAAA,EAAqB,GAAG,KAAA,CAAM,YAAa,EAAA,GAChD,mBACN,CAAA,CAAA;AAEA,IAAA,MAAM,YAAY,MAAM;AACtB,MAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AAAA,KACjB,CAAA;AAEA,IAAA,MAAM,WAAW,MAAM;AACrB,MAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AAAA,KAChB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}