{"version": 3, "file": "hooks.js", "sources": ["../../../../../../packages/components/view-layout/src/hooks.ts"], "sourcesContent": ["import { type Ref, onMounted, onUnmounted, ref } from \"vue\";\n\ninterface ElementSize {\n  width: Ref<number>;\n  height: Ref<number>;\n}\n\n/**\n * 监听DOM元素尺寸变化的Hook\n * @param target 目标元素的Ref或DOM元素\n * @param options 配置选项\n * @returns 包含width和height的响应式对象\n */\nexport function useElementSize(\n  target: Ref<HTMLElement | null> | HTMLElement | null,\n  options: {\n    initialWidth?: number;\n    initialHeight?: number;\n    debounce?: number;\n  } = {}\n): ElementSize {\n  const width = ref(options.initialWidth || 0);\n  const height = ref(options.initialHeight || 0);\n\n  let observer: ResizeObserver | null = null;\n  let timeoutId: number | null = null;\n\n  const updateSize = (entry: ResizeObserverEntry) => {\n    if (options.debounce) {\n      if (timeoutId) {\n        window.clearTimeout(timeoutId);\n      }\n      timeoutId = window.setTimeout(() => {\n        width.value = entry.contentRect.width;\n        height.value = entry.contentRect.height;\n      }, options.debounce);\n    } else {\n      width.value = entry.contentRect.width;\n      height.value = entry.contentRect.height;\n    }\n  };\n\n  const observeElement = (element: HTMLElement) => {\n    observer = new ResizeObserver((entries) => {\n      if (entries[0]) {\n        updateSize(entries[0]);\n      }\n    });\n    observer.observe(element);\n  };\n\n  onMounted(() => {\n    const element = target instanceof HTMLElement ? target : target?.value;\n\n    if (element) {\n      // 初始尺寸\n      width.value = element.clientWidth;\n      height.value = element.clientHeight;\n\n      observeElement(element);\n    }\n  });\n\n  onUnmounted(() => {\n    if (observer) {\n      observer.disconnect();\n    }\n    if (timeoutId) {\n      window.clearTimeout(timeoutId);\n    }\n  });\n\n  return {\n    width,\n    height,\n  };\n}\n"], "names": ["ref", "onMounted", "onUnmounted"], "mappings": ";;;;;;AAcE,SAAA,cAAA,CAAA,MAAA,EACA,OAII,GAAA,EACS,EAAA;AACb,EAAA,MAAM,KAAQ,GAAAA,OAAA,CAAI,OAAQ,CAAA,YAAA,IAAgB,CAAC,CAAA,CAAA;AAC3C,EAAA,MAAM,MAAS,GAAAA,OAAA,CAAI,OAAQ,CAAA,aAAA,IAAiB,CAAC,CAAA,CAAA;AAE7C,EAAA,IAAI,QAAkC,GAAA,IAAA,CAAA;AACtC,EAAA,IAAI,SAA2B,GAAA,IAAA,CAAA;AAE/B,EAAM,MAAA,UAAA,GAAa,CAAC,KAA+B,KAAA;AACjD,IAAA,IAAI,QAAQ,QAAU,EAAA;AACpB,MAAA,IAAI,SAAW,EAAA;AACb,QAAA,MAAA,CAAO,aAAa,SAAS,CAAA,CAAA;AAAA,OAC/B;AACA,MAAY,SAAA,GAAA,MAAA,CAAO,WAAW,MAAM;AAClC,QAAM,KAAA,CAAA,KAAA,GAAQ,MAAM,WAAY,CAAA,KAAA,CAAA;AAChC,QAAO,MAAA,CAAA,KAAA,GAAQ,MAAM,WAAY,CAAA,MAAA,CAAA;AAAA,OACnC,EAAG,QAAQ,QAAQ,CAAA,CAAA;AAAA,KACd,MAAA;AACL,MAAM,KAAA,CAAA,KAAA,GAAQ,MAAM,WAAY,CAAA,KAAA,CAAA;AAChC,MAAO,MAAA,CAAA,KAAA,GAAQ,MAAM,WAAY,CAAA,MAAA,CAAA;AAAA,KACnC;AAAA,GACF,CAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,CAAC,OAAyB,KAAA;AAC/C,IAAW,QAAA,GAAA,IAAI,cAAe,CAAA,CAAC,OAAY,KAAA;AACzC,MAAA,IAAI,QAAQ,CAAI,CAAA,EAAA;AACd,QAAA,UAAA,CAAW,QAAQ,CAAE,CAAA,CAAA,CAAA;AAAA,OACvB;AAAA,KACD,CAAA,CAAA;AACD,IAAA,QAAA,CAAS,QAAQ,OAAO,CAAA,CAAA;AAAA,GAC1B,CAAA;AAEA,EAAAC,aAAA,CAAU,MAAM;AACd,IAAA,MAAM,OAAU,GAAA,MAAA,YAAkB,WAAc,GAAA,MAAA,GAAS,MAAQ,EAAA,KAAA,CAAA;AAEjE,IAAA,IAAI,OAAS,EAAA;AAEX,MAAA,KAAA,CAAM,QAAQ,OAAQ,CAAA,WAAA,CAAA;AACtB,MAAA,MAAA,CAAO,QAAQ,OAAQ,CAAA,YAAA,CAAA;AAEvB,MAAA,cAAA,CAAe,OAAO,CAAA,CAAA;AAAA,KACxB;AAAA,GACD,CAAA,CAAA;AAED,EAAAC,eAAA,CAAY,MAAM;AAChB,IAAA,IAAI,QAAU,EAAA;AACZ,MAAA,QAAA,CAAS,UAAW,EAAA,CAAA;AAAA,KACtB;AACA,IAAA,IAAI,SAAW,EAAA;AACb,MAAA,MAAA,CAAO,aAAa,SAAS,CAAA,CAAA;AAAA,KAC/B;AAAA,GACD,CAAA,CAAA;AAED,EAAO,OAAA;AAAA,IACL,KAAA;AAAA,IACA,MAAA;AAAA,GACF,CAAA;AACF;;;;"}