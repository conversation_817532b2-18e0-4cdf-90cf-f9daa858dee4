import type { PropType, Ref } from "vue";
import type { DcFormItem, DcFormItemEffect, DcFormItemMargin } from "../../types";
declare const _default: import("vue").DefineComponent<{
    visible: {
        type: PropType<boolean | Ref<boolean>>;
        default: boolean;
    };
    config: {
        type: PropType<DcFormItem>;
        default: () => {};
    };
    modelValue: {
        type: PropType<any>;
    };
    margin: {
        type: PropType<DcFormItemMargin>;
        default: () => {};
    };
    isInline: {
        type: BooleanConstructor;
        default: boolean;
    };
    rowIndex: {
        type: NumberConstructor;
        default: number;
    };
}, {
    props: import("@vue/shared").LooseRequired<any>;
    emits: (event: "update:modelValue" | "triggerEffect", ...args: any[]) => void;
    formItemRef: Ref<any>;
    marginBottom: import("vue").ComputedRef<any>;
    marginRight: import("vue").ComputedRef<any>;
    marginLeft: import("vue").ComputedRef<any>;
    marginTop: import("vue").ComputedRef<any>;
    ruleTrigger: import("vue").ComputedRef<any>;
    handleBlur: () => void;
    triggerEffect: (effect: DcFormItemEffect, rowIndex?: number | undefined) => void;
    itemChange: (val: any) => void;
    readonly ElFormItem: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
        readonly label: StringConstructor;
        readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly prop: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly rules: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly error: StringConstructor;
        readonly validateStatus: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly for: StringConstructor;
        readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
        readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly size: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly label: StringConstructor;
            readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly prop: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
            readonly rules: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly error: StringConstructor;
            readonly validateStatus: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly for: StringConstructor;
            readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
            readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly size: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        slots: Readonly<{
            [name: string]: import("vue").Slot<any> | undefined;
        }>;
        formContext: import("element-plus").FormContext | undefined;
        parentFormItemContext: import("element-plus").FormItemContext | undefined;
        _size: import("vue").ComputedRef<"" | "default" | "small" | "large">;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string | undefined) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        labelId: string;
        inputIds: Ref<string[]>;
        validateState: Ref<"" | "success" | "error" | "validating">;
        validateStateDebounced: Readonly<Ref<"" | "success" | "error" | "validating">>;
        validateMessage: Ref<string>;
        formItemRef: Ref<HTMLDivElement | undefined>;
        initialValue: any;
        isResettingField: boolean;
        labelStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        contentStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        formItemClasses: import("vue").ComputedRef<(string | {
            [x: string]: boolean | undefined;
        })[]>;
        _inlineMessage: import("vue").ComputedRef<boolean>;
        validateClasses: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        propString: import("vue").ComputedRef<string>;
        hasLabel: import("vue").ComputedRef<boolean>;
        labelFor: import("vue").ComputedRef<string | undefined>;
        isGroup: import("vue").ComputedRef<boolean>;
        isNested: boolean;
        fieldValue: import("vue").ComputedRef<any>;
        normalizedRules: import("vue").ComputedRef<import("element-plus").FormItemRule[]>;
        validateEnabled: import("vue").ComputedRef<boolean>;
        getFilteredRule: (trigger: string) => import("async-validator").RuleItem[];
        isRequired: import("vue").ComputedRef<boolean>;
        shouldShowError: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        currentLabel: import("vue").ComputedRef<string>;
        setValidationState: (state: "" | "success" | "error" | "validating") => void;
        onValidationFailed: (error: import("element-plus").FormValidateFailure) => void;
        onValidationSucceeded: () => void;
        doValidate: (rules: import("async-validator").RuleItem[]) => Promise<true>;
        validate: (trigger: string, callback?: import("element-plus").FormValidateCallback | undefined) => import("element-plus").FormValidationResult;
        clearValidate: () => void;
        resetField: () => void;
        addInputId: (id: string) => void;
        removeInputId: (id: string) => void;
        context: import("element-plus").FormItemContext;
        FormLabelWrap: import("vue").DefineComponent<{
            isAutoWidth: BooleanConstructor;
            updateAll: BooleanConstructor;
        }, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            isAutoWidth: BooleanConstructor;
            updateAll: BooleanConstructor;
        }>>, {
            isAutoWidth: boolean;
            updateAll: boolean;
        }, {}>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly label: StringConstructor;
        readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly prop: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly rules: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly error: StringConstructor;
        readonly validateStatus: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly for: StringConstructor;
        readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
        readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly size: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {
        readonly required: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly labelWidth: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
        readonly showMessage: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly inlineMessage: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, BooleanConstructor], unknown, unknown>;
    }, {}>>;
    readonly DcRenderVNode: import("vue").DefineComponent<{
        vnode: {
            type: PropType<import("../../../../render-vnode").RenderVNodeFn>;
            default: () => null;
        };
        scope: {
            type: PropType<Record<string, any>>;
            default: () => {};
        };
        extraScope: {
            type: PropType<Record<string, any>>;
            default: () => {};
        };
    }, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        vnode: {
            type: PropType<import("../../../../render-vnode").RenderVNodeFn>;
            default: () => null;
        };
        scope: {
            type: PropType<Record<string, any>>;
            default: () => {};
        };
        extraScope: {
            type: PropType<Record<string, any>>;
            default: () => {};
        };
    }>>, {
        vnode: import("../../../../render-vnode").RenderVNodeFn;
        scope: Record<string, any>;
        extraScope: Record<string, any>;
    }, {}>;
    readonly Component: any;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("update:modelValue" | "triggerEffect")[], "update:modelValue" | "triggerEffect", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    visible: {
        type: PropType<boolean | Ref<boolean>>;
        default: boolean;
    };
    config: {
        type: PropType<DcFormItem>;
        default: () => {};
    };
    modelValue: {
        type: PropType<any>;
    };
    margin: {
        type: PropType<DcFormItemMargin>;
        default: () => {};
    };
    isInline: {
        type: BooleanConstructor;
        default: boolean;
    };
    rowIndex: {
        type: NumberConstructor;
        default: number;
    };
}>> & {
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
    onTriggerEffect?: ((...args: any[]) => any) | undefined;
}, {
    visible: boolean | Ref<boolean>;
    config: DcFormItem;
    margin: DcFormItemMargin;
    isInline: boolean;
    rowIndex: number;
}, {}>;
export default _default;
