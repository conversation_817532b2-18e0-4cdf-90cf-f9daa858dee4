/*
 * @Author: caiyinnan
 * @Date: 2023-03-10 10:18:52
 * @LastEditors: caiyinnan
 * @LastEditTime: 2023-03-24 18:00:29
 */
import service from '@/utils/Http'

/**
 * @description 获取修改记录列表
 * @param {clusterServiceConfigApi.updateRecordList} data
 * @return {*}
 */

export const getUpdateRecordList = (data: clusterServiceConfigApi.updateRecordList) => {
  return service.post<ApiResponse<PageList<clusterServiceConfigApi.UpdateRecordListRecordsItem>>>('/cluster-service-config/updateRecordList', data)
}

/**
 * @description 获取配置操作页面的文件问下拉内容
 * @param {clusterServiceConfigApi.confNameList} data
 * @return {*}
 */

export const confNameList = (data: clusterServiceConfigApi.confNameList) => {
  return service.post<ApiResponse<Array<string>>>('/cluster-service-config/confNameList', data)
}

/**
 * @description 根据组件服务id获取修改内容
 * @param {clusterServiceConfigApi.newUpdateAllConfigsByClusterId} data
 * @return {*}
 */

export const newUpdateAllConfigsByClusterId = (data: clusterServiceConfigApi.newUpdateAllConfigsByClusterId) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/newUpdateAllConfigsByClusterId', data)
}

/**
 * @description 获取configure列表
 * @param {clusterServiceConfigApi.newgetMergeConfigsByClusterId} data
 * @return {*}
 */

export const newgetMergeConfigsByClusterId = (data: clusterServiceConfigApi.newgetMergeConfigsByClusterId) => {
  return service.post<ApiResponse<clusterServiceConfigApi.ComponentListModelByIdRoleListData>>(
    '/cluster-service-config/newgetMergeConfigsByClusterId',
    data
  )
}

/**
 * @description 回滚接口
 * @param {clusterServiceConfigApi.rollback} data
 * @return {*}
 */

export const rollback = (data: clusterServiceConfigApi.rollback) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/rollback', data)
}

/**
 * @description 查看修改详情与配置快照
 * @param {clusterServiceConfigApi.updateDetail} data
 * @return {*}
 */

export const updateDetail = (data: clusterServiceConfigApi.updateDetail) => {
  return service.post<ApiResponse<PageList<clusterServiceConfigApi.MergeConfigsByClusterIdItem>>>('/cluster-service-config/updateDetail', data)
}

/**
 * @description 查询配置的扩展属性
 * @param {clusterServiceConfigApi.GetSelectExtraConf} data
 * @return {*}
 */

export const getSelectExtraConf = (data: clusterServiceConfigApi.GetSelectExtraConf) => {
  return service.post<ApiResponse<clusterServiceConfigApi.QueueItem>>('/cluster-service-config/selectExtraConf', data)
}

/**
 * @description 添加或更新配置的扩展属性
 * @param {clusterServiceConfigApi.ACEOaadddefnoprrttux} data
 * @return {*}
 */

export const updateOrAddExtraConf = (data: clusterServiceConfigApi.ACEOaadddefnoprrttux) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/updateOrAddExtraConf', data)
}

/**
 * @description 刷新资源池配置
 * @param {clusterServiceConfigApi.GetSelectExtraConf} data
 * @return {*}
 */

export const refreshExtraConf = (data: clusterServiceConfigApi.GetSelectExtraConf) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/refreshExtraConf', data)
}

/**
 * @description 配置类别
 * @return {*}
 */

export const configTypeList = () => {
  return service.get<ApiResponse<null>>('/enum/list/configType')
}

/**
 * @description 配置类别
 * @param {clusterServiceConfigApi.GetRoleNames} data
 * @return {*}
 */

export const getRoleNames = (data: clusterServiceConfigApi.GetRoleNames) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/getRoleNames', data)
}

/**
 * @description 添加自定义配置
 * @param { Array<clusterServiceConfigApi.AddCustomConf>} data
 * @return {*}
 */

export const addCustomConf = (data: Array<clusterServiceConfigApi.AddCustomConf>) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/addCustomConf', data)
}

/**
 * @description 删除自定义配置
 * @param { clusterServiceConfigApi.DeleteCustomConf} data
 * @return {*}
 */

export const deleteCustomConf = (data: clusterServiceConfigApi.DeleteCustomConf) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/deleteCustomConf', data)
}

/**
 * @description 版本回滚配置对比列表
 * @param { clusterServiceConfigApi.compareSnapShotDetail} data
 * @return {*}
 */

export const compareSnapShotDetail = (data: clusterServiceConfigApi.compareSnapShotDetail) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/compareSnapShotDetail', data)
}

/**
 * @description 组件概览——修改详情
 * @param { clusterServiceConfigApi.allUpdateDetail} data
 * @return {*}
 */

export const allUpdateDetail = (data: clusterServiceConfigApi.allUpdateDetail) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/allUpdateDetail', data)
}

/**
 * @description 设置yarn资源池的默认配置
 * @param { clusterServiceConfigApi.UpdateOrAddDefaultExtraConf} data
 * @return {*}
 */

export const updateOrAddDefaultExtraConf = (data: clusterServiceConfigApi.UpdateOrAddDefaultExtraConf) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/updateOrAddDefaultExtraConf', data)
}

/**
 * @description 设置yarn资源池的默认配置
 * @param { clusterServiceConfigApi.GetDefaultExtraConf} data
 * @return {*}
 */

export const getDefaultExtraConf = (data: clusterServiceConfigApi.GetDefaultExtraConf) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/getDefaultExtraConf', data)
}

/**
 * @description 创建计划模式
 * @param { clusterServiceConfigApi.CreateYarnSchedule} data
 * @return {*}
 */

export const createYarnSchedule = (data: clusterServiceConfigApi.CreateYarnSchedule) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/createYarnSchedule', data)
}

/**
 * @description 创建计划模式
 * @param { clusterServiceConfigApi.UpdateYarnSchedule} data
 * @return {*}
 */

export const updateYarnSchedule = (data: clusterServiceConfigApi.UpdateYarnSchedule) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/updateYarnSchedule', data)
}

/**
 * @description 获取计划模式列表
 * @param { clusterServiceConfigApi.GetSchedulePage} data
 * @return {*}
 */

export const getSchedulePage = (data: clusterServiceConfigApi.GetSchedulePage) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/getSchedulePage', data)
}

/**
 * @description 获取所有计划模式
 * @param { clusterServiceConfigApi.GetConfigSet} data
 * @return {*}
 */

export const getConfigSet = (data: clusterServiceConfigApi.GetConfigSet) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/getConfigSet', data)
}

/**
 * @description 删除计划模式
 * @param { clusterServiceConfigApi.SYacddeeeeehllnrtu} data
 * @return {*}
 */

export const deleteYarnSchedule = (data: clusterServiceConfigApi.SYacddeeeeehllnrtu) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/deleteYarnSchedule', data)
}

/**
 * @description 组件配置查询(分页)
 * @param { clusterServiceConfigApi.GetClusterConfigsPage} data
 * @return {*}
 */

export const getClusterConfigsPage = (data: clusterServiceConfigApi.GetClusterConfigsPage) => {
  return service.post<ApiResponse<null>>('/cluster-service-config/getClusterConfigsPage', data)
}

/**
 * @description 实例配置查询(分页)
 * @param { clusterServiceConfigApi.GetInstanceConfigsPage} data
 * @return {*}
 */

export const getInstanceConfigsPage = (data: clusterServiceConfigApi.GetInstanceConfigsPage) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/getInstanceConfigsPage', data)
}

/**
 * @description 校验自定义配置
 * @param { clusterServiceConfigApi.CheckCustomConf} data
 * @return {*}
 */

export const checkCustomConf = (data: clusterServiceConfigApi.CheckCustomConf) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/checkCustomConf', data)
}

/**
 * @description 校验优先级是否存在
 * @param { clusterServiceConfigApi.CheckPriority} data
 * @return {*}
 */

export const checkPriority = (data: clusterServiceConfigApi.CheckPriority) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/checkPriority', data)
}

/**
 * @description 校验
 * @param { clusterServiceConfigApi.GetYarnSchedule} data
 * @return {*}
 */

export const getYarnSchedule = (data: clusterServiceConfigApi.GetYarnSchedule) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/getYarnSchedule', data)
}

export const updateOrAddPlacementPolicy = (data: clusterServiceConfigApi.UpdateOrAddPlacementPolicyParam) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/updateOrAddQueuePlacementPolicy', data)
}

export const deletePlacementPolicy = (data: clusterServiceConfigApi.deletePlacementPolicyParams) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/deleteQueuePlacementPolicy', data)
}

export const placementPolicyPage = (data: clusterServiceConfigApi.placementPolicyPageParams) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/selectQueuePlacementPolicy', data)
}

export const userLimitPage = (data: clusterServiceConfigApi.userLimitPageParams) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/selectUserLimit', data)
}

export const userLimitAdd = (data: clusterServiceConfigApi.userLimitAddParams) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/updateOrAddUserLimit', data)
}

/**
 * @description 批量配置
 * @param {} data
 * @return {*}
 */

export const GetBatchConfiguration = (data: clusterServiceConfigApi.confNameList) => {
  return service.post<ApiResponse<any>>('/cluster-service-config/batchConfiguration', data)
}
