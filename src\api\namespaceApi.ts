import service from '@/utils/Http'

/**
 * @description 创建一个新的命名空间
 * @param {namespaceApi.INamespaceAdd} data
 * @return {*}
 */
export const namespaceAdd = async (data: NamespaceApi.INamespaceAdd) => {
  return await service.post<ApiResponse<string>>('/namespace/addCluster', data)
}

/**
 * @description 根据ID删除namespace[暂未使用]
 * @param {namespaceApi.INamespaceRemoveById} data
 * @return {*}
 */
export const namespaceRemoveById = async (data: NamespaceApi.INamespaceRemoveById) => {
  return await service.post<ApiResponse<boolean>>('/namespace/remove/by/id', data)
}

/**
 * @description 分页查询命名空间列表
 * @param {namespaceApi.INamespacePageByName} data
 * @return {*}
 */
export const namespacePageByName = async (data: NamespaceApi.INamespacePageByName) => {
  return await service.post<ApiResponse<PageList<NamespaceApi.PageByNameRecordsData>>>('/namespace/pageByName', data)
}

/**
 * @description 命名空间重命名
 * @param {namespaceApi.INamespaceUpdateName} data
 * @return {*}
 */
export const namespaceUpdateName = async (data: NamespaceApi.INamespaceUpdateName) => {
  return await service.post<ApiResponse<boolean>>('/namespace/updateName', data)
}

/**
 * @description 绑定k8s到命名空间
 * @param {K8sApi.IBindK8sToNamespace} data
 * @return {*}
 */
export const bindK8sToNamespace = (data: NamespaceApi.IBindK8sToNamespace) => {
  return service.post<ApiResponse<boolean | null>>('/namespace/bindKube', data)
}

/**
 * @description 从命名空间解绑k8s
 * @param {NamespaceApi.IUnBindK8sForNamespace} data
 * @return {*}
 */
export const unBindK8sForNamespace = (data: NamespaceApi.IUnBindK8sForNamespace) => {
  return service.post<ApiResponse<boolean | null>>('/namespace/unbindKube', data)
}

/**
 * @description 删除命名空间
 * @param {NamespaceApi.INamespaceRemoveById} data
 * @return {*}
 */
export const deleteNamespace = (data: NamespaceApi.INamespaceRemoveById) => {
  return service.post<ApiResponse<boolean>>('/namespace/deleteKube', data)
}

/**
 * @description 获取头部菜单中集群下拉列表
 * @param {} data
 * @return {*}
 */
export const getMenuNamespaceList = () => {
  return service.get<ApiResponse<Array<NamespaceApi.MenuNamespaceListData>>>('/namespace/dropDownClusters')
}

/**
 * @description 绑定k8s时可选的命名空间列表
 * @param {stringOrNumber} data
 * @return {*}
 */
export const getK8sNamespaceList = (data: { id: stringOrNumber }) => {
  return service.post<ApiResponse<string[]>>('/namespace/namespaceList', data)
}

/**
 * @description 集群维护模式开关
 * @param {NamespaceApi.Maintenance} data
 * @return {*}
 */
export const maintenance = (data: NamespaceApi.Maintenance) => {
  return service.post<ApiResponse<boolean>>('/namespace/maintenance', data)
}

export const namespaceAllocation = (data: NamespaceApi.NamespaceAllocation) => {
  return service.post<ApiResponse<null>>('/namespace/allocation', data)
}

export const namespaceAllocationList = (data: NamespaceApi.NamespaceAllocationList) => {
  return service.post<ApiResponse<null>>('/namespace/allocation/namespaceList', data)
}

export const namespaceAllocationBatch = (data: NamespaceApi.NamespaceAllocationBatch) => {
  return service.post<ApiResponse<null>>('/namespace/allocationBatch', data)
}

export const namespaceK8sCreate = (data: NamespaceApi.NamespaceK8sCreate) => {
  return service.post<ApiResponse<null>>('/namespace/k8s/create', data)
}

export const namespaceDelete = (data: NamespaceApi.NamespaceDelete) => {
  return service.post<ApiResponse<null>>('/namespace/delete', data)
}
export const namespacePage = (data: NamespaceApi.NamespacePage) => {
  return service.post<ApiResponse<null>>('/namespace/page', data)
}
export const namespaceListByK8sId = (data: NamespaceApi.NamespaceTenantList) => {
  return service.post<ApiResponse<NamespaceApi.NamespaceList[]>>('/namespace/pageAll', data)
}

export const namespaceTenantList = (data: NamespaceApi.NamespaceTenantList) => {
  return service.post<ApiResponse<null>>('/namespace/tenantList', data)
}

export const namespaceGatewayList = (data: NamespaceApi.namespaceGatewayList) => {
  return service.post<ApiResponse<null>>('/namespace/edge/node/namespaceList', data)
}

// CE后端模式（标准版、轻量版）
export const namespaceType = () => {
  return service.post<ApiResponse<number>>('/namespace/type')
}

// 检查命名空间权限
export const checkNamespaceAuthByK8sId = (data: NamespaceApi.NamespaceDelete) => {
  return service.get<ApiResponse<boolean>>(`/cluster/service/checkNamespaceAuth/${data.id}`)
}
