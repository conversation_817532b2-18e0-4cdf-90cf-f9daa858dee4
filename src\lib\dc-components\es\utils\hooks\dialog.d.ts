export declare function useDialog({ cancel, confirm, show, defaultData, }?: {
    cancel?: (val?: any, dialogData?: any) => void;
    confirm?: (val?: any, dialogData?: any) => void;
    show?: (val?: any) => void;
    defaultData?: any;
}): {
    showDialog: (val?: any) => void;
    onCancel: (val?: any) => void;
    onConfirm: (val?: any) => void;
    visible: import("vue").Ref<boolean>;
    dialogData: any;
};
