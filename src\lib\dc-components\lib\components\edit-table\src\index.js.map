{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/edit-table/src/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :model=\"formModel\">\r\n      <el-table v-bind=\"$attrs\" :data=\"tableData\">\r\n        <slot />\r\n      </el-table>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ElForm, ElTable } from \"element-plus\";\r\nimport type { PropType, Ref } from \"vue\";\r\n// eslint-disable-next-line import/order\r\nimport { computed, provide, ref, watchEffect } from \"vue\";\r\nimport type {\r\n  DcEditTableEditActions,\r\n  DcEditTableFormModel,\r\n  DcEditTableFormModelItem,\r\n  DcEditTableFormProps,\r\n  DcEditTableRequestFunc,\r\n} from \"./types\";\r\ndefineOptions({\r\n  name: \"DcEditTable\",\r\n});\r\n\r\nconst props = defineProps({\r\n  dataSource: {\r\n    type: Array as PropType<any[]>,\r\n  },\r\n  request: {\r\n    type: Object as PropType<DcEditTableRequestFunc<any>>,\r\n  },\r\n});\r\n\r\nconst formModel = ref<DcEditTableFormModel>({\r\n  model: [],\r\n});\r\n\r\nconst form = ref();\r\n\r\nconst formProps = ref<DcEditTableFormProps>(new Set());\r\n\r\nconst tableData = computed(() => formModel.value.model.map(({ data }) => data));\r\n\r\nconst resultData = computed(() => {\r\n  return formModel.value.model.reduce(\r\n    (resultData: any[], model: DcEditTableFormModelItem) => {\r\n      if (model.isNew) {\r\n        return resultData;\r\n      }\r\n      resultData.push({\r\n        ...model.data,\r\n      });\r\n      return resultData;\r\n    },\r\n    []\r\n  );\r\n});\r\n\r\nconst convertFormModel = (data: any[]): DcEditTableFormModelItem[] =>\r\n  data.map(\r\n    (row: any): DcEditTableFormModelItem => ({\r\n      data: { ...row },\r\n      formData: { ...row },\r\n      isEditing: false,\r\n      isNew: false,\r\n    })\r\n  );\r\n\r\nwatchEffect(async () => {\r\n  const model = [...(props?.dataSource ?? [])];\r\n  if (typeof props.request === \"function\") {\r\n    model.push(...(await Promise.resolve(props.request())));\r\n  }\r\n  formModel.value.model = convertFormModel(model);\r\n});\r\n\r\nconst generateValidateFields = (index: number) =>\r\n  Array.from(formProps.value).map((prop) => `model.${index}.formData.${prop}`);\r\n\r\nconst startEditable = (index: number) => {\r\n  formModel.value.model[index].isEditing = true;\r\n};\r\n\r\nconst deleteRow = (index: number) => {\r\n  formModel.value.model.splice(index, 1);\r\n};\r\n\r\nconst addRow = (row: Record<any, any> = {}) => {\r\n  formModel.value.model.push({\r\n    data: { ...row },\r\n    formData: { ...row },\r\n    isEditing: true,\r\n    isNew: true,\r\n  });\r\n};\r\n\r\nconst cancelEditable = (index: number) => {\r\n  if (!form.value) {\r\n    return;\r\n  }\r\n\r\n  form.value.resetFields &&\r\n    form.value.resetFields(generateValidateFields(index));\r\n  const formModelItem = formModel.value.model[index];\r\n  formModelItem.formData = { ...formModelItem.data };\r\n  if (formModelItem.isNew) {\r\n    formModel.value.model.splice(index, 1);\r\n  } else {\r\n    formModelItem.isEditing = false;\r\n  }\r\n};\r\n\r\nconst saveEditable = (index: number) => {\r\n  if (!form.value) {\r\n    return;\r\n  }\r\n\r\n  form.value.validateField &&\r\n    form.value.validateField(\r\n      generateValidateFields(index),\r\n      (validated: boolean) => {\r\n        if (!validated) {\r\n          return;\r\n        }\r\n        const formModelItem = formModel.value.model[index];\r\n        formModelItem.data = { ...formModelItem.formData };\r\n        formModelItem.isEditing = false;\r\n        formModelItem.isNew = false;\r\n      }\r\n    );\r\n};\r\n\r\nconst editActions: DcEditTableEditActions = {\r\n  addRow,\r\n  deleteRow,\r\n  startEditable,\r\n  cancelEditable,\r\n  saveEditable,\r\n};\r\n\r\nprovide<Ref<DcEditTableFormModel>>(\"formModel\", formModel);\r\n\r\nprovide<Ref<DcEditTableFormProps>>(\"formProps\", formProps);\r\n\r\nprovide<DcEditTableEditActions>(\"editActions\", editActions);\r\n\r\ndefineExpose({\r\n  resultData,\r\n  editActions,\r\n});\r\n</script>\r\n"], "names": ["DO_defineComponent", "ref", "computed", "watchEffect", "provide"], "mappings": ";;;;;;;;;;AAqBc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AASd,IAAA,MAAM,YAAYC,OAA0B,CAAA;AAAA,MAC1C,OAAO,EAAC;AAAA,KACT,CAAA,CAAA;AAED,IAAA,MAAM,OAAOA,OAAI,EAAA,CAAA;AAEjB,IAAA,MAAM,SAAY,GAAAA,OAAA,iBAA8B,IAAA,GAAA,EAAK,CAAA,CAAA;AAE/C,IAAA,MAAA,SAAA,GAAYC,YAAS,CAAA,MAAM,SAAU,CAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,CAAC,EAAE,IAAW,EAAA,KAAA,IAAI,CAAC,CAAA,CAAA;AAExE,IAAA,MAAA,UAAA,GAAaA,aAAS,MAAM;AAChC,MAAA,OAAO,UAAU,KAAM,CAAA,KAAA,CAAM,MAC3B,CAAA,CAAC,aAAmB,KAAoC,KAAA;AACtD,QAAA,IAAI,MAAM,KAAO,EAAA;AACR,UAAA,OAAA,WAAA,CAAA;AAAA,SACT;AACA,QAAA,WAAA,CAAW,IAAK,CAAA;AAAA,UACd,GAAG,KAAM,CAAA,IAAA;AAAA,SACV,CAAA,CAAA;AACM,QAAA,OAAA,WAAA,CAAA;AAAA,OACT,EACA,EACF,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,mBAAmB,CAAC,IAAA,KACxB,IAAK,CAAA,GAAA,CACH,CAAC,GAAwC,MAAA;AAAA,MACvC,IAAA,EAAM,EAAE,GAAG,GAAI,EAAA;AAAA,MACf,QAAA,EAAU,EAAE,GAAG,GAAI,EAAA;AAAA,MACnB,SAAW,EAAA,KAAA;AAAA,MACX,KAAO,EAAA,KAAA;AAAA,KAEX,CAAA,CAAA,CAAA;AAEF,IAAAC,eAAA,CAAY,YAAY;AACtB,MAAA,MAAM,QAAQ,CAAC,GAAI,KAAO,EAAA,UAAA,IAAc,EAAG,CAAA,CAAA;AACvC,MAAA,IAAA,OAAO,KAAM,CAAA,OAAA,KAAY,UAAY,EAAA;AACjC,QAAA,KAAA,CAAA,IAAA,CAAK,GAAI,MAAM,OAAA,CAAQ,QAAQ,KAAM,CAAA,OAAA,EAAS,CAAE,CAAA,CAAA;AAAA,OACxD;AACU,MAAA,SAAA,CAAA,KAAA,CAAM,KAAQ,GAAA,gBAAA,CAAiB,KAAK,CAAA,CAAA;AAAA,KAC/C,CAAA,CAAA;AAED,IAAA,MAAM,sBAAyB,GAAA,CAAC,KAC9B,KAAA,KAAA,CAAM,IAAK,CAAA,SAAA,CAAU,KAAK,CAAA,CAAE,GAAI,CAAA,CAAC,IAAS,KAAA,CAAA,MAAA,EAAS,kBAAkB,IAAM,CAAA,CAAA,CAAA,CAAA;AAEvE,IAAA,MAAA,aAAA,GAAgB,CAAC,KAAkB,KAAA;AAC7B,MAAA,SAAA,CAAA,KAAA,CAAM,KAAM,CAAA,KAAA,CAAA,CAAO,SAAY,GAAA,IAAA,CAAA;AAAA,KAC3C,CAAA;AAEM,IAAA,MAAA,SAAA,GAAY,CAAC,KAAkB,KAAA;AACnC,MAAA,SAAA,CAAU,KAAM,CAAA,KAAA,CAAM,MAAO,CAAA,KAAA,EAAO,CAAC,CAAA,CAAA;AAAA,KACvC,CAAA;AAEA,IAAA,MAAM,MAAS,GAAA,CAAC,GAAwB,GAAA,EAAO,KAAA;AACnC,MAAA,SAAA,CAAA,KAAA,CAAM,MAAM,IAAK,CAAA;AAAA,QACzB,IAAA,EAAM,EAAE,GAAG,GAAI,EAAA;AAAA,QACf,QAAA,EAAU,EAAE,GAAG,GAAI,EAAA;AAAA,QACnB,SAAW,EAAA,IAAA;AAAA,QACX,KAAO,EAAA,IAAA;AAAA,OACR,CAAA,CAAA;AAAA,KACH,CAAA;AAEM,IAAA,MAAA,cAAA,GAAiB,CAAC,KAAkB,KAAA;AACpC,MAAA,IAAA,CAAC,KAAK,KAAO,EAAA;AACf,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAA,CAAK,MAAM,WACT,IAAA,IAAA,CAAK,MAAM,WAAY,CAAA,sBAAA,CAAuB,KAAK,CAAC,CAAA,CAAA;AAChD,MAAA,MAAA,aAAA,GAAgB,SAAU,CAAA,KAAA,CAAM,KAAM,CAAA,KAAA,CAAA,CAAA;AAC5C,MAAA,aAAA,CAAc,QAAW,GAAA,EAAE,GAAG,aAAA,CAAc,IAAK,EAAA,CAAA;AACjD,MAAA,IAAI,cAAc,KAAO,EAAA;AACvB,QAAA,SAAA,CAAU,KAAM,CAAA,KAAA,CAAM,MAAO,CAAA,KAAA,EAAO,CAAC,CAAA,CAAA;AAAA,OAChC,MAAA;AACL,QAAA,aAAA,CAAc,SAAY,GAAA,KAAA,CAAA;AAAA,OAC5B;AAAA,KACF,CAAA;AAEM,IAAA,MAAA,YAAA,GAAe,CAAC,KAAkB,KAAA;AAClC,MAAA,IAAA,CAAC,KAAK,KAAO,EAAA;AACf,QAAA,OAAA;AAAA,OACF;AAEK,MAAA,IAAA,CAAA,KAAA,CAAM,iBACT,IAAK,CAAA,KAAA,CAAM,cACT,sBAAuB,CAAA,KAAK,CAC5B,EAAA,CAAC,SAAuB,KAAA;AACtB,QAAA,IAAI,CAAC,SAAW,EAAA;AACd,UAAA,OAAA;AAAA,SACF;AACM,QAAA,MAAA,aAAA,GAAgB,SAAU,CAAA,KAAA,CAAM,KAAM,CAAA,KAAA,CAAA,CAAA;AAC5C,QAAA,aAAA,CAAc,IAAO,GAAA,EAAE,GAAG,aAAA,CAAc,QAAS,EAAA,CAAA;AACjD,QAAA,aAAA,CAAc,SAAY,GAAA,KAAA,CAAA;AAC1B,QAAA,aAAA,CAAc,KAAQ,GAAA,KAAA,CAAA;AAAA,OAE1B,CAAA,CAAA;AAAA,KACJ,CAAA;AAEA,IAAA,MAAM,WAAsC,GAAA;AAAA,MAC1C,MAAA;AAAA,MACA,SAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,MACA,YAAA;AAAA,KACF,CAAA;AAEA,IAAAC,WAAA,CAAmC,aAAa,SAAS,CAAA,CAAA;AAEzD,IAAAA,WAAA,CAAmC,aAAa,SAAS,CAAA,CAAA;AAEzD,IAAAA,WAAA,CAAgC,eAAe,WAAW,CAAA,CAAA;AAE7C,IAAA,QAAA,CAAA;AAAA,MACX,UAAA;AAAA,MACA,WAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;"}