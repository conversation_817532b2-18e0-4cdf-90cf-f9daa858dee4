{"version": 3, "file": "index.js", "sources": ["../../../../../packages/utils/common/index.ts"], "sourcesContent": ["export const isObject = (val: unknown): val is Record<any, any> =>\r\n  val !== null && typeof val === \"object\";\r\n\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nexport const hasOwn = (\r\n  val: object,\r\n  key: string | symbol\r\n): key is keyof typeof val => hasOwnProperty.call(val, key);\r\n\r\nexport const fromPairs = (pairs: any[]) => {\r\n  const result: Record<string, any> = {};\r\n  if (pairs == null) {\r\n    return result;\r\n  }\r\n  for (const pair of pairs) {\r\n    result[pair[0]] = pair[1];\r\n  }\r\n  return result;\r\n};\r\n"], "names": [], "mappings": ";;;;AAAO,MAAM,WAAW,CAAC,GAAA,KACvB,GAAQ,KAAA,IAAA,IAAQ,OAAO,GAAQ,KAAA,SAAA;AAEjC,MAAM,cAAA,GAAiB,OAAO,SAAU,CAAA,cAAA,CAAA;AACjC,MAAM,SAAS,CACpB,GAAA,EACA,QAC4B,cAAe,CAAA,IAAA,CAAK,KAAK,GAAG,EAAA;AAE7C,MAAA,SAAA,GAAY,CAAC,KAAiB,KAAA;AACzC,EAAA,MAAM,SAA8B,EAAC,CAAA;AACrC,EAAA,IAAI,SAAS,IAAM,EAAA;AACjB,IAAO,OAAA,MAAA,CAAA;AAAA,GACT;AACA,EAAA,KAAA,MAAW,QAAQ,KAAO,EAAA;AACxB,IAAO,MAAA,CAAA,IAAA,CAAK,MAAM,IAAK,CAAA,CAAA,CAAA,CAAA;AAAA,GACzB;AACA,EAAO,OAAA,MAAA,CAAA;AACT;;;;;;"}