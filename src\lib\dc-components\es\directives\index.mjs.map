{"version": 3, "file": "index.mjs", "sources": ["../../../../packages/directives/index.ts"], "sourcesContent": ["// import directives\r\nimport copy from \"./common/copy\";\r\nimport type { App } from \"vue\";\r\n\r\nconst directivesList: any = {\r\n  copy,\r\n};\r\n\r\nconst directives = {\r\n  install(app: App<Element>) {\r\n    Object.keys(directivesList).forEach((key) => {\r\n      app.directive(key, directivesList[key]);\r\n    });\r\n  },\r\n};\r\n\r\nexport default directives;\r\n"], "names": [], "mappings": ";;AACA,MAAM,cAAc,GAAG;AACvB,EAAE,IAAI;AACN,CAAC,CAAC;AACG,MAAC,UAAU,GAAG;AACnB,EAAE,OAAO,CAAC,GAAG,EAAE;AACf,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACjD,MAAM,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C,KAAK,CAAC,CAAC;AACP,GAAG;AACH;;;;"}