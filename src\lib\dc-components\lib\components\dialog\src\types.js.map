{"version": 3, "file": "types.js", "sources": ["../../../../../../packages/components/dialog/src/types.ts"], "sourcesContent": ["import type { Ref } from \"vue\";\r\nimport type { Dc<PERSON>uttonConfig } from \"../../button\";\r\nimport type { RenderVNodeFn } from \"../../render-vnode/src/types\";\r\nimport type DcDialog from \"./index.vue\";\r\n\r\nexport interface DcDialogAction {\r\n  innerText?: string;\r\n  visible?: boolean | Ref<boolean>;\r\n  componentSlot?: Record<string, RenderVNodeFn>;\r\n  props?: Record<string, any>;\r\n  config?: DcButtonConfig;\r\n}\r\n\r\nexport enum DcDialogTypes {\r\n  HEADER_FOOTER = \"header_footer\",\r\n  FOOTER = \"footer\",\r\n  HEADER = \"header\",\r\n  ONLYCONTENT = \"ONLYCONTENT\",\r\n}\r\n\r\nexport type DcDialogInstance = InstanceType<typeof DcDialog>;\r\n"], "names": [], "mappings": ";;;;AAaY,IAAA,aAAA,qBAAA,cAAL,KAAA;AACL,EAAgB,cAAA,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;AAChB,EAAS,cAAA,CAAA,QAAA,CAAA,GAAA,QAAA,CAAA;AACT,EAAS,cAAA,CAAA,QAAA,CAAA,GAAA,QAAA,CAAA;AACT,EAAc,cAAA,CAAA,aAAA,CAAA,GAAA,aAAA,CAAA;AAJJ,EAAA,OAAA,cAAA,CAAA;AAAA,CAAA,EAAA,aAAA,IAAA,EAAA;;;;"}