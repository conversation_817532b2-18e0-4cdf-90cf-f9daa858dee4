declare const _default: {
    "zh-CN": {
        CyberEngine: {
            desc: string;
        };
        CyberData: {
            desc: string;
        };
        CyberAI: {
            desc: string;
        };
        welcome: string;
        usernameP: string;
        passwordP: string;
        codeP: string;
        usernameR: string;
        passwordR: string;
        codeR: string;
        login: string;
        dialogTitle: string;
        recentlyUsed: string;
        register: string;
        registerText: string;
        loginText: string;
        phoneP: string;
        phoneR: string;
        emailP: string;
        emailR: string;
        registerSu: string;
        usernameRule: string;
    };
    "en-US": {
        CyberEngine: {
            desc: string;
        };
        CyberData: {
            desc: string;
        };
        CyberAI: {
            desc: string;
        };
        welcome: string;
        usernameP: string;
        passwordP: string;
        codeP: string;
        usernameR: string;
        passwordR: string;
        codeR: string;
        login: string;
        dialogTitle: string;
        recentlyUsed: string;
        register: string;
        registerText: string;
        loginText: string;
        phoneP: string;
        phoneR: string;
        emailP: string;
        emailR: string;
        registerSu: string;
        usernameRule: string;
    };
    "ja-JP": {
        CyberEngine: {
            desc: string;
        };
        CyberData: {
            desc: string;
        };
        CyberAI: {
            desc: string;
        };
        welcome: string;
        usernameP: string;
        passwordP: string;
        codeP: string;
        usernameR: string;
        passwordR: string;
        codeR: string;
        login: string;
        dialogTitle: string;
        recentlyUsed: string;
        register: string;
        registerText: string;
        loginText: string;
        phoneP: string;
        phoneR: string;
        emailP: string;
        emailR: string;
        registerSu: string;
        usernameRule: string;
    };
};
export default _default;
