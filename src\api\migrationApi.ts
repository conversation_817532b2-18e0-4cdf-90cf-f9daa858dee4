import service from '@/utils/Http'
/**
 * @description 查询冷热数据分级规则
 * @param {MigrationApi.GetRule} data
 * @return {*}
 */

export const getRule = (data: MigrationApi.GetRule) => {
  return service.post<ApiResponse<any>>('/dataMigration/rule/get', data)
}

/**
 * @description 编辑更新冷热数据分级规则
 * @param {MigrationApi.EditRule} data
 * @return {*}
 */

export const editRule = (data: MigrationApi.EditRule) => {
  return service.post<ApiResponse<any>>('/dataMigration/rule/edit', data)
}

/**
 * @description 编辑更新冷热数据分级规则
 * @param {MigrationApi.GetMigrationList} data
 * @return {*}
 */

export const getMigrationList = (data: MigrationApi.GetMigrationList) => {
  return service.post<ApiResponse<any>>('/dataMigration/data/list', data)
}
