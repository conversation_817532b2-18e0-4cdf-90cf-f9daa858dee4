// hostsapi.ts
import service from '@/utils/Http'

/**
 * @description 添加主机
 * @param {HostsApi.IAddHost} data
 * @return {*}
 */
export const addHost = async (data: HostsApi.IAddHost) => {
  return service.post<ApiResponse<string>>('/hosts/add', data)
}

/**
 * @description 将主机绑定至命名空间
 * @param {HostsApi.IHostsBindToNamespace} data
 * @return {*}
 */
export const hostsBindToNamespace = async (data: HostsApi.IHostsBindToNamespace) => {
  return await service.post<ApiResponse<any>>('/hosts/bind2Namespace', data)
}

/**
 * @description 主机从命名空间解绑
 * @param {HostsApi.IHostsUnBind} data
 * @return {*}
 */
export const hostsUnBind = async (data: HostsApi.IHostsUnBind) => {
  return await service.post<ApiResponse<any>>('/hosts/unbind', data)
}

/**
 * @description 获取未托管的主机列表
 * @param {HostsApi.IGetHostsUnManagedList} data
 * @return {*}
 */
export const getHostsUnManagedList = async (data: HostsApi.IGetHostsUnManagedList) => {
  return await service.post<ApiResponse<Array<HostsApi.HostListRecordsData>>>('/hosts/unmanaged/list', data)
}

/**
 * @description 验证主机是否连通
 * @param {HostsApi.IHostsVerifyConnected} data
 * @return {*}
 */
export const hostsVerifyConnected = async (data: HostsApi.IHostsVerifyConnected) => {
  return await service.post<ApiResponse<string>>('/hosts/verify/connectedWithPwd', data)
}

/**
 * @description 主机登陆验证
 * @param {HostsApi.IHostsVerifyLogin} data
 * @return {*}
 */
export const hostsVerifyLogin = async (data: HostsApi.IHostsVerifyLogin) => {
  return await service.post<ApiResponse<boolean>>('/hosts/verify/login', data)
}

/**
 * @description 条件查询主机列表
 * @param {HostsApi.IGetHostList} data
 * @return {*}
 */
export const getHostList = async (data: HostsApi.IGetHostList) => {
  return await service.post<ApiResponse<PageList<HostsApi.HostListRecordsData>>>('/hosts/page', data)
}

/**
 * @description 条件查询主机列表
 * @param {HostsApi.IGetHostList} data
 * @return {*}
 */
export const getHostListAll = async () => {
  return await service.get<ApiResponse<PageList<HostsApi.HostListRecordsData>>>('/panel/condition')
}

/**
 * @description 获取集群列表
 * @return {*}
 */
export const getNamespaceList = async () => {
  return await service.get<ApiResponse<Array<NamespaceApi.PageByNameRecordsData>>>('/hosts/namespaceList')
}

/**
 * @description 获取不带默认集群的集群列表
 * @return {*}
 */
export const getNamespaceListExcludeDefault = async () => {
  return await service.get<ApiResponse<Array<NamespaceApi.PageByNameRecordsData>>>('/namespace/listExcludeDefault')
}

/**
 * @description 根据流水号获取主机安装进度
 * @param {HostsApi.IGetHostsAddProgress} data
 * @return {*}
 */
export const getHostsAddProgress = async (serialNumber: HostsApi.IGetHostsAddProgress) => {
  return await service.get<ApiResponse<HostsApi.ProgressData>>(`/hosts/add/progress/${serialNumber}`)
}

/**
 * @description 添加组件时获取可选主机列表
 * @param {ComponentApi.getComponentListAvailableList} data
 * @return {*}
 */
export const getComponentListAvailableList = async (data: HostsApi.getComponentListAvailableList) => {
  return service.post<ApiResponse<Array<HostsApi.HostListRecordsData>>>(`/hosts/available/list`, data)
}

/**
 * @description 根据流水号调用主机绑定进度查询接口
 * @param {HostsApi.IHostsBindProgress} data
 * @return {*}
 */
export const hostsBindProgress = async (serialNumber: HostsApi.IHostsBindProgress) => {
  return await service.get<ApiResponse<HostsApi.ProgressData>>(`/hosts/bind/progress/${serialNumber}`)
}

/**
 * @description 删除主机
 * @param {HostsApi.deleteHost} data
 * @return {*}
 */
export const deleteHost = async (data: HostsApi.deleteHost) => {
  return await service.post<ApiResponse<string>>(`/hosts/deleteHost`, data)
}
/**
 * @description 批量删除主机
 * @param {HostsApi.deleteHost} data
 * @return {*}
 */
export const batchDeleteHost = async (data: HostsApi.BatchDeleteHost) => {
  return await service.post<ApiResponse<any>>(`/hosts/batchDeleteHost`, data)
}
/**
 * @description 批量删除主机检查
 * @param {HostsApi.deleteHost} data
 * @return {*}
 */
export const batchDeleteHostCheck = async (data: HostsApi.BatchDeleteHost) => {
  return await service.post<ApiResponse<any>>(`/hosts/batchDeleteHost/check`, data)
}

/**
 * @description 主机删除进度
 * @param {HostsApi.deleteHost} data
 * @return {*}
 */
export const hostDeletionProgress = async (data: HostsApi.deleteHost) => {
  return await service.get<ApiResponse<HostsApi.DeleteHostData>>(`/hosts/del/progress/${data.id}`)
}
/**
 * @description 下载主机秘钥文件
 * @param {HostsApi.deleteHost} data
 * @return {*}
 */
export const downPublicKey = async () => {
  return await service.get(`/hosts/download/system/publicKey`)
}

/**
 * @description 批量添加主机
 * @param {HostsApi.BatchAddHosts} data
 * @return {*}
 */
export const batchAddHosts = async (data: HostsApi.BatchAddHosts) => {
  return await service.post<ApiResponse<string>>(`/hosts/batchAddHosts`, data)
}

/**
 * @description 查询进度
 * @param {string} data
 * @return {*}
 */
export const getProgress = async (data: string) => {
  return await service.get<ApiResponse<any>>(`/hosts/batchAdd/progress/${data}`)
}

/**
 * @description 重新添加
 * @param {HostsApi.IAddHost} data
 * @return {*}
 */
export const reExecuteAddHosts = async (data: HostsApi.IAddHost) => {
  return await service.post<ApiResponse<any>>(`/hosts/reExecuteAddHosts`, data)
}

/**
 * @description 主机角色列表
 * @param {HostsApi.HostRoleInfo} data
 * @return {*}
 */
export const hostRoleInfo = async (data: HostsApi.HostRoleInfo) => {
  return await service.post<ApiResponse<PageList<HostsApi.HostRoleInfoDataRecords[]>>>(`/hosts/hostRoleInfo`, data)
}

/**
 * @description 主机详情
 * @param {HostsApi.HostDetail} data
 * @return {*}
 */
export const hostDetail = async (data: HostsApi.HostDetail) => {
  return await service.get<ApiResponse<HostsApi.HostDetailData>>(`/hosts/hostDetail/?hostId=${data.hostId}`)
}
/*
 * @description 主机名称修改
 * @param {HostsApi.UpdateHostName} data
 * @return {*}
 */
export const updateHostName = async (data: HostsApi.UpdateHostName) => {
  return await service.post<ApiResponse<any>>(`/hosts/updateHostName`, data)
}

/**
 * @description 主机名称修改
 * @param {HostsApi.UpdateHostNameProcess} data
 * @return {*}
 */
export const updateHostNameProcess = async (data: HostsApi.UpdateHostNameProcess) => {
  return await service.get<ApiResponse<any>>(`/hosts/updateHostName/process/${data.id}`)
}

/**
 * @description 获取主机检测报告
 * @param {YarnApi.SetTimedKill} data
 * @return {*}
 */

export const getHostReport = (checkUid: stringOrNumber) => {
  return service.get<ApiResponse<Array<any>>>(`/hosts/check/demo/report/${checkUid}`)
}

/**
 * @description 主机全量检测
 * @param {YarnApi.SetTimedKill} data
 * @return {*}
 */

export const hostsCheck = (data: { namespaceId?: stringOrNumber }) => {
  return service.post<ApiResponse<Array<any>>>(`/hosts/check/demo`, data)
}

/**
 * @description 主机全量检测
 * @param {YarnApi.SetTimedKill} data
 * @return {*}
 */

export const getHostsCheckList = (checkUid?: stringOrNumber) => {
  return service.get<ApiResponse<Array<any>>>(`/hosts/check/demo/list/${checkUid}`)
}

/**
 * @description 主机维护模式切换
 * @param {HostsApi.HostsMaintenance} data
 * @return {*}
 */

export const hostsMaintenance = (data: HostsApi.HostsMaintenance) => {
  return service.post<ApiResponse<any>>(`/hosts/maintenance`, data)
}

/**
 * @description 主机资源分配
 * @param {HostsApi.HostsAllocation} data
 * @return {*}
 */

export const hostsAllocation = (data: HostsApi.HostsAllocation) => {
  return service.post<ApiResponse<any>>(`/hosts/resource/allocation`, data)
}

/**
 * @description 根据租户id获取所有的主机
 * @param {HostsApi.GetAllHostsByTenantId} data
 * @return {*}
 */

export const getAllHostsByTenantId = (data: HostsApi.GetAllHostsByTenantId) => {
  return service.post<ApiResponse<any>>(`/hosts/getAllHostsByTenantId`, data)
}

/**
 * @description 获取是否有添加主机权限
 * @param {HostsApi.GetResourceLimitConfig} data
 * @return {*}
 */

export const getResourceLimitConfig = (data: HostsApi.GetResourceLimitConfig) => {
  return service.post<ApiResponse<any>>(`/hosts/getResourceLimitConfig`, data)
}

/**
 * @description 编辑主机别名
 * @param {HostsApi.EditAlias} data
 * @return {*}
 */

export const editAlias = (data: HostsApi.EditAlias) => {
  return service.post<ApiResponse<any>>(`/hosts/editAlias`, data)
}

export const instanceRemove = (data: HostsApi.instanceRemove) => {
  return service.post<ApiResponse<any>>(`/component/hadoop/instance/remove`, data)
}

export const decommissionCancel = (data: HostsApi.decommissionCancel) => {
  return service.post<ApiResponse<any>>(`/component/datanode/decommission/cancel`, data)
}

export const decommissionMigration = (data: HostsApi.decommissionMigration) => {
  return service.post<ApiResponse<any>>(`/component/datanode/decommission/migration`, data)
}

export const decommission = (data: HostsApi.decommission) => {
  return service.post<ApiResponse<any>>(`/component/datanode/decommission`, data)
}

export const addProgress = (data: string) => {
  return service.get<ApiResponse<any>>(`/hosts/add/progress/${data}`)
}

export const getCmakUrl = (data: { clusterServiceId: string; searchKey: string }) => {
  return service.post<ApiResponse<any>>(`/cluster-service-config/getValueByKey`, data)
}

export const getGrafanaPort = () => {
  return service.get<ApiResponse<any>>(`/ops/globalConf/grafanaPort`)
}

export const hostClusterConfig = (data: { id: string }) => {
  return service.post<ApiResponse<any>>(`/hosts/select/component/client/config`, data)
}

export const updateHostClusterConfig = (data: { hostId: string; namespaceId: string; clusterServiceIds: string[] }) => {
  return service.post<ApiResponse<any>>(`/hosts/update/component/client/config`, data)
}

export const hostAddProgress = (data: { hostIp: string }) => {
  return service.get<ApiResponse<any>>(`/hosts/hostAddProgress?hostIp=${data.hostIp}`)
}
export const deleteEdgeHost = async (data: HostsApi.deleteHost) => {
  return await service.post<ApiResponse<string>>(`/hosts/delete/edge`, data)
}
