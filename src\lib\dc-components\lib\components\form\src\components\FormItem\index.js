'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/form-item/style/css');
var index = require('../../../../render-vnode/src/index.js');
var constants = require('./constants.js');
var pluginVue_exportHelper = require('../../../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcFormItem"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    config: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: [String, Number, Boolean, Array, Object]
    },
    margin: {
      type: Object,
      default: () => ({})
    },
    isInline: {
      type: Boolean,
      default: false
    },
    rowIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ["update:modelValue", "triggerEffect"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const formItemRef = vue.ref();
    const marginBottom = vue.computed(() => props.margin.bottom ? props.margin.bottom : "18px");
    const marginRight = vue.computed(() => props.margin.right ? props.margin.right : props.isInline ? "32px" : "0px");
    const marginLeft = vue.computed(() => props.margin.left ? props.margin.left : "unset");
    const marginTop = vue.computed(() => props.margin.top ? props.margin.top : "unset");
    const ruleTrigger = vue.computed(() => {
      return (props.config.rules || []).map((item) => item.trigger);
    });
    const handleBlur = () => {
      if (ruleTrigger.value.includes("blur")) {
        formItemRef.value?.validate("blur", () => {
        });
      }
    };
    const triggerEffect = (effect, rowIndex) => {
      emits("triggerEffect", effect, rowIndex);
    };
    const itemChange = (val) => {
      emits("update:modelValue", val, props.config);
      if (props.config.effect && typeof props.config.effect === "function") {
        triggerEffect(props.config.effect, props.rowIndex);
      }
    };
    vue.watch(() => props.modelValue, () => {
      if (ruleTrigger.value.includes("change")) {
        formItemRef.value?.validate("change", () => {
        });
      }
    });
    return (_ctx, _cache) => {
      return vue.withDirectives((vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElFormItem), {
        ref_key: "formItemRef",
        ref: formItemRef,
        prop: props.config.model,
        label: props.config.label,
        "label-width": props.config.labelWidth,
        rules: props.visible ? props.config.rules : [],
        "inline-message": props.config.inlineMessage,
        class: vue.normalizeClass(`dc-form-item ${props.config.class || ""}`),
        style: vue.normalizeStyle({
          marginRight: marginRight.value,
          marginBottom: marginBottom.value,
          marginTop: marginTop.value,
          marginLeft: marginLeft.value
        })
      }, vue.createSlots({
        default: vue.withCtx(() => [
          (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(vue.unref(constants.Component)[props.config.component]), vue.mergeProps({ ...props.config.props || {} }, {
            "model-value": props.modelValue,
            "file-list": props.modelValue,
            "row-index": props.rowIndex,
            disabled: props.config.disabled,
            "onUpdate:modelValue": itemChange,
            onBlur: handleBlur,
            onTriggerEffect: triggerEffect
          }), vue.createSlots({ _: 2 }, [
            vue.renderList(Object.keys(props.config.componentSlot || {}), (slotI) => {
              return {
                name: slotI,
                fn: vue.withCtx((scope) => [
                  vue.createVNode(vue.unref(index["default"]), {
                    vnode: (props.config.componentSlot || {})[slotI],
                    scope
                  }, null, 8, ["vnode", "scope"])
                ])
              };
            })
          ]), 1040, ["model-value", "file-list", "row-index", "disabled"])),
          props.config.rightSlot ? (vue.openBlock(), vue.createBlock(vue.unref(index["default"]), {
            key: 0,
            vnode: props.config.rightSlot
          }, null, 8, ["vnode"])) : vue.createCommentVNode("v-if", true)
        ]),
        _: 2
      }, [
        props.config.labelSlot ? {
          name: "label",
          fn: vue.withCtx((scope) => [
            vue.createVNode(vue.unref(index["default"]), {
              vnode: props.config.labelSlot,
              scope
            }, null, 8, ["vnode", "scope"])
          ]),
          key: "0"
        } : void 0
      ]), 1032, ["prop", "label", "label-width", "rules", "inline-message", "class", "style"])), [
        [vue.vShow, props.visible]
      ]);
    };
  }
});
var DcFormItem = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\form\\src\\components\\FormItem\\index.vue"]]);

exports["default"] = DcFormItem;
//# sourceMappingURL=index.js.map
