<template>
  <el-dialog class="host-add-detail-dialog" :model-value="props.visible" width="500px" :title="$t('replenish.hostAdditionStatus')" @close="onCancel">
    <el-steps class="host-add-detail" direction="vertical" :active="currentStep">
      <el-step v-for="(item, index) in currentExecuteRecordBoList" :key="index">
        <template #title>
          <div class="host-add-detail-top">
            <div class="host-add-detail-title">{{ $t('replenish.steps') }}{{ index + 1 }}</div>
            <div class="host-add-detail-dash"></div>
            <div class="host-add-detail-icon">
              <span :class="`icon ${statusColor[item.status]}`">
                <i class="iconfont icon-icon_chenggong" v-if="item.status === '2'" />
                <i class="iconfont icon-icon_shibai" v-if="item.status === '3'" />
                <i class="iconfont icon-icon_jinhangzhong" v-if="item.status === '1'" />
                <el-icon v-if="item.status === '0'"><RemoveFilled /></el-icon>
              </span>

              {{ statusText[item.status] }}
            </div>
          </div>
        </template>
        <template #description>
          <div class="host-add-detail-desc">{{ item.stepMsg }}</div>
          <div class="host-add-detail-err" v-if="item.failReason">
            {{ item.failReason }}
          </div>
        </template>
      </el-step>
    </el-steps>
    <template #footer>
      <BaseButton type="primary" :disabled="reAddLoading" @click="onReAdd" v-if="canReAdd"> 重新添加 </BaseButton>
      <BaseButton :loading="detailLoading" type="primary" @click="onReExec" v-if="canReExec"> 重新执行 </BaseButton>
      <BaseButton type="info" @click="onCancel"> {{ $t('replenish.close') }} </BaseButton>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { RemoveFilled } from '@element-plus/icons-vue'
import elConfim from '@/utils/elConfim'
import { HostType } from '../types'

const { route, t, router, store, $has, setBreadList } = useBasicTool()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  executeRecordBoList: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  isFail: {
    type: Boolean,
    default: false
  },
  hostId: {
    type: String,
    default: ''
  },
  hostType: {
    type: Number as PropType<HostType>,
    default: HostType.HOST
  },
  hostData: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['close', 'refresh'])

const statusText: Record<string, string> = {
  '0': t('replenish.awaitingDeployment2'),
  '1': t('replenish.deploying'),
  '2': t('replenish.success'),
  '3': t('replenish.failure')
}

const statusColor: Record<string, string> = {
  '0': 'gray',
  '1': 'orange',
  '2': 'green',
  '3': 'red'
}

const reAddLoading = ref(false)
const currentExecuteRecordBoList = ref<any[]>([])
let timer: NodeJS.Timer
const detailLoading = ref(false)

const currentStep = computed(() => {
  const index = props.executeRecordBoList?.findIndex((i) => i.status === '1')
  return index === -1 ? props.executeRecordBoList.length : index
})

const canReAdd = computed(() => {
  const step1 = props.executeRecordBoList[0]
  const step2 = props.executeRecordBoList[1]
  return props.isFail && (step1?.status === '3' || step2?.status === '3')
})

const canReExec = computed(() => {
  const step1 = props.executeRecordBoList[0]
  const step2 = props.executeRecordBoList[1]
  return props.isFail && step1?.status === '2' && step2?.status === '2'
})
const onCancel = () => {
  emits('close')
}

const onReAdd = () => {
  elConfim
    .confim({
      isCancelButton: true,
      message: `点击重新添加，将会删除当前主机记录并重新添加主机。`
    })
    .then((res) => {
      reAddLoading.value = true
      store
        .dispatch('hosts/deleteHost', { id: props.hostId })
        .then((res) => {
          router.push({
            name: 'hostsAdd',
            query: {
              type: props.hostType
            }
          })
        })
        .finally(() => {
          reAddLoading.value = false
        })
    })
}
const onReExec = () => {
  const { id, ip, name, namespaceId, rackId, sshPort, sudoUser, userPwd } = props.hostData
  store
    .dispatch('hosts/reExecuteAddHosts', {
      hostType: props.hostType,
      ip,
      name,
      namespaceId,
      rackId,
      sshPort,
      sudoUser,
      userPwd,
      loginType: sudoUser ? 1 : 2,
      id,
      serialNo: id
    })
    .then((res) => {
      startDetailLoop()
    })
}

const startDetailLoop = () => {
  detailLoading.value = true
  timer = setInterval(() => {
    getDetail()
  }, 3000)
  getDetail()
}

const stopDetailLoop = () => {
  detailLoading.value = false
  if (timer) {
    clearInterval(timer)
  }
}

const getDetail = () => {
  store
    .dispatch('hosts/hostAddProgress', { hostIp: props.hostData.ip })
    .then((res) => {
      currentExecuteRecordBoList.value = res.data
      if (
        currentExecuteRecordBoList.value.some((item) => item.status === '3') ||
        currentExecuteRecordBoList.value.every((item) => item.status === '2')
      ) {
        stopDetailLoop()
      }
    })
    .catch((err) => {
      stopDetailLoop()
    })
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      currentExecuteRecordBoList.value = [...props.executeRecordBoList]
    } else {
      stopDetailLoop()
    }
  }
)
onBeforeMount(() => {
  stopDetailLoop()
})
</script>

<style lang="scss">
.host-add-detail {
  overflow-y: auto;
  max-height: 400px;
  padding: 20px 60px;
  .el-step__main {
    margin-top: 0 !important;
    margin-bottom: 20px;
  }
  .el-step__description {
    padding-right: 0;
  }
  &-top {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  &-title {
    font-size: 14px;
  }
  &-icon {
    width: 100px;
    font-size: 14px;
    color: rgba(41, 51, 78, 0.85);
    .icon {
      .el-icon {
        position: relative;
        top: 2px;
        font-size: 15px;
      }
      .iconfont {
        font-size: 14px;
      }
    }
    .green {
      color: rgba(59, 187, 110, 1);
    }
    .red {
      color: rgba(240, 93, 79, 1);
    }
    .orange {
      color: rgba(250, 173, 20, 1);
    }
    .gray {
      color: RGBA(118, 125, 141, 1);
    }
    .gray2 {
      color: rgba(153, 160, 181, 1);
    }
  }
  &-dash {
    flex: 1;
    height: 0px;
    border-top: 1px dashed rgba(41, 51, 78, 0.85);
    margin: 0 40px;
  }
  &-desc {
    font-size: 14px;
    font-weight: 400;
    color: rgba(153, 160, 181, 1);
  }
  &-err {
    padding: 12px 16px;
    border-radius: 2px;
    margin-top: 8px;
    font-size: 14px;
    line-height: 22px;
    color: rgba(240, 93, 79, 1);
    background: #f4f5f9;
  }
  &-dialog {
    .el-dialog__body {
      padding: 0;
    }
  }
}
</style>
