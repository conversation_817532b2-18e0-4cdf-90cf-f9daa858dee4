{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/edit-table-column/src/index.vue"], "sourcesContent": ["<!-- eslint-disable vue/valid-attribute-name -->\r\n<template>\r\n  <el-table-column v-bind=\"$attrs\" :prop=\"prop\" :label=\"label\" :width=\"width\">\r\n    <template #default=\"scope\">\r\n      <el-form-item\r\n        v-if=\"isEditing(scope.$index)\"\r\n        :prop=\"`model.${scope.$index}.formData.${prop}`\"\r\n        :rules=\"rules\"\r\n      >\r\n        <slot\r\n          name=\"edit\"\r\n          :$index=\"scope.$index\"\r\n          :row=\"getEditRow(scope.$index)\"\r\n          :column=\"scope.column\"\r\n          :actions=\"editActions ?? defaultEditActions\"\r\n        >\r\n          {{ calculateColumnDefaultValue(scope) }}\r\n        </slot>\r\n      </el-form-item>\r\n      <slot\r\n        v-else\r\n        :$index=\"scope.$index\"\r\n        :row=\"scope.row\"\r\n        :column=\"scope.column\"\r\n        :actions=\"editActions ?? defaultEditActions\"\r\n      >\r\n        {{ calculateColumnDefaultValue(scope) }}\r\n      </slot>\r\n    </template>\r\n  </el-table-column>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ElFormItem, ElTableColumn } from \"element-plus\";\r\nimport type { FormItemRule } from \"element-plus\";\r\nimport type { PropType, Ref } from \"vue\";\r\nimport type { DcEditTableColumnScope } from \"./types\";\r\n// eslint-disable-next-line import/order\r\nimport { inject, watchEffect } from \"vue\";\r\nimport type {\r\n  DcEditTableEditActions,\r\n  DcEditTableFormModel,\r\n  DcEditTableFormModelItem,\r\n  DcEditTableFormProps,\r\n} from \"../../edit-table/src/types\";\r\n\r\ndefineOptions({\r\n  name: \"DcEditTableColumn\",\r\n});\r\n\r\nconst props = defineProps({\r\n  prop: {\r\n    type: String,\r\n  },\r\n  label: {\r\n    type: String,\r\n  },\r\n  width: {\r\n    type: String,\r\n  },\r\n  rules: {\r\n    type: Array as PropType<FormItemRule[]>,\r\n  },\r\n});\r\n\r\nconst defaultEditActions: DcEditTableEditActions = {};\r\n\r\nconst editActions = inject<DcEditTableEditActions | undefined>(\"editActions\");\r\n\r\nconst formModel = inject<Ref<DcEditTableFormModel | undefined>>(\"formModel\");\r\n\r\nconst formProps = inject<Ref<DcEditTableFormProps | undefined>>(\"formProps\");\r\n\r\nwatchEffect(() => {\r\n  if (props.prop) {\r\n    formProps?.value?.add(props.prop);\r\n  }\r\n});\r\n\r\nconst getEditModel = (index: number): DcEditTableFormModelItem => {\r\n  if (!formModel || !formModel.value?.model) {\r\n    return {\r\n      isEditing: false,\r\n      isNew: false,\r\n      formData: {},\r\n      data: {},\r\n    };\r\n  }\r\n  return formModel.value.model[index];\r\n};\r\n\r\nconst getEditRow = (index: number): any => getEditModel(index).formData;\r\n\r\nconst isEditing = (index: number): boolean =>\r\n  getEditModel(index).isEditing ?? false;\r\n\r\nconst calculateColumnDefaultValue = (scope: DcEditTableColumnScope) => {\r\n  if (props.prop) return scope.row?.[props.prop];\r\n};\r\n</script>\r\n"], "names": ["DO_defineComponent", "inject", "watchEffect"], "mappings": ";;;;;;;;;;AA6Cc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAed,IAAA,MAAM,qBAA6C,EAAC,CAAA;AAE9C,IAAA,MAAA,WAAA,GAAcC,WAA2C,aAAa,CAAA,CAAA;AAEtE,IAAA,MAAA,SAAA,GAAYA,WAA8C,WAAW,CAAA,CAAA;AAErE,IAAA,MAAA,SAAA,GAAYA,WAA8C,WAAW,CAAA,CAAA;AAE3E,IAAAC,eAAA,CAAY,MAAM;AAChB,MAAA,IAAI,MAAM,IAAM,EAAA;AACH,QAAA,SAAA,EAAA,KAAA,EAAO,GAAI,CAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AAAA,OAClC;AAAA,KACD,CAAA,CAAA;AAEK,IAAA,MAAA,YAAA,GAAe,CAAC,KAA4C,KAAA;AAChE,MAAA,IAAI,CAAC,SAAA,IAAa,CAAC,SAAA,CAAU,OAAO,KAAO,EAAA;AAClC,QAAA,OAAA;AAAA,UACL,SAAW,EAAA,KAAA;AAAA,UACX,KAAO,EAAA,KAAA;AAAA,UACP,UAAU,EAAC;AAAA,UACX,MAAM,EAAC;AAAA,SACT,CAAA;AAAA,OACF;AACO,MAAA,OAAA,SAAA,CAAU,MAAM,KAAM,CAAA,KAAA,CAAA,CAAA;AAAA,KAC/B,CAAA;AAEA,IAAA,MAAM,UAAa,GAAA,CAAC,KAAuB,KAAA,YAAA,CAAa,KAAK,CAAE,CAAA,QAAA,CAAA;AAE/D,IAAA,MAAM,YAAY,CAAC,KAAA,KACjB,YAAa,CAAA,KAAK,EAAE,SAAa,IAAA,KAAA,CAAA;AAE7B,IAAA,MAAA,2BAAA,GAA8B,CAAC,KAAkC,KAAA;AACrE,MAAA,IAAI,KAAM,CAAA,IAAA;AAAa,QAAA,OAAA,KAAA,CAAM,MAAM,KAAM,CAAA,IAAA,CAAA,CAAA;AAAA,KAC3C,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}