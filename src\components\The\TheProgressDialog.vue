<template>
  <el-dialog :model-value="modelValue" :title="title || $t('replenish.progress')" width="800px" :before-close="handleClose">
    <div class="header">
      <div class="header-item">
        {{ $t('form.state') + '：' }}
        <ProgressState :state="headerData?.state ?? 0" if-text>
          <span v-if="[0, 1].includes(headerData?.state) || !headerData?.state">
            {{ headerData?.state === 1 ? stateRunTextMap.get(state) : stateWaitTextMap.get(state) }}
          </span>
        </ProgressState>
      </div>
      <div class="header-item">{{ $t('form.time') + '：' }}{{ headerData?.createTime }}</div>
      <div class="header-item">{{ $t('form.durationOfOperation') + '：' }}{{ headerData?.executeTime ?? '-' }}</div>
    </div>
    <BaseTable
      class="the-progress-dialog-table"
      :columns="tableData.columns"
      :data="data"
      row-key="id"
      :height="400"
      :expand-row-keys="expandRowKeys"
      :tree-props="{ children: 'childList' }"
    >
      <template #progressContent="scope">
        <ProgressState :state="scope.row?.state ?? 0" :if-text="false" />
        {{ scope.row?.progressContent }}
      </template>
      <template #executeTime="scope">
        {{ scope.row?.executeTime ?? '-' }}
      </template>
    </BaseTable>
    <template #footer>
      <span>
        <BaseButton type="primary" v-if="isHistory" :disabled="headerData?.state !== 3" @click="rerun(headerData.groupNumber)">{{
          $t('button.reexecute')
        }}</BaseButton>
        <BaseButton type="info" @click="handleClose"> {{ $t('button.down') }} </BaseButton>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import ProgressState from './TheProgressDialogState.vue'
const { t } = useI18n()
const expandRowKeys = ref([])
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
    default: false
  },
  data: {
    type: Array as any,
    default: () => []
  },
  title: {
    type: String,
    default: ''
  },
  state: {
    type: Number,
    default: NaN
  },
  isHistory: {
    type: Boolean,
    default: true
  }
})
const headerData = computed(() => {
  if (props.data) return props.data[0]
  else return {}
})
// 状态 0-未执行 1-执行中 2-执行成功 3-执行失败
const stateRunTextMap = new Map([
  [1, t('replenish.deploying')],
  [10, t('replenish.starting')],
  [11, t('replenish.restarting')],
  [12, t('replenish.stopping')],
  [13, t('replenish.deleting')],
  [18, t('replenish.scaling')],
  [20, t('replenish.scheduling')],
  [22, t('replenish.updating')],
  [26, t('replenish.rollingRestarting')]
])

const stateWaitTextMap = new Map([
  [1, t('replenish.awaitingDeployment')],
  [2, t('replenish.awaitingDeployment')],
  [10, t('replenish.awaitingStart')],
  [11, t('replenish.awaitingRestart')],
  [12, t('replenish.awaitingStop')],
  [13, t('replenish.awaitingDeletion')],
  [14, t('replenish.awaitingStart')],
  [15, t('replenish.awaitingRestart')],
  [16, t('replenish.awaitingStop')],
  [17, t('replenish.awaitingDeletion')],
  [18, t('replenish.awaitingExpansion')],
  [19, t('replenish.awaitingExpansion')],
  [20, t('replenish.awaitingScheduling')],
  [21, t('replenish.awaitingScheduling')],
  [22, t('replenish.awaitingUpdate')],
  [22, t('replenish.awaitingUpdate')],
  [26, t('replenish.awaitingRollingRestart')],
  [27, t('replenish.awaitingRollingRestart')]
])

// 表格数据
const tableData = reactive({
  columns: [
    {
      prop: 'progressContent',
      label: computed(() => t('table.stepContent')),
      slot: true,
      'show-overflow-tooltip': true
    },
    { prop: 'createTime', label: computed(() => t('table.time')), width: 180 },
    { prop: 'executeTime', label: computed(() => t('table.durationOfOperation')), width: 90, slot: true }
  ]
})
const emit = defineEmits(['update:modelValue', 'clone', 'restartRerun'])
function handleClose() {
  emit('update:modelValue', false)
  emit('clone')
}
function rerun(groupNumber: string) {
  emit('restartRerun', groupNumber)
}
</script>

<style lang="scss" scoped>
.header {
  @include flex();
  .header-item {
    width: 252px;
    font-size: 14px;
  }
}
.the-progress-dialog-table {
  margin-top: 20px;
}
</style>
