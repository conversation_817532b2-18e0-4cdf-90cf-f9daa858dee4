<template>
  <BaseElTooltip :content="content" :disabled="isTip" placement="top">
    <span class="box">
      <span ref="contentRef">{{ content }}</span>
    </span>
  </BaseElTooltip>
</template>
<script lang="ts" setup>
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  width: {
    type: Number,
    default: 100
  }
})
const contentRef = ref()
const widthPx = computed(() => props.width + 'px')
const offsetWidth = computed(() => contentRef.value?.offsetWidth)
const isTip = computed(() => offsetWidth.value < props.width)
</script>
<style lang="scss" scoped>
.box {
  display: inline-block;
  max-width: v-bind(widthPx);
  @include text-no-wrap();
  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
