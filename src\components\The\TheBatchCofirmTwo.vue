<template>
  <el-dialog :model-value="modelValue" :title="$t('title.tip')" width="440px" :before-close="handleClose">
    <div class="messge-batch-box">
      <div class="messge-container">
        <i class="iconfont icon-icon_jinggaoo" />
        <div>
          {{ params.message }}
        </div>
      </div>
      <div class="messge-description">
        {{ params.desc }}
      </div>
      <div class="batch-item-box">
        <div class="batch-item-box_left">
          {{ params.batchLable }}
        </div>
        <div class="batch-item-box_right">
          <div :class="{ 'batch-item-box_right-content': isExpand }">
            <span ref="batchItemRef">{{ params.itemList }}</span>
          </div>
          <span class="button" v-if="batchItemRightButton">
            <slot name="button">
              <BaseButton type="primary" link @click="() => (isExpand = !isExpand)">
                {{ isExpand ? $t('button.expand') : $t('button.putAway') }}
              </BaseButton>
            </slot>
          </span>
        </div>
      </div>
    </div>
    <template #footer>
      <span>
        <BaseButton type="primary" @click="submitForm"> {{ $t('button.sure') }} </BaseButton>
        <BaseButton type="info" @click="resetForm">{{ $t('button.cancel') }}</BaseButton>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
type Params = {
  message: string
  desc: string
  batchLable: string
  itemList: string
}
interface Props {
  modelValue: boolean
  params: Params
}
withDefaults(defineProps<Props>(), {
  modelValue: false,
  params: () => ({
    message: '我是谁?',
    desc: '',
    batchLable: '已选主机(12):',
    itemList:
      '主机1（IP）；主机2（IP）；主机3（IP）；主机4（IP）；主机1（IP）；主机2（IP）；主机3（IP）；主机4（IP）；主机1（IP）；主机2（IP）；主机3（IP）；；主机2（IP）；主机3'
  })
})
const batchItemRef = ref()
const isExpand = ref(true)
const batchItemRightButton = computed(() => {
  if (batchItemRef.value?.offsetHeight > 80) {
    return true
  } else {
    return false
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])
const handleClose = () => {
  shutDown()
}
function shutDown() {
  emit('update:modelValue', false)
}

function submitForm() {
  emit('submit')
  shutDown()
}

function resetForm() {
  shutDown()
}
</script>

<style lang="scss" scoped>
.messge-batch-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  min-height: 140px;
  padding-right: 20px;
  padding-bottom: 10px;
  padding-left: 35px;
  margin-top: -8px;
  margin-right: 0;
  margin-left: -16px;
  .iconfont {
    color: var(--ops-state-abnormal-color);
  }
}
.batch-item-box {
  position: relative;
  padding: 16px 20px;
  font-size: 14px;
  color: var(--ops-secondary-tips-text-color);
  background-color: rgb(200 204 218 / 30%);
  .batch-item-box_left {
    color: var(--ops-text-color);
  }
  .batch-item-box_right {
    .batch-item-box_right-content {
      @include text-ellipsis(4);
    }
  }
  .button {
    position: absolute;
    right: 16px;
    bottom: 7px;
    background-color: #ecedf2;
  }
}
.messge-container {
  display: flex;
  padding: 0px 20px 8px 0;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  color: var(--ops-tips-text-color);
  .iconfont {
    margin-right: 10px;
  }
}
.messge-description {
  padding-right: 20px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 400;
  color: var(--ops-secondary-tips-text-color);
}
</style>
