import { ref } from 'vue';

function useDrawer({
  cancel,
  confirm,
  show,
  defaultData
} = {}) {
  const visible = ref(false);
  const drawerData = ref(defaultData);
  const showDrawer = (val) => {
    visible.value = true;
    drawerData.value = val;
    if (show && typeof show === "function") {
      show(val);
    }
  };
  const onCancel = (val) => {
    visible.value = false;
    if (cancel && typeof cancel === "function") {
      cancel(val, drawerData.value);
    }
  };
  const onConfirm = (val) => {
    onCancel();
    if (confirm && typeof confirm === "function") {
      confirm(val, drawerData.value);
    }
  };
  return {
    showDrawer,
    onCancel,
    onConfirm,
    visible,
    drawerData
  };
}

export { useDrawer };
//# sourceMappingURL=drawer.mjs.map
