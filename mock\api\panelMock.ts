export default [
  {
    url: '/api/ops/panel/queryByLabel',
    method: 'POST',
    response: () => ({
      "code": 0,
      "msg": "成功",
      "data": [
        {
          "id": 4,
          "label": "panel-host",
          "remark": "主机监控-连接记录数",
          "panelUrl": "http://***********:32763/d/U2RL6BBnz/node-exporter-server-metrics?orgId=1&viewPanel=24&var-node=************:9100&kiosk",
          "panelWidth": null,
          "panelHeight": null
        },
        {
          "id": 4,
          "label": "panel-host",
          "remark": "主机监控-连接记录数",
          "panelUrl": "http://***********:32763/d/U2RL6BBnz/node-exporter-server-metrics?orgId=1&viewPanel=24&var-node=************:9100&kiosk",
          "panelWidth": null,
          "panelHeight": null
        },
        {
          "id": 4,
          "label": "panel-host",
          "remark": "主机监控-连接记录数",
          "panelUrl": "http://***********:32763/d/U2RL6BBnz/node-exporter-server-metrics?orgId=1&viewPanel=24&var-node=************:9100&kiosk",
          "panelWidth": null,
          "panelHeight": null
        },
        {
          "id": 4,
          "label": "panel-host",
          "remark": "主机监控-连接记录数",
          "panelUrl": "http://***********:32763/d/U2RL6BBnz/node-exporter-server-metrics?orgId=1&viewPanel=24&var-node=************:9100&kiosk",
          "panelWidth": null,
          "panelHeight": null
        },
      ]
    })
  },
]