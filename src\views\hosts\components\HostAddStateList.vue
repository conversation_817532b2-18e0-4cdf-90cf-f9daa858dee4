<template>
  <div class="host-add-state">
    <BaseLayout :is-header="false">
      <template #content>
        <div class="host-add-state-content">
          <TheTitle inline>
            <template #titleText>{{ $t('replenish.hostAdditionProgress') }}</template>
          </TheTitle>

          <BaseTable :columns="tableData.column" :data="tableData.data">
            <template #stepMsg="scope">
              <el-icon v-if="scope.row.stete === 'LOADING'" class="is-loading icon-loading">
                <Loading />
              </el-icon>
              <span v-else :class="['status-spot', `${scope.row.stete === 'FAIL' ? 'status-spot--failure' : 'status-spot--running'}`]" />
              <span>{{ scope.row.stepMsg }}</span>
            </template>
            <template #operate="scope">
              <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" />
            </template>
          </BaseTable>

          <div class="host-add-state-page">
            <BasePagination v-model="pageInfo" @page-change="handlePageChange" />
          </div>
        </div>
      </template>
      <template #footer>
        <div>
          <BaseButton type="primary" @click="router.push({ name: 'hosts' })"> {{ $t('replenish.returnToList') }} </BaseButton>
        </div>
      </template>
    </BaseLayout>
  </div>
</template>

<script lang="ts" setup>
import TheTitle from '@/components/The/TheTitle.vue'
import Loop from '@/utils/Loop'
import { Loading } from '@element-plus/icons-vue'
const { pageInfo } = usePage()
const { route, t, router, store } = useBasicTool()
const pageData = ref([])
const lock = ref<boolean>(false)
let timer: any
onMounted(() => {
  getProgress()
  timer = new Loop(() => {
    getProgress()
  }, 10000)
})
onUnmounted(() => {
  timer.clearLoop()
})
// 列表配置项
const tableData = reactive<any>({
  column: [
    { prop: 'onlineState', label: computed(() => t('table.serialNumber')), width: '60px', type: 'index' },
    { prop: 'ip', label: 'IP', width: '220px' },
    { prop: 'hostName', label: computed(() => t('table.hostName')), width: '220px' },
    { prop: 'stepMsg', label: computed(() => t('table.state')), slot: true },
    { prop: 'operate', label: computed(() => t('table.operate')), slot: true, width: '120px', fixed: 'right' }
  ],
  data: []
})

// 表格操作栏数据
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('button.reexecute'),
        disabled: false,
        click: () => {
          store
            .dispatch('hosts/reExecuteAddHosts', {
              ...scope.row.hostAddRequest,
              serialNo: route.query.serialNumber,
              hostType: route.query.hostType ? Number(route.query.hostType) : undefined
            })
            .then((res) => {
              router.replace({
                name: 'HostAddStateList',
                query: { serialNumber: res.data, hostType: route.query.hostType }
              })
              timer && timer.clearLoop()
              getProgress()
              timer = new Loop(() => {
                getProgress()
              }, 10000)
            })
        },
        isButton: scope.row.stete === 'FAIL'
      }
    ]
  }
}
function getProgress() {
  if (lock.value) return
  lock.value = true
  store
    .dispatch('hosts/getProgress', route.query.serialNumber)
    .then((res) => {
      const data: any = []
      Object.keys(res.data || {})
        .sort()
        .reverse()
        .map((item) => {
          data.push({
            ...res.data[item],
            ip: item
          })
        })
      pageData.value = data
      pageInfo.total = pageData.value.length
      if (res.data && !data.some((item: any) => item.stete === 'LOADING')) {
        timer.clearLoop()
      }
      setTableData()
    })
    .finally(() => {
      lock.value = false
    })
}

function setTableData() {
  const start = (pageInfo.pageNo - 1) * pageInfo.pageSize
  tableData.data = pageData.value.splice(start, pageInfo.pageSize)
}
function handlePageChange() {
  setTableData()
}
</script>

<style lang="scss" scoped>
.host-add-state {
  :deep(.layout) {
    height: auto;
    min-height: calc(100vh - 100px);
    .layout-header {
      height: 48px;
      border-bottom: 1px solid var(--ops-border-color);
    }
    .layout-content {
      height: calc(100vh - 208px);
      padding: 0px 136px;
      margin: 0 auto;
    }
    .layout-footer {
      height: 60px;
      border-top: 1px solid var(--ops-border-color);
    }
  }
  &-content {
    padding-top: 20px;
  }
  &-page {
    display: flex;
    flex-direction: row-reverse;
    padding-top: 5px;
  }
  .icon-loading {
    position: relative;
    top: 2px;
    left: -3px;
  }
}
</style>
