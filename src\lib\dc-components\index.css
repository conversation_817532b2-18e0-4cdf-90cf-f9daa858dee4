.view-layout {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.view-header {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  box-shadow: inset 0px -1px 0px 0px #dfe1e5;
}
.view-header--name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #29344e;
  line-height: 24px;
  text-align: left;
}

.view-search {
  padding: 20px;
}

.view-table {
  padding: 0 20px;
  flex: 1;
  margin-bottom: v-bind(contentPadding);
  overflow: auto;
}

.view-pagination {
  width: 100%;
  position: absolute;
  right: 0px;
  bottom: 0;
  padding: 0 20px;
  box-sizing: border-box;
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fff;
}

.dc-search-filter-expand {
  font-size: 14px;
  cursor: pointer;
  color: var(--el-color-primary);
}
.dc-search-filter-expand_text {
  margin-left: 5px;
}

.dc-search-filter.with-expand {
  display: inline-flex;
  width: 100%;
  flex-flow: row nowrap;
}
.dc-search-filter.with-expand .dc-search-filter-form {
  flex: 1;
}
.dc-drawer.no-footer .el-drawer__footer {
  display: none;
}
.dc-drawer.no-header .el-drawer__header {
  display: none;
}

.dc-dialog.no-footer .el-dialog__footer {
  display: none;
}
.dc-dialog.no-header .el-dialog__header {
  display: none;
}

.dc-table-columns-filter_icon {
  cursor: pointer;
}
.dc-table-columns-filter_operator {
  display: flex;
  justify-content: space-between;
  padding: 0px 10px;
}
.dc-table-columns-filter_button-box {
  display: flex;
  flex-direction: row-reverse;
  padding: 10px 10px;
}
.dc-table-columns-filter_dropdown-box {
  min-width: 200px;
}

:root {
  --dc-component--color-blue-1: #eef2fe;
  --dc-component--color-blue-2: #ccd7ff;
  --dc-component--color-blue-3: #567aff;
  --dc-component--color-red-1: #feefed;
  --dc-component--color-red-2: #f9beb9;
  --dc-component--color-red-3: #f05d4f;
  --dc-component--color-yellow-1: #fff7e8;
  --dc-component--color-yellow-2: #fddea2;
  --dc-component--color-yellow-3: #faad14;
  --dc-component--color-green-1: #e5f9ed;
  --dc-component--color-green-2: #99eab9;
  --dc-component--color-green-3: #3bbb6e;
  --dc-component--color-text-1: #29344e;
  --dc-component--color-text-2: rgba(41, 52, 78, 0.85);
  --dc-component--color-text-3: rgba(41, 52, 78, 0.65);
  --dc-component--color-text-5: rgba(41, 52, 78, 0.3);
  --dc-component--color-text-6: rgba(41, 52, 78, 0.4);
  --dc-component--color-text-7: #545c71;
  --dc-component--color-gray-1: rgba(41, 52, 78, 0.15);
  --dc-component--color-gray-6: rgba(236, 241, 246, 0.55);
  --dc-component--color-gray-13: #eff1f5;
  --dc-component--color-gray-8: #f7f9fc;
  --dc-component--color-title: var(--dc-component--color-text-1);
  --dc-component--color-text: var(--dc-component--color-text-2);
  --dc-component--color-icon: var(--dc-component--color-text-7);
  --dc-component--color-placeholder: var(--dc-component--color-text-6);
  --dc-component--color-disabled: var(--dc-component--color-text-6);
  --dc-component--color-border: var(--dc-component--color-gray-1);
  --dc-component--color-shadow: var(--dc-component--color-gray-1);
  --dc-component--color-dialog-header-bg: var(--dc-component--color-gray-6);
  --dc-component--color-primary: var(--dc-component--color-blue-3);
  --dc-component--color-primary-border: var(--dc-component--color-blue-2);
  --dc-component--color-primary-shadow: var(--dc-component--color-blue-2);
  --dc-component--color-primary-hover: #708fff;
  --dc-component--color-primary-active: #4d6ee6;
}

.cyber-login {
  height: 100%;
  display: flex;
  min-height: 580px;
}
.cyber-login .el-input .el-input__inner {
  font-size: 14px;
}
.cyber-login .el-input input:-webkit-autofill,
.cyber-login .el-input textarea:-webkit-autofill,
.cyber-login .el-input select:-webkit-autofill {
  background-color: transparent;
  transition: background-color 50000s ease-in-out 0s;
}
.cyber-login .el-input input::-webkit-input-placeholder {
  color: var(--dc-component--color-placeholder);
}
.cyber-login .el-input input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: var(--dc-component--color-placeholder);
}
.cyber-login .el-input input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: var(--dc-component--color-placeholder);
}
.cyber-login .mll {
  margin-left: 16px;
}
.cyber-login .flex {
  display: flex;
  align-items: center;
}
.cyber-login .login-img {
  width: 37.5%;
  height: 100%;
  background: no-repeat;
  background-size: 100% 100%;
  position: relative;
  min-width: 620px;
}
.cyber-login .login-img .el-image {
  width: 100%;
  height: 100%;
}
.cyber-login .login-img .el-carousel {
  height: 100%;
}
.cyber-login .login-img .el-carousel .el-carousel__container {
  height: 100%;
}
.cyber-login .login-img .el-carousel .el-carousel__container .login-img {
  width: 100%;
}
.cyber-login .login-img .el-carousel .el-carousel__indicators {
  bottom: 60px;
}
.cyber-login .login-img .el-carousel .el-carousel__indicators .el-carousel__indicator .el-carousel__button {
  width: 10px;
  height: 10px;
  background: var(--dc-component--color-primary);
  opacity: 0.3;
  border-radius: 5px;
}
.cyber-login .login-img .el-carousel .el-carousel__indicators .el-carousel__indicator.is-active .el-carousel__button {
  opacity: 1;
  width: 40px;
}
.cyber-login .login-img .content {
  position: absolute;
  left: 50px;
  top: 12%;
  width: calc(100% - 100px);
}
.cyber-login .login-img .content .title {
  font-size: 32px;
  font-weight: 600;
  color: var(--dc-component--color-title);
  margin-bottom: 12px;
}
.cyber-login .login-img .content .desc {
  font-size: 20px;
  font-weight: 400;
  color: var(--dc-component--color-text-3);
}
.cyber-login .login-content {
  flex: 1;
  background: #ffffff;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  position: relative;
}
.cyber-login .login-content .login-content-logo {
  padding: 0 30px;
  font-size: 24px;
  font-weight: 600;
  color: var(--dc-component--color-primary);
  line-height: 33px;
  height: 93px;
  display: flex;
  align-items: center;
}
.cyber-login .login-content .login-content-logo .login-content-logo-desc {
  font-size: 16px;
  font-weight: 400;
  color: var(--dc-component--color-text-3);
  margin-left: 16px;
}
.cyber-login .login-content .login-content-form {
  width: 340px;
  margin: auto;
  transform: translateY(-42px);
}
.cyber-login .login-content .login-content-form .form-title {
  margin-bottom: 30px;
  display: flex;
  color: var(--dc-component--color-title);
}
.cyber-login .login-content .login-content-form .form-title div:first-child {
  font-size: 24px;
  margin-right: 8px;
}
.cyber-login .login-content .login-content-form .form-title div:last-child {
  font-size: 16px;
  vertical-align: bottom;
  padding-top: 7px;
}
.cyber-login .login-content .login-content-form .support-register {
  width: 100%;
  margin-top: -50px;
}
.cyber-login .login-content .login-content-form .support-register .w100 {
  width: 100%;
}
.cyber-login .login-content .login-content-form .support-register .text-center {
  text-align: center;
  cursor: pointer;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item {
  margin: 30px 0;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content {
  display: flex;
  position: relative;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content::before {
  content: "";
  height: 50px;
  width: 14px;
  position: absolute;
  left: 220px;
  top: -1px;
  background: #fff;
  z-index: 100;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content .el-input {
  width: 220px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content .code-img {
  flex: 1;
  height: 48px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content .code-img .el-image {
  width: 100%;
  height: 48px;
  cursor: pointer;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content .code-img .el-image .el-image__error {
  padding-left: 14px;
  width: calc(100% - 14px);
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.code-form-item .el-form-item__content .code-img .el-icon {
  width: 100%;
  height: 100%;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item:last-child {
  margin-bottom: 100px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item .el-input > .el-input__wrapper:hover {
  border: 1px solid var(--dc-component--color-primary);
  padding-left: 47px;
  padding-right: 10px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item .el-input > .el-input__wrapper.is-focus {
  border: 2px solid var(--dc-component--color-primary-border);
  box-shadow: 0 0 0 1px var(--dc-component--color-primary) inset;
  padding-left: 46px;
  padding-right: 9px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.is-error .el-input > .el-input__wrapper {
  border: 1px solid var(--dc-component--color-red-3);
  padding-left: 47px;
  padding-right: 10px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item.is-error .el-input > .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--dc-component--color-red-3) inset !important;
  border: 2px solid var(--dc-component--color-red-2) !important;
  padding-left: 46px;
  padding-right: 9px;
}
.cyber-login .login-content .login-content-form .el-form .el-input {
  background: #f2f4f9;
  width: 360px;
  max-width: none;
  border-radius: 2px;
  box-sizing: border-box;
  height: 48px;
  overflow: hidden;
}
.cyber-login .login-content .login-content-form .el-form .el-input .el-input-group__prepend {
  background: transparent;
  padding-left: 16px;
}
.cyber-login .login-content .login-content-form .el-form .el-input .el-input-group__prepend svg {
  display: inline-flex;
  align-items: center;
  width: 16px;
  height: 16px;
  fill: currentColor;
  overflow: hidden;
  outline: none;
  flex-shrink: 0;
  font-size: 16px;
  color: var(--dc-component--color-icon);
}
.cyber-login .login-content .login-content-form .el-form .el-input .el-input__suffix {
  background: #f2f4f9;
}
.cyber-login .login-content .login-content-form .el-form .el-input .el-input__suffix svg {
  display: inline-flex;
  align-items: center;
  width: 14px;
  height: 14px;
  fill: currentColor;
  overflow: hidden;
  outline: none;
  cursor: pointer;
  flex-shrink: 0;
  font-size: 16px;
  color: var(--dc-component--color-icon);
}
.cyber-login .login-content .login-content-form .el-form .el-input__wrapper {
  background: transparent;
  border: none;
  box-shadow: none;
  padding-left: 48px;
  border-radius: 2px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item__error {
  padding-top: 3px;
}
.cyber-login .login-content .login-content-form .el-form .el-input-group__prepend {
  padding: 18px 0 18px 19px;
  height: 44px;
  box-sizing: border-box;
  border: none;
  box-shadow: none;
  position: absolute;
}
.cyber-login .login-content .login-content-form .el-form .el-input-group__prepend img {
  width: 14px;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item__content .el-input .el-input__inner {
  height: 44px;
  line-height: 44px;
  border: none;
}
.cyber-login .login-content .login-content-form .el-form .el-form-item__content .el-button {
  width: 100%;
  height: 48px;
  line-height: 48px;
  border-radius: 2px;
  border: none;
  margin-top: 20px;
  letter-spacing: 2px;
  background: var(--dc-component--color-primary);
  font-size: 16px;
  box-shadow: 0px 4px 10px 0px var(--dc-component--color-primary-shadow);
}
.cyber-login .login-content .login-content-form .el-form .el-form-item__content .el-button:hover, .cyber-login .login-content .login-content-form .el-form .el-form-item__content .el-button:focus {
  background: var(--dc-component--color-primary-hover);
}
.cyber-login .login-content .login-content-form .el-form .el-form-item__content .el-button:active {
  background: var(--dc-component--color-primary-active);
}
.cyber-login .login-content .login-content-form .el-form .el-form-item__content .el-button .el-button__text--expand {
  letter-spacing: 2px;
}
.cyber-login .login-content .login-content-bottom {
  position: absolute;
  font-size: 16px;
  font-weight: 400;
  bottom: 40px;
  left: 0;
  color: var(--dc-component--color-title);
  text-align: center;
  width: 100%;
}
.cyber-login .login-switch-language {
  position: absolute;
  right: 30px;
  top: 25px;
}
.cyber-login .login-switch-language svg {
  box-shadow: 0px 4px 10px 0px var(--dc-component--color-shadow);
  border-radius: 12px;
  cursor: pointer;
}
.cyber-login .el-overlay .el-overlay-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}
.cyber-login .el-overlay .el-dialog {
  margin: 0;
}
.cyber-login .el-overlay .el-dialog .el-dialog__header {
  height: 45px;
  background: var(--dc-component--color-dialog-header-bg);
  border-radius: 2px 2px 0px 0px;
  padding: 0;
  margin: 0;
  border-bottom: 1px solid var(--dc-component--color-border);
}
.cyber-login .el-overlay .el-dialog .el-dialog__header .el-dialog__title {
  font-size: 14px;
  line-height: 45px;
  padding-left: 15px;
  font-weight: 600;
  color: var(--dc-component--color-title);
}
.cyber-login .el-overlay .el-dialog .el-dialog__header .el-dialog__headerbtn {
  top: 0;
  height: 45px;
  width: 45px;
}
.cyber-login .el-overlay .el-dialog .el-dialog__header .el-dialog__headerbtn:hover .el-dialog__close {
  color: var(--dc-component--color-primary);
}
.cyber-login .el-overlay .el-dialog .el-dialog__body {
  padding: 35px 44px 60px 44px;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content {
  display: flex;
  justify-content: space-between;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item {
  width: 160px;
  height: 160px;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid var(--dc-component--color-border);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 22px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item .el-image img {
  height: 80px;
  width: 80px;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item:hover {
  border-color: var(--dc-component--color-primary);
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item .is-use {
  position: absolute;
  left: 0;
  top: 0;
  width: 48px;
  height: 20px;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: var(--dc-component--color-primary);
  line-height: 20px;
  text-align: center;
  border-right: 1px solid var(--dc-component--color-border);
  border-bottom: 1px solid var(--dc-component--color-border);
  border-radius: 2px;
  padding: 0 8px;
  background: #eef2fe;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item.disabled {
  background: #f7f9fc;
  cursor: not-allowed;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item.disabled:hover {
  border-color: #d6dde7;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item.disabled .title {
  opacity: 0.6;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item.disabled .base-image {
  opacity: 0.6;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item.disabled .recently-used {
  opacity: 0.6;
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item .title {
  margin-top: 18px;
  font-size: 16px;
  font-weight: 500;
  color: var(--dc-component--color-title);
}
.cyber-login .el-overlay .el-dialog .el-dialog__body .content .item .recently-used {
  position: absolute;
  height: 20px;
  background: #eef2fe;
  border-radius: 2px;
  border: 1px solid var(--dc-component--color-primary-border);
  padding: 0 8px;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 12px;
  font-weight: 400;
  color: var(--dc-component--color-primary);
  line-height: 20px;
}

html .el-popper.is-light.login-popper {
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px var(--dc-component--color-shadow);
  border: none;
}
html .el-popper.is-light.login-popper .el-popper__arrow::before {
  box-shadow: 0px 4px 20px 0px var(--dc-component--color-shadow);
  border: none;
}
html .lang-tolltip {
  padding: 5px 0;
}
html .lang-tolltip .lang-item {
  cursor: pointer;
  padding: 4px 11px;
  min-width: 60px;
  text-align: center;
}
html .lang-tolltip .lang-item:hover {
  background: rgba(41, 52, 78, 0.06);
}
html .lang-tolltip .lang-item.active {
  color: var(--dc-component--color-primary-active);
}

.dc-empty {
  height: 100%;
  width: 100%;
}
.dc-empty .el-empty {
  height: 100%;
}
.dc-empty .el-empty .el-empty__image {
  line-height: 0;
}
.dc-empty .el-empty .el-empty__image .el-image {
  height: 120px;
  width: 120px;
}
.dc-empty .el-empty .el-empty__description {
  margin-top: 8px;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: rgba(41, 51, 78, 0.65);
  line-height: 20px;
}
.dc-empty .el-empty .el-empty__description p {
  font-size: 12px;
}
.dc-empty .el-empty .el-empty__bottom {
  margin-top: 16px;
}
.dc-empty .el-empty div:first-child {
  margin-top: 0;
}

.dc-form-table .el-table__row:has(.cell .el-form-item.is-error) td.el-table__cell .cell {
  padding-bottom: 16px;
}

.dc-form-text {
  white-space: pre-wrap;
  word-break: break-all;
}