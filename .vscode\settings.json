{
  // eslint配置
  "javascript.format.enable": true,
  "typescript.format.enable": true,
  "editor.formatOnSave": true,
  // eslint配置项，保存时自动修复
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
    // "source.organizeImports": "explicit"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "vscode.html-language-features"
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 默认lf换行
  "files.eol": "\n",
  // stylelint配置
  "stylelint.enable": true,
  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,
  "[scss]": {
    "editor.formatOnSave": true
  },
  "stylelint.validate": [
    "css",
    "less",
    "postcss",
    "scss",
    "sass",
    "vue"
  ],
  "stylelint.customSyntax": "postcss-scss",
  //i18n-ally配置
  "i18n-ally.localesPaths": [
    "src/locales/lang"
  ],
  "i18n-ally.keystyle": "flat",
  "i18n-ally.sortKeys": true,
  "i18n-ally.namespace": true,
  "i18n-ally.pathMatcher": "{locale}/{namespaces}.{ext}",
  "i18n-ally.enabledParsers": [
    "json"
  ],
  "i18n-ally.sourceLanguage": "zh-CN",
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.enabledFrameworks": [
    "vue"
  ],
  "i18n-ally.translate.parallels": 1,
  "i18n-ally.translate.engines": [
    "openai"
  ],
  "i18n-ally.translate.baidu.appid": "20241030002189942",
  "i18n-ally.translate.baidu.apiSecret": "2w2wT_Vfq0g5nKj1bQl3",
  "testing.automaticallyOpenPeekView": "never",
  // 控制相关文件嵌套展示
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.env": "$(capture).env.*",
    "README.md": "Guide.md",
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,package-lock.json,.gitattributes,.gitignore,.gitpod.yml,CNAME,.npmrc,.browserslistrc,.nvmrc",
    ".eslintrc.js": ".eslintignore,.prettierignore,.stylelintignore,stylelint.config.js,.commitlintrc.js,commitlint.config.js,.prettierrc.js,.stylelintrc.js,.lintstagedrc"
  },
  "commentTranslate.source": "DarkCWK.youdao-youdao",
  "commentTranslate.hover.enabled": true,
  "commentTranslate.targetLanguage": "zh-CN",
  // 标签配置
  "todo-tree.regex.regex": "((%|#|//|<!--|^\\s*\\*)\\s*($TAGS)|^\\s*- \\[ \\])",
  "todo-tree.general.tags": [
    "todo", //添加自定义的标签成员,将在下面实现它们的样式
    "bug",
    "tag",
    "done",
    "mark",
    "test",
    "update",
    "fixme"
  ],
  "todo-tree.regex.regexCaseSensitive": false,
  "todo-tree.highlights.defaultHighlight": {
    //如果相应变量没赋值就会使用这里的默认值
    "foreground": "#000000", //字体颜色
    "background": "#0ae439", //背景色
    "icon": "check", //标签样式 check 是一个对号的样式
    "rulerColour": "#0ae439", //边框颜色
    "type": "tag", //填充色类型  可在TODO TREE 细节页面找到允许的值
    "iconColour": "#0ae439" //标签颜色
  },
  "todo-tree.highlights.customHighlight": {
    // 需要做的功能
    "todo": {
      "icon": "alert", //标签样式
      "background": "#F9D569", //背景色
      "rulerColour": "#F9D569", //外框颜色
      "iconColour": "#F9D569" //标签颜色
    },
    // 必须要修复的BUG
    "bug": {
      "background": "#E36777",
      "icon": "bug",
      "rulerColour": "#E36777",
      "iconColour": "#E36777"
    },
    // 标签
    "tag": {
      "background": "#9FD8FF",
      "icon": "tag",
      "rulerColour": "#9FD8FF",
      "iconColour": "#9FD8FF",
      "rulerLane": "full"
    },
    // 已完成的功能点
    "done": {
      "background": "#5eec95",
      "icon": "verified",
      "rulerColour": "#5eec95",
      "iconColour": "#5eec95"
    },
    // 需要注意的地方
    "mark": {
      "background": "#f90",
      "icon": "note",
      "rulerColour": "#f90",
      "iconColour": "#f90"
    },
    // 需要测试的地方
    "test": {
      "background": "#C39EE2",
      "icon": "flame",
      "rulerColour": "#C39EE2",
      "iconColour": "#C39EE2"
    },
    // 需要升级的地方
    "update": {
      "background": "#F690AA",
      "icon": "sync",
      "rulerColour": "#F690AA",
      "iconColour": "#F690AA"
    },
    // 需要修复的问题
    "fixme": {
      "background": "#FFB599",
      "icon": "tools",
      "rulerColour": "#FFB599",
      "iconColour": "#FFB599"
    }
  },
  "todo-tree.tree.expanded": true,
  "todo-tree.tree.buttons.export": true
}