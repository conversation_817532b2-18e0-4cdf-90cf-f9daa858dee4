<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.checkHost')" width="800px" :before-close="handleClose">
    <div v-loading="loading">
      <div class="header">
        <div class="header-item mr-20px">
          {{ $t('form.state') + '：' }}
          <span v-if="data?.checkState === 2"><i class="iconfont icon-icon_chenggong f-14 c-hex-517f27"></i>{{ $t('replenish.completed') }}</span>
          <span v-if="data?.checkState === 1">
            <el-icon class="is-loading" style="position: relative; top: 2px; left: -3px"> <Loading /> </el-icon>{{ $t('replenish.inProgress') }}</span
          >
        </div>
        <div class="header-item flex-1">{{ $t('form.time') + '：' }} {{ data?.createTime }}</div>
      </div>
      <div style="height: 300px">
        <BaseTable
          class="the-progress-dialog-table"
          :columns="tableData.columns"
          :data="data?.opsHostsCheckDetailEntity"
          row-key="id"
          :expand-row-keys="expandRowKeys"
          :tree-props="{ children: 'childList' }"
        >
          <template #checkState="scope">
            {{ scope.row.checkState === 1 ? $t('replenish.inProgress') : $t('replenish.completed') }}
          </template>
        </BaseTable>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <BaseButton type="primary" :disabled="data?.checkState === 1 || !data?.checkState" @click="viewDiagnosticReport">{{
          t('replenish.viewDiagnosisReport')
        }}</BaseButton>
        <BaseButton type="info" @click="handleClose"> {{ $t('button.down') }} </BaseButton>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import router from '@/router'
import Loop from '@/utils/Loop'
import { Loading } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const expandRowKeys = ref([])
const store = useStore()
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
    default: false
  },
  checkUid: {
    type: String,
    default: ''
  }
})
const data: any = ref()
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      loading.value = true
      refreshList()
    } else {
      if (refreshLoop.clearLoop) {
        refreshLoop.clearLoop()
      }
    }
  }
)
const loading = ref(true)
let refreshLoop: any = {}
// 刷新列表
function refreshList() {
  if (refreshLoop.clearLoop) {
    refreshLoop.clearLoop()
  }
  refreshLoop = new Loop(function () {
    getDataList()
  }, 1200)
}
// 表格数据
const tableData = reactive({
  columns: [
    {
      prop: 'hostName',
      label: t('replenish.hostName')
    },
    { prop: 'ip', label: t('replenish.hostIp') },
    { prop: 'checkState', label: t('replenish.checkStatus'), slot: true },
    { prop: 'createTime', label: t('replenish.checkStartTime') }
  ]
})

const emit = defineEmits(['update:modelValue', 'clone'])
function handleClose() {
  emit('update:modelValue', false)
  emit('clone')
}
function viewDiagnosticReport() {
  const { href } = router.resolve({
    name: 'diagnosticReport',
    query: { checkUid: props.checkUid, createTime: data.value.createTime }
  })
  window.open(href, '_blank')
}

function getDataList() {
  store
    .dispatch('hosts/getHostsCheckList', props.checkUid)
    .then((res) => {
      data.value = res.data
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
.header {
  @include flex();
  margin-top: -10px;
  .header-item {
    width: 252px;
    font-size: 14px;
  }
}
.the-progress-dialog-table {
  margin-top: 20px;
  margin-bottom: -31px;
}
</style>
