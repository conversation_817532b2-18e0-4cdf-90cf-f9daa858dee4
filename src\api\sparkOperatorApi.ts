// esApi.ts
import service, { downService } from '@/utils/Http'
import { getPath } from '@/utils/Path'
/**
 * @description 任务管理-列表查询
 * @param {SparkOperatorApi.GetSparkOperatorList} data
 * @return {*}
 */
export const getSparkOperatorList = async (data: SparkOperatorApi.GetSparkOperatorList) => {
  return await service.post<ApiResponse<any>>('/sparkOperator/list', data)
}

/**
 * @description 任务管理-查看事件
 * @param {SparkOperatorApi.GetSparkOperatorListEvent} data
 * @return {*}
 */
export const getSparkOperatorListEvent = async (data: SparkOperatorApi.GetSparkOperatorListEvent) => {
  return await service.post<ApiResponse<any>>('/sparkOperator/listEvent', data)
}

/**
 * @description 任务管理-添加job
 * @param {SparkOperatorApi.SparkOperatorAddJob} data
 * @return {*}
 */
export const sparkOperatorAddJob = async (data: SparkOperatorApi.SparkOperatorAddJob) => {
  return await service.post<ApiResponse<any>>('/opsSparkDeploymentConfig/add/deployment', data)
}

/**
 * @description 任务管理-配置操作
 * @param {SparkOperatorApi.SparkOperatorSelectConfig} data
 * @return {*}
 */
export const sparkOperatorSelectConfig = async (data: SparkOperatorApi.SparkOperatorSelectConfig) => {
  return await service.post<ApiResponse<any>>('/sparkOperator/selectConfig', data)
}

/**
 * @description 任务管理-删除
 * @param {SparkOperatorApi.SparkOperatorDelete} data
 * @return {*}
 */
export const sparkOperatorDelete = async (data: SparkOperatorApi.SparkOperatorDelete) => {
  return await service.post<ApiResponse<any>>('/sparkOperator/remove', data)
}

/**
 * @description 任务管理-更新配置
 * @param {SparkOperatorApi.SparkOperatorUpdate} data
 * @return {*}
 */
export const sparkOperatorUpdate = async (data: SparkOperatorApi.SparkOperatorUpdate) => {
  return await service.post<ApiResponse<any>>('/sparkOperator/update/deployment', data)
}

export const esTaskLog = async (data: SparkOperatorApi.EsTaskLogParam) => {
  return await service.post<ApiResponse<any>>('/es/task/log', data)
}

export const sparkDeploymentYaml = async (data: SparkOperatorApi.SparkDeploymentYaml) => {
  return await downService().post(getPath(`/opsSparkDeploymentConfig/get/deployment`), data)
}
