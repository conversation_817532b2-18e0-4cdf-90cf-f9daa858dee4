import service from '@/utils/Http'

/**
 * @description 添加角色
 * @param {clusterApi.IClusterServiceAddrole} data
 * @return {*}
 */

export const clusterServiceAddrole = (data: clusterApi.IClusterServiceAddrole) => {
  return service.post<ApiResponse<any>>('/cluster/service/addrole', data)
}

/**
 * @description 部署集群
 * @param {ClusterApi.IClusterServiceDeploy} data
 * @return {*}
 */
export const clusterServiceDeploy = (data: clusterApi.IClusterServiceDeploy) => {
  return service.post<ApiResponse<any>>('/cluster/service/deploy', data)
}

/**
 * @description 部署集群分页查询
 * @param {clusterApi.IClusterServiceInstancePage} data
 * @return {*}
 */
export const clusterServiceInstancePage = (data: clusterApi.IClusterServiceInstancePage) => {
  return service.post<ApiResponse<any>>('/cluster/service/instance/page', data)
}

/**
 * @description 组件重启
 * @param {clusterApi.IClusterServiceRestart} data
 * @return {*}
 */
export const clusterServiceRestart = (data: clusterApi.IClusterServiceRestart) => {
  return service.post<ApiResponse<string>>('/cluster/service/restart', data)
}

/**
 * @description 组件启动
 * @param {clusterApi.IClusterServiceStart} data
 * @return {*}
 */
export const clusterServiceStart = (data: clusterApi.IClusterServiceStart) => {
  return service.post<ApiResponse<string>>('/cluster/service/start', data)
}

/**
 * @description 组件停止
 * @param {clusterApi.IClusterServiceStop} data
 * @return {*}
 */
export const clusterServiceStop = (data: clusterApi.IClusterServiceStop) => {
  return service.post<ApiResponse<string>>('/cluster/service/stop', data)
}

/**
 * @description 组件卸载
 * @param {clusterApi.IClusterServiceUninstall} data
 * @return {*}
 */
export const clusterServiceUninstall = (data: clusterApi.IClusterServiceUninstall) => {
  return service.post<ApiResponse<string>>('/cluster/service/uninstall', data)
}
/**
 * @description 组件更新
 * @param {clusterApi.IClusterServiceUpdate} data
 * @return {*}
 */
export const clusterServiceUpdate = (data: clusterApi.IClusterServiceUpdate) => {
  return service.post<ApiResponse<string>>('/cluster/service/upgrade', data)
}

/**
 * @description 组件可更新选项列表
 * @param {clusterApi.IClusterServiceUpdateList} data
 * @return {*}
 */
export const clusterServiceUpdateList = (data: clusterApi.IClusterServiceUpdateList) => {
  return service.post<ApiResponse<string>>('/cluster/service/upgrade/list', data)
}
/**
 * @description 获取某命名空间下的组件列表
 * @param {ClusterApi.IClusterServicePage} data
 * @return {*}
 */
export const clusterServicePage = (data: clusterApi.IClusterServicePage) => {
  return service.post<ApiResponse<PageList<clusterApi.ClusterServicePageRecordsItem>>>('/cluster/service/page', data)
}

/**
 * @description 部署详情-组件列表
 * @param {clusterApi.IClusterServiceList} data
 * @return {*}
 */

export const clusterServiceList = (data: clusterApi.IClusterServiceList) => {
  return service.post<ApiResponse<any>>('/cluster/service/list')
}

/**
 * @description 角色分配完成点击下一步的是后执行(添加角色接口)
 * @param {clusterApi.AddRole} data
 * @return {*}
 */
export const addRole = async (data: clusterApi.AddRole) => {
  return service.post<ApiResponse<number>>(`/cluster/service/addrole`, data)
}
/**
 * @description 部署集群(配置服务参数下一步时调用)
 * @param {clusterApi.updateClusterServiceConfigs} data
 * @return {*}
 */
export const ClusterServiceConfigs = async (data: clusterApi.updateClusterServiceConfigs) => {
  return service.post(`/cluster/service/deploy`, data)
}
/**
 * @description 部署组件时用户跳出页面需要清除记录
 * @param {clusterApi.deleteByClusterServiceId} data
 * @return {*}
 */
export const deleteByClusterServiceId = async (id: clusterApi.deleteByClusterServiceId) => {
  return service.get<ApiResponse<any>>(`/cluster/service/deleteByClusterServiceId/${id}`)
}
/**
 * @description 轮循接口获取安装信息
 * @param {clusterApi.lookDeployProgress} serialNumber
 * @return {*}
 */
export const lookDeployProgress = async (data: clusterApi.lookDeployProgress) => {
  return service.post<ApiResponse<any>>(`/cluster/service/deploy/progress/`, data)
}
/**
 * @description 配置服务参数列表获取
 * @param {ComponentApi.SelectList} data
 * @return {*}
 */
export const getSelectList = async (data: clusterApi.SelectList) => {
  return service.post<ApiResponse<Array<clusterApi.SelectListDataItem>>>(`/cluster-service-config/selectListV1.7`, data)
}

/**
 * @description 集群概览-服务组件列表
 * @param {clusterApi.IClusterServiceComponentList} data
 * @return {*}
 */
export const getClusterServiceComponentList = async (data: clusterApi.IClusterServiceComponentList) => {
  return service.post<ApiResponse<Array<clusterApi.ClusterServiceComponentListDataItem>>>(`/cluster/service/componentList`, data)
}

/**
 * @description 集群概览- 组件卸载前回调
 * @param {clusterApi.IClusterServiceUninstall} data
 * @return {*}
 */
export const uninstallDeleteServiceComponentList = async (data: clusterApi.IClusterServiceUninstall) => {
  return service.post<ApiResponse<string>>(`/cluster/service/uninstall/check`, data)
}

/**
 * @description 获取当前集群信息
 * @param {clusterApi.GetClusterInfo} data
 * @return {*}
 */
export const getClusterInfo = async (data: clusterApi.GetClusterInfo) => {
  return service.post<ApiResponse<clusterApi.ClusterInfoData>>(`/namespace/getClusterInfo`, data)
}

/**
 * @description 获取扩缩容卡片列表
 * @param {clusterApi.GetServiceAll} data
 * @return {*}
 */
export const getServiceAll = async (data: clusterApi.GetServiceAll) => {
  return service.post<ApiResponse<Array<clusterApi.ClusterServicePageRecordsItem>>>(`/cluster/service/canscaling/all`, data)
}

/**
 * @description 获取组件扩缩容信息列表
 * @param {clusterApi.GetServiceScalingList} data
 * @return {*}
 */
export const getServiceScalingList = async (data: clusterApi.GetServiceScalingList) => {
  return service.post<ApiResponse<Array<clusterApi.ServiceScalingListDataItem>>>(`/cluster/service/scalingList`, data)
}

/**
 * @description 扩缩容
 * @param {clusterApi.ServiceScale} data
 * @return {*}
 */
export const serviceScale = async (data: clusterApi.ServiceScale) => {
  return service.post<ApiResponse<any>>(`/cluster/service/scale`, data)
}

/**
 * @description 组件部署时依赖检查
 * @param {clusterApi.ServiceComponentDeploy} data
 * @return {*}
 */
export const serviceComponentDeploy = async (data: clusterApi.ServiceComponentDeploy) => {
  return service.post<ApiResponse<clusterApi.ServiceComponentDeployData>>(`/cluster/service/component/deploy/check`, data)
}

/**
 * @description 组件批量删除前依赖检查
 * @param {clusterApi.ServiceUninstallByIds} data
 * @return {*}
 */
export const serviceUninstallByIds = async (data: clusterApi.ServiceUninstallByIds) => {
  return service.post<ApiResponse<any>>(`/cluster/service/uninstallByIds/check`, data)
}
/**
 * @description 组件批量删除前依赖检查
 * @param {clusterApi.BatchDelectComponent} data
 * @return {*}
 */
export const batchDelectComponent = async (data: clusterApi.BatchDelectComponent) => {
  return service.post<ApiResponse<any>>(`/cluster/service/uninstallByIds`, data)
}

/**
 * @description 组件及实例批量重启
 * @param {clusterApi.BatchRestart} data
 * @return {*}
 */
export const batchRestart = async (data: clusterApi.BatchRestart) => {
  return service.post<ApiResponse<any>>(`/cluster/service/batch/restart`, data)
}

/**
 * @description 查询操作进度
 * @param {clusterApi.OperateProgress} data
 * @return {*}
 */
export const operateProgress = async (data: clusterApi.OperateProgress) => {
  return service.post<ApiResponse<any>>(`/cluster/service/operate/progress`, data)
}

/**
 * @description 查询操作进度
 * @param {clusterApi.ClusterRestartPage} data
 * @return {*}
 */
export const clusterRestartPage = async (data: clusterApi.ClusterRestartPage) => {
  return service.post<ApiResponse<any>>(`/cluster/service/cluster/restart/page`, data)
}

/**
 * @description 重新执行操作
 * @param {clusterApi.ClusterRestartRerun} data
 * @return {*}
 */
export const clusterRestartRerun = async (data: clusterApi.ClusterRestartRerun) => {
  return service.post<ApiResponse<any>>(`/cluster/service/rerun`, data)
}

/**
 * @description 进度历史列表
 * @param {clusterApi.GetOperationHistory} data
 * @return {*}
 */
export const getOperationHistory = async (data: clusterApi.GetOperationHistory) => {
  return service.post<ApiResponse<any>>(`/cluster/service/operate/progress/page`, data)
}

/**
 * @description 是否能批量重启
 * @param
 * @return {*}
 */
export const canBatchRestart = async (namespaceId: any) => {
  return service.get<ApiResponse<boolean>>(`/cluster/service/canBatchRestart?namespaceId=${namespaceId}`)
}

/**
 * @description 根据组件id列表返回依赖组件提示
 * @param {clusterApi.ComponentRestartCheck} data
 * @return {*}
 */
export const componentRestartCheck = async (data: clusterApi.ComponentRestartCheck) => {
  return service.post<ApiResponse<clusterApi.ComponentRestartCheckData>>(`/cluster/service/component/restart/check`, data)
}

/**
 * @description 判断是否是批量操作
 * @param {clusterApi.GetIsBatch} data
 * @return {*}
 */
export const getIsBatch = async (data: clusterApi.GetIsBatch) => {
  return service.post<ApiResponse<{ isBatch: boolean; groupNumber: string }>>(`/cluster/service/operate/progress/isBatch`, data)
}

/**
 * @description 判断是否是批量操作
 * @param {clusterApi.ServiceComponentInfo} data
 * @return {*}
 */
export const serviceComponentInfo = async (data: clusterApi.ServiceComponentInfo) => {
  return service.post<ApiResponse<any>>(`/cluster/service/componentInfo`, data)
}

/**
 * @description 实例历史状态
 * @param {clusterApi.ServiceComponentStateHistory} data
 * @return {*}
 */
export const serviceComponentStateHistory = async (data: clusterApi.ServiceComponentStateHistory) => {
  return service.post<ApiResponse<any>>(`/cluster/service/componentStateHistory`, data)
}

/**
 * @description 查询已经被使用的关键词
 * @param {clusterApi.ShutToast} data
 * @return {*}
 */
export const shutToast = (data: clusterApi.ShutToast) => {
  return service.post<ApiResponse<any>>('/cluster/service/shutToast', data)
}

/**
 * @description 查询已经被使用的关键词
 * @param {clusterApi.Maintenance} data
 * @return {*}
 */
export const maintenance = (data: clusterApi.Maintenance) => {
  return service.post<ApiResponse<any>>('/cluster/service/maintenance', data)
}

/**
 * @description 滚动重启
 * @param {clusterApi.IClusterServiceRestart} data
 * @return {*}
 */
export const rollingRestart = (data: clusterApi.IClusterServiceRestart) => {
  return service.post<ApiResponse<any>>('/cluster/service/rollingRestart', data)
}

/**
 * @description 滚动重启
 * @param {clusterApi.GetHdfsClusterByNameSpaceId} data
 * @return {*}
 */
export const getHdfsClusterByNameSpaceId = (data: clusterApi.GetHdfsClusterByNameSpaceId) => {
  return service.post<ApiResponse<any>>('/cluster/service/getHdfsClusterByNameSpaceId', data)
}

export const getComponentUnhealthyReason = (data: { id: number }) => {
  return service.post<ApiResponse<any>>('/cluster/service/unhealthy/reason', data)
}

export const getDashboardStatics = (data: clusterApi.GetDashboardStatics) => {
  return service.post<ApiResponse<any>>('/cluster/service/firstPage', data)
}

export const getClusterComponents = (data: { namespaceId: string }) => {
  return service.post<ApiResponse<any>>('/cluster/service/componentListByNameSpaceId', data)
}

export const getClusterComponentsErrIns = (data: clusterApi.GetClusterComponentsErrIns) => {
  return service.post<ApiResponse<any>>('/service/instance/firstPageList', data)
}

export const getClusterServiceNode = (data: clusterApi.getClusterServiceNode) => {
  return service.post<ApiResponse<any>>('/cluster/service/edge/node/clusters', data)
}
