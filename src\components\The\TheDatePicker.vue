<template>
  <div class="date-picker">
    <el-date-picker
      v-bind="$attrs"
      type="datetimerange"
      :range-separator="$t('mixed.to')"
      prefix-icon="el-icon-date"
      :start-placeholder="$t('form.startDate')"
      :end-placeholder="$t('form.endDate')"
      :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
      value-format="YYYY-MM-DD HH:mm:ss"
      :clearable="false"
      :disabled-date="disabledDate"
    />
    <BaseElTooltip :content="$t('mixed.dataTip')" placement="top">
      <img src="@/assets/img/icon_tishi.png" class="iconf" alt="tishi" />
    </BaseElTooltip>
  </div>
</template>
<script lang="ts" setup>
// 限制选择一个月
function disabledDate(time: Date) {
  return time.getTime() > Date.now() || time.getTime() < Date.now() - 1000 * 60 * 60 * 24 * 30
}
</script>
<style lang="scss" scoped>
.date-picker {
  @include flex();
  .iconf {
    position: relative;
    left: 5px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
}
</style>
