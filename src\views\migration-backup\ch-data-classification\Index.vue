<template>
  <TemplateIndex :tab-data="tabData" />
</template>
<script lang="ts" setup>
import TemplateIndex from '../components/TemplateIndex.vue'
const { $has, setBreadList, t } = useBasicTool()
const tabData = reactive<{ label: string; name: string; isTabs: boolean }[]>([
  { label: t('replenish.hotColdDataGrading'), name: 'dataClassification', isTabs: $has('data-classification') },
  { label: t('replenish.hotDataList'), name: 'hotDataList', isTabs: $has('hot-data-list') },
  { label: t('replenish.coldDataList'), name: 'coldDataList', isTabs: $has('cold-data-list') }
])
onMounted(() => {
  setBreadList([
    {
      name: t('replenish.migrationBackup')
    },
    {
      name: t('replenish.hotColdDataGrading')
    }
  ])
})
</script>
<style lang="scss" scoped></style>
