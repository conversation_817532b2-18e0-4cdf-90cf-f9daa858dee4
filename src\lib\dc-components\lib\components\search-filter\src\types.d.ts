import type { RenderVNodeFn } from "../../render-vnode/src/types";
import type { Ref, VNode } from "vue";
import type { DcButtonConfig } from "../../button";
import type { DcFormConfig, DcFormItemMargin } from "../../form";
import type DcSearchFilter from "./index.vue";
export interface DcSearchFilterAction {
    innerText?: string;
    visible?: boolean | Ref<boolean>;
    componentSlot?: Record<string, RenderVNodeFn>;
    props?: Record<string, any>;
    config?: DcButtonConfig;
}
export interface DcSearchFilterExpand {
    showExpand: boolean;
    expandText?: string;
    collapseText?: string;
    expandFields: string[];
}
export interface DcSearchFilterConfig {
    queryAction?: DcSearchFilterAction;
    resetAction?: DcSearchFilterAction;
    refreshAction?: DcSearchFilterAction;
    actionMargin?: DcFormItemMargin;
    formConfig: DcFormConfig;
    expand?: DcSearchFilterExpand;
    extendSlot?: VNode;
    extendMargin?: DcFormItemMargin;
}
export declare type DcSearchFilterInstance = InstanceType<typeof DcSearchFilter>;
