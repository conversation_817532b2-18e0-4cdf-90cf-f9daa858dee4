'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('../../form/index.js');
var index = require('../../form/src/index.js');
var Action = require('./Action.js');
var Expand = require('./Expand.js');
require('./index.vue_vue_type_style_index_0_scoped_true_lang.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');
var types = require('../../form/src/types.js');

const clsPrefix = "dc-search-filter";
const actionModel = "dc-search-filter-model";
const extendModel = "dc-search-filter-extend";
const __default__ = vue.defineComponent({
  name: "DcSearchFilter"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["query", "reset", "refresh"],
  setup(__props, { expose: __expose, emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const formRef = vue.ref();
    const isExpand = vue.ref(false);
    const expandConfig = vue.computed(() => {
      return props.config.expand || { showExpand: false, expandFields: [] };
    });
    const formConfigChildren = vue.computed(() => {
      return (props.config.formConfig?.children || []).map((item) => {
        return {
          ...item,
          visible: (modelValue) => {
            if (isExpand.value) {
              return !expandConfig.value.expandFields.includes(item.model);
            }
            return item.visible ? item.visible(modelValue) : true;
          }
        };
      });
    });
    const formConfig = vue.computed(() => {
      const children = formConfigChildren.value;
      children.push({
        model: actionModel,
        label: "",
        labelWidth: 0,
        defaultValue: "",
        visible: () => true,
        component: types.DcFormComponentEnum.CUSTOM,
        margin: props.config.actionMargin,
        props: {
          renderCustom: () => {
            return vue.h(Action["default"], {
              config: props.config,
              clsPrefix,
              onQuery,
              onRefresh,
              onReset
            });
          }
        }
      });
      if (props.config.extendSlot) {
        children.push({
          model: extendModel,
          label: "",
          labelWidth: 0,
          defaultValue: "",
          visible: () => true,
          component: types.DcFormComponentEnum.CUSTOM,
          margin: props.config.extendMargin,
          props: {
            renderCustom: () => {
              return props.config.extendSlot;
            }
          }
        });
      }
      return props.config.formConfig ? {
        ...props.config.formConfig,
        children
      } : { children, labelWidth: "auto" };
    });
    const toggleExpand = (val) => {
      isExpand.value = val;
    };
    const getValues = () => {
      const query = formRef.value.getValues();
      query[actionModel] = void 0;
      query[extendModel] = void 0;
      return query;
    };
    const setValues = (modelValue) => {
      formRef.value.initModel(modelValue);
    };
    const onQuery = () => {
      const query = getValues();
      emits("query", query);
    };
    const onReset = () => {
      formRef.value.reset();
      const query = getValues();
      emits("reset", query);
    };
    const onRefresh = () => {
      const query = getValues();
      emits("refresh", query);
    };
    __expose({
      query: onQuery,
      reset: onReset,
      refresh: onRefresh,
      getValues,
      setValues,
      formRef
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(`${clsPrefix} ${expandConfig.value.showExpand ? "with-expand" : ""}`)
      }, [
        vue.createVNode(index["default"], {
          ref_key: "formRef",
          ref: formRef,
          config: formConfig.value,
          inline: "",
          class: vue.normalizeClass(`${clsPrefix}-form `)
        }, null, 8, ["config", "class"]),
        expandConfig.value.showExpand ? (vue.openBlock(), vue.createElementBlock("div", {
          key: 0,
          class: vue.normalizeClass(`${clsPrefix}-expand`)
        }, [
          vue.createVNode(Expand["default"], {
            "collapse-text": expandConfig.value.collapseText,
            "expand-text": expandConfig.value.expandText,
            onToggleExpand: toggleExpand
          }, null, 8, ["collapse-text", "expand-text"])
        ], 2)) : vue.createCommentVNode("v-if", true)
      ], 2);
    };
  }
});
var SearchFilter = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__scopeId", "data-v-7cdc81e0"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\search-filter\\src\\index.vue"]]);

exports["default"] = SearchFilter;
//# sourceMappingURL=index.js.map
