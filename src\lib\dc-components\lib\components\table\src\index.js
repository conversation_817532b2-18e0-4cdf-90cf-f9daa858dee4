'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/loading/style/css');
require('element-plus/es/components/table/style/css');
require('element-plus/es/components/table-column/style/css');
var index = require('../../empty/src/index.js');
var ColumnsFilter = require('./components/ColumnsFilter.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcTable"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    columns: {
      type: Array
    },
    isColumnFilter: {
      type: Boolean
    },
    append: {
      type: Function
    },
    filterSubmitText: {
      type: String
    },
    filterCancelText: {
      type: String
    },
    filterAllText: {
      type: String
    },
    filterResetText: {
      type: String
    },
    height: {
      type: String,
      default: "100%"
    },
    loading: {
      type: Boolean,
      default: false
    },
    showSelectColumn: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Function,
      default: () => true
    },
    selectionWidth: {
      type: Number,
      default: 40
    },
    showEmptyImg: {
      type: Boolean,
      default: true
    },
    locale: {
      type: String,
      default: "zh-CN"
    },
    theme: {
      type: String,
      default: "light"
    }
  },
  setup(__props, { expose: __expose }) {
    ;
    const props = __props;
    const $refs = vue.ref();
    const columnsFilter = vue.ref([]);
    let loadingInstance;
    const columnsData = vue.computed(() => {
      if (props?.isColumnFilter) {
        return props?.columns?.filter((column) => (column?.columnDefault ? columnsFilter.value.includes(column.label) && column?.columnDefault : columnsFilter.value.includes(column.label)) || column?.columnRequired || !column.label);
      } else {
        return props.columns;
      }
    });
    const doLoading = (loading) => {
      if (loading) {
        vue.nextTick(() => {
          if ($refs.value?.$el) {
            loadingInstance = elementPlus.ElLoading.service({
              target: $refs.value.$el,
              background: "transparent",
              body: false,
              fullscreen: false
            });
          }
        });
      } else {
        loadingInstance?.close();
      }
    };
    vue.watch(() => props.loading, (loading) => {
      doLoading(loading);
    }, { immediate: true });
    function onColumnsFilterChange(checkList) {
      columnsFilter.value = checkList;
    }
    __expose({ $refs });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTable), vue.mergeProps({
        ref_key: "$refs",
        ref: $refs,
        class: "dc-table"
      }, _ctx.$attrs, {
        style: { height: props.height }
      }), {
        append: vue.withCtx(() => [
          vue.renderSlot(_ctx.$slots, "append", {}, () => [
            __props.append && typeof __props.append() === "string" ? (vue.openBlock(), vue.createElementBlock(vue.Fragment, { key: 0 }, [
              vue.createTextVNode(vue.toDisplayString(__props.append()), 1)
            ], 64)) : __props.append ? (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(__props.append), { key: 1 })) : vue.createCommentVNode("v-if", true)
          ])
        ]),
        empty: vue.withCtx(() => [
          vue.createVNode(index["default"], {
            "show-img": __props.showEmptyImg,
            locale: __props.locale,
            theme: __props.theme
          }, null, 8, ["show-img", "locale", "theme"])
        ]),
        default: vue.withCtx(() => [
          __props.isColumnFilter ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), {
            key: 0,
            width: "48px",
            align: "center"
          }, {
            header: vue.withCtx(() => [
              vue.createVNode(ColumnsFilter["default"], {
                style: { "position": "relative", "top": "3px" },
                columns: __props.columns,
                "filter-submit-text": __props.filterSubmitText,
                "filter-cancel-text": __props.filterCancelText,
                "filter-all-text": __props.filterAllText,
                "filter-reset-text": __props.filterResetText,
                onChange: onColumnsFilterChange
              }, null, 8, ["columns", "filter-submit-text", "filter-cancel-text", "filter-all-text", "filter-reset-text"])
            ]),
            _: 1
          })) : vue.createCommentVNode("v-if", true),
          vue.renderSlot(_ctx.$slots, "before"),
          props.showSelectColumn ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), {
            key: 1,
            type: "selection",
            align: "center",
            width: props.selectionWidth,
            selectable: props.selectable
          }, null, 8, ["width", "selectable"])) : vue.createCommentVNode("v-if", true),
          (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(columnsData.value, (col, index) => {
            return vue.openBlock(), vue.createElementBlock(vue.Fragment, null, [
              col?.slot ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), vue.mergeProps({ key: 0 }, col, { key: index }), {
                default: vue.withCtx(({ row, column, $index }) => [
                  vue.createCommentVNode("  eslint-disable-next-line vue/valid-attribute-name "),
                  vue.renderSlot(_ctx.$slots, col.prop, {
                    row,
                    column,
                    $index
                  })
                ]),
                _: 2
              }, 1040)) : vue.createCommentVNode("v-if", true),
              col.type === "expand" && !col?.slot ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), vue.mergeProps({ key: 1 }, col, { key: index }), {
                default: vue.withCtx(({ row, column, $index }) => [
                  (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(col.formatter(row, column, row[col.prop], $index))))
                ]),
                _: 2
              }, 1040)) : vue.createCommentVNode("v-if", true),
              !col?.slot && col.type !== "expand" ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), vue.mergeProps({ key: 2 }, col, { key: index }), null, 16)) : vue.createCommentVNode("v-if", true)
            ], 64);
          }), 256)),
          vue.renderSlot(_ctx.$slots, "after")
        ]),
        _: 3
      }, 16, ["style"]);
    };
  }
});
var table = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\table\\src\\index.vue"]]);

exports["default"] = table;
//# sourceMappingURL=index.js.map
