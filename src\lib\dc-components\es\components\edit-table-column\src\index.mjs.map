{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/edit-table-column/src/index.vue"], "sourcesContent": ["<!-- eslint-disable vue/valid-attribute-name -->\r\n<template>\r\n  <el-table-column v-bind=\"$attrs\" :prop=\"prop\" :label=\"label\" :width=\"width\">\r\n    <template #default=\"scope\">\r\n      <el-form-item\r\n        v-if=\"isEditing(scope.$index)\"\r\n        :prop=\"`model.${scope.$index}.formData.${prop}`\"\r\n        :rules=\"rules\"\r\n      >\r\n        <slot\r\n          name=\"edit\"\r\n          :$index=\"scope.$index\"\r\n          :row=\"getEditRow(scope.$index)\"\r\n          :column=\"scope.column\"\r\n          :actions=\"editActions ?? defaultEditActions\"\r\n        >\r\n          {{ calculateColumnDefaultValue(scope) }}\r\n        </slot>\r\n      </el-form-item>\r\n      <slot\r\n        v-else\r\n        :$index=\"scope.$index\"\r\n        :row=\"scope.row\"\r\n        :column=\"scope.column\"\r\n        :actions=\"editActions ?? defaultEditActions\"\r\n      >\r\n        {{ calculateColumnDefaultValue(scope) }}\r\n      </slot>\r\n    </template>\r\n  </el-table-column>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ElFormItem, ElTableColumn } from \"element-plus\";\r\nimport type { FormItemRule } from \"element-plus\";\r\nimport type { PropType, Ref } from \"vue\";\r\nimport type { DcEditTableColumnScope } from \"./types\";\r\n// eslint-disable-next-line import/order\r\nimport { inject, watchEffect } from \"vue\";\r\nimport type {\r\n  DcEditTableEditActions,\r\n  DcEditTableFormModel,\r\n  DcEditTableFormModelItem,\r\n  DcEditTableFormProps,\r\n} from \"../../edit-table/src/types\";\r\n\r\ndefineOptions({\r\n  name: \"DcEditTableColumn\",\r\n});\r\n\r\nconst props = defineProps({\r\n  prop: {\r\n    type: String,\r\n  },\r\n  label: {\r\n    type: String,\r\n  },\r\n  width: {\r\n    type: String,\r\n  },\r\n  rules: {\r\n    type: Array as PropType<FormItemRule[]>,\r\n  },\r\n});\r\n\r\nconst defaultEditActions: DcEditTableEditActions = {};\r\n\r\nconst editActions = inject<DcEditTableEditActions | undefined>(\"editActions\");\r\n\r\nconst formModel = inject<Ref<DcEditTableFormModel | undefined>>(\"formModel\");\r\n\r\nconst formProps = inject<Ref<DcEditTableFormProps | undefined>>(\"formProps\");\r\n\r\nwatchEffect(() => {\r\n  if (props.prop) {\r\n    formProps?.value?.add(props.prop);\r\n  }\r\n});\r\n\r\nconst getEditModel = (index: number): DcEditTableFormModelItem => {\r\n  if (!formModel || !formModel.value?.model) {\r\n    return {\r\n      isEditing: false,\r\n      isNew: false,\r\n      formData: {},\r\n      data: {},\r\n    };\r\n  }\r\n  return formModel.value.model[index];\r\n};\r\n\r\nconst getEditRow = (index: number): any => getEditModel(index).formData;\r\n\r\nconst isEditing = (index: number): boolean =>\r\n  getEditModel(index).isEditing ?? false;\r\n\r\nconst calculateColumnDefaultValue = (scope: DcEditTableColumnScope) => {\r\n  if (props.prop) return scope.row?.[props.prop];\r\n};\r\n</script>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;AA6Cc,EAAA,IAAA,EAAA,mBAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,kBAAQ,GAAA,EAAA,CAAA;AAed,IAAA,MAAM,qBAA6C,aAAC,CAAA,CAAA;AAEpD,IAAM,MAAA,SAAA,GAAA,kBAAsE,CAAA,CAAA;AAE5E,IAAM,MAAA,SAAA,GAAY,OAA8C,WAAW,CAAA,CAAA;AAE3E,IAAM,WAAA,CAAA,MAAY;AAElB,MAAA,IAAA,KAAA,CAAY,IAAM,EAAA;AAChB,QAAA,SAAgB,EAAA,KAAA,EAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACd,OAAW;AAAqB,KAClC,CAAA,CAAA;AAAA,IACF,MAAC,YAAA,GAAA,CAAA,KAAA,KAAA;AAED,MAAM,IAAA,CAAA,SAAA,IAAA,CAAe,SAA6C,CAAA,KAAA,EAAA,KAAA,EAAA;AAChE,QAAA,OAAK;AACH,UAAO,SAAA,EAAA,KAAA;AAAA,UACL,KAAW,EAAA,KAAA;AAAA,UACX,QAAO,EAAA,EAAA;AAAA,UACP;AAAW,SAAA,CACX;AAAO,OACT;AAAA,MACF,OAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAO,CAAA;AAAsB,IAC/B,MAAA,UAAA,GAAA,CAAA,KAAA,KAAA,YAAA,CAAA,KAAA,CAAA,CAAA,QAAA,CAAA;AAEA,IAAA,MAAM,SAAa,GAAA,CAAA,KAAwB,KAAA,YAAA,CAAA,MAAkB,CAAE,SAAA,IAAA,KAAA,CAAA;AAE/D,IAAA,MAAM,2BACS,GAAA,CAAA,KAAA,KAAK;AAEpB,MAAM,IAAA,KAAA,CAAA,IAAA;AACJ,QAAA,OAAU,KAAA,CAAA,GAAA,GAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAM,KAAO,CAAA;AAAkB,IAC3C,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}