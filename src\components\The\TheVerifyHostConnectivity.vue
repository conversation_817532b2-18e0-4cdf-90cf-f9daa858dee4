<template>
  <BaseButton :loading="buttonLoading" :disabled="isTestButtonDisabled" class="test-button" type="primary" @click="testConnectivity">
    {{ buttonLoading ? $t('button.undertest') : $t('button.testConnectivity') }}
  </BaseButton>
  <span v-if="isTestButtonTip" class="test-information" :class="{ 'test-information--successed': modelValue }">
    <i :class="testButtonIconClass" />
    {{ buttonText || testButtonTip }}
  </span>
</template>
<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const store = useStore()
const props = defineProps({
  hostForm: {
    type: Object,
    default: () => {
      return {}
    }
  },
  modelValue: {
    type: Boolean,
    default: false
  },
  testButtonTip: {
    type: String,
    default: ''
  }
})
const connectedForm = computed(() => {
  return {
    ip: props.hostForm.ip,
    sshPort: props.hostForm.sshPort,
    sudoUser: props.hostForm.sudoUser,
    userPwd: props.hostForm.userPwd
  }
})
const isTestButtonDisabled = computed(() => Object.values(connectedForm.value).includes(''))
const isTestButtonTip = computed(() => !Object.values(connectedForm.value).includes('') && (buttonText.value !== '' || props.testButtonTip))
const emit = defineEmits(['update:modelValue', 'update:testButtonTip', 'setHostName'])
watch(
  () => connectedForm.value,
  () => {
    buttonText.value = ''
    emit('update:testButtonTip', '')
    emit('update:modelValue', false)
  }
)
const buttonText = ref('')
const testButtonIconClass = computed(() => ['iconfont', props.modelValue ? 'icon-icon_chenggong' : 'icon-icon_shibai'])
const buttonLoading = ref(false)

function testConnectivity() {
  buttonLoading.value = true
  buttonText.value = ''
  emit('update:testButtonTip', '')
  store
    .dispatch('hosts/hostsVerifyConnected', connectedForm.value)
    .then((res) => {
      if (res.code === 0) {
        emit('update:modelValue', true)
        if (props.hostForm.namespaceId === -1) emit('setHostName', res.data)
        buttonText.value = t('message.tetstConnectionSuccessed')
      }
    })
    .catch((err) => {
      emit('update:modelValue', false)
      buttonText.value = t('message.testConnectionFailed')
    })
    .finally(() => {
      buttonLoading.value = false
    })
}
</script>
<style lang="scss" scoped>
.test-button {
  margin-left: 100px;
}
.test-information {
  position: relative;
  top: 2px;
  margin-left: 10px;
  font-size: 14px;
  font-weight: 400;
  color: #ee5e5e;
  .iconfont {
    margin-right: 3px;
  }
}
.test-information--successed {
  color: var(--ops-state-success-color);
}
</style>
