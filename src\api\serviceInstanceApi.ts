// serviceInstanceApi.ts
import service from '@/utils/Http'

/**
 * @description 获取服务实例列表
 * @param {ServiceInstanceApi.IGetServiceInstanceList} data
 * @return {*}
 */
export const getServiceInstanceList = (data: ServiceInstanceApi.IGetServiceInstanceList) => {
  return service.post<ApiResponse<PageList<ServiceInstanceApi.ServiceInstanceListRecords>>>('/service/instance/list', data)
}

/**
 * @description 根据组件服务id获取其下所有的角色列表（用于条件筛选）
 * @param {ServiceInstanceApi.IGetServiceInstanceRoleList} data
 * @return {*}
 */
export const getServiceInstanceRoleList = (data: ServiceInstanceApi.IGetServiceInstanceRoleList) => {
  return service.post<ApiResponse<Array<string>>>('/service/instance/role/list', data)
}

/**
 * @description 获取角色下拉框
 * @param {ServiceInstanceApi.IGetServiceInstancelistroles} data
 * @return {*}
 */
export const getServiceInstancelistroles = (data: ServiceInstanceApi.IGetServiceInstancelistroles) => {
  return service.post<ApiResponse<Array<ServiceInstanceApi.ServiceInstancelistrolesItem>>>(
    `/service/instance/listroles/${data.type}?clusterId=${data.clusterId}`
  )
}
