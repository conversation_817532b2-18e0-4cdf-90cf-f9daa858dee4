export declare const DcRenderVNode: import("dc-components/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    vnode: {
        type: import("vue").PropType<import("./src/types").RenderVNodeFn>;
        default: () => null;
    };
    scope: {
        type: import("vue").PropType<Record<string, any>>;
        default: () => {};
    };
    extraScope: {
        type: import("vue").PropType<Record<string, any>>;
        default: () => {};
    };
}, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    vnode: {
        type: import("vue").PropType<import("./src/types").RenderVNodeFn>;
        default: () => null;
    };
    scope: {
        type: import("vue").PropType<Record<string, any>>;
        default: () => {};
    };
    extraScope: {
        type: import("vue").PropType<Record<string, any>>;
        default: () => {};
    };
}>>, {
    vnode: import("./src/types").RenderVNodeFn;
    scope: Record<string, any>;
    extraScope: Record<string, any>;
}, {}>> & Record<string, any>;
export default DcRenderVNode;
export * from "./src/types";
