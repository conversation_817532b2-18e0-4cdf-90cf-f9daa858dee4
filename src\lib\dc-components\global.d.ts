/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-02-14 11:03:48
 * @LastEditTime: 2025-02-28 15:45:30
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @Description:
 */
// GlobalComponents for Volar
declare module "@vue/runtime-core" {
  export interface GlobalComponents {
    DcButton: typeof import("dc-components")["DcButton"];
    DcCheckbox: typeof import("dc-components")["DcCheckbox"];
    DcEditTable: typeof import("dc-components")["DcEditTable"];
    DcEditTableColumn: typeof import("dc-components")["DcEditTableColumn"];
    DcForm: typeof import("dc-components")["DcForm"];
    DcRadio: typeof import("dc-components")["DcRadio"];
    DcRenderVNode: typeof import("dc-components")["DcRenderVNode"];
    DcSearchFilter: typeof import("dc-components")["DcSearchFilter"];
    DcSelect: typeof import("dc-components")["DcSelect"];
    DcTable: typeof import("dc-components")["DcTable"];
    DcEmpty: typeof import("dc-components")["DcEmpty"];
    DcLogin: typeof import("dc-components")["DcLogin"];
    DcViewLayout: typeof import("dc-components")["DcViewLayout"];
  }
}

export {};
