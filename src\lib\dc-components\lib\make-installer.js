'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index_js = require('./constants/index.js');
var directives = require('./directives/index.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var directives__default = /*#__PURE__*/_interopDefaultLegacy(directives);

const makeInstaller = (components = []) => {
  const install = (app) => {
    if (app[index_js.INSTALLED_KEY])
      return;
    app[index_js.INSTALLED_KEY] = true;
    components.forEach((c) => app.use(c));
    app.use(directives__default["default"]);
  };
  return {
    install
  };
};

exports.makeInstaller = makeInstaller;
//# sourceMappingURL=make-installer.js.map
