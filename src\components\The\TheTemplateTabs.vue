<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-08-05 09:33:36
 * @LastEditTime: 2025-08-08 10:58:04
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @Description:
-->
<template>
  <div class="view">
    <BaseElTabs :model-value="isTabs" @tab-change="tabsChange">
      <el-tab-pane v-for="(item, index) in tabData?.filter((item) => item?.isTabs ?? true)" :key="index" :label="item.label" :name="item.name" />
    </BaseElTabs>
    <div style="height: calc(100% - 56px)">
      <router-view />
    </div>
    <img src="@/assets/img/back.png" class="cursor-pointer back-icon" @click="back" />
  </div>
</template>
<script lang="ts" setup>
import type { TabPaneName } from 'element-plus'
const props = defineProps({
  tabData: {
    type: Array<{ label: string; name: string; isTabs?: boolean }>
  },
  needQuery: {
    type: Boolean,
    default: false
  }
})
const { route, t, router, store, $has } = useBasicTool()
const isTabs = computed<any>(() => activeTabs.value || route.name)
const activeTabs = ref('')
function switchRouter(routerName: any): void {
  if (props.needQuery) {
    router.push({
      name: routerName,
      query: {
        ...route.query
      }
    })
  } else {
    router.push({
      name: routerName
    })
  }
}
// tabs切换事件
function tabsChange(name: TabPaneName) {
  if (activeTabs.value === name) return
  switchRouter(name)
}
function setActiveTabs(name: string) {
  activeTabs.value = name
}

const back = () => {
  router.back()
}

defineExpose({
  setActiveTabs
})
</script>
<style lang="scss" scoped>
.view {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--ops-bg-white-color);
  :deep(.base-el-tabs) {
    .el-tabs__nav-scroll {
      padding-left: 60px;
    }
  }
  .back-icon {
    position: absolute;
    top: 12px;
    left: 20px;
    width: 24px;
    height: 24px;
  }
}
</style>
