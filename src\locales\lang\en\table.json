{"account": "Account", "actionProperties": "Operation attribute", "addRole": "Add Role", "addTime": "Added At", "alarmCategory": "Alarm category", "alarmContent": "Alarm content", "alarmTime": "Alarm time", "belongingCluster": "Cluster", "bindHost": "Bind Host", "bindK8s": "Bind Kubernetes", "bindTime": "Bind Time", "cancel": "Cancel", "check": "View", "clear": "clear", "clusterName": "Cluster Name", "clusterUnbinding": "Unbind Cluster", "componentName": "Component Name", "configurationDownload": "Config Download", "configurationOperation": "Config Action", "configurationSnapshot": "Snapshot", "containerName": "Container", "creationTime": "Created At", "currentRole": "Current Role", "customName": "Custom Name", "delete": "Delete", "deploymentMethod": "Deployment Method", "describe": "Description", "durationOfOperation": "Duration", "edit": "Edit", "employeeSName": "Employee Name", "endTime": "End Time", "eventTime": "Event time", "executUser": "Execute user", "existingRoles": "Current Role", "expansionAndContraction": "Automatic expansion and shrinkage", "frameNumber": "Frame Number", "freeze": "Freeze", "hostName": "Host Name", "information": "Message", "installComponents": "Install Components", "instance": "Instance", "instanceAddress": "Instance IP", "instanceName": "Instance Name", "ip": "IP", "k8sName": "Kubernetes Name", "k8sNamespace": "Kubernetes Namespace", "kubernetesClusterAddress": "Kubernetes Cluster IP", "kubernetesName": "Kubernetes-Name", "logLevel": "Log Level", "loginAccount": "<PERSON><PERSON> Account", "memory": "Memory", "modifyReason": "Reason for Change", "monitorChart": "Monitor Chart", "name": "Name", "namespaceName": "Namespace", "nodeName": "Node", "nodeScheduling": "Node scheduling", "numberOfDeployedHosts": "Number of deployed hosts", "operate": "Operation", "operatingTime": "Operated At", "operationContent": "Operation content", "operator": "Operated By", "perceptualState": "Perceptual state", "permissionModification": "Modify Permissions", "phone": "Mobile Number", "physicalSpace": "Physical space", "podName": "Pod", "rack": "rack", "reboot": "<PERSON><PERSON>", "remarks": "remarks", "rename": "<PERSON><PERSON>", "resetPassword": "Reset Password", "result": "results of enforcement", "roleLogFile": "Role log file", "roleName": "Role Name", "save": "Save", "serialNumber": "Serial number", "serviceType": "Service Type", "serviceVersion": "Service Version", "startUp": "Start", "startingTime": "Start Time", "state": "Status", "stepContent": "Step Description", "stop": "Stop", "temporarilyAbsent": "Temporarily absent", "thaw": "Unfreeze", "time": "Time", "typeOfOperation": "Type of operation", "unbindK8s": "<PERSON><PERSON><PERSON>", "update": "Update", "verificationMethod": "Auth Method", "version": "Version", "versionNumber": "Version Number", "versionRollback": "Version Rollback", "viewEvents": "View events", "viewLog": "View Logs", "viewModification": "View Change", "viewingProgress": "Viewing progress", "region": "Region", "baseStatus": "Base status", "deploymentMode": "Deployment mode", "resourceName": "Resource Name"}