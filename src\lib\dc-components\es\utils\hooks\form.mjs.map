{"version": 3, "file": "form.mjs", "sources": ["../../../../../packages/utils/hooks/form.ts"], "sourcesContent": ["import { reactive } from \"vue\";\r\n\r\ntype IData = Record<string, any>;\r\nexport function useDependModel(defaultModelData: IData = {}) {\r\n  const dependModel = reactive<IData>({ ...defaultModelData });\r\n  const changeDependModel = (val: IData) => {\r\n    Object.keys(val).forEach((item) => {\r\n      dependModel[item] = val[item];\r\n    });\r\n  };\r\n\r\n  return {\r\n    dependModel,\r\n    changeDependModel,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;AACO,SAAS,cAAc,CAAC,gBAAgB,GAAG,EAAE,EAAE;AACtD,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,GAAG,gBAAgB,EAAE,CAAC,CAAC;AACxD,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACrC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACvC,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}