// esApi.ts
import service, { downService } from '@/utils/Http'
import { getPath } from '@/utils/Path'

/**
 * @description 任务管理-列表查询
 * @param {FlinkOperatorApi.GetFlinkOperatorList} data
 * @return {*}
 */
export const getFlinkOperatorList = async (data: FlinkOperatorApi.GetFlinkOperatorList) => {
  return await service.post<ApiResponse<any>>('/flinkOperator/list', data)
}

/**
 * @description 任务管理-查看事件
 * @param {FlinkOperatorApi.GetFlinkOperatorListEvent} data
 * @return {*}
 */
export const getFlinkOperatorListEvent = async (data: FlinkOperatorApi.GetFlinkOperatorListEvent) => {
  return await service.post<ApiResponse<any>>('/flinkOperator/listEvent', data)
}

/**
 * @description 任务管理-添加job
 * @param {FlinkOperatorApi.FlinkOperatorAddJob} data
 * @return {*}
 */
export const flinkOperatorAddJob = async (data: FlinkOperatorApi.FlinkOperatorAddJob) => {
  return await service.post<ApiResponse<any>>('/opsFlinkDeploymentConfig/add/deployment', data)
}

/**
 * @description 任务管理-配置操作
 * @param {FlinkOperatorApi.FlinkOperatorSelectConfig} data
 * @return {*}
 */
export const flinkOperatorSelectConfig = async (data: FlinkOperatorApi.FlinkOperatorSelectConfig) => {
  return await service.post<ApiResponse<any>>('/flinkOperator/selectConfig', data)
}

/**
 * @description 任务管理-删除
 * @param {FlinkOperatorApi.FlinkOperatorDelete} data
 * @return {*}
 */
export const flinkOperatorDelete = async (data: FlinkOperatorApi.FlinkOperatorDelete) => {
  return await service.post<ApiResponse<any>>('/flinkOperator/remove', data)
}

/**
 * @description 任务管理-删除
 * @param {FlinkOperatorApi.FlinkOperatorUpdate} data
 * @return {*}
 */
export const flinkOperatorUpdate = async (data: FlinkOperatorApi.FlinkOperatorUpdate) => {
  return await service.post<ApiResponse<any>>('/flinkOperator/update/deployment', data)
}

export const flinkDeploymentYaml = async (data: FlinkOperatorApi.FlinkDeploymentYaml) => {
  return await downService().post(getPath(`/opsFlinkDeploymentConfig/get/deployment`), data)
}
