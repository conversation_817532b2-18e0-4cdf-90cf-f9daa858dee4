<template>
  <BaseLayout>
    <template #header>
      <BaseSearch :searchItemData="searchItemData" @on-submit="submitSearch" @reset-search="resetSearch" />
      <BaseElTooltip :content="$t('replenish.namespacePermissionTip')" placement="top" :disabled="!namespaceAddDisabled">
        <BaseAddButton :is-button="$has('k8s-namespace')" :disabled="namespaceAddDisabled" @click="handleAddNS">{{
          $t('replenish.addNamespace')
        }}</BaseAddButton>
      </BaseElTooltip>
    </template>
    <template #content>
      <BaseTable v-loading="tableLoading" :columns="tableData.columns" :data="tableData.data">
        <template #clusterName="scope">
          <BaseButton link @click="handleNamespaceView(scope)" v-if="scope.row.clusterName">
            <span style="white-space: pre">{{ scope.row.clusterName }}</span>
          </BaseButton>
          <div v-else>-</div>
        </template>
        <template #after>
          <el-table-column v-if="$has('k8s-namespace')" :label="$t('table.operate')" :width="300" fixed="right">
            <template #default="scope">
              <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" :dropdown-data="tableToolbar(scope).dropdownData" />
            </template>
          </el-table-column>
        </template>
      </BaseTable>
    </template>
    <template #footer>
      <BasePagination v-model="pageInfo" @page-change="onPageChange" />
    </template>
  </BaseLayout>
  <AddNamespace v-model="addVisible" @submit="getNSList" :k8s-id="k8sId" :adminAuth="adminAuth" />
  <NamespaceAssign v-model="namespaceAssignVisible" :id="currentNSId" @submit="getNSList" />
</template>

<script lang="ts" setup>
import elConfim from '@/utils/elConfim'
import AddNamespace from './components/AddNamespace.vue'
import NamespaceAssign from './components/NamespaceAssign.vue'
import { tr } from 'element-plus/es/locale'
import { checkNamespaceAuthByK8sId } from '@/api/namespaceApi'

const { t, store, $has, setBreadList, route, hasBusinessAuthority, router } = useBasicTool()
const { pageInfo, resetPageInfo } = usePage()
onMounted(() => {
  getNSList()
  setBreadList([])
  checkAddPermission()
})
const tableLoading = ref(true)
const tenantId = computed(() => store.state.user.tenantId)
const type = computed(() => Number(store.state.user.userInfo.type))
const searchValue = ref('')
const addVisible = ref(false)
const namespaceAssignVisible = ref(false)
const k8sId = computed(() => store.state.k8s.k8sId)
const adminAuth = computed(() => store.state.k8s.k8sAdminAuth)
const tableData = reactive({
  columns: [
    { label: `Namespace${t('replenish.designation')}`, prop: 'kubeNamespaceName' },
    { label: t('replenish.tenantBelonging'), prop: 'tenantName' },
    { label: t('replenish.relatedCluster'), prop: 'clusterName', slot: true }
  ],
  data: []
})
// 搜索配置项
const searchItemData = reactive<Array<BaseSearch.SearchItemData>>([
  {
    label: computed(() => t('form.name') + '：'),
    type: 'input',
    param: 'name',
    defaultValue: '',
    placeholder: t('replenish.pleaseEnterName')
  }
])
const currentNSId = ref('')
const namespaceAddDisabled = ref(true)
// 搜索提交
function submitSearch(value: any) {
  pageInfo.pageNo = 1
  searchOpera(value)
}
// 重置搜索框
function resetSearch(value: any) {
  resetPageInfo()
  searchOpera(value)
}
// 搜索操作
function searchOpera(value: any) {
  searchValue.value = value.name
  tableLoading.value = true
  getNSList()
}

function getNSList() {
  store
    .dispatch('namespace/namespacePage', {
      namespaceName: searchValue.value,
      pageNo: pageInfo.pageNo,
      pageSize: pageInfo.pageSize,
      searchAudit: true,
      id: k8sId.value,
      tenantId: tenantId.value
    })
    .then((res) => {
      tableData.data = res.data.records || []
      pageInfo.total = res.data.total
      tableLoading.value = false
    })
}
function handleAddNS() {
  addVisible.value = true
}
function onPageChange() {
  getNSList()
}

// 表格操作栏数据
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  return {
    buttonData: [
      {
        buttonName: t('replenish.resourceAllocation'),
        disabled: false,
        click: () => {
          currentNSId.value = scope.row.id
          namespaceAssignVisible.value = true
        },
        isButton: type.value === 2 && !tenantId.value
      },
      {
        buttonName: t('table.delete'),
        disabled: false,
        click: () => {
          deleteNS(scope)
        },
        isButton: hasBusinessAuthority('k8s-namespace-delete', k8sId.value)
      }
    ]
  }
}

function deleteNS(scope: any) {
  elConfim
    .confim({
      isCancelButton: true,
      message: t('replenish.isDelete').replace('{value}', scope.row.kubeNamespaceName)
    })
    .then(() => {
      store.dispatch('namespace/namespaceDelete', { id: scope.row.id, tenantId: tenantId.value }).then((res) => {
        getNSList()
      })
    })
}

function handleNamespaceView(scope: any) {
  const id = scope?.row.namespaceId
  const namespaceTitle = scope?.row.clusterName
  const deployMode = scope?.row?.deployMode
  store.commit('namespace/setNamespaceName', namespaceTitle)
  store.commit('component/setNamespaceId', id)
  store.commit('component/setDeployMode', deployMode)
  // store.commit('namespace/SET_CURR_NAMESPACE_OF_VIEW_NAMESPACE', scope?.row)
  store.commit('namespace/setMeunCurrentNamespaceTitle', namespaceTitle)
  router.push({
    name: 'namespaceOverview',
    query: { namespaceId: id, namespaceTitle, deployMode }
  })
}

const checkAddPermission = () => {
  checkNamespaceAuthByK8sId({
    id: k8sId.value
  }).then((res) => {
    namespaceAddDisabled.value = !res.data.data
  })
}
</script>
