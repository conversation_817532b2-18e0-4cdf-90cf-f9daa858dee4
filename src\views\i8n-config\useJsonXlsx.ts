import jsonConfigs from '@/locales/lang'
import jsonZh from '@/locales/lang/zh-CN'
import { diffLines } from 'diff'
import type { UploadFile } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import type { WritingOptions } from 'xlsx'
import * as XLSX from 'xlsx'

let branchJson: any

const loadScript = (src: any, callback: any) => {
  // const script = document.createElement("script");
  // script.src = src;
  // script.onload = callback;
  // script.onerror = () => {
  //   callback({});
  // };
  // document.head.appendChild(script);
  const xhr = new XMLHttpRequest()
  xhr.open('GET', src, true)
  xhr.onreadystatechange = () => {
    if (xhr.readyState === 4 && xhr.status === 200) {
      const fileContent = xhr.responseText
      callback(fileContent)
    } else if (xhr.readyState === 4) {
      console.error(`无法加载文件: ${src}`)
      callback(JSON.stringify(jsonConfigs))
    }
  }
  xhr.send()
}

export const importJson = () => {
  return new Promise((resolve) => {
    const filePath = '../../git_diff_branch_json.json'
    if (branchJson) {
      resolve(branchJson)
    } else {
      loadScript(filePath, (e) => {
        branchJson = JSON.parse(e)
        resolve(branchJson)
      })
    }
  })
}

type ExcelArr = {
  // key?: string;
  // en: string;
  // zh: string;
  // jp: string;
  [key: string]: string
}[]

interface JsonItem {
  [key: string]: string
}

interface MyJson {
  [key: string]: JsonItem
}

export const useJsonToXlsx = () => {
  const oldJson = reactive(jsonConfigs)
  const s2ab = (s) => {
    const buf = new ArrayBuffer(s.length)
    const view = new Uint8Array(buf)
    for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
    return buf
  }

  const workbook2blob = (workbook) => {
    // 生成excel的配置项
    const wopts: WritingOptions = {
      // 要生成的文件类型
      bookType: 'xlsx',
      // // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
      bookSST: false,
      type: 'binary'
    }
    const wbout = XLSX.write(workbook, wopts)
    // 将字符串转ArrayBuffer
    const blob = new Blob([s2ab(wbout)], {
      type: 'application/octet-stream'
    })
    return blob
  }

  const openDownloadDialog = (blob, fileName) => {
    if (typeof blob === 'object' && blob instanceof Blob) {
      blob = URL.createObjectURL(blob) // 创建blob地址
    }
    const aLink = document.createElement('a')
    aLink.href = blob
    // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，有时候 file:///模式下不会生效
    aLink.download = fileName || ''
    let event
    if (window.MouseEvent) event = new MouseEvent('click')
    //   移动端
    else {
      event = document.createEvent('MouseEvents')
      event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
    }
    aLink.dispatchEvent(event)
  }

  const exportExcelBySheets = async () => {
    await importJson()
    const wb = XLSX.utils.book_new()
    const locales = Object.keys(jsonConfigs)

    Object.keys(branchJson?.en).forEach((key) => {
      if (!['name', 'el'].includes(key)) {
        const jsonArr = [] as ExcelArr
        // const keys
        Object.keys(branchJson?.en[key]).forEach((el) => {
          const item = {
            key: el
          }
          locales.forEach((k) => {
            item[k] = jsonConfigs[k][key][el]
          })
          jsonArr.push(item)
        })

        const sheet = XLSX.utils.json_to_sheet(jsonArr)

        XLSX.utils.book_append_sheet(wb, sheet, key)
      }
    })

    // 导出最后的总表
    openDownloadDialog(workbook2blob(wb), '国际化.xlsx')
  }

  return {
    exportExcelBySheets,
    oldJson,
    branchJson
  }
}

export const useXlsxToJson = (locale) => {
  const newJson = reactive<any>(cloneDeep({}))

  const modules = ref(
    Object.keys(jsonZh).map((el) => {
      return {
        label: el,
        num: 0
      }
    })
  )

  const module = ref('')

  const updateObj = (oldObj, newObj) => {
    for (const key in newObj) {
      if (typeof newObj[key] === 'object' && newObj[key] !== null) {
        if (!(key in oldObj)) {
          oldObj[key] = {}
        }
        updateObj(oldObj[key], newObj[key])
      } else if (oldObj[key] !== newObj[key]) {
        oldObj[key] = newObj[key]
      }
    }
  }

  const diffFun = async () => {
    await importJson()
    const info = jsonConfigs[locale.value]
    modules.value = Object.keys(info).map((el) => {
      return {
        label: el,
        num: 0
      }
    })
    modules.value.forEach((el) => {
      const oldObj = cloneDeep(info[el.label])
      const oldVal = JSON.stringify(info[el.label], null, 2)
      const newObj = cloneDeep(newJson?.[el.label]?.[locale.value] || {})
      updateObj(oldObj, newObj)
      const newVal = JSON.stringify(oldObj, null, 2)
      el.num = diffLines(oldVal, newVal)?.filter((item) => item.added || item.removed).length || 0
    })
    // nextTick(() => {
    //   modules.value = modules.value.filter((el) => el.num > 0);
    // });
  }

  const readFile = (file: any) => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.readAsBinaryString(file)
      reader.onload = (ev) => {
        if (ev && ev.target) resolve(ev.target.result)
      }
    })
  }

  const updateJson = async (rawFile: UploadFile) => {
    const dataBinary = await readFile(rawFile.raw)
    const workBook = XLSX.read(dataBinary, { type: 'binary', cellDates: true })
    workBook.SheetNames.forEach((key, i) => {
      newJson[key] = {} as MyJson
      const workSheet = workBook.Sheets[workBook.SheetNames[i]]
      const data = XLSX.utils.sheet_to_json(workSheet) as ExcelArr
      const en = {} as JsonItem
      const zh = {} as JsonItem
      const jp = {} as JsonItem
      data.forEach((el) => {
        en[el.key] = el.en
        zh[el.key] = el.zh
        jp[el.key] = el.jp
      })
      newJson[key]['en'] = en
      newJson[key]['zh'] = zh
      newJson[key]['jp'] = jp
    })
    diffFun()
  }

  return {
    updateJson,
    newJson,
    modules,
    diffFun,
    updateObj,
    module
  }
}
