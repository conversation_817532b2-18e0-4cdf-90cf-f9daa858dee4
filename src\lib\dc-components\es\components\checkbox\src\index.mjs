import { defineComponent, useAttrs, computed, isRef, openBlock, createBlock, unref, mergeProps, withCtx, createElementBlock, Fragment, renderList, createTextVNode, toDisplayString } from 'vue';
import { ElCheckboxGroup, ElCheckbox } from 'element-plus';
import 'element-plus/es/components/checkbox/style/css';
import 'element-plus/es/components/checkbox-group/style/css';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcCheckbox"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: Array
    },
    options: {
      type: [Array, Object],
      default: () => []
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const attrs = useAttrs();
    const options = computed(() => {
      return isRef(props.options) ? props.options.value : props.options;
    });
    const handleChange = (val) => {
      emits("update:modelValue", val);
      if (typeof attrs.onChange === "function") {
        attrs.onChange(val);
      }
    };
    ;
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElCheckboxGroup), mergeProps({ ..._ctx.$attrs }, {
        "model-value": props.modelValue,
        onChange: handleChange
      }), {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(options.value, (item, index) => {
            return openBlock(), createBlock(unref(ElCheckbox), {
              key: index,
              label: item.value
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(item.label), 1)
              ]),
              _: 2
            }, 1032, ["label"]);
          }), 128))
        ]),
        _: 1
      }, 16, ["model-value"]);
    };
  }
});
var DcCheckbox = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\checkbox\\src\\index.vue"]]);

export { DcCheckbox as default };
//# sourceMappingURL=index.mjs.map
