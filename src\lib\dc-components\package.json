{"name": "dc-components", "version": "0.0.35", "main": "lib/index.js", "module": "es/index.mjs", "types": "es/index.d.ts", "exports": {".": {"types": "./es/index.d.ts", "import": "./es/index.mjs", "require": "./lib/index.js"}, "./es": "./es/index.mjs", "./lib": "./lib/index.js", "./es/*.mjs": "./es/*.mjs", "./es/*": "./es/*.mjs", "./lib/*.js": "./lib/*.js", "./lib/*": "./lib/*.js", "./*": "./*"}, "unpkg": "dist/index.full.js", "peerDependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^1.1.4", "crypto-js": "^4.2.0", "element-plus": "^2.2.19", "lodash": "^4.17.21", "vue": "^3.2.37"}, "dependencies": {}}