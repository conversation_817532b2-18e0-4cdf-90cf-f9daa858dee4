'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');

var RenderVNode = vue.defineComponent({
  name: "DcRenderVNode",
  props: {
    vnode: {
      type: Function,
      default: () => null
    },
    scope: {
      type: Object,
      default: () => ({})
    },
    extraScope: {
      type: Object,
      default: () => ({})
    }
  },
  render() {
    return typeof this.$props.vnode === "function" ? this.$props.vnode(this.$props.scope, this.$props.extraScope) : null;
  }
});

exports["default"] = RenderVNode;
//# sourceMappingURL=index.js.map
