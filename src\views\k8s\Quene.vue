<template>
  <div class="quene h-full p-20px">
    <BaseLayout>
      <template #main>
        <div class="quene-content">
          <div class="quene-card">
            <div class="quene-card-item">
              <div class="card-img">
                <img src="@/assets/img/cpu.png" alt="" />
              </div>
              <div class="card-content">
                <div class="card-title">
                  <span>{{ $t('title.sumCpu') }}</span>
                  <span class="card-title-tip">{{ $t('title.queneCardTip') }}</span>
                </div>
                <div class="card-num">
                  {{ Math.floor(overviewInfo?.totalCpuCapacity - overviewInfo?.totalCpuUsed) }}/{{ overviewInfo?.totalCpuCapacity }}(vcore)
                </div>
                <div class="card-tip">{{ $t('title.queneCardTip1') }}</div>
              </div>
            </div>
            <div class="quene-card-item">
              <div class="card-img">
                <img src="@/assets/img/memore.png" alt="" />
              </div>
              <div class="card-content">
                <div class="card-title">
                  <span>{{ $t('title.sumMemory') }}</span>
                  <span class="card-title-tip">{{ $t('title.queneCardTip') }}</span>
                </div>
                <div class="card-num">
                  {{ Math.floor(overviewInfo?.totalMemoryCapacity - overviewInfo?.totalMemoryUsed) }}/{{ overviewInfo?.totalMemoryCapacity }}(GB)
                </div>
                <div class="card-tip">{{ $t('title.queneCardTip1') }}</div>
              </div>
            </div>
            <div class="quene-card-item">
              <div class="card-img">
                <img src="@/assets/img/gpu-sum.png" alt="" />
              </div>
              <div class="card-content">
                <div class="card-title">
                  <span>{{ $t('title.sumGpu') }}</span>
                  <span class="card-title-tip">{{ $t('title.queneCardTip') }}</span>
                </div>
                <div class="card-num">{{ overviewInfo?.totalGpuCapacity }}(Num)</div>
                <div class="card-tip">{{ $t('title.summary') }}</div>
              </div>
            </div>
            <!-- <div class="quene-card-item">
              <div class="card-img">
                <img src="@/assets/img/gpu.png" alt="" />
              </div>
              <div class="card-content">
                <div class="card-title">
                  <span>{{ $t('title.sumGpuStorage') }}</span>
                  <span class="card-title-tip">{{ $t('title.queneCardTip') }}</span>
                </div>
                <div class="card-num">
                  {{ Math.floor(overviewInfo?.totalGpuMemoryCapacity - overviewInfo?.totalGpuMemoryUsed) }}/{{
                    overviewInfo?.totalGpuMemoryCapacity
                  }}(MB)
                </div>
                <div class="card-tip">{{ $t('title.queneCardTip1') }}</div>
              </div>
            </div> -->
          </div>
          <!-- 搜索 -->
          <div class="search">
            <BaseSearch :searchItemData="searchItemData" @on-submit="submitSearch" @reset-search="resetSearch">
              <template #button>
                <div class="append-btn">
                  <BaseButton class="base-add-button" type="primary" @click="openAddQuene">
                    {{ $t('form.addQuene') }}
                  </BaseButton>
                </div>
              </template>
            </BaseSearch>
          </div>
          <!-- 表格 -->
          <div class="quene-table" ref="tableRef">
            <el-table
              :data="quenetableData"
              :header-cell-style="{
                'background-color': 'var(--ops-table-bg-heder-color)',
                'font-weight': 400,
                color: 'var(--ops-table-text-heder-color)'
              }"
              :cell-style="{
                color: 'var(--ops-text-color)'
              }"
              style="width: 100%"
              :height="tableHeight"
            >
              <el-table-column type="expand">
                <template #default="props">
                  <div class="children-table pl-20px">
                    <el-table
                      :data="props.row.task"
                      :border="childBorder"
                      :header-cell-style="{
                        'background-color': 'var(--ops-table-bg-heder-color)',
                        'font-weight': 400,
                        color: 'var(--ops-table-text-heder-color)'
                      }"
                      :cell-style="{
                        color: 'var(--ops-text-color)'
                      }"
                    >
                      <el-table-column label="taskName" prop="ID" />
                      <el-table-column :label="$t('replenish.user')" prop="userName" />
                      <el-table-column :label="$t('form.state')" prop="status">
                        <template #default="{ row }">{{ stateMap[row.status] }}</template>
                      </el-table-column>
                      <el-table-column :label="$t('replenish.startTime')" prop="startTime" />
                      <el-table-column :label="$t('replenish.endTime')" prop="endTime" />
                      <el-table-column :label="$t('replenish.type')" prop="taskType" />
                      <el-table-column :label="$t('form.allocatedCpu')" prop="cpuAllocated" />
                      <el-table-column :label="$t('form.allocatedMemory')" prop="memoryAllocated" />
                      <!-- <el-table-column :label="$t('form.allocatedGpuMemory')" prop="gpuMemoryAllocated" /> -->
                    </el-table>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('form.queneName')" prop="name" />
              <el-table-column :label="$t('form.attribute')" prop="type">
                <template #default="{ row }">
                  {{ row.type === 0 ? '新增' : '存量' }}
                </template>
              </el-table-column>

              <el-table-column :label="$t('form.queneTableProcess')" prop="name">
                <template #default="{ row }"> {{ row.runningTask }}/{{ row.completedTask }}/{{ row.totalTask }} </template>
              </el-table-column>
              <el-table-column :label="$t('form.queneDesc')" prop="description" />
              <el-table-column :label="$t('form.weight')" prop="weight" />
              <el-table-column :label="$t('form.cpuCanUse')" prop="name">
                <template #default="{ row }"> {{ row.totalCpuCapacity - row.totalCpuUsed }}/{{ row.totalCpuCapacity }} </template>
              </el-table-column>
              <el-table-column :label="$t('form.memoizeCanUse')" prop="name">
                <template #default="{ row }"> {{ row.totalMemoryCapacity - row.totalMemoryUsed }}/{{ row.totalMemoryCapacity }} </template>
              </el-table-column>
              <el-table-column :label="$t('form.gpuSum')" prop="totalGpuCapacity" />
              <!-- <el-table-column :label="$t('form.gpuShow')" prop="name">
                <template #default="{ row }"> {{ row.totalGpuMemoryCapacity - row.totalGpuMemoryUsed }}/{{ row.totalGpuMemoryCapacity }} </template>
              </el-table-column> -->
              <!-- <el-table-column :label="$t('form.tenant')" prop="tenant" /> -->
              <el-table-column :label="$t('table.operate')" fixed="right">
                <template #default="scope">
                  <el-button text link @click="handleEdit(scope.row)"> {{ $t('table.edit') }} </el-button>
                  <el-button text link @click="deleteQueneItem(scope.row)"> {{ $t('table.delete') }} </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="footer">
            <BasePagination v-model="pageInfo" @pageChange="onPageChange" />
          </div>
        </div>
      </template>
    </BaseLayout>
  </div>
  <AddQuene :rowItem="currentRow" :dialogVisible="dialogVisible" :title="dialogTitle" @close="dialogVisible = false" @confirm="addQueneConfirm" />
</template>

<script lang="ts" setup>
import elConfim from '@/utils/elConfim'
import AddQuene from './components/AddQuene.vue'
import NamespaceAssign from './components/NamespaceAssign.vue'
import { checkNamespaceAuthByK8sId } from '@/api/namespaceApi'
import BaseLayout from '@/components/common/BaseLayout.vue'
import BasePagination from '@/components/common/BasePagination.vue'
import { useElementSize } from '@vueuse/core'
import { deleteQuene, getQueneOverView, getQueneList } from '@/api/queneApi'
const tableRef = ref()
const { height: tableHeight } = useElementSize(tableRef)
const { t, store, $has, setBreadList, route, hasBusinessAuthority, router } = useBasicTool()
const { pageInfo, resetPageInfo } = usePage()
const overviewInfo = ref({})
const tableLoading = ref(true)
const tenantId = computed(() => store.state.user.tenantId)
const type = computed(() => Number(store.state.user.userInfo.type))
const searchValue = ref('')
const addVisible = ref(false)
const namespaceAssignVisible = ref(false)
const k8sId = computed(() => store.state.k8s.k8sId || route.query.id)
const adminAuth = computed(() => store.state.k8s.k8sAdminAuth)
const tableData = reactive({
  columns: [
    { label: `Namespace${t('replenish.designation')}`, prop: 'kubeNamespaceName' },
    { label: t('replenish.tenantBelonging'), prop: 'tenantName' },
    { label: t('replenish.createTime'), prop: 'createTime' },
    { label: t('replenish.relatedCluster'), prop: 'clusterName', slot: true }
  ],
  data: []
})
const stateMap: any = {
  Created: '已接受',
  Running: '运行中',
  Restarting: '重启中',
  Succeeded: '已完成',
  Failed: '失败'
}
// 搜索配置项
const searchItemData = reactive<Array<BaseSearch.SearchItemData>>([
  {
    label: computed(() => t('form.queneName') + '：'),
    type: 'input',
    param: 'name',
    defaultValue: '',
    placeholder: t('replenish.pleaseEnterName')
  }
  // {
  //   label: computed(() => t('form.queneType') + '：'),
  //   type: 'select',
  //   selectOptions: [],
  //   param: 'type',
  //   defaultValue: '',
  //   placeholder: computed(() => t('form.pleaseSelectQueue'))
  // }
])
const searchForm = reactive({
  name: '',
  type: ''
})
const dialogVisible = ref(false)
const dialogTitle = ref('')
const currentRow = ref()
const currentNSId = ref('')
const namespaceAddDisabled = ref(true)
// 搜索提交
function submitSearch(value: any) {
  pageInfo.pageNo = 1
  searchOpera(value)
}
// 重置搜索框
function resetSearch(value: any) {
  resetPageInfo()
  searchOpera(value)
}
// 搜索操作
function searchOpera(value: any) {
  searchForm.name = value.name
  searchForm.type = value.type
  tableLoading.value = true
  getQueneListData()
  // getNSList()
}
const parentBorder = ref(false)
const childBorder = ref(false)
const preserveExpanded = ref(false)
const quenetableData = ref([])

function onPageChange(pageInfo: any) {
  getQueneListData()
}

function deleteQueneItem(row: any) {
  elConfim
    .confim({
      isCancelButton: true,
      message: t('replenish.isDelete').replace('{value}', row.name)
    })
    .then(() => {
      deleteQuene({ id: row.id }).then((res: any) => {
        getQueneListData()
      })
    })
}

function openAddQuene() {
  dialogVisible.value = true
  dialogTitle.value = t('form.addQuene')
  console.log('dialogVisible.value', dialogVisible.value)
  currentRow.value = {}
}
function handleEdit(row: any) {
  dialogVisible.value = true
  dialogTitle.value = t('form.editQuene')
  currentRow.value = row
}
function addQueneConfirm() {
  dialogVisible.value = false
  getQueneListData()
}
async function getAllResource() {
  const res = await getQueneOverView({ id: k8sId.value })
  console.log('res', res)
  overviewInfo.value = res.data.data
}
async function getQueneListData() {
  const params = {
    id: k8sId.value,
    pageNo: pageInfo.pageNo,
    pageSize: pageInfo.pageSize,
    queueName: searchForm.name
  }
  const res = await getQueneList(params)
  console.log('res', res)
  quenetableData.value = res.data.data.records
  pageInfo.total = res.data.data.total
}
onMounted(() => {
  // getNSList()
  setBreadList([])
  getAllResource()
  getQueneListData()
})
</script>

<style lang="scss" scoped>
.quene-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .quene-card {
    display: flex;
    width: 100%;
  }
  .quene-card-item {
    display: flex;
    align-items: center;
    flex: 1;
    height: 124px;
    border-radius: 6px;
    margin-right: 16px;
    &:last-child {
      margin-right: 0;
    }
    &:nth-child(1) {
      background: linear-gradient(135deg, rgba(86, 122, 255, 0.1) 0%, rgba(86, 122, 255, 0.06) 99%, rgba(196, 239, 238, 0) 100%);
    }
    &:nth-child(2) {
      background: linear-gradient(135deg, rgba(75, 193, 100, 0.1) 0%, rgba(75, 193, 100, 0.06) 100%, rgba(196, 239, 238, 0) 100%);
    }
    &:nth-child(3) {
      background: linear-gradient(135deg, rgba(195, 105, 252, 0.1) 0%, rgba(195, 105, 252, 0.06) 100%, rgba(196, 239, 238, 0) 100%);
    }
    &:nth-child(4) {
      background: linear-gradient(135deg, rgba(0, 176, 255, 0.1) 0%, rgba(0, 176, 255, 0.05) 100%, rgba(196, 239, 238, 0) 100%);
    }
    .card-img {
      img {
        width: 54px;
        height: 54px;
        margin-left: 24px;
      }
    }
    .card-content {
      margin-left: 20px;
      .card-title {
        font-size: 14px;
        line-height: 20px;
        font-weight: 500;
        color: #2e3442;
        .card-title-tip {
          font-weight: 400;
          color: rgba(46, 52, 66, 0.75);
        }
      }
      .card-num {
        height: 24px;
        margin-top: 14px;
        font-size: 20px;
        line-height: 24px;
        font-weight: 600;
        color: #2e3442;
      }
      .card-tip {
        font-size: 14px;
        font-weight: 400;
        color: rgba(46, 52, 66, 0.65);
      }
    }
  }
  :deep(.base-search) {
    flex: 1;
    min-width: 100%;
    .el-form {
      display: inline-flex;
      flex-flow: row wrap;
      width: 100%;
    }
    .search-button {
      flex: 1;
      min-width: 400px;
      margin-right: 0;
      .el-form-item__content {
        display: inline-flex;
        flex-flow: row;
        width: 100%;
      }
      .append-btn {
        flex: 1;
        text-align: right;
      }
    }
  }
  .quene-table {
    flex: 1;
  }
  .footer {
    display: flex;
    justify-content: flex-end;
  }
}
:deep(.layout-main) {
  height: 100%;
  margin: 0;
}
</style>
