/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    BaseAddButton: typeof import('./../components/common/BaseAddButton.vue')['default']
    BaseBreadCrumb: typeof import('./../components/common/BaseBreadCrumb.vue')['default']
    BaseButton: typeof import('./../components/common/BaseButton.vue')['default']
    BaseCopy: typeof import('./../components/common/BaseCopy.vue')['default']
    BaseElCollapse: typeof import('./../components/common/BaseElCollapse.vue')['default']
    BaseElTabs: typeof import('./../components/common/BaseElTabs.vue')['default']
    BaseElTooltip: typeof import('./../components/common/BaseElTooltip.vue')['default']
    BaseEmpty: typeof import('./../components/common/BaseEmpty.vue')['default']
    BaseFilterConinter: typeof import('./../components/common/BaseFilterConinter.vue')['default']
    BaseFrom: typeof import('./../components/common/BaseFrom.vue')['default']
    BaseHeaderTitle: typeof import('./../components/common/BaseHeaderTitle.vue')['default']
    BaseLayout: typeof import('./../components/common/BaseLayout.vue')['default']
    BasePagination: typeof import('./../components/common/BasePagination.vue')['default']
    BaseSearch: typeof import('./../components/common/BaseSearch.vue')['default']
    BaseState: typeof import('./../components/common/BaseState.vue')['default']
    BaseTable: typeof import('./../components/common/BaseTable.vue')['default']
    BaseTableToolbar: typeof import('./../components/common/BaseTableToolbar.vue')['default']
    BaseTitle: typeof import('./../components/common/BaseTitle.vue')['default']
    BaseTooltip: typeof import('./../components/common/BaseTooltip.vue')['default']
    CodeDiffEditor: typeof import('./../components/CodeDiffEditor/index.vue')['default']
    Codemirror: typeof import('./../components/common/Codemirror.vue')['default']
    DiffEditor: typeof import('./../components/DiffEditor/index.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPageHeader: typeof import('element-plus/es')['ElPageHeader']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTimeSelect: typeof import('element-plus/es')['ElTimeSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    MonacoEditor: typeof import('./../components/MonacoEditor/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./../components/common/SvgIcon.vue')['default']
    TheAddState: typeof import('./../components/The/TheAddState.vue')['default']
    TheBatchCofirm: typeof import('./../components/The/TheBatchCofirm.vue')['default']
    TheBatchCofirmTwo: typeof import('./../components/The/TheBatchCofirmTwo.vue')['default']
    TheCheckHost: typeof import('./../components/The/TheCheckHost.vue')['default']
    TheColumnsFilter: typeof import('./../components/The/TheColumnsFilter.vue')['default']
    TheDatePicker: typeof import('./../components/The/TheDatePicker.vue')['default']
    TheDialog: typeof import('./../components/The/TheDialog.vue')['default']
    TheHostAliasRename: typeof import('./../components/The/TheHostAliasRename.vue')['default']
    TheHostEdit: typeof import('./../components/The/TheHostEdit.vue')['default']
    TheHostsAddInputDropdown: typeof import('./../components/The/TheHostsAddInputDropdown.vue')['default']
    TheHostTotal: typeof import('./../components/The/TheHostTotal.vue')['default']
    TheInstallationProgress: typeof import('./../components/The/TheInstallationProgress.vue')['default']
    TheProgressDialog: typeof import('./../components/The/TheProgressDialog.vue')['default']
    TheProgressDialogState: typeof import('./../components/The/TheProgressDialogState.vue')['default']
    TheStaus: typeof import('./../components/The/TheStaus.vue')['default']
    TheTemplateTabs: typeof import('./../components/The/TheTemplateTabs.vue')['default']
    TheTitle: typeof import('./../components/The/TheTitle.vue')['default']
    TheTooltipOverflow: typeof import('./../components/The/TheTooltipOverflow.vue')['default']
    TheVerifyHostConnectivity: typeof import('./../components/The/TheVerifyHostConnectivity.vue')['default']
    TheViewAllHost: typeof import('./../components/The/TheViewAllHost.vue')['default']
    TheViewAllOption: typeof import('./../components/The/TheViewAllOption.vue')['default']
    TheWebTerminalbutton: typeof import('./../components/The/TheWebTerminalbutton.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
