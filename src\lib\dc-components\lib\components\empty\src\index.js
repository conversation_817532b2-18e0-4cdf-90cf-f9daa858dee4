'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/empty/style/css');
require('element-plus/es/components/image/style/css');
var constants = require('./constants.js');
require('./index.vue_vue_type_style_index_0_lang.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const _hoisted_1 = { class: "dc-empty" };
const _hoisted_2 = { key: 1 };
const __default__ = vue.defineComponent({
  name: "DcEmpty"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    locale: {
      type: String,
      default: "zh-CN"
    },
    type: {
      type: String,
      default: "list"
    },
    theme: {
      type: String,
      default: "light"
    },
    description: {
      type: String,
      default: ""
    },
    showImg: {
      type: Boolean,
      default: true
    },
    img: {
      type: String,
      default: ""
    }
  },
  setup(__props) {
    ;
    const props = __props;
    const image = vue.computed(() => props.img || constants.imgConfig[props.theme]?.[props.type]);
    const desc = vue.computed(() => props.description || constants.descConfig[props.locale]?.[props.type]);
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", _hoisted_1, [
        vue.createVNode(vue.unref(elementPlus.ElEmpty), { description: desc.value }, vue.createSlots({
          image: vue.withCtx(() => [
            __props.showImg ? (vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElImage), {
              key: 0,
              src: image.value
            }, null, 8, ["src"])) : (vue.openBlock(), vue.createElementBlock("div", _hoisted_2))
          ]),
          default: vue.withCtx(() => [
            vue.renderSlot(_ctx.$slots, "default")
          ]),
          _: 2
        }, [
          _ctx.$slots.description ? {
            name: "description",
            fn: vue.withCtx(() => [
              vue.renderSlot(_ctx.$slots, "description")
            ]),
            key: "0"
          } : void 0
        ]), 1032, ["description"])
      ]);
    };
  }
});
var DcEmpty = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\empty\\src\\index.vue"]]);

exports["default"] = DcEmpty;
//# sourceMappingURL=index.js.map
