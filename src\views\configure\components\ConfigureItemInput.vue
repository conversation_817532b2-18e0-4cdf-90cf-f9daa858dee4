<template>
  <div class="configure-input-group">
    <div class="icon-group ml-20px mr--4px">
      <BaseElTooltip :content="$t('button.restoreDefaultsTip')" placement="top">
        <img v-show="isShowDefault" src="@/assets/icons/icon_huifu.svg" class="cursor-pointer" alt="icon" @click="onRestDefault" />
      </BaseElTooltip>
      <BaseElTooltip :content="$t('button.revokeTip')" placement="top">
        <i v-show="isShowValue" class="iconfont icon-icon_chexiao1" @click="onRestValue" />
      </BaseElTooltip>
    </div>
    <div v-if="['yarn.scheduler.fair.config', 'yarn.scheduler.capacity.config'].includes(confKey)" style="flex: 1">
      <Codemirror :model-value="modelValue" :default-value="modelValue" @input="onInput"></Codemirror>
    </div>
    <el-input
      v-else
      :model-value="modelValue"
      :disabled="disabled"
      type="textarea"
      :rows="1"
      :placeholder="$t('form.pleaseEnterConfigurationInformation')"
      @input="onInput"
    />
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  modelValue: {
    type: String as any,
    default: ''
  },
  confKey: {
    type: String,
    default: ''
  },
  dataConfValue: {
    type: String as any,
    default: ''
  },
  defaultValue: {
    type: String as any,
    default: ''
  },
  editConfValue: {
    type: String as any,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['input', 'update:modelValue'])
onMounted(() => {
  onShow(props.modelValue)
  if (props.editConfValue) {
    if (props.modelValue !== props.editConfValue) {
      const isEqualDataConfValue = props.editConfValue !== props.dataConfValue
      emit('update:modelValue', props.editConfValue)
      setShow(false, true)
      emit('input', props.editConfValue, isEqualDataConfValue)
    }
  }
})
const isShowDefault = ref(false)
const isShowValue = ref(false)
function setShow(setDefaultShow = false, setValueShow = false): void {
  isShowDefault.value = setDefaultShow
  isShowValue.value = setValueShow
}
// 重置为默认配置
function onRestDefault(): void {
  onInput(props.defaultValue)
  setShow(false, true)
}

// 重置为初始配置
function onRestValue(): void {
  onInput(props.dataConfValue)
}
// 响应输入框变化
function onInput(value: string): void {
  onShow(value)
  // const isEqualDataConfValue = value !== props.dataConfValue
  const isEqualDataConfValue = true
  emit('input', value, isEqualDataConfValue)
  emit('update:modelValue', value)
}

function onShow(value: string): void {
  if (value !== props.dataConfValue) return setShow(false, true)
  if (value !== props.defaultValue) return setShow(true, false)
  return setShow()
}
</script>
<style lang="scss" scoped>
.configure-input-group {
  @include flex(flex-end, center);
  flex: 1;
  width: 560px;
  .icon-group {
    width: 40px;
    color: var(--ops-primary-color);
    .iconfont {
      margin: 0 5px;
      cursor: pointer;
    }
  }
}
</style>
