import { createElementVNode, defineComponent, computed, ref, reactive, watch, nextTick, onMounted, resolveDirective, openBlock, createElementBlock, createVNode, unref, withCtx, Fragment, renderList, toDisplayString, withDirectives, vShow, createBlock, with<PERSON><PERSON><PERSON>, createCommentVNode, createTextVNode, normalizeClass } from 'vue';
import { ElMessage, ElCarousel, ElCarouselItem, ElImage, ElForm, ElFormItem, ElInput, ElIcon, ElButton, ElTooltip, ElDialog } from 'element-plus';
import 'element-plus/es/components/button/style/css';
import 'element-plus/es/components/carousel/style/css';
import 'element-plus/es/components/carousel-item/style/css';
import 'element-plus/es/components/dialog/style/css';
import 'element-plus/es/components/form/style/css';
import 'element-plus/es/components/form-item/style/css';
import 'element-plus/es/components/icon/style/css';
import 'element-plus/es/components/image/style/css';
import 'element-plus/es/components/input/style/css';
import 'element-plus/es/components/message/style/css';
import 'element-plus/es/components/tooltip/style/css';
import { Loading } from '@element-plus/icons-vue';
import config from './constants.mjs';
import { decrypt, getQueryParam, encrypt } from './encryption.mjs';
import img$2 from './images/CyberAI.mjs';
import img$5 from './images/CyberAIEnter.mjs';
import img from './images/CyberData.mjs';
import img$3 from './images/CyberDataEnter.mjs';
import img$1 from './images/CyberEngine.mjs';
import img$4 from './images/CyberEngineEnter.mjs';
import img$6 from './images/logo.mjs';
import './index.vue_vue_type_style_index_0_lang.mjs';
import './index.vue_vue_type_style_index_1_lang.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const _hoisted_1 = { class: "cyber-login" };
const _hoisted_2 = { class: "login-img" };
const _hoisted_3 = { class: "content" };
const _hoisted_4 = { class: "title" };
const _hoisted_5 = { class: "desc" };
const _hoisted_6 = { class: "content" };
const _hoisted_7 = { class: "title" };
const _hoisted_8 = { class: "desc" };
const _hoisted_9 = { class: "login-content" };
const _hoisted_10 = { class: "login-content-logo" };
const _hoisted_11 = { class: "login-content-form" };
const _hoisted_12 = {
  key: 0,
  class: "form-title"
};
const _hoisted_13 = {
  key: 1,
  class: "form-title"
};
const _hoisted_14 = /* @__PURE__ */ createElementVNode("div", null, "Hello!", -1);
const _hoisted_15 = /* @__PURE__ */ createElementVNode("svg", {
  xmlns: "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink",
  t: "1701420180107",
  class: "icon",
  viewBox: "0 0 1024 1024",
  version: "1.1",
  "p-id": "11971",
  width: "16",
  height: "16"
}, [
  /* @__PURE__ */ createElementVNode("path", {
    d: "M444.48 563.264h135.04A316.48 316.48 0 0 1 896 879.744a63.36 63.36 0 0 1-63.36 63.296H191.36a63.36 63.36 0 0 1-63.36-63.296 316.48 316.48 0 0 1 316.48-316.48zM512 499.968c-123.712 0-224-99.2-224-221.568C288 156.16 388.288 56.96 512 56.96s224 99.2 224 221.504c0 122.368-100.288 221.568-224 221.568z",
    "p-id": "11972",
    fill: "#545C71"
  })
], -1);
const _hoisted_16 = /* @__PURE__ */ createElementVNode("svg", {
  xmlns: "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink",
  t: "1701420415373",
  class: "icon",
  viewBox: "0 0 1024 1024",
  version: "1.1",
  "p-id": "12358",
  width: "16",
  height: "16"
}, [
  /* @__PURE__ */ createElementVNode("path", {
    d: "M832 960H192c-35.328 0-64-26.688-64-59.776v-448c0-33.024 28.672-59.712 64-59.712h640c35.328 0 64 26.688 64 59.712v448c0 33.088-28.672 59.776-64 59.776M512 541.888c-52.992 0-96 40.128-96 89.6 0 33.088 19.392 61.696 48 77.184v85.312c0 25.728 21.504 46.72 48 46.72a47.36 47.36 0 0 0 48-46.72v-85.312c28.608-15.488 48-44.096 48-77.184 0-49.472-43.008-89.6-96-89.6M512 153.6c-105.984 0-192 80.192-192 179.2H224C224 184.32 352.896 64 512 64s288 120.32 288 268.8H704c0-99.008-86.016-179.2-192-179.2",
    "p-id": "12359",
    fill: "#545C71"
  })
], -1);
const _hoisted_17 = /* @__PURE__ */ createElementVNode("path", {
  d: "M512 147.236571c197.485714 0 358.4 104.228571 482.816 312.685715l11.995429 20.48a64 64 0 0 1 0 63.122285C880.859429 765.805714 715.922286 876.836571 512 876.836571c-197.485714 0-358.4-104.301714-482.816-312.758857l-11.995429-20.48a64 64 0 0 1 0-63.122285C143.140571 258.194286 308.077714 147.236571 512 147.236571z m0 95.963429c-155.501714 0-283.209143 78.994286-388.900571 247.222857L109.933714 512l1.682286 2.925714c104.301714 174.811429 230.034286 259.949714 383.780571 265.508572l16.603429 0.365714c155.501714 0 283.209143-78.994286 388.900571-247.222857l13.165715-21.650286-1.682286-2.779428c-104.301714-174.811429-230.034286-260.022857-383.780571-265.654858L512 243.2zM351.963429 512a160.036571 160.036571 0 1 0 320.073142 0 160.036571 160.036571 0 0 0-320.073142 0z",
  "p-id": "12714",
  fill: "rgba(41, 52, 78, 0.4)"
}, null, -1);
const _hoisted_18 = [
  _hoisted_17
];
const _hoisted_19 = /* @__PURE__ */ createElementVNode("path", {
  d: "M161.92 104.704a25.6 25.6 0 0 1 18.112 7.488l257.92 257.92a160 160 0 0 1 215.872 215.936l257.984 257.92a25.6 25.6 0 0 1 0 36.224l-31.616 31.616a25.6 25.6 0 0 1-36.224 0L112.192 180.032a25.6 25.6 0 0 1 0-36.224l31.616-31.616a25.6 25.6 0 0 1 18.112-7.488z m-10.624 182.208l67.84 67.84c-32 32.64-62.08 71.744-90.24 117.312l-13.888 23.296L105.6 512l9.408 16.64c97.088 168.32 217.664 255.68 364.032 266.88l16.384 0.96 16.576 0.32c45.12 0 87.872-6.848 128.384-20.672l73.984 73.984A483.2 483.2 0 0 1 512 892.8c-211.584 0-380.544-122.048-503.424-358.656a48 48 0 0 1 0-44.288c42.24-81.28 89.856-148.992 142.72-202.88v-0.064zM512 131.2c211.584 0 380.544 122.048 503.424 358.656a48 48 0 0 1 0 44.288c-42.24 81.216-89.792 148.928-142.72 202.88l-67.84-67.84c32-32.64 62.08-71.68 90.24-117.248l13.888-23.296 9.344-16.64-9.344-16.64c-97.088-168.32-217.664-255.68-364.032-266.88l-16.384-0.96L512 227.2c-45.12 0-87.872 6.848-128.384 20.672L309.632 173.888A483.2 483.2 0 0 1 512 131.2z",
  "p-id": "13069",
  fill: "rgba(41, 52, 78, 0.4)"
}, null, -1);
const _hoisted_20 = [
  _hoisted_19
];
const _hoisted_21 = /* @__PURE__ */ createElementVNode("svg", {
  t: "1745217311571",
  class: "icon",
  viewBox: "0 0 1024 1024",
  version: "1.1",
  xmlns: "http://www.w3.org/2000/svg",
  "p-id": "18991",
  width: "48",
  height: "48"
}, [
  /* @__PURE__ */ createElementVNode("path", {
    d: "M432.394737 591.587288C331.530931 490.723482 234.497396 373.261835 281.099028 327.298582c63.837852-63.837852 123.845433-107.247591 6.383785-255.351408S92.138987 40.028248 27.662756 104.504479C-46.389152 178.556387 23.832485 454.335907 296.420113 727.561913s549.005526 342.809264 623.057434 268.757356c63.837852-63.837852 176.192471-142.35841 30.642169-259.820057s-187.044906-60.007581-255.351408 6.383785c-46.601632 45.963253-163.424901-50.431903-264.288706-151.295709z m0 0",
    fill: "#545C71",
    "p-id": "18992"
  })
], -1);
const _hoisted_22 = /* @__PURE__ */ createElementVNode("svg", {
  t: "1745217477962",
  class: "icon",
  viewBox: "0 0 1024 1024",
  version: "1.1",
  xmlns: "http://www.w3.org/2000/svg",
  "p-id": "20617",
  width: "48",
  height: "48"
}, [
  /* @__PURE__ */ createElementVNode("path", {
    d: "M1022.08 716.928V300.48l-217.28 217.344 217.28 199.04M655.36 667.2a188.16 188.16 0 0 1-133.888 55.488c-50.56 0-98.176-19.712-133.952-55.488L270.976 550.592 20.864 779.84v38.4c0 50.24 40.832 91.072 91.008 91.072h819.2c50.176 0 91.008-40.832 91.008-91.008v-39.68l-249.536-228.608-117.12 117.12",
    fill: "#545C71",
    "p-id": "20618"
  }),
  /* @__PURE__ */ createElementVNode("path", {
    d: "M932.928 128h-819.2C63.616 128 22.848 168.832 22.848 219.008v19.008l398.784 398.912c27.2 27.136 63.36 42.112 101.76 42.112 38.464 0 74.624-14.976 101.76-42.112L1024 238.016v-19.008C1024 168.832 983.04 128 932.928 128M20.864 300.48v417.6L238.72 518.4 20.864 300.48",
    fill: "#545C71",
    "p-id": "20619"
  })
], -1);
const _hoisted_23 = /* @__PURE__ */ createElementVNode("svg", {
  width: "16px",
  height: "16px",
  viewBox: "0 0 16 16",
  version: "1.1",
  xmlns: "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink"
}, [
  /* @__PURE__ */ createElementVNode("g", {
    id: "\u9875\u9762-1",
    stroke: "none",
    "stroke-width": "1",
    fill: "none",
    "fill-rule": "evenodd"
  }, [
    /* @__PURE__ */ createElementVNode("g", { id: "\u9A8C\u8BC1\u7801" }, [
      /* @__PURE__ */ createElementVNode("rect", {
        id: "\u77E9\u5F62",
        opacity: "0",
        x: "0",
        y: "0",
        width: "16",
        height: "16"
      }),
      /* @__PURE__ */ createElementVNode("path", {
        id: "\u5F62\u72B6",
        d: "M7.46916366,0.998385906 C6.78171341,1.60346587 6.04199219,2.10400391 5.25,2.5 C4.44042969,2.90478516 3.52162536,3.14571929 2.49358702,3.2228024 C2.07311192,3.25228729 1.7478524,3.60315613 1.75,4.0246582 L1.75,8 L1.75,8 C1.75,10.8278809 3.60113661,13.1374435 7.30340983,14.9286878 L7.30336224,14.9287862 C7.74340515,15.1416123 8.25659485,15.1416123 8.69663776,14.9287862 C12.3988634,13.1374435 14.25,10.8278809 14.25,8 L14.25,4.0246582 C14.2521476,3.60315613 13.9268881,3.25228729 13.506413,3.2228024 C12.4783746,3.14571929 11.5595703,2.90478516 10.75,2.5 C9.95605469,2.10302734 9.21463847,1.60099649 8.52575135,0.993907452 L8.52475913,0.995038829 C8.22215091,0.729651247 7.76949016,0.730410711 7.46777416,0.996812215 Z M11.94,6.68 L7.64,10.74 C7.44,10.92 7.16,10.96 6.92,10.82 L6.8,10.72 L4.42,8.24 C4.2,8 4.22,7.62 4.46,7.4 C4.7,7.18 5.08,7.2 5.3,7.44 L7.26,9.5 L11.14,5.82 C11.38,5.6 11.76,5.62 11.98,5.86 C12.2,6.1 12.18,6.48 11.94,6.7 L11.94,6.68 L11.94,6.68 Z",
        fill: "#545C71"
      })
    ])
  ])
], -1);
const _hoisted_24 = {
  key: 3,
  class: "flex"
};
const _hoisted_25 = { style: { "height": "40px", "width": "40px" } };
const _hoisted_26 = /* @__PURE__ */ createElementVNode("div", { class: "mll" }, "\u81EA\u52A8\u767B\u5F55\u4E2D", -1);
const _hoisted_27 = {
  key: 0,
  class: "w100 text-center"
};
const _hoisted_28 = {
  key: 1,
  class: "w100 text-center"
};
const _hoisted_29 = { class: "login-content-bottom" };
const _hoisted_30 = { class: "login-switch-language" };
const _hoisted_31 = {
  key: 0,
  class: "switch-language"
};
const _hoisted_32 = /* @__PURE__ */ createElementVNode("path", {
  d: "M0 512c0 282.752 229.248 512 512 512s512-229.248 512-512S794.752 0 512 0 0 229.248 0 512z",
  fill: "#FFFFFF",
  "p-id": "13546"
}, null, -1);
const _hoisted_33 = /* @__PURE__ */ createElementVNode("path", {
  d: "M512 213.333333a298.666667 298.666667 0 1 1 0 597.333334 298.666667 298.666667 0 0 1 0-597.333334z m27.264 44.117334l3.84 3.328c9.557333 8.96 14.336 17.322667 14.336 25.258666 0 27.136-27.136 18.090667-27.136 27.136 0 9.045333 0 45.226667-18.090667 54.272-12.458667 6.186667-29.44 6.186667-33.962666 13.952-4.522667 7.722667 4.522667 16.597333 15.488 19.882667 10.965333 3.328 27.52 11.349333 36.565333-6.741333 9.045333-18.090667 45.226667-9.045333 36.181333 9.045333-9.045333 18.090667-36.181333 45.226667-36.181333 63.317333 0 4.266667 1.024 9.514667 2.176 15.061334l1.792 8.490666c3.157333 15.573333 4.565333 30.72-13.013333 30.72-27.136 0-45.226667-45.226667-63.317334-45.226666-18.090667 0-36.181333 0-36.181333 27.136 0 14.378667-5.973333 59.818667-24.490667 66.56-18.517333 6.826667-26.069333-19.029333-48.725333-28.16-19.2-7.68-20.778667-41.386667-20.778667-68.522667 0-27.136-11.093333-29.866667-26.666666-24.448-12.288 4.266667-23.765333 18.901333-41.386667 20.437333a256 256 0 0 0 484.906667 150.101334c-23.936 21.76-51.797333 21.973333-51.797334 4.949333 0-27.989333-12.202667-37.333333-30.890666-74.666667-18.645333-37.333333-1.408-77.568 15.786666-93.568 21.632-20.053333 28.586667-15.658667 33.024-23.978666 4.48-8.277333-5.632-25.173333 8.405334-34.688a23.722667 23.722667 0 0 1 19.541333-4.181334 256.170667 256.170667 0 0 0-199.424-135.466666z",
  fill: "#545C71",
  "p-id": "13547"
}, null, -1);
const _hoisted_34 = /* @__PURE__ */ createElementVNode("path", {
  d: "M397.226667 585.301333c4.181333-23.210667 47.530667-30.250667 74.453333-25.173333 26.922667 5.12 31.914667 31.402667 56.746667 39.893333 24.874667 8.533333 24.874667 39.936 32.64 39.936 7.722667 0 33.706667-9.898667 41.642666 11.392 7.936 21.290667-19.882667 26.88-33.621333 47.829334-13.781333 21.034667-8.021333 58.069333-32.170667 62.293333-24.106667 4.181333-26.666667-20.650667-37.717333-35.84-11.093333-15.104-57.258667-20.096-67.584-59.52s7.082667-46.293333-3.242667-57.6c-10.325333-11.221333-35.242667 0-31.146666-23.210667z",
  fill: "#545C71",
  opacity: ".5",
  "p-id": "13548"
}, null, -1);
const _hoisted_35 = [
  _hoisted_32,
  _hoisted_33,
  _hoisted_34
];
const _hoisted_36 = ["onClick"];
const _hoisted_37 = { class: "content" };
const _hoisted_38 = ["onClick"];
const _hoisted_39 = {
  key: 0,
  class: "is-use"
};
const _hoisted_40 = { class: "title" };
const _hoisted_41 = {
  key: 1,
  class: "recently-used"
};
const __default__ = defineComponent({
  name: "DcLogin"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    productKey: {
      type: String,
      default: "UserCenter"
    },
    useLocale: {
      type: Boolean,
      default: true
    },
    baseUrl: {
      type: String,
      default: ""
    },
    locale: {
      type: String,
      default: "zh-CN"
    },
    title: {
      type: String,
      default: ""
    },
    logo: {
      type: String,
      default: ""
    },
    logoDesc: {
      type: String,
      default: ""
    },
    bottomDesc: {
      type: String,
      default: ""
    },
    showLogo: {
      type: Boolean,
      default: true
    },
    autoLogin: {
      type: Boolean,
      default: false
    },
    autoLoginLoading: {
      type: Boolean,
      default: false
    },
    productDesc: {
      type: String,
      default: ""
    },
    supportLanguages: {
      type: Array,
      default: () => ["zh-CN", "en-US", "ja-JP"]
    },
    multipointLogin: {
      type: Boolean,
      default: false
    }
  },
  emits: ["updateLanguage", "loginSuccess"],
  setup(__props, { emit: __emit }) {
    const bgImg = {
      CyberData: img,
      CyberEngine: img$1,
      CyberAI: img$2,
      CyberDataEnter: img$3,
      CyberEngineEnter: img$4,
      CyberAIEnter: img$5
    };
    ;
    const props = __props;
    const langs = [
      {
        label: "\u4E2D\u6587",
        value: "zh-CN"
      },
      {
        label: "English",
        value: "en-US"
      },
      {
        label: "\u65E5\u672C\u8A9E",
        value: "ja-JP"
      }
    ];
    const showLangs = computed(() => langs.filter((el) => props.supportLanguages.includes(el.value)));
    const formRef = ref();
    const loginForm = reactive({
      name: "",
      password: "",
      code: "",
      uuid: "",
      phone: "",
      email: ""
    });
    const loading = ref(false);
    const showPwd = ref(false);
    const dialogVisible = ref(false);
    const recentlyUsed = localStorage.getItem("recentlyUsed") || "";
    const roleList = ref(["1", "2", "3", "4"]);
    const requestUrl = computed(() => props.baseUrl || decrypt("eBzuUdcdYxzfOLTq5UqbLHy5QHwlhli67obxiHGdOYA="));
    const cdUrlObj = {
      dev: "eBzuUdcdYxzfOLTq5UqbLAiOzyyWdZ+iE5rmaWy0BrBObVlg/6nTL/LEijtwH9nb",
      test: "eBzuUdcdYxzfOLTq5UqbLC/Q854Xs7wfzMBpNp2pNMoVK+6WxBP0Fet9slOOfhy4",
      prod: "dZPIhU9h4zdgwsGaJYmnoW3UbBbXkSdhsLs0x+mmD+NweqN2wRaAcarRZvVUyqNm",
      demo: "dZPIhU9h4zdgwsGaJYmnobzvcsCnAhTEpm35xhCkHPtIl/7CKzpbfmk+uIqHv34V"
    };
    const ceUrlObj = {
      dev: "rqSPgr+4lfpsb05cvi5KSFPDL2tOe2VImhlCmVVpL3K8vyXqSemE/hP+p5yBLfx9",
      test: "eBzuUdcdYxzfOLTq5UqbLNYeC7g0VLatoxfLKJZ63/TyIP5XISvp/WyTrZcQ4c/z",
      prod: "Qg3klVfA4KId6+KSbylvvWpuhVXWabZsVQ+GSTathKf84tHWDfNKNI91IaP4Efmz",
      demo: "Qg3klVfA4KId6+KSbylvvQ54CSTgmaXTXXrcTfqfBnNHt3nSD4QrLThb1dUE0rja"
    };
    const ciUrlObj = {
      dev: "eBzuUdcdYxzfOLTq5UqbLNOzwL6vS+EKrG11bRujh82rvcL6xrmUInR0QMIZ5NGm",
      test: "eBzuUdcdYxzfOLTq5UqbLNOzwL6vS+EKrG11bRujh82rvcL6xrmUInR0QMIZ5NGm",
      prod: "dO8hWz7j+ph+K7FrYE7BsLXMGLcmD5er9RTOEcE6qnBDkyrkElwTabLOKKsojh8+",
      demo: "dO8hWz7j+ph+K7FrYE7BsCnLRfSX6l5VtbjbhydzYL6lgEIWkHdmIqAk/r3VIW41"
    };
    const rules = computed(() => {
      const rule = {
        code: [
          {
            required: true,
            message: config[props.locale].codeR,
            trigger: "blur"
          }
        ],
        phone: [
          {
            required: true,
            trigger: "blur",
            message: config[props.locale].phoneP
          },
          {
            pattern: /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
            message: config[props.locale].phoneR
          }
        ],
        email: [
          {
            required: true,
            trigger: "blur",
            message: config[props.locale].emailP
          },
          {
            pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/,
            message: config[props.locale].emailR
          }
        ]
      };
      if (isRegister.value) {
        return {
          ...rule,
          name: [
            {
              required: true,
              message: config[props.locale].usernameR,
              trigger: "blur"
            },
            {
              pattern: /^[0-9a-zA-Z-_]{6,16}$/,
              message: config[props.locale].usernameRule
            }
          ],
          password: [
            {
              required: true,
              message: config[props.locale].passwordR,
              trigger: "blur"
            },
            {
              pattern: /^[0-9a-zA-Z-_]{6,16}$/,
              message: config[props.locale].usernameRule
            }
          ]
        };
      } else {
        return {
          ...rule,
          name: [
            {
              required: true,
              message: config[props.locale].usernameR,
              trigger: "blur"
            }
          ],
          password: [
            {
              required: true,
              message: config[props.locale].passwordR,
              trigger: "blur"
            }
          ]
        };
      }
    });
    const carouselList = [
      {
        title: "CyberEngine",
        desc: config[props.locale].CyberEngine.desc
      },
      {
        title: "CyberData",
        desc: config[props.locale].CyberData.desc
      },
      {
        title: "CyberAI",
        desc: config[props.locale].CyberAI.desc
      }
    ];
    const enterList = ref([
      {
        img: "CyberEngineEnter",
        title: "CyberEngine",
        role: "2",
        url: ""
      },
      {
        img: "CyberDataEnter",
        title: "CyberData",
        role: "3",
        url: ""
      },
      {
        img: "CyberAIEnter",
        title: "CyberAI",
        role: "4",
        url: ""
      }
    ]);
    const useProductKey = sessionStorage.getItem("productKey");
    const emit = __emit;
    const imgBox = ref();
    const codeImg = ref();
    const codeLoading = ref(false);
    const useCode = ref(false);
    const isRegister = ref(false);
    const gcpToken = getQueryParam("gcpToken");
    const supportRegister = computed(() => !!gcpToken);
    watch(() => props.locale, () => {
      window.location.reload();
    });
    watch(() => props.autoLoginLoading, (newVal) => {
      console.log("autoLoginLoading", newVal);
      setTimeout(() => {
        loading.value = newVal;
      });
    });
    const updateLanguage = (l) => {
      if (l === props.locale)
        return;
      emit("updateLanguage", l);
    };
    const goRegister = () => {
      isRegister.value = !isRegister.value;
      nextTick(() => {
        setTimeout(() => {
          formRef.value.clearValidate();
        });
      });
    };
    const setHeader = (useToken = false) => {
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      if (useToken) {
        headers.append("JwtToken", sessionStorage.getItem("jwtToken") || "");
      }
      headers.append("lang", props.locale);
      if (props.productKey === "UserCenter") {
        headers.append("ProductKey", "CyberData,CyberEngine,CyberAI");
      } else {
        headers.append("ProductKey", props.productKey);
      }
      return headers || {
        "Content-Type": "application/json",
        ProductKey: props.productKey === "UserCenter" ? ["CyberData", "CyberAI", "CyberEngine"] : props.productKey,
        ...useToken ? { JwtToken: sessionStorage.getItem("jwtToken") } : {}
      };
    };
    const getLoginCode = () => {
      if (codeLoading.value)
        return;
      codeLoading.value = true;
      fetch(`${requestUrl.value}/captcha`, {
        method: "GET",
        headers: setHeader()
      }).then((res) => {
        return res.json();
      }).then((res) => {
        console.log(res);
        if (res.code === "200") {
          codeImg.value = res.data.base64Img;
          loginForm.uuid = res.data.captchaKey;
        } else {
          ElMessage.error(res.msg);
        }
      }).catch((err) => {
      }).finally(() => codeLoading.value = false);
    };
    const getCaptchaEnabled = () => {
      fetch(`${requestUrl.value}/auth/captchaEnabled`, {
        method: "GET",
        headers: setHeader()
      }).then((res) => {
        return res.json();
      }).then((res) => {
        console.log(res);
        if (res.code === "200") {
          useCode.value = res.data;
          if (useCode.value) {
            getLoginCode();
          }
        }
      }).catch((err) => {
      });
    };
    const getUserInfo = () => {
      fetch(`${requestUrl.value}/user/getUserInfo`, {
        method: "POST",
        headers: setHeader(true)
      }).then((res) => {
        return res.json();
      }).then((res) => {
        console.log(res);
        if (res.code === "200") {
          roleList.value = res.data.version;
          if (roleList.value.length === 1) {
            const url = `${enterList.value[Number(roleList.value[0]) - 2].url}?jwtToken=${sessionStorage.getItem("jwtToken")}`;
            sessionStorage.setItem("productKey", enterList.value[Number(roleList.value[0]) - 2].title);
            window.open(url, "_self");
          } else {
            dialogVisible.value = true;
          }
        } else {
          ElMessage.error(res.msg);
        }
      }).catch((err) => {
      });
    };
    const registerRequest = () => {
      loading.value = true;
      fetch(`${requestUrl.value}/marketplace/register`, {
        method: "POST",
        headers: setHeader(),
        body: JSON.stringify({
          loginName: loginForm.name,
          aliasName: loginForm.name,
          tenantName: loginForm.name,
          password: encrypt(loginForm.password),
          phone: loginForm.phone,
          email: loginForm.email,
          jwtToken: gcpToken
        })
      }).then((res) => {
        return res.json();
      }).then((res) => {
        console.log(res);
        if (res.code === "200") {
          ElMessage.success(config[props.locale].registerSu);
          setTimeout(() => {
            isRegister.value = false;
            formRef.value.resetFields();
          }, 100);
        } else {
          ElMessage.error(res.msg);
          if (useCode.value) {
            getLoginCode();
          }
        }
      }).catch((err) => {
      }).finally(() => loading.value = false);
    };
    const loginRequest = () => {
      loading.value = true;
      const url = useCode.value ? "/auth/loginWithCaptcha" : "/auth/login";
      fetch(`${requestUrl.value}${url}`, {
        method: "POST",
        headers: setHeader(),
        body: useCode.value ? JSON.stringify({
          loginName: loginForm.name,
          password: encrypt(loginForm.password),
          code: loginForm.code,
          uuid: loginForm.uuid,
          multipointLogin: props.multipointLogin
        }) : JSON.stringify({
          loginName: loginForm.name,
          password: encrypt(loginForm.password),
          multipointLogin: props.multipointLogin
        })
      }).then((res) => {
        return res.json();
      }).then((res) => {
        console.log(res);
        if (res.code === "200") {
          const jwtToken = res?.data?.jwtToken;
          sessionStorage.setItem("jwtToken", jwtToken);
          if (props.productKey !== "UserCenter") {
            emit("loginSuccess", {
              title: props.productKey,
              jwtToken,
              loginName: loginForm.name,
              password: loginForm.password
            });
          } else {
            getUserInfo();
          }
        } else {
          ElMessage.error(res.msg);
          if (useCode.value) {
            getLoginCode();
          }
        }
      }).catch((err) => {
      }).finally(() => loading.value = false);
    };
    const Login = () => {
      formRef.value.validate((f) => {
        if (!f)
          return;
        if (isRegister.value) {
          registerRequest();
        } else {
          loginRequest();
        }
      });
    };
    const goDetail = (el) => {
      if (!roleList.value.includes(el.role))
        return;
      sessionStorage.setItem("productKey", el.title);
      const url = `${el.url}?jwtToken=${sessionStorage.getItem("jwtToken")}`;
      window.open(url, "_self");
    };
    onMounted(() => {
      const h = document.documentElement.clientHeight;
      const bi = 1200 / h;
      imgBox.value.style.width = `${800 / bi}px`;
      const port = window.location.port;
      getCaptchaEnabled();
      nextTick(() => {
        imgBox.value.click();
      });
      switch (port) {
        case "30011":
          enterList.value[0].url = decrypt(ceUrlObj.dev);
          enterList.value[1].url = decrypt(cdUrlObj.dev);
          enterList.value[2].url = decrypt(ciUrlObj.dev);
          break;
        case "30012":
          enterList.value[0].url = decrypt(ceUrlObj.test);
          enterList.value[1].url = decrypt(cdUrlObj.test);
          enterList.value[2].url = decrypt(ciUrlObj.test);
          break;
        case "30013":
          enterList.value[0].url = decrypt(ceUrlObj.prod);
          enterList.value[1].url = decrypt(cdUrlObj.prod);
          enterList.value[2].url = decrypt(ciUrlObj.prod);
          break;
        default:
          enterList.value[0].url = decrypt(ceUrlObj.demo);
          enterList.value[1].url = decrypt(cdUrlObj.demo);
          enterList.value[2].url = decrypt(ciUrlObj.demo);
      }
      if (window.location.hostname === "localhost") {
        enterList.value[0].url = decrypt(ceUrlObj.dev);
        enterList.value[1].url = decrypt(cdUrlObj.dev);
        enterList.value[2].url = decrypt(ciUrlObj.dev);
      }
    });
    return (_ctx, _cache) => {
      const _directive_loading = resolveDirective("loading");
      return openBlock(), createElementBlock("div", _hoisted_1, [
        __props.productKey === "UserCenter" ? (openBlock(), createElementBlock("div", {
          key: 0,
          ref_key: "imgBox",
          ref: imgBox,
          class: "login-img"
        }, [
          createVNode(unref(ElCarousel), {
            trigger: "click",
            arrow: "never",
            interval: 5e3
          }, {
            default: withCtx(() => [
              (openBlock(), createElementBlock(Fragment, null, renderList(carouselList, (el) => {
                return createVNode(unref(ElCarouselItem), {
                  key: el.title
                }, {
                  default: withCtx(() => [
                    createElementVNode("div", _hoisted_2, [
                      createVNode(unref(ElImage), {
                        src: bgImg[el.title]
                      }, null, 8, ["src"]),
                      createElementVNode("div", _hoisted_3, [
                        createElementVNode("div", _hoisted_4, toDisplayString(el.title), 1),
                        createElementVNode("div", _hoisted_5, toDisplayString(el.desc), 1)
                      ])
                    ])
                  ]),
                  _: 2
                }, 1024);
              }), 64))
            ]),
            _: 1
          })
        ], 512)) : (openBlock(), createElementBlock("div", {
          key: 1,
          ref_key: "imgBox",
          ref: imgBox,
          class: "login-img"
        }, [
          createVNode(unref(ElImage), {
            src: bgImg[__props.productKey]
          }, null, 8, ["src"]),
          createElementVNode("div", _hoisted_6, [
            createElementVNode("div", _hoisted_7, toDisplayString(__props.title || __props.productKey), 1),
            createElementVNode("div", _hoisted_8, toDisplayString(__props.productDesc || unref(config)?.[__props.locale]?.[__props.productKey]?.desc), 1)
          ])
        ], 512)),
        createElementVNode("div", _hoisted_9, [
          createElementVNode("div", _hoisted_10, [
            withDirectives(createVNode(unref(ElImage), {
              style: { "width": "auto", "height": "48px" },
              src: __props.logo || unref(img$6)
            }, null, 8, ["src"]), [
              [vShow, __props.showLogo]
            ]),
            withDirectives(createElementVNode("div", { class: "login-content-logo-desc" }, toDisplayString(__props.logoDesc), 513), [
              [vShow, __props.showLogo]
            ])
          ]),
          createElementVNode("div", _hoisted_11, [
            isRegister.value ? (openBlock(), createElementBlock("div", _hoisted_12, [
              createElementVNode("div", null, toDisplayString(unref(config)[__props.locale].register), 1)
            ])) : (openBlock(), createElementBlock("div", _hoisted_13, [
              _hoisted_14,
              createElementVNode("div", null, toDisplayString(unref(config)[__props.locale].welcome), 1)
            ])),
            !__props.autoLogin ? (openBlock(), createBlock(unref(ElForm), {
              key: 2,
              ref_key: "formRef",
              ref: formRef,
              model: loginForm,
              rules: rules.value
            }, {
              default: withCtx(() => [
                createVNode(unref(ElFormItem), { prop: "name" }, {
                  default: withCtx(() => [
                    createVNode(unref(ElInput), {
                      modelValue: loginForm.name,
                      "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => loginForm.name = $event),
                      link: "",
                      autocomplete: "off",
                      disabled: loading.value,
                      placeholder: unref(config)[__props.locale].usernameP,
                      onKeyup: _cache[1] || (_cache[1] = withKeys(($event) => Login(), ["enter"]))
                    }, {
                      prepend: withCtx(() => [
                        _hoisted_15
                      ]),
                      _: 1
                    }, 8, ["modelValue", "disabled", "placeholder"])
                  ]),
                  _: 1
                }),
                createVNode(unref(ElFormItem), { prop: "password" }, {
                  default: withCtx(() => [
                    createVNode(unref(ElInput), {
                      modelValue: loginForm.password,
                      "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => loginForm.password = $event),
                      type: showPwd.value ? "text" : "password",
                      autocomplete: "off",
                      placeholder: unref(config)[__props.locale].passwordP,
                      disabled: loading.value,
                      onKeyup: _cache[5] || (_cache[5] = withKeys(($event) => Login(), ["enter"]))
                    }, {
                      prepend: withCtx(() => [
                        _hoisted_16
                      ]),
                      suffix: withCtx(() => [
                        showPwd.value ? (openBlock(), createElementBlock("svg", {
                          key: 0,
                          xmlns: "http://www.w3.org/2000/svg",
                          "xmlns:xlink": "http://www.w3.org/1999/xlink",
                          t: "1701420495142",
                          class: "icon",
                          viewBox: "0 0 1024 1024",
                          version: "1.1",
                          "p-id": "12713",
                          width: "16",
                          height: "16",
                          onClick: _cache[2] || (_cache[2] = () => showPwd.value = false)
                        }, [..._hoisted_18])) : (openBlock(), createElementBlock("svg", {
                          key: 1,
                          xmlns: "http://www.w3.org/2000/svg",
                          "xmlns:xlink": "http://www.w3.org/1999/xlink",
                          t: "1701420602146",
                          class: "icon",
                          viewBox: "0 0 1024 1024",
                          version: "1.1",
                          "p-id": "13068",
                          width: "16",
                          height: "16",
                          onClick: _cache[3] || (_cache[3] = () => showPwd.value = true)
                        }, [..._hoisted_20]))
                      ]),
                      _: 1
                    }, 8, ["modelValue", "type", "placeholder", "disabled"])
                  ]),
                  _: 1
                }),
                isRegister.value ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                  createVNode(unref(ElFormItem), { prop: "phone" }, {
                    default: withCtx(() => [
                      createVNode(unref(ElInput), {
                        modelValue: loginForm.phone,
                        "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => loginForm.phone = $event),
                        link: "",
                        autocomplete: "off",
                        disabled: loading.value,
                        placeholder: unref(config)[__props.locale].phoneP,
                        onKeyup: _cache[7] || (_cache[7] = withKeys(($event) => Login(), ["enter"]))
                      }, {
                        prepend: withCtx(() => [
                          _hoisted_21
                        ]),
                        _: 1
                      }, 8, ["modelValue", "disabled", "placeholder"])
                    ]),
                    _: 1
                  }),
                  createVNode(unref(ElFormItem), { prop: "email" }, {
                    default: withCtx(() => [
                      createVNode(unref(ElInput), {
                        modelValue: loginForm.email,
                        "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => loginForm.email = $event),
                        link: "",
                        autocomplete: "off",
                        disabled: loading.value,
                        placeholder: unref(config)[__props.locale].emailP,
                        onKeyup: _cache[9] || (_cache[9] = withKeys(($event) => Login(), ["enter"]))
                      }, {
                        prepend: withCtx(() => [
                          _hoisted_22
                        ]),
                        _: 1
                      }, 8, ["modelValue", "disabled", "placeholder"])
                    ]),
                    _: 1
                  })
                ], 64)) : createCommentVNode("v-if", true),
                !isRegister.value && useCode.value ? (openBlock(), createBlock(unref(ElFormItem), {
                  key: 1,
                  prop: "code",
                  class: "code-form-item"
                }, {
                  default: withCtx(() => [
                    createVNode(unref(ElInput), {
                      modelValue: loginForm.code,
                      "onUpdate:modelValue": _cache[10] || (_cache[10] = ($event) => loginForm.code = $event),
                      link: "",
                      autocomplete: "off",
                      disabled: loading.value,
                      placeholder: unref(config)[__props.locale].codeP,
                      onKeyup: _cache[11] || (_cache[11] = withKeys(($event) => Login(), ["enter"]))
                    }, {
                      prepend: withCtx(() => [
                        _hoisted_23
                      ]),
                      _: 1
                    }, 8, ["modelValue", "disabled", "placeholder"]),
                    createElementVNode("div", {
                      class: "code-img",
                      onClick: getLoginCode
                    }, [
                      codeLoading.value ? (openBlock(), createBlock(unref(ElIcon), {
                        key: 0,
                        class: "el-icon is-loading"
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(Loading))
                        ]),
                        _: 1
                      })) : (openBlock(), createBlock(unref(ElImage), {
                        key: 1,
                        src: codeImg.value
                      }, null, 8, ["src"]))
                    ])
                  ]),
                  _: 1
                })) : createCommentVNode("v-if", true),
                createVNode(unref(ElFormItem), null, {
                  default: withCtx(() => [
                    isRegister.value ? (openBlock(), createBlock(unref(ElButton), {
                      key: 0,
                      type: "primary",
                      loading: loading.value,
                      onClick: _cache[12] || (_cache[12] = ($event) => Login())
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(unref(config)[__props.locale].register), 1)
                      ]),
                      _: 1
                    }, 8, ["loading"])) : (openBlock(), createBlock(unref(ElButton), {
                      key: 1,
                      type: "primary",
                      loading: loading.value,
                      onClick: _cache[13] || (_cache[13] = ($event) => Login())
                    }, {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(unref(config)[__props.locale].login), 1)
                      ]),
                      _: 1
                    }, 8, ["loading"]))
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["model", "rules"])) : (openBlock(), createElementBlock("div", _hoisted_24, [
              withDirectives(createElementVNode("div", _hoisted_25, null, 512), [
                [_directive_loading, __props.autoLoginLoading]
              ]),
              _hoisted_26
            ])),
            supportRegister.value ? (openBlock(), createElementBlock("div", {
              key: 4,
              class: "support-register",
              onClick: goRegister
            }, [
              isRegister.value ? (openBlock(), createElementBlock("div", _hoisted_27, toDisplayString(unref(config)[__props.locale].loginText), 1)) : (openBlock(), createElementBlock("div", _hoisted_28, toDisplayString(unref(config)[__props.locale].registerText), 1))
            ])) : createCommentVNode("v-if", true)
          ]),
          createElementVNode("div", _hoisted_29, toDisplayString(__props.bottomDesc), 1)
        ]),
        createElementVNode("div", _hoisted_30, [
          __props.useLocale ? (openBlock(), createElementBlock("div", _hoisted_31, [
            createVNode(unref(ElTooltip), {
              "hide-after": 200,
              effect: "light",
              content: "Switch to English",
              placement: "bottom",
              "popper-class": "login-popper lang-tolltip"
            }, {
              content: withCtx(() => [
                (openBlock(true), createElementBlock(Fragment, null, renderList(showLangs.value, (item) => {
                  return openBlock(), createElementBlock("div", {
                    key: item.value,
                    class: normalizeClass(["lang-item", { active: __props.locale === item.value }]),
                    onClick: ($event) => updateLanguage(item.value)
                  }, toDisplayString(item.label), 11, _hoisted_36);
                }), 128))
              ]),
              default: withCtx(() => [
                (openBlock(), createElementBlock("svg", {
                  t: "1729158173316",
                  class: "icon",
                  viewBox: "0 0 1024 1024",
                  version: "1.1",
                  xmlns: "http://www.w3.org/2000/svg",
                  "p-id": "13545",
                  "xmlns:xlink": "http://www.w3.org/1999/xlink",
                  width: "24",
                  height: "24",
                  onClick: updateLanguage
                }, [..._hoisted_35]))
              ]),
              _: 1
            })
          ])) : createCommentVNode("v-if", true)
        ]),
        createVNode(unref(ElDialog), {
          modelValue: dialogVisible.value,
          "onUpdate:modelValue": _cache[14] || (_cache[14] = ($event) => dialogVisible.value = $event),
          title: unref(config)[__props.locale].dialogTitle,
          width: "600px",
          "close-on-click-modal": false
        }, {
          default: withCtx(() => [
            createElementVNode("div", _hoisted_37, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(enterList.value, (el) => {
                return openBlock(), createElementBlock("div", {
                  key: el.title,
                  class: normalizeClass(["item", { disabled: !roleList.value.includes(el.role) }]),
                  onClick: ($event) => goDetail(el)
                }, [
                  unref(useProductKey) === el.title ? (openBlock(), createElementBlock("div", _hoisted_39, "\u6700\u8FD1\u4F7F\u7528")) : createCommentVNode("v-if", true),
                  createVNode(unref(ElImage), {
                    src: bgImg[el.img]
                  }, null, 8, ["src"]),
                  createElementVNode("div", _hoisted_40, toDisplayString(el.title), 1),
                  unref(recentlyUsed) === el.title ? (openBlock(), createElementBlock("div", _hoisted_41, toDisplayString(unref(config)[__props.locale].recentlyUsed), 1)) : createCommentVNode("v-if", true)
                ], 10, _hoisted_38);
              }), 128))
            ])
          ]),
          _: 1
        }, 8, ["modelValue", "title"])
      ]);
    };
  }
});
var Login = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\login\\src\\index.vue"]]);

export { Login as default };
//# sourceMappingURL=index.mjs.map
