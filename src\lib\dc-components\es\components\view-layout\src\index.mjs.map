{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/view-layout/src/index.vue"], "sourcesContent": ["<template>\n  <div class=\"view-layout\">\n    <div class=\"view-header\">\n      <div v-if=\"slots.headerLeft\">\n        <slot name=\"headerLeft\" />\n      </div>\n\n      <span v-else class=\"view-header--name\">\n        <el-icon v-if=\"showBack\" @click=\"$router.back()\"\n          ><ArrowLeftBold\n        /></el-icon>\n        {{ headerName }}</span\n      >\n\n      <div v-if=\"slots.headerRight\">\n        <slot name=\"headerRight\" />\n      </div>\n    </div>\n    <div v-if=\"slots.search\" class=\"view-search\">\n      <slot name=\"search\" />\n    </div>\n    <div ref=\"tableRef\" class=\"view-table\">\n      <slot name=\"table\" :table-height=\"height\" />\n    </div>\n    <div v-if=\"slots.pagination\" class=\"view-pagination\">\n      <slot name=\"pagination\" />\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, defineProps, ref, useSlots } from \"vue\";\nimport { ElIcon } from \"element-plus\";\nimport { ArrowLeftBold } from \"@element-plus/icons-vue\";\nimport { useElementSize } from \"./hooks\";\n\ndefineOptions({\n  name: \"DcViewLayout\",\n});\ndefineProps({\n  showBack: {\n    type: Boolean,\n    default: false,\n  },\n  headerName: {\n    type: String,\n    default: \"\",\n  },\n});\n\nconst tableRef = ref();\nconst { height } = useElementSize(tableRef);\nconst slots = useSlots();\nconsole.log(slots, \"======slots\");\nconst contentPadding = computed(() => {\n  if (slots.pagination) return \"60px\";\n  return \"0px\";\n});\n</script>\n<style lang=\"scss\" scoped>\n.view-layout {\n  position: relative;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n.view-header {\n  height: 48px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  box-sizing: border-box;\n  box-shadow: inset 0px -1px 0px 0px #dfe1e5;\n  &--name {\n    font-family: PingFangSC, PingFang SC;\n    font-weight: 500;\n    font-size: 16px;\n    color: #29344e;\n    line-height: 24px;\n    text-align: left;\n  }\n}\n.view-search {\n  padding: 20px;\n}\n.view-table {\n  padding: 0 20px;\n  flex: 1;\n  margin-bottom: v-bind(contentPadding);\n  overflow: auto;\n}\n.view-pagination {\n  width: 100%;\n  position: absolute;\n  right: 0px;\n  bottom: 0;\n  padding: 0 20px;\n  box-sizing: border-box;\n  height: 60px;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  background-color: #fff;\n}\n</style>\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;sBAmCc;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;AAYA,IAAA,MAAM,aAAe,cAAA,CAAA,QAAA,CAAA,CAAA;AACrB,IAAM,MAAA,KAAa,GAAA,QAAA,EAAA,CAAA;AACnB,IAAA,iBAAuB,EAAA,aAAA,CAAA,CAAA;AACvB,IAAQ,MAAA,cAAwB,GAAA,QAAA,CAAA,MAAA;AAChC,MAAM,IAAA,KAAA,CAAA,UAAA;AACJ,QAAA,OAAU,MAAA,CAAA;AAAY,MAAO,OAAA,KAAA,CAAA;AAC7B,KAAO,CAAA,CAAA;AAAA,IACT,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}