<template>
  <el-dropdown ref="refDropdown" max-height="230px" :disabled="isdisabled" trigger="click" @command="setRackNumber">
    <el-input
      :model-value="modelValue"
      class="input-width"
      ref="refInput"
      :placeholder="$t('form.pleaseChoose')"
      @input="getOptions"
      @keydown.tab="close"
    />
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="optionsItem in options" :key="optionsItem.id" :command="optionsItem.rackNumber">
          <div class="dropdown__item input-width">
            {{ optionsItem.rackNumber }}
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const options = ref<ShelfApi.GetFramePageListItem[]>([])
const isdisabled = ref(true)
const refDropdown = ref()
const refInput = ref()
const emit = defineEmits(['update:modelValue'])
defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})
function getOptions(frameNumber: string, isOpen = true) {
  emit('update:modelValue', frameNumber)
  if (frameNumber) {
    isdisabled.value = false
    store.dispatch('shelf/getRackList', { frameNumber }).then((res) => {
      options.value = res.data
      if (options.value.length > 0 && isOpen) refDropdown.value.handleOpen()
    })
  } else {
    isdisabled.value = true
    options.value = []
    close()
  }
}

function close() {
  refDropdown.value.handleClose()
}

function setRackNumber(rackNumber: string) {
  getOptions(rackNumber, false)
  refInput.value.focus()
  emit('update:modelValue', rackNumber)
}
</script>
<style lang="scss" scoped>
.input-width {
  width: 280px;
}
.dropdown__item {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  height: 34px;
  padding: 0 32px 0 20px;
  font-size: var(--el-font-size-base);
  line-height: 34px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--el-text-color-regular);
  cursor: pointer;
}
.dropdown__item:hover {
  background-color: var(--el-fill-color-light);
}
</style>
