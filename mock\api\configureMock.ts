export default [
    {
        url: '/api/ops/cluster-service-config/getMergeConfigsByClusterId',
        method: 'POST',
        response: () => ({
            "code": 0,
            "msg": "成功",
            "data": [
                {
                    "clusterServiceId": 630,
                    "confKey": "default_hdfs_superuser",
                    "confValue": "root",
                    "confDefaultValue": "root",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "default_user",
                    "confValue": "root",
                    "confDefaultValue": "root",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "django_debug_mode",
                    "confValue": "false",
                    "confDefaultValue": "false",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "engine",
                    "confValue": "mysql",
                    "confDefaultValue": "mysql",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "fs_defaultfs",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "hadoop.log.dir",
                    "confValue": "/home/<USER>/hadoop-3.2.2/logs",
                    "confDefaultValue": "/home/<USER>/hadoop-3.2.2/logs",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "hadoop.log.file",
                    "confValue": "hadoop.log",
                    "confDefaultValue": "hadoop.log",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "history_server_api_url",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "hive_conf_dir",
                    "confValue": "/home/<USER>/hive-3.1.2/conf",
                    "confDefaultValue": "/home/<USER>/hive-3.1.2/conf",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "hive_server_host",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "hive_server_port",
                    "confValue": "10000",
                    "confDefaultValue": "10000",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "host",
                    "confValue": "localhost",
                    "confDefaultValue": "localhost",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "http_500_debug_mode",
                    "confValue": "false",
                    "confDefaultValue": "false",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "http_host",
                    "confValue": "0.0.0.0",
                    "confDefaultValue": "0.0.0.0",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "http_port",
                    "confValue": "8888",
                    "confDefaultValue": "8888",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "idle_session_timeout",
                    "confValue": "-1",
                    "confDefaultValue": "-1",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "interface",
                    "confValue": "flink",
                    "confDefaultValue": "flink",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.console",
                    "confValue": "org.apache.log4j.ConsoleAppender",
                    "confDefaultValue": "org.apache.log4j.ConsoleAppender",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.console.layout",
                    "confValue": "org.apache.log4j.PatternLayout",
                    "confDefaultValue": "org.apache.log4j.PatternLayout",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.console.layout.ConversionPattern",
                    "confValue": "%d{yy/MM/dd HH:mm:ss} %p %c{2}: %m%n",
                    "confDefaultValue": "%d{yy/MM/dd HH:mm:ss} %p %c{2}: %m%n",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.console.target",
                    "confValue": "System.err",
                    "confDefaultValue": "System.err",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.DRFA",
                    "confValue": "org.apache.log4j.DailyRollingFileAppender",
                    "confDefaultValue": "org.apache.log4j.DailyRollingFileAppender",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.DRFA.DatePattern",
                    "confValue": ".yyyy-MM-dd",
                    "confDefaultValue": ".yyyy-MM-dd",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.DRFA.File",
                    "confValue": "${657089}/${657090}",
                    "confDefaultValue": "${hadoop.log.dir}/${hadoop.log.file}",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.DRFA.layout",
                    "confValue": "org.apache.log4j.PatternLayout",
                    "confDefaultValue": "org.apache.log4j.PatternLayout",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.DRFA.layout.ConversionPattern",
                    "confValue": "%d{ISO8601} %p %c: %m%n",
                    "confDefaultValue": "%d{ISO8601} %p %c: %m%n",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.appender.EventCounter",
                    "confValue": "org.apache.hadoop.metrics.jvm.EventCounter",
                    "confDefaultValue": "org.apache.hadoop.metrics.jvm.EventCounter",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.rootLogger",
                    "confValue": "INFO,console, EventCounter, DRFA",
                    "confDefaultValue": "INFO,console, EventCounter, DRFA",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "log4j.threshhold",
                    "confValue": "ALL",
                    "confDefaultValue": "ALL",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "log4j.properties"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "logical_name",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "name",
                    "confValue": "Flink",
                    "confDefaultValue": "Flink",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "ops.use.flink.cluster_service_id",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "cyberops.cfg"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "ops.use.hdfs.cluster_service_id",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "cyberops.cfg"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "ops.use.hive.cluster_service_id",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "cyberops.cfg"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "ops.use.spark.cluster_service_id",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "cyberops.cfg"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "ops.use.yarn.cluster_service_id",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "cyberops.cfg"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "options",
                    "confValue": "'{\"url\": \"http://127.0.0.1:8081\"}'",
                    "confDefaultValue": "'{\"url\": \"http://127.0.0.1:8081\"}'",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "password",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "port",
                    "confValue": "25",
                    "confDefaultValue": "25",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "proxy_api_url",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "resourcemanager_api_url",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "secret_key",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "server_group",
                    "confValue": "root",
                    "confDefaultValue": "root",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "server_user",
                    "confValue": "root",
                    "confDefaultValue": "root",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "submit_to",
                    "confValue": "True",
                    "confDefaultValue": "True",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "time_zone",
                    "confValue": "Asia/Shanghai",
                    "confDefaultValue": "Asia/Shanghai",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "tls",
                    "confValue": "no",
                    "confDefaultValue": "no",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "user",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "webhdfs_url",
                    "confValue": "",
                    "confDefaultValue": "",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[aws]",
                    "confValue": "[aws]",
                    "confDefaultValue": "[aws]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[azure]",
                    "confValue": "[azure]",
                    "confDefaultValue": "[azure]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[beeswax]",
                    "confValue": "[beeswax]",
                    "confDefaultValue": "[beeswax]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[dashboard]",
                    "confValue": "[dashboard]",
                    "confDefaultValue": "[dashboard]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[desktop]",
                    "confValue": "[desktop]",
                    "confDefaultValue": "[desktop]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[filebrowser]",
                    "confValue": "[filebrowser]",
                    "confDefaultValue": "[filebrowser]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[hadoop]",
                    "confValue": "[hadoop]",
                    "confDefaultValue": "[hadoop]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[hbase]",
                    "confValue": "[hbase]",
                    "confDefaultValue": "[hbase]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[impala]",
                    "confValue": "[impala]",
                    "confDefaultValue": "[impala]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[indexer]",
                    "confValue": "[indexer]",
                    "confDefaultValue": "[indexer]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[jobbrowser]",
                    "confValue": "[jobbrowser]",
                    "confDefaultValue": "[jobbrowser]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[jobsub]",
                    "confValue": "[jobsub]",
                    "confDefaultValue": "[jobsub]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[kafka]",
                    "confValue": "[kafka]",
                    "confDefaultValue": "[kafka]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[liboauth]",
                    "confValue": "[liboauth]",
                    "confDefaultValue": "[liboauth]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[liboozie]",
                    "confValue": "[liboozie]",
                    "confDefaultValue": "[liboozie]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[librdbms]",
                    "confValue": "[librdbms]",
                    "confDefaultValue": "[librdbms]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[libsaml]",
                    "confValue": "[libsaml]",
                    "confDefaultValue": "[libsaml]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[libsentry]",
                    "confValue": "[libsentry]",
                    "confDefaultValue": "[libsentry]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[libsolr]",
                    "confValue": "[libsolr]",
                    "confDefaultValue": "[libsolr]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[libzookeeper]",
                    "confValue": "[libzookeeper]",
                    "confDefaultValue": "[libzookeeper]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[metadata]",
                    "confValue": "[metadata]",
                    "confDefaultValue": "[metadata]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[metastore]",
                    "confValue": "[metastore]",
                    "confDefaultValue": "[metastore]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[notebook]",
                    "confValue": "[notebook]",
                    "confDefaultValue": "[notebook]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[oozie]",
                    "confValue": "[oozie]",
                    "confDefaultValue": "[oozie]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[pig]",
                    "confValue": "[pig]",
                    "confDefaultValue": "[pig]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[proxy]",
                    "confValue": "[proxy]",
                    "confDefaultValue": "[proxy]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[search]",
                    "confValue": "[search]",
                    "confDefaultValue": "[search]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[security]",
                    "confValue": "[security]",
                    "confDefaultValue": "[security]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[spark]",
                    "confValue": "[spark]",
                    "confDefaultValue": "[spark]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[sqoop]",
                    "confValue": "[sqoop]",
                    "confDefaultValue": "[sqoop]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[useradmin]",
                    "confValue": "[useradmin]",
                    "confDefaultValue": "[useradmin]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[zookeeper]",
                    "confValue": "[zookeeper]",
                    "confDefaultValue": "[zookeeper]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[abfs_clusters]]",
                    "confValue": "[[abfs_clusters]]",
                    "confDefaultValue": "[[abfs_clusters]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[adls_clusters]]",
                    "confValue": "[[adls_clusters]]",
                    "confDefaultValue": "[[adls_clusters]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[auth]]",
                    "confValue": "[[auth]]",
                    "confDefaultValue": "[[auth]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[aws_accounts]]",
                    "confValue": "[[aws_accounts]]",
                    "confDefaultValue": "[[aws_accounts]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[azure_accounts]]",
                    "confValue": "[[azure_accounts]]",
                    "confDefaultValue": "[[azure_accounts]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[catalog]]",
                    "confValue": "[[catalog]]",
                    "confDefaultValue": "[[catalog]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[clusters]]",
                    "confValue": "[[clusters]]",
                    "confDefaultValue": "[[clusters]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[custom]]",
                    "confValue": "[[custom]]",
                    "confDefaultValue": "[[custom]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[databases]]",
                    "confValue": "[[databases]]",
                    "confDefaultValue": "[[databases]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[database]]",
                    "confValue": "[[database]]",
                    "confDefaultValue": "[[database]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[django_admins]]",
                    "confValue": "[[django_admins]]",
                    "confDefaultValue": "[[django_admins]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[engines]]",
                    "confValue": "[[engines]]",
                    "confDefaultValue": "[[engines]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[gc_accounts]]",
                    "confValue": "[[gc_accounts]]",
                    "confDefaultValue": "[[gc_accounts]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[hdfs_clusters]]",
                    "confValue": "[[hdfs_clusters]]",
                    "confDefaultValue": "[[hdfs_clusters]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[interpreters]]",
                    "confValue": "[[interpreters]]",
                    "confDefaultValue": "[[interpreters]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[kafka]]",
                    "confValue": "[[kafka]]",
                    "confDefaultValue": "[[kafka]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[kerberos]]",
                    "confValue": "[[kerberos]]",
                    "confDefaultValue": "[[kerberos]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[knox]]",
                    "confValue": "[[knox]]",
                    "confDefaultValue": "[[knox]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[ldap]]",
                    "confValue": "[[ldap]]",
                    "confDefaultValue": "[[ldap]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[manager]]",
                    "confValue": "[[manager]]",
                    "confDefaultValue": "[[manager]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[metrics]]",
                    "confValue": "[[metrics]]",
                    "confDefaultValue": "[[metrics]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[navigator]]",
                    "confValue": "[[navigator]]",
                    "confDefaultValue": "[[navigator]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[oauth]]",
                    "confValue": "[[oauth]]",
                    "confDefaultValue": "[[oauth]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[oidc]]",
                    "confValue": "[[oidc]]",
                    "confDefaultValue": "[[oidc]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[optimizer]]",
                    "confValue": "[[optimizer]]",
                    "confDefaultValue": "[[optimizer]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[password_policy]]",
                    "confValue": "[[password_policy]]",
                    "confDefaultValue": "[[password_policy]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[prometheus]]",
                    "confValue": "[[prometheus]]",
                    "confDefaultValue": "[[prometheus]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[query_store]]",
                    "confValue": "[[query_store]]",
                    "confDefaultValue": "[[query_store]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[raz]]",
                    "confValue": "[[raz]]",
                    "confDefaultValue": "[[raz]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[session]]",
                    "confValue": "[[session]]",
                    "confDefaultValue": "[[session]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[slack]]",
                    "confValue": "[[slack]]",
                    "confDefaultValue": "[[slack]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[smtp]]",
                    "confValue": "[[smtp]]",
                    "confDefaultValue": "[[smtp]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[ssl]]",
                    "confValue": "[[ssl]]",
                    "confDefaultValue": "[[ssl]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[task_server]]",
                    "confValue": "[[task_server]]",
                    "confDefaultValue": "[[task_server]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[tracing]]",
                    "confValue": "[[tracing]]",
                    "confDefaultValue": "[[tracing]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[vcs]]",
                    "confValue": "[[vcs]]",
                    "confDefaultValue": "[[vcs]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[yarn_clusters]]",
                    "confValue": "[[yarn_clusters]]",
                    "confDefaultValue": "[[yarn_clusters]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[default]]]",
                    "confValue": "[[[default]]]",
                    "confDefaultValue": "[[[default]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[flink]]]",
                    "confValue": "[[[flink]]]",
                    "confDefaultValue": "[[[flink]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[groups]]]",
                    "confValue": "[[[groups]]]",
                    "confDefaultValue": "[[[groups]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[ha]]]",
                    "confValue": "[[[ha]]]",
                    "confDefaultValue": "[[[ha]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[hive]]]",
                    "confValue": "[[[hive]]]",
                    "confDefaultValue": "[[[hive]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[ldap_servers]]]",
                    "confValue": "[[[ldap_servers]]]",
                    "confDefaultValue": "[[[ldap_servers]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                },
                {
                    "clusterServiceId": 630,
                    "confKey": "[[[users]]]",
                    "confValue": "[[[users]]]",
                    "confDefaultValue": "[[[users]]]",
                    "descCh": "Hue 是一个Web应用，用来简化用户和Hadoop集群的交互。Hue技术架构，如下图所示，从总体上来讲，Hue应用采用的是B/S架构，该web应用的后台采用python编程语言别写的。大体上可以分为三层，分别是前端view层、Web服务层和Backend服务层。Web服务层和Backend服务层之间使用RPC的方式调用",
                    "confName": "hue.ini"
                }
            ]
        })
    },
    {
        url: '/api/ops/cluster-service-config/selectList',
        method: 'POST',
        response: () => ({
            "code": 0,
            "msg": "成功",
            "data": [
                {
                    "id": 500704,
                    "moduleId": 9,
                    "clusterServiceId": 547,
                    "clusterServiceInstanceId": 1906,
                    "confName": "cyberops.cfg",
                    "confKey": "ops.zookeeper.cluster_service.id",
                    "descCh": "kafka依赖的zookeeper集群服务id",
                    "installNecessary": 1,
                    "recordUid": "1509094697572892672",
                    "extraProperty": "3",
                    "relyType": 1,
                    "clusterServiceList": [
                        {
                            "name": "ZooKeeper",
                            "value": 542
                        }
                    ]
                },
                {
                    "id": 500791,
                    "moduleId": 9,
                    "clusterServiceId": 547,
                    "clusterServiceInstanceId": 1907,
                    "confName": "cyberops.cfg",
                    "confKey": "ops.zookeeper.cluster_service.id",
                    "descCh": "kafka依赖的zookeeper集群服务id",
                    "installNecessary": 1,
                    "recordUid": "1509094697572892672",
                    "extraProperty": "3",
                    "relyType": 1,
                    "clusterServiceList": [
                        {
                            "name": "ZooKeeper",
                            "value": 542
                        }
                    ]
                },
                {
                    "id": 500878,
                    "moduleId": 9,
                    "clusterServiceId": 547,
                    "clusterServiceInstanceId": 1908,
                    "confName": "cyberops.cfg",
                    "confKey": "ops.zookeeper.cluster_service.id",
                    "descCh": "kafka依赖的zookeeper集群服务id",
                    "installNecessary": 1,
                    "recordUid": "1509094697572892672",
                    "extraProperty": "3",
                    "relyType": 1,
                    "clusterServiceList": [
                        {
                            "name": "ZooKeeper",
                            "value": 542
                        }
                    ]
                }
            ]
        })
    }
]