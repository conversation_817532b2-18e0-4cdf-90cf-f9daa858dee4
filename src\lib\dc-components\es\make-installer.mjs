import { INSTALLED_KEY } from './constants/index.mjs';
import directives from './directives/index.mjs';

const makeInstaller = (components = []) => {
  const install = (app) => {
    if (app[INSTALLED_KEY])
      return;
    app[INSTALLED_KEY] = true;
    components.forEach((c) => app.use(c));
    app.use(directives);
  };
  return {
    install
  };
};

export { makeInstaller };
//# sourceMappingURL=make-installer.mjs.map
