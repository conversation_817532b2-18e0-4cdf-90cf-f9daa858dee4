/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-08-08 13:47:32
 * @LastEditTime: 2025-08-09 16:21:57
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @Description:
 */
declare namespace QueneApi {
  export interface CreateQueneRequest {
    cpuAllocated?: number
    cpuMemoryAllocated?: number
    description?: string
    gpuAllocated?: number
    gpuMemoryAllocated?: number
    kubernetesId?: number
    memoryAllocated?: number
    name?: string
    weight: number
  }
  export interface queneListReq {
    id?: number
    pageNo: number
    pageSize: number
    projectId?: number
    queueName?: any
    searchAudit?: boolean
    tenantId?: number
  }
}
