{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/table/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-table\r\n    ref=\"$refs\"\r\n    class=\"dc-table\"\r\n    v-bind=\"$attrs\"\r\n    :style=\"{ height: props.height }\"\r\n  >\r\n    <el-table-column v-if=\"isColumnFilter\" width=\"48px\" align=\"center\">\r\n      <template #header>\r\n        <ColumnsFilter\r\n          style=\"position: relative; top: 3px\"\r\n          :columns=\"columns\"\r\n          :filter-submit-text=\"filterSubmitText\"\r\n          :filter-cancel-text=\"filterCancelText\"\r\n          :filter-all-text=\"filterAllText\"\r\n          :filter-reset-text=\"filterResetText\"\r\n          @change=\"onColumnsFilterChange\"\r\n        />\r\n      </template>\r\n    </el-table-column>\r\n    <slot name=\"before\" />\r\n    <el-table-column\r\n      v-if=\"props.showSelectColumn\"\r\n      type=\"selection\"\r\n      align=\"center\"\r\n      :width=\"props.selectionWidth\"\r\n      :selectable=\"props.selectable\"\r\n    />\r\n    <template v-for=\"(col, index) in columnsData\">\r\n      <el-table-column v-if=\"col?.slot\" v-bind=\"col\" :key=\"index\">\r\n        <template #default=\"{ row, column, $index }\">\r\n          <!--  eslint-disable-next-line vue/valid-attribute-name -->\r\n          <slot :name=\"col.prop\" :row=\"row\" :column=\"column\" :$index=\"$index\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        v-if=\"col.type === 'expand' && !col?.slot\"\r\n        v-bind=\"col\"\r\n        :key=\"index\"\r\n      >\r\n        <template #default=\"{ row, column, $index }\">\r\n          <component :is=\"col.formatter(row, column, row[col.prop], $index)\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        v-if=\"!col?.slot && col.type !== 'expand'\"\r\n        v-bind=\"col\"\r\n        :key=\"index\"\r\n      />\r\n    </template>\r\n    <slot name=\"after\" />\r\n    <template #append>\r\n      <slot name=\"append\">\r\n        <template v-if=\"append && typeof append() === 'string'\">\r\n          {{ append() }}\r\n        </template>\r\n        <template v-else-if=\"append\">\r\n          <component :is=\"append\" />\r\n        </template>\r\n      </slot>\r\n    </template>\r\n    <template #empty>\r\n      <DcEmpty :show-img=\"showEmptyImg\" :locale=\"locale\" :theme=\"theme\" />\r\n    </template>\r\n  </el-table>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n// eslint-disable-next-line import/order\r\nimport { ElLoading, ElTable, ElTableColumn } from \"element-plus\";\r\nimport DcEmpty from \"../../empty/src/index.vue\";\r\nimport ColumnsFilter from \"./components/ColumnsFilter.vue\";\r\n// eslint-disable-next-line import/order\r\nimport { computed, nextTick, ref, watch } from \"vue\";\r\n// eslint-disable-next-line import/order\r\nimport type { PropType, VNode } from \"vue\";\r\nimport type { DcTableColumnItem } from \"./types\";\r\nimport type { DcLocaleType, DcThemeType } from \"../../types\";\r\n// import { cloneDeep } from \"lodash\";\r\ndefineOptions({\r\n  name: \"DcTable\",\r\n});\r\n\r\nconst props = defineProps({\r\n  columns: {\r\n    type: Array as PropType<DcTableColumnItem[] | any>,\r\n  },\r\n  isColumnFilter: {\r\n    type: Boolean,\r\n  },\r\n  append: {\r\n    type: Function as PropType<() => VNode | string>,\r\n  },\r\n  filterSubmitText: {\r\n    type: String,\r\n  },\r\n  filterCancelText: {\r\n    type: String,\r\n  },\r\n  filterAllText: {\r\n    type: String,\r\n  },\r\n  filterResetText: {\r\n    type: String,\r\n  },\r\n  height: {\r\n    type: String,\r\n    default: \"100%\",\r\n  },\r\n  loading: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  showSelectColumn: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  selectable: {\r\n    type: Function as PropType<(row: any, index: number) => boolean>,\r\n    default: () => true,\r\n  },\r\n  selectionWidth: {\r\n    type: Number,\r\n    default: 40,\r\n  },\r\n  showEmptyImg: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  locale: {\r\n    type: String as PropType<DcLocaleType>,\r\n    default: \"zh-CN\",\r\n  },\r\n  theme: {\r\n    type: String as PropType<DcThemeType>,\r\n    default: \"light\",\r\n  },\r\n});\r\n\r\n// 表格的ref\r\nconst $refs = ref<InstanceType<typeof ElTable>>();\r\n\r\nconst columnsFilter = ref<any>([]);\r\n\r\nlet loadingInstance: any;\r\n\r\nconst columnsData = computed(() => {\r\n  if (props?.isColumnFilter) {\r\n    return props?.columns?.filter(\r\n      (column: DcTableColumnItem) =>\r\n        (column?.columnDefault\r\n          ? columnsFilter.value.includes(column.label) && column?.columnDefault\r\n          : columnsFilter.value.includes(column.label)) ||\r\n        column?.columnRequired ||\r\n        !column.label\r\n    );\r\n  } else {\r\n    return props.columns;\r\n  }\r\n});\r\n\r\nconst doLoading = (loading: boolean) => {\r\n  if (loading) {\r\n    nextTick(() => {\r\n      if ($refs.value?.$el) {\r\n        loadingInstance = ElLoading.service({\r\n          target: $refs.value.$el,\r\n          background: \"transparent\",\r\n          body: false,\r\n          fullscreen: false,\r\n        });\r\n      }\r\n    });\r\n  } else {\r\n    loadingInstance?.close();\r\n  }\r\n};\r\n\r\nwatch(\r\n  () => props.loading,\r\n  (loading) => {\r\n    doLoading(loading);\r\n  },\r\n  { immediate: true }\r\n);\r\n\r\nfunction onColumnsFilterChange(checkList: any) {\r\n  columnsFilter.value = checkList;\r\n}\r\n// 把$ref暴露出去方便父组件使用\r\ndefineExpose({ $refs });\r\n</script>\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["DO_defineComponent", "ref", "computed", "nextTick", "ElLoading", "watch"], "mappings": ";;;;;;;;;;;;;AA+Ec,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAyDd,IAAA,MAAM,QAAQC,OAAkC,EAAA,CAAA;AAE1C,IAAA,MAAA,aAAA,GAAgBA,OAAS,CAAA,EAAE,CAAA,CAAA;AAE7B,IAAA,IAAA,eAAA,CAAA;AAEE,IAAA,MAAA,WAAA,GAAcC,aAAS,MAAM;AACjC,MAAA,IAAI,OAAO,cAAgB,EAAA;AAClB,QAAA,OAAA,KAAA,EAAO,OAAS,EAAA,MAAA,CACrB,CAAC,MAAA,cACU,aACL,GAAA,aAAA,CAAc,KAAM,CAAA,QAAA,CAAS,MAAO,CAAA,KAAK,KAAK,MAAQ,EAAA,aAAA,GACtD,aAAc,CAAA,KAAA,CAAM,QAAS,CAAA,MAAA,CAAO,KAAK,CAAA,KAC7C,MAAQ,EAAA,cAAA,IACR,CAAC,MAAA,CAAO,KACZ,CAAA,CAAA;AAAA,OACK,MAAA;AACL,QAAA,OAAO,KAAM,CAAA,OAAA,CAAA;AAAA,OACf;AAAA,KACD,CAAA,CAAA;AAEK,IAAA,MAAA,SAAA,GAAY,CAAC,OAAqB,KAAA;AACtC,MAAA,IAAI,OAAS,EAAA;AACX,QAAAC,YAAA,CAAS,MAAM;AACT,UAAA,IAAA,KAAA,CAAM,OAAO,GAAK,EAAA;AACpB,YAAA,eAAA,GAAkBC,sBAAU,OAAQ,CAAA;AAAA,cAClC,MAAA,EAAQ,MAAM,KAAM,CAAA,GAAA;AAAA,cACpB,UAAY,EAAA,aAAA;AAAA,cACZ,IAAM,EAAA,KAAA;AAAA,cACN,UAAY,EAAA,KAAA;AAAA,aACb,CAAA,CAAA;AAAA,WACH;AAAA,SACD,CAAA,CAAA;AAAA,OACI,MAAA;AACL,QAAA,eAAA,EAAiB,KAAM,EAAA,CAAA;AAAA,OACzB;AAAA,KACF,CAAA;AAEA,IAAAC,SAAA,CACE,MAAM,KAAA,CAAM,OACZ,EAAA,CAAC,OAAY,KAAA;AACX,MAAA,SAAA,CAAU,OAAO,CAAA,CAAA;AAAA,KAEnB,EAAA,EAAE,SAAW,EAAA,IAAA,EACf,CAAA,CAAA;AAEA,IAAA,SAAA,qBAAA,CAA+B,SAAgB,EAAA;AAC7C,MAAA,aAAA,CAAc,KAAQ,GAAA,SAAA,CAAA;AAAA,KACxB;AAEa,IAAA,QAAA,CAAA,EAAE,OAAO,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}