<template>
  <div>
    <div class="flex justify-between items-center px-20px pt-20px">
      <TheTitle class="mb-0px!" :content="$t('replenish.lastInspection')" />
      <BaseButton v-if="$has('inspection-manual')" type="primary" class="w-108px" @click="inspection">
        <img src="@/assets/icons/icon-shoudong.svg" class="mr-4px" alt="icon" />
        <span>{{ $t('replenish.manualInspection') }}</span>
      </BaseButton>
    </div>
    <div class="p-20px">
      <div class="item">
        <div class="label">{{ $t('replenish.inspectionProgress') }}</div>
        <el-progress :percentage="percentage" :striped-flow="true" color="#4777FF">
          <template #default>
            <span class="f-14">{{ (data?.inspectionCompleteNum ?? '-') + '/' + (data?.inspectionTotalNum ?? '-') }}</span>
          </template>
        </el-progress>
      </div>
      <div class="item">
        <div class="label">{{ $t('replenish.inspectionStatus') }}</div>
        <div class="value">
          <span :class="typeClass.get(data?.inspectionStatus)">{{ stateText.get(data?.inspectionStatus) }}</span>
        </div>
      </div>
      <div class="item">
        <div class="label">{{ $t('replenish.startTime') }}</div>
        <div class="value">{{ data?.startTime ?? '-' }}</div>
      </div>
      <div class="item">
        <div class="label">{{ $t('replenish.endTime') }}</div>
        <div class="value">{{ data?.endTime ?? '-' }}</div>
      </div>
      <div class="item">
        <div class="label">{{ $t('replenish.inspectionDuration') }}</div>
        <div class="value">{{ data?.inspectionTime >= 0 ? data.inspectionTime + ' mins' : '-' }}</div>
      </div>
      <div class="item">
        <div class="label">{{ $t('replenish.inspectionItems') }}</div>
        <div class="value">{{ data?.inspectionTotalNum ?? '-' }}</div>
      </div>
      <div>
        <!-- <BaseButton link @click="down">
          <img src="@/assets/icons/icon-daochu.svg" alt="icon" />
          <span class="ml-2px">导出</span>
        </BaseButton>
        <el-divider direction="vertical" /> -->
        <BaseButton link :disabled="![2, 3].includes(data?.inspectionStatus)" @click="viewReport">
          <img v-if="data?.inspectionStatus === 1" src="@/assets/icons/icon-chakanh.svg" class="mr-4px" alt="icon" />
          <img v-else src="@/assets/icons/icon-chakan.svg" class="mr-4px" alt="icon" />
          <span>{{ $t('replenish.viewReport') }}</span>
        </BaseButton>
      </div>
    </div>
    <ManualOperation v-model="manualOperation" @submit="submit" />
  </div>
</template>
<script lang="ts" setup>
import Loop from '@/utils/Loop'
import { bus } from '@/utils/mitt'
import ManualOperation from './MessageBlockFinallyInspectionManualOperation.vue'
const { router, store, $has } = useBasicTool()
const percentage = computed(() => {
  if (data.value?.inspectionTotalNum || data.value?.inspectionCompleteNum)
    return (data.value?.inspectionCompleteNum / data.value?.inspectionTotalNum) * 100
  return 0
})
const manualOperation = ref(false)
const lock = ref<boolean>(false)
const data = ref()
function inspection() {
  manualOperation.value = true
}
function viewReport() {
  router.push({ name: 'inspectionReport', query: { ...data.value } })
}
function down() {}

onMounted(() => {
  getData()
  refreshList()
})
onBeforeUnmount(() => {
  if (refreshLoop?.clearLoop) {
    refreshLoop.clearLoop()
  }
})
let refreshLoop: any
function refreshList() {
  if (refreshLoop?.clearLoop) {
    refreshLoop.clearLoop()
  }
  refreshLoop = new Loop(function () {
    getData()
  }, 10000)
}

function getData() {
  if (lock.value) return
  lock.value = true
  store
    .dispatch('inspection/inspectionLatestInfo')
    .then((res) => {
      data.value = res.data
    })
    .finally(() => {
      lock.value = false
    })
}

const typeClass = new Map()
  .set(2, 'grade-label grade-label--success')
  .set(3, 'grade-label grade-label--danger')
  .set(1, 'grade-label grade-label--ordinary')
const stateText = new Map().set(1, t('replenish.inProgress')).set(2, t('replenish.complete')).set(3, t('replenish.abnormal'))

function submit() {
  bus.emit('inspectionPage')
}
</script>
<style lang="scss" scoped>
.border-bottom {
  border-bottom: 1px solid var(--ops-border-color);
}
.grade-label {
  width: auto;
  padding: 0px 10px;
}
.item {
  margin-bottom: 20px;
  .label {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    color: #99a0b5;
  }
  .value {
    margin-top: 2px;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
