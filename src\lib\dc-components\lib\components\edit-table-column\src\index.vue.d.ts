import type { FormItemRule } from "element-plus";
import type { PropType, Ref } from "vue";
import type { DcEditTableColumnScope } from "./types";
import type { DcEditTableEditActions, DcEditTableFormModel, DcEditTableFormModelItem, DcEditTableFormProps } from "../../edit-table/src/types";
declare const _default: import("vue").DefineComponent<{
    prop: {
        type: StringConstructor;
    };
    label: {
        type: StringConstructor;
    };
    width: {
        type: StringConstructor;
    };
    rules: {
        type: PropType<FormItemRule[]>;
    };
}, {
    props: import("@vue/shared").LooseRequired<any>;
    defaultEditActions: DcEditTableEditActions;
    editActions: DcEditTableEditActions | undefined;
    formModel: Ref<DcEditTableFormModel | undefined> | undefined;
    formProps: Ref<DcEditTableFormProps | undefined> | undefined;
    getEditModel: (index: number) => DcEditTableFormModelItem;
    getEditRow: (index: number) => any;
    isEditing: (index: number) => boolean;
    calculateColumnDefaultValue: (scope: DcEditTableColumnScope) => any;
    readonly ElFormItem: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
        readonly label: StringConstructor;
        readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly prop: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly rules: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => FormItemRule | FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<FormItemRule>) | ((new (...args: any[]) => FormItemRule | FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<FormItemRule>))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly error: StringConstructor;
        readonly validateStatus: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly for: StringConstructor;
        readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
        readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly size: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly label: StringConstructor;
            readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly prop: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
            readonly rules: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => FormItemRule | FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<FormItemRule>) | ((new (...args: any[]) => FormItemRule | FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<FormItemRule>))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly error: StringConstructor;
            readonly validateStatus: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly for: StringConstructor;
            readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
            readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly size: {
                readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        slots: Readonly<{
            [name: string]: import("vue").Slot<any> | undefined;
        }>;
        formContext: import("element-plus").FormContext | undefined;
        parentFormItemContext: import("element-plus").FormItemContext | undefined;
        _size: import("vue").ComputedRef<"" | "default" | "small" | "large">;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string | undefined) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        labelId: string;
        inputIds: Ref<string[]>;
        validateState: Ref<"" | "success" | "error" | "validating">;
        validateStateDebounced: Readonly<Ref<"" | "success" | "error" | "validating">>;
        validateMessage: Ref<string>;
        formItemRef: Ref<HTMLDivElement | undefined>;
        initialValue: any;
        isResettingField: boolean;
        labelStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        contentStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
        formItemClasses: import("vue").ComputedRef<(string | {
            [x: string]: boolean | undefined;
        })[]>;
        _inlineMessage: import("vue").ComputedRef<boolean>;
        validateClasses: import("vue").ComputedRef<(string | {
            [x: string]: boolean;
        })[]>;
        propString: import("vue").ComputedRef<string>;
        hasLabel: import("vue").ComputedRef<boolean>;
        labelFor: import("vue").ComputedRef<string | undefined>;
        isGroup: import("vue").ComputedRef<boolean>;
        isNested: boolean;
        fieldValue: import("vue").ComputedRef<any>;
        normalizedRules: import("vue").ComputedRef<FormItemRule[]>;
        validateEnabled: import("vue").ComputedRef<boolean>;
        getFilteredRule: (trigger: string) => import("async-validator").RuleItem[];
        isRequired: import("vue").ComputedRef<boolean>;
        shouldShowError: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
        currentLabel: import("vue").ComputedRef<string>;
        setValidationState: (state: "" | "success" | "error" | "validating") => void;
        onValidationFailed: (error: import("element-plus").FormValidateFailure) => void;
        onValidationSucceeded: () => void;
        doValidate: (rules: import("async-validator").RuleItem[]) => Promise<true>;
        validate: (trigger: string, callback?: import("element-plus").FormValidateCallback | undefined) => import("element-plus").FormValidationResult;
        clearValidate: () => void;
        resetField: () => void;
        addInputId: (id: string) => void;
        removeInputId: (id: string) => void;
        context: import("element-plus").FormItemContext;
        FormLabelWrap: import("vue").DefineComponent<{
            isAutoWidth: BooleanConstructor;
            updateAll: BooleanConstructor;
        }, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            isAutoWidth: BooleanConstructor;
            updateAll: BooleanConstructor;
        }>>, {
            isAutoWidth: boolean;
            updateAll: boolean;
        }, {}>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly label: StringConstructor;
        readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly prop: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
        readonly rules: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => FormItemRule | FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<FormItemRule>) | ((new (...args: any[]) => FormItemRule | FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<FormItemRule>))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly error: StringConstructor;
        readonly validateStatus: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly for: StringConstructor;
        readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
        readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly size: {
            readonly type: PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {
        readonly required: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly labelWidth: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
        readonly showMessage: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly inlineMessage: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, BooleanConstructor], unknown, unknown>;
    }, {}>>;
    readonly ElTableColumn: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
        type: {
            type: StringConstructor;
            default: string;
        };
        label: StringConstructor;
        className: StringConstructor;
        labelClassName: StringConstructor;
        property: StringConstructor;
        prop: StringConstructor;
        width: {
            type: (NumberConstructor | StringConstructor)[];
            default: string;
        };
        minWidth: {
            type: (NumberConstructor | StringConstructor)[];
            default: string;
        };
        renderHeader: PropType<(data: {
            column: import("element-plus/es/components/table/src/table-column/defaults").TableColumnCtx<any>;
            $index: number;
        }) => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }>>;
        sortable: {
            type: (BooleanConstructor | StringConstructor)[];
            default: boolean;
        };
        sortMethod: PropType<(a: any, b: any) => number>;
        sortBy: PropType<string | string[] | ((row: any, index: number) => string)>;
        resizable: {
            type: BooleanConstructor;
            default: boolean;
        };
        columnKey: StringConstructor;
        align: StringConstructor;
        headerAlign: StringConstructor;
        showOverflowTooltip: {
            type: PropType<boolean | Partial<Pick<import("element-plus").ElTooltipProps, "showArrow" | "effect" | "enterable" | "popperClass" | "offset" | "placement" | "popperOptions" | "showAfter" | "hideAfter">> | undefined>;
            default: undefined;
        };
        fixed: (BooleanConstructor | StringConstructor)[];
        formatter: PropType<(row: any, column: import("element-plus/es/components/table/src/table-column/defaults").TableColumnCtx<any>, cellValue: any, index: number) => string | import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }>>;
        selectable: PropType<(row: any, index: number) => boolean>;
        reserveSelection: BooleanConstructor;
        filterMethod: PropType<import("element-plus/es/components/table/src/table-column/defaults").FilterMethods<any>>;
        filteredValue: PropType<string[]>;
        filters: PropType<import("element-plus/es/components/table/src/table-column/defaults").Filters>;
        filterPlacement: StringConstructor;
        filterMultiple: {
            type: BooleanConstructor;
            default: boolean;
        };
        index: PropType<number | ((index: number) => number)>;
        sortOrders: {
            type: PropType<("ascending" | "descending" | null)[]>;
            default: () => (string | null)[];
            validator: (val: ("ascending" | "descending" | null)[]) => boolean;
        };
    }, void, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        type: {
            type: StringConstructor;
            default: string;
        };
        label: StringConstructor;
        className: StringConstructor;
        labelClassName: StringConstructor;
        property: StringConstructor;
        prop: StringConstructor;
        width: {
            type: (NumberConstructor | StringConstructor)[];
            default: string;
        };
        minWidth: {
            type: (NumberConstructor | StringConstructor)[];
            default: string;
        };
        renderHeader: PropType<(data: {
            column: import("element-plus/es/components/table/src/table-column/defaults").TableColumnCtx<any>;
            $index: number;
        }) => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }>>;
        sortable: {
            type: (BooleanConstructor | StringConstructor)[];
            default: boolean;
        };
        sortMethod: PropType<(a: any, b: any) => number>;
        sortBy: PropType<string | string[] | ((row: any, index: number) => string)>;
        resizable: {
            type: BooleanConstructor;
            default: boolean;
        };
        columnKey: StringConstructor;
        align: StringConstructor;
        headerAlign: StringConstructor;
        showOverflowTooltip: {
            type: PropType<boolean | Partial<Pick<import("element-plus").ElTooltipProps, "showArrow" | "effect" | "enterable" | "popperClass" | "offset" | "placement" | "popperOptions" | "showAfter" | "hideAfter">> | undefined>;
            default: undefined;
        };
        fixed: (BooleanConstructor | StringConstructor)[];
        formatter: PropType<(row: any, column: import("element-plus/es/components/table/src/table-column/defaults").TableColumnCtx<any>, cellValue: any, index: number) => string | import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
            [key: string]: any;
        }>>;
        selectable: PropType<(row: any, index: number) => boolean>;
        reserveSelection: BooleanConstructor;
        filterMethod: PropType<import("element-plus/es/components/table/src/table-column/defaults").FilterMethods<any>>;
        filteredValue: PropType<string[]>;
        filters: PropType<import("element-plus/es/components/table/src/table-column/defaults").Filters>;
        filterPlacement: StringConstructor;
        filterMultiple: {
            type: BooleanConstructor;
            default: boolean;
        };
        index: PropType<number | ((index: number) => number)>;
        sortOrders: {
            type: PropType<("ascending" | "descending" | null)[]>;
            default: () => (string | null)[];
            validator: (val: ("ascending" | "descending" | null)[]) => boolean;
        };
    }>>, {
        type: string;
        width: string | number;
        minWidth: string | number;
        showOverflowTooltip: boolean | Partial<Pick<import("element-plus").ElTooltipProps, "showArrow" | "effect" | "enterable" | "popperClass" | "offset" | "placement" | "popperOptions" | "showAfter" | "hideAfter">> | undefined;
        sortOrders: ("ascending" | "descending" | null)[];
        sortable: string | boolean;
        resizable: boolean;
        reserveSelection: boolean;
        filterMultiple: boolean;
    }, {}>>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    prop: {
        type: StringConstructor;
    };
    label: {
        type: StringConstructor;
    };
    width: {
        type: StringConstructor;
    };
    rules: {
        type: PropType<FormItemRule[]>;
    };
}>>, {}, {}>;
export default _default;
