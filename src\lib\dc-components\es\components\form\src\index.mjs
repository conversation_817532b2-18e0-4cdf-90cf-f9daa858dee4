import { defineComponent, ref, computed, watch, openBlock, createBlock, unref, withCtx, createElement<PERSON><PERSON>, Fragment, renderList } from 'vue';
import { ElForm } from 'element-plus';
import 'element-plus/es/components/form/style/css';
import { cloneDeep, isObject } from 'lodash';
import DcFormItem from './components/FormItem/index.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcForm"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    config: {
      type: Object,
      default: () => ({
        children: []
      })
    }
  },
  emits: ["validate"],
  setup(__props, { expose: __expose, emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const formRef = ref();
    const model = ref({});
    const visibleControl = ref({});
    const inline = computed(() => typeof props.config.inline !== "undefined" ? props.config.inline : false);
    const handleValidate = (prop, isValid, message) => {
      emits("validate", prop, isValid, message);
    };
    const validate = (callback) => {
      return formRef.value.validate(callback);
    };
    const validateField = (props2, callback) => {
      return formRef.value.validateField(props2, callback);
    };
    const resetFields = (props2) => {
      formRef.value.resetFields(props2);
    };
    const scrollToField = (prop) => {
      formRef.value.scrollToField(prop);
    };
    const clearValidate = (props2) => {
      formRef.value.clearValidate(props2);
    };
    const initModel = (initVal) => {
      const modelVal = model.value;
      model.value = {
        ...modelVal,
        ...cloneDeep(initVal)
      };
    };
    const getValues = () => {
      return {
        ...model.value
      };
    };
    const setFieldsValue = (fn) => {
      model.value = fn(model.value);
    };
    const reset = (clearValidate2 = true) => {
      const modelVal = { ...model.value };
      props.config.children.map((item) => {
        modelVal[item.model] = isObject(item.defaultValue) ? cloneDeep(item.defaultValue) : item.defaultValue;
        return null;
      });
      model.value = modelVal;
      clearValidate2 && setTimeout(() => {
        formRef.value?.clearValidate();
      }, 0);
    };
    const itemChange = (effect, rowIndex) => {
      if (effect && typeof effect === "function") {
        effect(model.value, initModel, rowIndex);
      }
    };
    const asyncValidate = (errorInfo) => {
      return new Promise((resolve, reject) => {
        formRef.value?.validate((isValid) => {
          if (isValid) {
            resolve({
              ...getValues()
            });
          } else {
            reject(errorInfo);
          }
        });
      });
    };
    watch(() => props.config.children.length, () => {
      const modelVal = { ...model.value };
      props.config.children.map((item) => {
        if (typeof modelVal[item.model] === "undefined") {
          modelVal[item.model] = isObject(item.defaultValue) ? cloneDeep(item.defaultValue) : item.defaultValue;
        }
        if (typeof visibleControl.value[item.model] === "undefined") {
          visibleControl.value[item.model] = item.visible ? computed(() => {
            const res = item.visible ? item.visible(model.value) : false;
            return res;
          }) : true;
        }
        return null;
      });
      model.value = modelVal;
    }, { deep: true, immediate: true });
    __expose({
      validate,
      validateField,
      resetFields,
      scrollToField,
      clearValidate,
      getValues,
      initModel,
      reset,
      setFieldsValue,
      asyncValidate,
      model
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElForm), {
        ref_key: "formRef",
        ref: formRef,
        inline: inline.value,
        "label-width": props.config.labelWidth,
        "label-position": props.config.labelPosition,
        "show-message": props.config.showMessage,
        "inline-message": props.config.inlineMessage,
        disabled: props.config.disabled,
        model: model.value,
        class: "dc-form",
        onValidate: handleValidate
      }, {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(props.config.children, (item) => {
            return openBlock(), createBlock(DcFormItem, {
              key: item.model,
              modelValue: model.value[item.model],
              "onUpdate:modelValue": ($event) => model.value[item.model] = $event,
              config: item,
              visible: visibleControl.value[item.model],
              "is-inline": inline.value,
              margin: {
                ...props.config.itemMargin || {},
                ...item.margin || {}
              },
              onTriggerEffect: itemChange
            }, null, 8, ["modelValue", "onUpdate:modelValue", "config", "visible", "is-inline", "margin"]);
          }), 128))
        ]),
        _: 1
      }, 8, ["inline", "label-width", "label-position", "show-message", "inline-message", "disabled", "model"]);
    };
  }
});
var DcForm = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\form\\src\\index.vue"]]);

export { DcForm as default };
//# sourceMappingURL=index.mjs.map
