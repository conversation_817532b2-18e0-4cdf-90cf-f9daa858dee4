<template>
  <div :class="`host-role-list`">
    <BaseLayout :is-header="isPage">
      <template #header>
        <el-page-header :icon="ArrowLeft" @back="back">
          <template #content>
            <div class="header-info">
              <span class="mr-10px">
                <span class="c-hex-99A0B5">{{ $t('form.host') + '：' }}</span>
                {{ route.query?.hostName }}
              </span>
              <span>
                <span class="c-hex-99A0B5"> IP：</span>
                {{ route.query?.ip }}
              </span>
            </div>
          </template>
        </el-page-header>
        <BaseButton v-if="$has('host-role-batch-restart')" type="primary" :disabled="isBatchOperat" @click="batchRestart">
          {{ $t('button.batchRestart') }}
        </BaseButton>
      </template>
      <template #content>
        <BaseTable
          ref="baseTableRef"
          v-loading="tableLoading"
          :columns="tableData.columns"
          :data="tableData.data"
          row-key="instanceId"
          @select="handleSelect"
          @select-all="handleSelect"
        >
          <template #instanceName="scope">
            <BaseButton link @click="openInstance(scope)">
              {{ scope.row.instanceName }}
            </BaseButton>
          </template>
          <template #state="scope">
            <div class="state">
              <!-- is-loading 为element-plus 转动样式 -->
              <el-icon v-if="[1, 10, 11, 12, 13, 18].includes(scope.row.state)" class="is-loading" style="position: relative; top: 2px; left: -3px">
                <Loading />
              </el-icon>
              <span v-else :class="['status-spot', `${stateMapStyle.get(scope.row.state)}`]" />
              <span>{{ stateMap.get(scope.row.state) }}</span>
              <BaseElTooltip :content="$t('replenish.blacklistedNodeMessage')">
                <img class="lahei-icon" v-if="scope.row.roleName === 'DataNode' && scope.row.showRemove" src="@/assets/icons/icon-lahei.svg" />
              </BaseElTooltip>
            </div>
          </template>
          <template #after>
            <el-table-column
              v-if="$has('host-role-instanse-restart') || $has('host-role-view-progress')"
              :label="$t('table.operate')"
              width="240"
              fixed="right"
            >
              <template #default="scope">
                <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" :dropdown-data="tableToolbar(scope).dropdownData" />
              </template>
            </el-table-column>
          </template>
        </BaseTable>
      </template>
      <template #footer>
        <BasePagination v-model="pageInfo" @page-change="handlePageChange" />
      </template>
    </BaseLayout>
    <TheBatchCofirmTwo v-model="batchCofirmOpen" :params="params" @submit="submit">
      <template #button>
        <BaseButton class="view-all-button" type="primary" link @click="openViewAll">
          {{ $t('button.viewAll') }}
        </BaseButton>
      </template>
    </TheBatchCofirmTwo>
    <TheViewAllOption v-model="viewAllOpen" :title="theViewAllOptionTitle" :content="params.itemList" />
    <TheProgressDialog
      v-model="progressDialog"
      :title="progressDialogTitle"
      :data="progressData"
      :state="itemState"
      @restart-rerun="restartRerun"
      @close="closeProgresDialog"
    />
  </div>
</template>
<script lang="ts" setup>
import { useComponentStatus } from '@/hooks/useComponentStatus'
import { useProgress } from '@/hooks/useProgress'
import useTableSelect from '@/hooks/useTableSelect'
import Loop from '@/utils/Loop'
import elConfim from '@/utils/elConfim'
import { ArrowLeft, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
const { route, t, router, store, $has, setBreadList } = useBasicTool()
const baseTableRef = ref()
const { pageInfo } = usePage()
const tableLoading = ref(true)
const { progressDialog, progressDialogTitle, TitleMap, itemState, itemDataIndex, progressData, closeProgresDialog, openLoop, rerun } = useProgress()
const { stateMap, stateMapStyle } = useComponentStatus()
const { selectionList, isBatchOperat, handleSelect } = useTableSelect()
const batchCofirmOpen = ref(false)
const viewAllOpen = ref(false)
const theViewAllOptionTitle = ref('')
const ids = ref({ instanceId: '', clusterId: '' })
const params = reactive({
  message: t('replenish.restartSelectedRoleService'),
  desc: '',
  batchLable: t('replenish.selectedRole') + ':',
  itemList: ''
})
const { namespaceTitle, namespaceId } = route.query

const isPage = ['/namespace/hosts/hostRoleList', '/hosts/hostRoleList'].includes(route.path)
onMounted(() => {
  getHostRoleInfoList()
  refreshList()
  if (isPage) {
    setBreadList([
      {
        name: t('title.roleList')
      }
    ])
  } else {
    setBreadList([])
  }
})
onBeforeUnmount(() => {
  if (refreshLoop.clearLoop) refreshLoop.clearLoop()
})
// 轮询对象
let refreshLoop = reactive<any>({})
// 刷新列表
function refreshList() {
  if (refreshLoop.clearLoop) {
    refreshLoop.clearLoop()
  }
  refreshLoop = new Loop(function () {
    getHostRoleInfoList()
  }, 10000)
}
// 表格数据
const tableData = reactive<any>({
  columns: [
    { type: 'selection', 'reserve-selection': true, selectable: ifSelectable },
    { prop: 'roleName', label: computed(() => t('table.roleName')) },
    { prop: 'instanceName', label: computed(() => t('table.instance')), slot: true },
    { prop: 'state', label: computed(() => t('table.state')), slot: true, width: 140 },
    { prop: 'lastRestartTime', label: computed(() => t('replenish.lastRestartTime')), width: 190 }
  ],
  data: []
})
function handlePageChange() {
  getHostRoleInfoList()
}
function batchRestart() {
  params.itemList = selectionList.value.map((item) => item.roleName).join('；')
  batchCofirmOpen.value = true
}
function submit() {
  const instanceIds = selectionList.value.map((item) => item.instanceId)
  store.dispatch('cluster/batchRestart', { instanceIds }).then((res) => {
    getHostRoleInfoList()
    selectionList.value = []
    baseTableRef.value.$refs.clearSelection()
  })
}
function openViewAll() {
  theViewAllOptionTitle.value = t('replenish.selectAllSelectedRoles', {
    value: selectionList.value.length
  })
  viewAllOpen.value = true
}
const lock = ref(false)
function getHostRoleInfoList() {
  if (lock.value) return
  lock.value = true
  store
    .dispatch('hosts/hostRoleInfo', { hostId: route.query.hostId, ...pageInfo })
    .then((res) => {
      tableLoading.value = false
      tableData.data = res.data.records
      pageInfo.total = res.data.total
      itemState.value = tableData.data[itemDataIndex.value]?.state
    })
    .finally(() => {
      lock.value = false
    })
}
// 选项是否可选
function ifSelectable(row: any) {
  if ([1, 10, 11, 12, 13].includes(row.state)) return false
  return true
}
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  const canDelete = $has('host-role-instanse-delete')
  const btns = [
    {
      buttonName: t('table.reboot'),
      disabled: [1, 10, 11, 12, 13, 18, 20, 22, 26].includes(scope.row.state),
      click: () => {
        onRestartService(scope)
      },
      isButton: $has('host-role-instanse-restart')
    },
    {
      buttonName: t('replenish.blacklist'),
      disabled: [1, 10, 11, 12, 13, 18, 19, 20, 22, 26].includes(scope.row.state),
      click: () => {
        elConfim
          .confim({
            isCancelButton: true,
            message: t('replenish.confirmBlacklistDataNode')
          })
          .then((res) => {
            if (res === 'confirm') {
              tableLoading.value = true
              store
                .dispatch('hosts/decommission', { id: scope.row.instanceId })
                .then((res) => {
                  if (res.data) {
                    ElMessage.success(t('replenish.blacklistSuccess'))
                    getHostRoleInfoList()
                  } else {
                    ElMessage.error(res.msg)
                  }
                })
                .finally(() => {
                  tableLoading.value = false
                })
            }
          })
      },
      isButton: canDelete && !scope.row.showRemove && scope.row.roleName === 'DataNode'
    },
    {
      buttonName: t('replenish.unblacklist'),
      disabled: [1, 10, 11, 12, 13, 18, 19, 20, 22, 26].includes(scope.row.state),
      click: () => {
        elConfim
          .confim({
            isCancelButton: true,
            message: t('replenish.confirmUnblacklistDataNode')
          })
          .then((res) => {
            if (res === 'confirm') {
              tableLoading.value = true
              store
                .dispatch('hosts/decommissionCancel', { id: scope.row.instanceId })
                .then((res) => {
                  if (res.data) {
                    ElMessage.success(t('replenish.unblacklistSuccess'))
                    getHostRoleInfoList()
                  } else {
                    ElMessage.error(res.msg)
                  }
                })
                .finally(() => {
                  tableLoading.value = false
                })
            }
          })
      },
      isButton: canDelete && scope.row.showRemove && scope.row.roleName === 'DataNode'
    },
    {
      buttonName: t('table.delete'),
      disabled: [1, 10, 11, 12, 13, 18, 20, 22, 26].includes(scope.row.state),
      click: async () => {
        let confirm = false
        if (scope.row.roleName === 'DataNode') {
          confirm = await new Promise((resolve) => {
            store.dispatch('hosts/decommissionMigration', { id: scope.row.instanceId }).then((res) => {
              const { data } = res
              elConfim
                .confim({
                  isCancelButton: true,
                  message: `${
                    data === 0
                      ? t('replenish.confirmDeleteRetiredNode')
                      : t('replenish.nodeStillHasReplicaData', {
                          value: data
                        })
                  } （${scope.row.instanceName}）`
                })
                .then((res) => {
                  if (res === 'confirm') {
                    resolve(true)
                  }
                })
            })
          })
        } else {
          confirm = await new Promise((resolve) => {
            elConfim
              .confim({
                isCancelButton: true,
                message: `${t('replenish.deleteCurrentRole')}${scope.row.instanceName}）`
              })
              .then((res) => {
                if (res === 'confirm') {
                  resolve(true)
                }
              })
          })
        }

        if (confirm) {
          tableLoading.value = true
          store
            .dispatch('hosts/instanceRemove', { id: scope.row.instanceId })
            .then((res) => {
              if (res.data.result) {
                ElMessage.success(t('replenish.operationSuccess'))
                getHostRoleInfoList()
              } else {
                ElMessage.error(res.msg)
              }
            })
            .finally(() => {
              tableLoading.value = false
            })
        }
      },
      isButton: canDelete && scope.row.showRemove
    },
    {
      buttonName: t('table.viewingProgress'),
      disabled: ![1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 26].includes(scope.row.state),
      click: () => {
        itemState.value = scope.row.state
        itemDataIndex.value = scope.$index
        progressDialogTitle.value = t('replenish.instance') + scope.row.instanceName + TitleMap.get(scope.row.state)
        ids.value = { instanceId: scope.row.instanceId, clusterId: scope.row.clusterServiceId }
        openLoop({ ...ids.value })
      },
      isButton: $has('host-role-view-progress')
    }
  ]
  return {
    buttonData: btns
  }
}
function openInstance(scope: any) {
  router.push({
    name: 'ComponentsView',
    query: {
      ...scope.row,
      id: scope.row.clusterServiceId,
      namespaceTitle: scope.row.namespaceName,
      name: scope.row.componentName,
      componentname: scope.row.clusterName,
      isTabs: '0'
    }
  })
}
function back() {
  router.go(-1)
}
// 显示重启服务弹框
function onRestartService(scope: any) {
  elConfim
    .confim({
      isCancelButton: true,
      message: t('message.restartRole', { value: scope.row.instanceName })
    })
    .then(() => {
      store
        .dispatch('cluster/clusterServiceRestart', {
          id: scope.row.clusterServiceId,
          clusterServiceInstanceId: scope.row.instanceId
        })
        .then(() => {
          getHostRoleInfoList()
        })
    })
}
function restartRerun(value: string) {
  rerun(value, ids.value.instanceId, ids.value.clusterId).then(() => {
    getHostRoleInfoList()
  })
}
</script>
<style lang="scss" scoped>
.host-role-list {
  height: 100%;
  .header-info {
    font-size: 14px;
  }
  .state {
    display: inline-flex;
    align-items: center;
    width: 100%;
    line-height: 20px;
  }
  .lahei-icon {
    width: 12px;
    height: 12px;
    margin-left: 4px;
    cursor: pointer;
  }
}
</style>
