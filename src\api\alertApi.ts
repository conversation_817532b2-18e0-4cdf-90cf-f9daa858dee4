import service from '@/utils/Http'

/**
 * @description 获取告警类别
 * @param {AlertApi.GetAlertTypeList} data
 * @return {*}
 */

export const getAlertTypeList = (data: AlertApi.GetAlertTypeList) => {
  return service.get<ApiResponse<Array<string>>>(`/alert/alertType?namespaceId=${data.namespaceId}`)
}

/**
 * @description 告警信息列表查询
 * @param {AlertApi.GetAlertPage} data
 * @return {*}
 */
export const getAlertPage = (data: AlertApi.GetAlertPage) => {
  return service.post<ApiResponse<PageList<AlertApi.GetAlertPageItem>>>('/alert/selectPage', data)
}

/**
 * @description 删除告警信息
 * @param {AlertApi.DeleteAlert} data
 * @return {*}
 */
export const deleteAlert = (data: AlertApi.DeleteAlert) => {
  return service.delete<ApiResponse<boolean>>('/alert/deleteById', { data })
}

/**
 * @description 批量删除告警信息
 * @param {AlertApi.DeleteAlert} data
 * @return {*}
 */
export const deleteBatchAlert = (data: AlertApi.DeleteBatchAlert) => {
  return service.delete<ApiResponse<boolean>>('/alert/deleteBatchId', { data })
}

/**
 * @description 告警规则查询
 * @param {AlertApi.GetAlertRule} data
 * @return {*}
 */
export const getAlertRule = (data: AlertApi.GetAlertRule) => {
  return service.post<ApiResponse<AlertApi.GetAlertRuleData>>('/alert/rule/select', data)
}

/**
 * @description 告警规则更新或新增
 * @param {AlertApi.UpdateAlertRule} data
 * @return {*}
 */
export const updateAlertRule = (data: AlertApi.UpdateAlertRule) => {
  return service.post<ApiResponse<boolean>>('/alert/rule/update', data)
}

/**
 * @description 告警通知配置查询
 * @param {AlertApi.GetAlertNotice} data
 * @return {*}
 */
export const getAlertNotice = (data: AlertApi.GetAlertNotice) => {
  return service.post<ApiResponse<AlertApi.GetAlertNoticeData>>('/alert/notice/select', data)
}

/**
 * @description 告警通知配置更新
 * @param {AlertApi.UpdateAlertNotice} data
 * @return {*}
 */
export const updateAlertNotice = (data: AlertApi.UpdateAlertNotice) => {
  return service.post<ApiResponse<boolean>>('/alert/notice/update', data)
}

/**
 * @description 告警通知邮箱测试
 * @param {AlertApi.TestEmailAlert} data
 * @return {*}
 */
export const testEmailAlert = (data: AlertApi.TestEmailAlert) => {
  return service.post<ApiResponse<boolean>>('/alert/email/test', data)
}

/**
 * @description 获取管理员用户列表
 * @return {*}
 */
export const getSystemUserList = () => {
  return service.post<ApiResponse<boolean>>('/tenant/getSystemUserList')
}

/**
 * @description 创建日志告警和组件状态告警
 * @param {AlertApi.ChangeAlertRule} data
 * @return {*}
 */
export const changeAlertRule = (data: AlertApi.ChangeAlertRule) => {
  return service.post<ApiResponse<boolean>>('/alert/otherRule/change', data)
}

/**
 * @description 创建日志告警和组件状态告警信息
 * @param {AlertApi.OtherPage} data
 * @return {*}
 */
export const otherPage = (data: AlertApi.OtherPage) => {
  return service.post<ApiResponse<any>>('/alert/other/page', data)
}

/**
 * @description 查询日志告警关键字列表
 * @param {AlertApi.KeywordPage} data
 * @return {*}
 */
export const keywordPage = (data: AlertApi.KeywordPage) => {
  return service.post<ApiResponse<any>>('/alert/LogKeywordPage', data)
}

/**
 * @description 删除日志告警关键字
 * @param {AlertApi.DeleteKeyword} data
 * @return {*}
 */
export const deleteKeyword = (data: AlertApi.DeleteKeyword) => {
  return service.post<ApiResponse<any>>('/alert/deleteLogKeyword', data)
}

/**
 * @description 编辑日志告警关键字
 * @param {AlertApi.EditKeyword} data
 * @return {*}
 */
export const editKeyword = (data: AlertApi.EditKeyword) => {
  return service.post<ApiResponse<any>>('/alert/editLogKeyword', data)
}

/**
 * @description 新增日志告警关键字
 * @param {AlertApi.AddLogKeyword} data
 * @return {*}
 */
export const addLogKeyword = (data: AlertApi.AddLogKeyword) => {
  return service.post<ApiResponse<any>>('/alert/addLogKeyword', data)
}

/**
 * @description 日志告警数据查询查询
 * @param {AlertApi.KeywordAlert} data
 * @return {*}
 */
export const keywordAlert = (data: AlertApi.KeywordAlert) => {
  return service.post<ApiResponse<any>>('/alert/keywordAlert/page', data)
}

/**
 * @description 日志关键词查询
 * @param {AlertApi.KeywordList} data
 * @return {*}
 */
export const keywordList = (data: AlertApi.KeywordList) => {
  return service.post<ApiResponse<any>>('/alert/keyword/list', data)
}

/**
 * @description 审计日志告警
 * @param {AlertApi.ChangeAudit[]} data
 * @return {*}
 */
export const changeAudit = (data: AlertApi.ChangeAudit[] | any) => {
  return service.post<ApiResponse<any>>('/alert/operation/change', data)
}

/**
 * @description 日志关键词告警设置
 * @param {AlertApi.ChangeAudit[]} data
 * @return {*}
 */
export const changeKeywordAlert = (data: any) => {
  return service.post<ApiResponse<any>>('/alert/keywordAlert/change', data)
}

/**
 * @description 查询审计日志告警信息
 * @param {AlertApi.OperationAlertData} data
 * @return {*}
 */
export const operationAlert = (data: AlertApi.OperationAlertData) => {
  return service.post<ApiResponse<any>>('/alert/operation/page', data)
}

/**
 * @description 查询日志关键词告警信息
 * @param {AlertApi.KeywordAlertPage} data
 * @return {*}
 */
export const keywordAlertPage = (data: AlertApi.KeywordAlertPage) => {
  return service.post<ApiResponse<any>>('/alert/keywordAlert/page', data)
}

/**
 * @description 查询已经被使用的关键词
 * @param {AlertApi.KeywordUsed} data
 * @return {*}
 */
export const keywordUsed = (data: AlertApi.KeywordUsed) => {
  return service.post<ApiResponse<any>>('/alert/keywordUsed/list', data)
}

export const alertTest = (data: AlertApi.AlertTest) => {
  return service.post<ApiResponse<any>>('/alert/test', data)
}

export const alertTestDD = (data: AlertApi.AlertTest) => {
  return service.post<ApiResponse<any>>('/alert/dingTalk/test', data)
}
