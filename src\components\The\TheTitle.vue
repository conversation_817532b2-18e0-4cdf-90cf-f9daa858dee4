<template>
  <div class="title-box" :style="{ padding: props.padding }">
    <div class="title-box__vertical inline" />
    <div class="title-box__text inline">
      <slot name="titleText">{{ content }}</slot>
    </div>
    <div v-if="slot.tip" :class="{ 'title-box__tips': true, inline }">
      <slot name="tip"></slot>
    </div>
    <div v-if="slot.right" class="title-box__right">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useSlots } from 'vue'
const slot = useSlots()
const props = defineProps({
  inline: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: 's'
  },
  padding: {
    type: String,
    default: ''
  }
})
</script>
<style lang="scss" scoped>
.title-box {
  margin-bottom: 20px;
  .inline {
    display: inline-block;
  }
  .title-box__vertical {
    position: relative;
    top: 2px;
    width: 2px;
    height: 14px;
    border-radius: var(--radius-m);
    margin-right: 5px;
    background-color: var(--ops-primary-color);
  }
  .title-box__text {
    height: 24px;
    margin-right: 5px;
    font-size: 14px;
    line-height: 24px;
    font-weight: 600;
    color: var(--ops-text-color);
  }
  .title-box__tips {
    margin-left: 9px;
    font-size: 14px;
    color: var(--ops-disabled-color);
  }
  .title-box__right {
    float: right;
  }
}
</style>
