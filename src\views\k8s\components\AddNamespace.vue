<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.addNamespace')" width="500px" :before-close="resetForm">
    <el-form class="tp-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="auto">
      <el-form-item v-if="!isServiceAccount" :label="`namespace${$t('replenish.designation')}：`" prop="name">
        <el-input style="width: 200px" :placeholder="$t('replenish.pleaseEnterNamespaceName')" v-model="ruleForm.name" />
      </el-form-item>

      <el-form-item v-if="!isServiceAccount" label="serviceAccount：" label-width="140px" prop="serviceAccount">
        <el-input style="width: 200px" :placeholder="$t('replenish.pleaseEnterServiceAccount')" v-model="ruleForm.serviceAccount" />
      </el-form-item>
      <el-form-item v-if="isServiceAccount" label="namespace：" prop="namespace">
        <el-select v-model="ruleForm.namespace" class="w-280px">
          <el-option :label="$t('replenish.autoGenerate')" value="" />
          <el-option v-for="(item, index) in namespaceList" :key="index" :label="item" :value="item" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
const { route, t, router, store, $has } = useBasicTool()
const isLoading = ref(false)
const formRef = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  k8sId: {
    type: Number,
    default: 0
  },
  adminAuth: {
    type: Boolean,
    default: false
  }
})

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    } else {
      if (isServiceAccount.value) {
        store.dispatch('namespace/getK8sNamespaceList', { id: props.k8sId }).then((res) => {
          namespaceList.value = res.data
        })
      }
    }
  }
)
const ruleForm = reactive<any>({
  name: '',
  serviceAccount: '',
  namespace: ''
})
const rules = {
  name: [
    {
      trigger: 'change',
      message: t('replenish.pleaseEnterNamespaceName'),
      required: true
    }
  ],
  serviceAccount: [
    {
      required: true,
      message: t('replenish.pleaseEnterServiceAccount'),
      trigger: 'change'
    }
  ]
}
const tenantId = computed(() => store.state.user.tenantId)
const namespaceList = ref<string[]>([])
const isServiceAccount = computed(() => props.adminAuth)
function submitForm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      isLoading.value = true
      store
        .dispatch('namespace/namespaceK8sCreate', {
          k8sId: props.k8sId,
          namespaceName: isServiceAccount.value ? ruleForm.namespace : ruleForm.name,
          serviceAccount: ruleForm.serviceAccount,
          tenantId: tenantId.value
        })
        .then((res) => {
          emit('submit')
          resetForm()
        })
        .finally(() => {
          isLoading.value = false
        })
    } else {
      return false
    }
  })
}
// 重置
function resetForm() {
  formRef.value.resetFields()
  ruleForm.name = ''
  ruleForm.namespace = ''
  ruleForm.serviceaccount = ''
  emit('update:modelValue', false)
}
</script>
