{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/drawer/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport Drawer from \"./src/index.vue\";\r\n\r\nexport const DcDrawer = withInstall(Drawer);\r\n\r\nexport default DcDrawer;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}