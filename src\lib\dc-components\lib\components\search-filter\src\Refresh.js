'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/icon/style/css');
var iconsVue = require('@element-plus/icons-vue');

var Refresh = vue.defineComponent({
  render() {
    return vue.createVNode(elementPlus.ElIcon, null, {
      default: () => [vue.createVNode(iconsVue.Refresh, null, null)]
    });
  }
});

exports["default"] = Refresh;
//# sourceMappingURL=Refresh.js.map
