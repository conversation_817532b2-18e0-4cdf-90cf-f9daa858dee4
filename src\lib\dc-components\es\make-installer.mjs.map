{"version": 3, "file": "make-installer.mjs", "sources": ["../../../../packages/dc-components/make-installer.ts"], "sourcesContent": ["import { INSTALLED_KEY } from \"@dc-components/constants\";\r\nimport directives from \"@dc-components/directives\";\r\nimport type { Plugin } from \"vue\";\r\n\r\nexport const makeInstaller = (components: Plugin[] = []) => {\r\n  const install = (app: any) => {\r\n    if (app[INSTALLED_KEY]) return;\r\n    app[INSTALLED_KEY] = true;\r\n    components.forEach((c) => app.use(c));\r\n    app.use(directives);\r\n  };\r\n\r\n  return {\r\n    install,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEY,MAAC,aAAa,GAAG,CAAC,UAAU,GAAG,EAAE,KAAK;AAClD,EAAE,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK;AAC3B,IAAI,IAAI,GAAG,CAAC,aAAa,CAAC;AAC1B,MAAM,OAAO;AACb,IAAI,GAAG,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;AAC9B,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACxB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}