{"ClusterDeleteSuccessed": "Cluster deleted", "HTTPVersion": "505 HTTP Version Not Supported", "SSHPortNumber": "Enter a valid SSH port number", "accidentalClusterName": "To prevent accidental operations, enter the cluster name again.", "accidentalComponent": "To prevent accidental operations, enter the custom component name again.", "accidentalHostname": "To prevent accidental operations, enter the host name again.", "accidentalRack": "To prevent accidental operation, enter the rack number", "addHostFailed": "Failed to add the host. Check the parameters.", "addHostTip": "You can install Agent on the new host. You can keep the new host and add it to a new or existing cluster.", "addUserSuccess": "User added.", "added": "Added", "adding": "Adding", "advancedSettings": "You can set advanced configurations to customize cluster assignment.", "afterTheHostIsUnbound": "After the host is unbound, the host returns to the default resource pool and can be deleted", "bindSuccessful": "Bound successfully", "cannotSpace": "Cannot start or end with a space.", "changeFailed": "Failed to change the password due to a server error.", "changePhoneFailed": "Mobile change failed.", "changePhoneSuccessed": "Mobile changed", "changeSuccessful": "The password is changed successfully. Log on again.", "changes": "The system will not save your modifications.", "characters": "3 to 20 characters", "charactersLength": "The length can be 1 to 20 characters. Characters other than English characters are not supported.", "checkVersion": "Check version", "clusterDeleted": "The current cluster ({value}) cannot be deleted.", "clusterRenameSuccessful": "Cluster renamed", "clustersDeletedTip": "Clusters with bound hosts and installed components or clusters that are bound to Kubernetes cannot be deleted.", "componentDeleted": "The current component ({value}) cannot be deleted.", "componentDependencies": "Dependency exists ({value})", "componentDependency": "The selected component ({value}) depends on ({string}). Install {string} before installing {value}.", "componentIsRunning": "The current component cannot be deleted because it is running.", "componentNameLength": "The custom component name cannot exceed {value} characters in length.", "componentsCheck": "Check whether relevant components have been installed.", "configRollbackSuccessful": "Configuration rolled back", "configurationSaveFailed": "Failed to save and publish the configuration.", "configurationSavesuccess": "Configuration is saved and published.", "configurationServiceParameters": "Some required service parameters are not set.", "configured": "Check if each component is configured properly.", "confirmCommit": "Are you sure you want to submit?", "connectionError": "Connection Error ({value})!", "currentComponentVersion": "Current component version：v{value}", "defaultClusterHost": "After you unbind the host, the host resides in the default cluster and can be added to a cluster again.", "delete": "Delete this account?", "deleteRack": "Are you sure to delete the current rack? (Rack ID: {name})", "deleteSuccessful": "Deleted successfully", "deletedClusterName": "Please enter the cluster name to be deleted", "deletedHostName": "Please enter the host name to be deleted", "dependencyChecking": "Checking dependency", "eachRoleMeet": "Check if the host assigned to each role meets the requirements", "edituserSuccess": "User edited.", "emailAddressFailed": "Email change failed", "emailAddressIncorrect": "Invalid email address. Please enter again.", "emailAddressSaved": "Email address saved", "enterComponentName": "Please enter the name of the custom component to be deleted", "enterCustomComponent": "Enter the custom component name", "enterEmail": "Enter a valid email address", "enterHostsNumber": "Enter the host count", "enterNewPassword": "Enter a new password", "enterOriginalPassword": "Enter the original password", "enterPasswordAgain": "Confirm the password", "enterPhone": "Enter a valid mobile number", "enterRoleNodes": "Enter the number of nodes of the role", "enterTheRackNumber": "Enter the rack number", "error": "Error", "expandedAndReduced": "After submission, the system starts scaling. This may take a few minutes.", "fileSize": "File size cannot exceed 100kb", "freeze": "Freeze this account?", "frozenSuccessful": "Froze successfully", "gettingInformation": "Getting Information...", "hasDataLake": "Integrated data lake:", "hostBindFailed": "Host bind failed", "hostName": "Please enter the name of the host to unbind", "hostUnbundled": "The host is unbound.", "isDeleteCluster": "Are you sure you want to delete the current cluster ({value})?", "isDeleteComponent": "Are you sure you want to delete the current component service ({value})?", "isDeleteHost": "Are you sure you want to delete the current host ({value})?", "isInstallation": "Confirm install?", "isSaveConfigurationFile": "Save and publish the current configuration profile?", "k8sAddedSuccessful": "Kubernetes added successfully", "leavePage": "Are you sure you want to leave this page?", "loginExpired": "Logon expired", "mailboxlength": "The email address cannot exceed 30 characters in length.", "modifyPermissionsSuccess": "Permissions modified.", "namespaceLength": "The cluster name cannot exceed {number} characters in length.", "networkError": "502 Network Error", "networkTimeout": "504 Network Timeout", "noChartAvailable": "No diagrams available", "noPermission": "Permission Denied", "only": "Support only digits, letters, underscores (_), and dashes (-).", "onlyCanRole": "Enter a positive integer for the [{value}] node", "passwordLength": "The password can contain 6 to 20 characters.", "phoneNumberIncorrect": "Invalid mobile format", "queue": "The queue name already exists.", "rackIsDeleted": "After a rack is deleted, the deployment relationship between the rack and hosts is removed.", "requestError": "404 Request Failed", "requestTimedOut": "Request times out or server exceptions occur. Check the network connection or contact the administrator.", "requestTimeout": "408 Request Timeout", "requetError": "400 Bad Request", "requiredSelect": "Required", "requiredinput": "Required <PERSON>", "reset": "Reset the password?", "resetPasswordSuccessful": "Reset successfully", "restartComponent": "Restart the current component ({value}) service?", "restartRole": "Do you want to restart the current role ({value}) service?", "rexNamespaceName": "The name contains letters, digits, underscores (_), and hyphens (-), and cannot start or end with a hyphen or underscore.", "roleAssignmentTip": "You can customize the role assignment for new services. Note that improper assignment will affect the performance. For example, an excessive amount of roles are assigned to a host.", "rolledBack": "Are you sure you want to roll the configuration back to the version ({value})?", "saveFailed": "Save failed", "saveSuccess": "Saved successfully", "savingAndPublishing": "After the configuration profile is saved and published, the configuration takes effect when the component is restarted.", "selectAuthentication": "Select the authentication type", "selectClusterTip": "Use the host to create a cluster or expand the existing cluster. If the cluster uses Kerberos authentication, make sure that the Kerberos package is installed on the new host. Otherwise, services on the host cannot run.", "selectComponent": "Select a component", "selectFile": "Select a file", "selectK8s": "Please Select K8s", "selectK8sCluster": "Select a Kubernetes cluster", "selectNode": "Select node", "selectOneHost": "Select at least one host", "selectServiceType": "Select the service type that you want to add", "selectUpdateVersion": "Select the version to upgrade ", "selectVersion": "Select a version", "serverError": "500 Server Error", "serverException": "Server exceptions occur. Contact the administrator.", "serviceUnavailable": "503 Service Unavailable", "serviceUnrealized": "501 Not Implemented", "startComponent": "Enable the current component ({value}) service?", "startInstallationTip": "When installation starts, you can only view the configurations. Confirm the configurations and click OK.", "stopComponent": "Disable the current component ({value}) service?", "success": "Success", "testConnectionFailed": "Connectivity test failed. Check the configuration.", "testConnectivityFirst": "Test connectivity first", "tetstConnectionSuccessed": "Connected successfully. Continue operations.", "twoPassword": "Passwords do not match", "ubindHost": "Are you sure you want to unbind the host ({value}) from the cluster?", "unbindSelectedHost": "Are you sure to unbind the selected host?", "unbundled": "Unbound", "unfrozenSuccessful": "<PERSON><PERSON><PERSON><PERSON> successfully", "unknownReason": "Unknown reason", "whetherUnfreeze": "Unfreeze this account?"}