{"ReAdd": "Add Again", "add": "Add", "addCluster": "Add Cluster", "addComponents": "Add Component", "addHost": "Add Host", "addJop": "Add Job", "addK8s": "Add <PERSON>", "addRack": "<PERSON><PERSON>", "addTask": "Add task", "advancedConfiguration": "Advanced Configuration", "back": "Back", "batchDelete": "<PERSON><PERSON> Delete", "batchRestart": "Batch restart", "batchUnbind": "<PERSON><PERSON>", "bind": "Bind", "cancel": "Cancel", "clear": "clear", "commandTerminalTip": "Command terminal", "configurationRecord": "Configuration history", "confirmRollback": "Confirm", "createChildPool": "Create Child Pool", "deployNow": "Deploy Now", "deployment": "One-click Deploy", "down": "Down", "downloadLog": "Download Log", "edit": "Edit", "expand": "Show", "expansionAndContraction": "Scale", "finish": "Completed", "hideAllDescription": "Hide all descriptions", "inquire": "Search", "installNow": "Install Now", "instanceConfiguration": "Instance Configuration", "latestConfiguration": "Latest Configuration", "newComponents": "New Component", "newUsers": "New User", "nextStep": "Next", "operationHistory": "Operation History", "previous": "Previous Step", "putAway": "<PERSON>de", "reexecute": "reexecute", "reset": "Reset", "restartList": "Restart list", "restoreDefaultsTip": "Restore to default parameter settings", "returnClusterList": "Back to Cluster List", "returnHostList": "Back to Host List", "returnTheListOfComponents": "Back to Components", "revokeTip": "Undo", "save": "Save", "saveAndPublish": "Save & Publish", "selectAll": "Select All", "selectHost": "Select Host", "showAllDescriptions": "Show all descriptions", "signIn": "login Account", "submit": "Submit", "sure": "OK", "tabCombinationInstance": "Combination Instance", "tabCombinedConfiguration": "Component Configuration", "tabDockerDeploy": "Docker Deployment", "tabKubernetesDeploy": "Kubernetes Deployment", "taskManagement": "Task management", "testConnectivity": "Test Connectivity", "unbind": "Unbind", "undertest": "Under Test", "uploadFiles": "Upload File", "viewAll": "View All", "yarnResource": "Yarn Resource pool configuration"}