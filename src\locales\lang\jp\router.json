{"EngineManager": "EngineManager", "accountInfo": "アカウント情報", "addCluster": "クラスタの追加", "addComponents": "コンポーネントを追加します。", "addHost": "ホストの追加", "audit": "監査", "clusterHostList": "クラスタホストのリスト", "clusterList": "クラスタ・リスト", "clusterMonitoring": "クラスタ監視", "clusterOverview": "クラスタの概要", "componentConfig": "コンポーネントの構成", "componentInstance": "コンポーネントインスタンス", "componentMonitoring": "コンポーネントの監視", "componentsConfigurationRecord": "コンポーネント構成レコード", "configurationRecord": "構成レコード", "diagnosticReport": "診断レポート", "expansionAndContraction": "かくさんようりょう", "giveAlarm": "アラームサービス", "hostDetails": "ホストの詳細", "hostList": "ホストのリスト", "hostMonitoring": "ホスト監視", "hostRoleList": "ホスト役割リスト", "installComponents": "コンポーネントをインストール", "instanceConfiguration": "インスタンス構成", "log": "ログ", "login": "ログイン", "monitor": "監視", "oneClickDeployment": "ワンタッチ配置", "operationHistory": "操作履歴", "productDocumentation": "製品ドキュメント", "restartList": "リストを再起動", "serviceComponent": "サービスコンポーネント", "shelf": "ラック管理", "taskManagement": "タスク管理", "userManagement": "ユーザー管理", "viewComponents": "コンポーネントの表示", "webTerminal": "web端末", "yarnResource": "Yarnリソースプールの構成", "kubernetesManagement": "Kubernetes管理", "hostManagement": "ホスト管理"}