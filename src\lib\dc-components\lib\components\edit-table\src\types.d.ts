import type DcEditTable from "./index.vue";
export declare type DcEditTableRequestResult<T> = T[];
export declare type DcEditTableRequestFunc<T> = () => Promise<DcEditTableRequestResult<T>> | DcEditTableRequestResult<T>;
export interface DcEditTableEditActions {
    addRow?: (row?: Record<any, any>) => void;
    deleteRow?: (index: number) => void;
    startEditable?: (index: number) => void;
    cancelEditable?: (index: number) => void;
    saveEditable?: (index: number) => void;
}
export interface DcEditTableFormModelItem {
    isEditing: boolean;
    isNew: boolean;
    data: Record<string | number | symbol, any>;
    formData: Record<string | number | symbol, any>;
}
export interface DcEditTableFormModel {
    model: DcEditTableFormModelItem[];
}
export declare type DcEditTableFormProps = Set<string>;
export declare type DcEditTableInstance = InstanceType<typeof DcEditTable>;
