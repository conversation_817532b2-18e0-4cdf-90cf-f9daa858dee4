import {
  addTenant,
  addUser,
  deleteTenant,
  deteleUserById,
  getAllTenantInfo,
  getLicenseDetail,
  getRoleList,
  getTenantList,
  getTenatPermission,
  getUserDetails,
  getUserInfo,
  getUserList,
  resetPassword,
  resetTenantPassword,
  tenantAll,
  updateCurrentUserInfo,
  updatePassword,
  updateTenant,
  updateTenantState,
  updateTenatPermission,
  updateUser,
  updateUserRole,
  updateUserStatus,
  getTerminalVisible,
  setTerminalVisible,
  updateLogoInfo,
  getLogoInfo
} from '@/api/userApi'
import { encrypt } from '@/utils/Auth'
import { Module } from 'vuex'

interface StoreUser {
  userName: string
  userList: Array<unknown>
  userInfo: unknown
  points: Array<unknown>
  roleList: Array<unknown>
  userDetails: Object
  tenantId: stringOrNumber
  businessAuthority: Record<string, stringOrNumber[]>
  terminalVisible: boolean
  logoInfo: {
    browserTab: Blob | null
    companyName: string
    logo: Blob | null
    logoInfo: string
    logoStatus: string
    platformDescribe: string
    platformName: string
    versionStatus: string
  }
  licenseInfo: UserApi.LicenseDetail | null
}

const store: Module<StoreUser, unknown> = {
  namespaced: true,
  state() {
    return {
      userName: '', // 当前用户名
      userList: [], // 用户列表
      userInfo: {}, // 用户信息
      points: [], // 权限控制点
      roleList: [], // 角色列表
      userDetails: {},
      tenantId: '',
      businessAuthority: {},
      terminalVisible: true,
      logoInfo: {
        browserTab: null,
        companyName: '',
        logo: null,
        logoInfo: '0',
        logoStatus: '0',
        platformDescribe: '',
        platformName: '',
        versionStatus: '0'
      },
      licenseInfo: null
    }
  },
  mutations: {
    setUserName(state, value) {
      state.userName = value
    },
    setTenantId(state, value) {
      state.tenantId = value
    },
    SET_USER_LIST(state, value) {
      state.userList = value
    },
    SET_USER_INFO(state, value) {
      state.userInfo = value
    },
    SET_POINTS(state, value) {
      // state.points = [...state.points, ...value]
      state.points = value
    },
    SET_ROLE_LIST(state, value) {
      state.roleList = value
    },
    SET_LICENSE_INFO(state, value) {
      state.licenseInfo = value
    },
    SET_USER_DETAILS(state, value) {
      state.userDetails = value
    },
    SET_BUSINESS_AUTHORITY(state, value) {
      state.businessAuthority = value
    },
    SET_TERMINAL_VISIBLE(state, value) {
      state.terminalVisible = value
    },
    SET_LOGO_INFO(state, value) {
      state.logoInfo = value
    }
  },
  actions: {
    // 获取License信息
    async getLicenseInfo({ commit }) {
      try {
        const response = await getLicenseDetail()
        if (response?.data && response.data.data) {
          commit('SET_LICENSE_INFO', response.data.data)
          return response.data.data
        }
        return null
      } catch (error) {
        console.error('获取License信息失败:', error)
        return null
      }
    },
    // 新增用户
    addUser(_, value) {
      const parameter: UserApi.IAddUser = {
        userName: value.userName,
        email: value.email,
        loginName: value.loginName,
        password: value.password,
        phone: value.phone,
        roleList: value.roleIds,
        sex: value.sex,
        description: value.description,
        position: value.position
      }
      return addUser(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 根据ID删除用户
    deleteUserById(_, value) {
      const parameter: UserApi.IDeteleUserById = {
        userIds: value?.userIds
      }
      return deteleUserById(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 根据ID获取用户详细信息
    getUserDetails({ commit }, value) {
      const parameter: UserApi.IGetUserDetails = value
      return getUserDetails(parameter).then((response) => {
        const result = response.data
        commit('SET_USER_DETAILS', result.data)
        return result
      })
    },
    // 获取用户列表
    getUserList({ commit }, value) {
      const parameter: UserApi.IGetUserList = {
        condition: value?.condition,
        pageNo: value?.pageNo || 1,
        pageSize: value?.pageSize || 10,
        roleId: value?.roleId,
        tenantId: value?.tenantId
      }
      return getUserList(parameter).then((response) => {
        const result = response.data
        commit('SET_USER_LIST', result.data.records)
        return result
      })
    },
    // 根据ID重置用户密码
    resetPassword(_, value) {
      const parameter: UserApi.IResetPassword = {
        id: value?.id
      }
      return resetPassword(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 修改用户信息
    updateUser(_, value) {
      const parameter: UserApi.IUpdateUser = {
        position: value?.position,
        email: value?.email,
        id: value?.id,
        roleIds: value?.roleIds,
        sex: value?.sex,
        phone: value?.phone,
        loginName: value?.loginName,
        userName: value?.userName
      }
      return updateUser(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 用户账号冻结/解冻
    updateUserStatus(_, value) {
      const parameter: UserApi.IUpdateUserStatus = value
      return updateUserStatus(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 修改当前用户的邮箱或手机号
    updateCurrentUserInfo(_, value) {
      const parameter: UserApi.IUpdateCurrentUserInfo = {
        email: value?.email,
        phone: value?.phone
      }
      return updateCurrentUserInfo(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 用户修改密码
    updatePassword(_, value) {
      const parameter: UserApi.IUpdatePassword = {
        oldPassword: value?.oldPassword,
        newPassword: value?.newPassword
      }
      return updatePassword(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 修改用户角色信息
    updateUserRole(_, value) {
      const parameter: UserApi.IUpdateUserRole = {
        id: value?.id,
        roleIds: value?.roleIds
      }
      return updateUserRole(parameter).then((response) => {
        const result = response.data
        return result
      })
    },
    // 获取用户信息
    getUserInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getUserInfo()
          .then((response) => {
            const result = response.data
            commit('SET_USER_INFO', result.data)
            const appendPoint: string[] = []
            if (result.data?.type === '2') {
              appendPoint.push('k8s-add', 'k8s-namespace-add', 'k8s-namespace-delete')
            }
            const currentFeaturePoints: string[] = []
            commit('SET_POINTS', [...(result.data?.componentList || []), ...appendPoint, ...currentFeaturePoints])
            commit('setUserName', result.data.loginName)
            commit('SET_BUSINESS_AUTHORITY', result.data.businessAuthority || {})
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    // 获取角色列表
    getRoleList({ commit }) {
      return getRoleList().then((response) => {
        const result = response.data
        commit('SET_ROLE_LIST', result.data.roleInfoList)
        return result
      })
    },
    getAllTenantInfo() {
      return getAllTenantInfo().then((response) => {
        const result = response.data
        return result
      })
    },
    getTenantList({ commit }, value) {
      const parameter: UserApi.GetTenantList = {
        name: value.name,
        pageNo: value.pageNo,
        pageSize: value.pageSize,
        product: [2], // 代表着CE
        state: value.state
      }
      return new Promise((resolve, reject) => {
        getTenantList(parameter)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    addTenant({ commit }, value) {
      const parameter: UserApi.AddTenant = {
        director: value.director,
        email: value.email,
        phone: value.phone,
        productAndVersionReqList: [
          {
            productName: 'CyberEngine',
            productVersionName: ''
          }
        ],
        loginName: value.loginName,
        password: encrypt(value.password),
        tenantName: value.tenantName
      }
      return new Promise((resolve, reject) => {
        addTenant(parameter)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    updateTenant({ commit }, value) {
      const parameter: UserApi.UpdateTenant = {
        director: value.director,
        email: value.email,
        phone: value.phone,
        productAndVersionReqList: [
          {
            productName: 'CyberEngine',
            productVersionName: ''
          }
        ],
        loginName: value.loginName,
        tenantId: value.tenantId,
        tenantName: value.tenantName
      }
      return new Promise((resolve, reject) => {
        updateTenant(parameter)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    updateTenantState({ commit }, value) {
      const parameter: UserApi.UpdateTenantState = {
        tenantId: value.tenantId,
        state: value.state
      }
      return new Promise((resolve, reject) => {
        updateTenantState(parameter)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    resetTenantPassword({ commit }, value) {
      const parameter: UserApi.ResetTenantPassword = {
        loginName: value.loginName
      }
      return new Promise((resolve, reject) => {
        resetTenantPassword(parameter)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    deleteTenant({ commit }, value) {
      const parameter: UserApi.DeleteTenant = {
        tenantId: value.tenantId
      }
      return new Promise((resolve, reject) => {
        deleteTenant(parameter)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    tenantAll({ commit }, value) {
      return new Promise((resolve, reject) => {
        tenantAll()
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    getTenatPermission(_, value) {
      return new Promise((resolve, reject) => {
        getTenatPermission(value)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    updateTenatPermission(_, value) {
      return new Promise((resolve, reject) => {
        updateTenatPermission(value)
          .then((response) => {
            const result = response.data
            resolve(result)
          })
          .catch((error: any) => {
            reject(error)
          })
      })
    },
    getTerminalVisible({ commit }) {
      return getTerminalVisible().then((response) => {
        const result = response.data
        commit('SET_TERMINAL_VISIBLE', result.data === 'true')
        return result
      })
    },
    setTerminalVisible(_, value) {
      return setTerminalVisible(value).then((res) => {
        const result = res.data

        return result
      })
    },
    updateLogoInfo(_, value) {
      return updateLogoInfo(value).then((res) => {
        const result = res.data

        return result
      })
    },
    getLogoInfo({ commit }, _) {
      return getLogoInfo().then((res) => {
        const result: any = res.data
        const { browserTab, companyName, logo, logoInfo, logoStatus, platformDescribe, platformName, versionStatus } = result.data || {}
        commit('SET_LOGO_INFO', {
          browserTab,
          companyName,
          logo,
          logoInfo,
          logoStatus,
          platformDescribe,
          platformName,
          versionStatus
        })
        return result
      })
    }
  }
}

export default store
