<template>
  <el-dialog :model-value="modelValue" :title="$t('title.edit')" width="300px" :before-close="handleClose">
    <el-alert :title="$t('replenish.modifyHostnameWarning')" style="margin-top: -10px" type="warning" show-icon :closable="false" />
    <el-form ref="ruleFormRef" style="margin-top: 20px; margin-bottom: -10px" :model="ruleForm" :rules="rules">
      <el-form-item :label="$t('form.hostName') + '：'" prop="name">
        <el-input v-model="ruleForm.name" :placeholder="$t('form.pleaseEnter')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" @click="submitForm(ruleFormRef)"> {{ $t('button.sure') }} </BaseButton>
      <BaseButton type="info" @click="resetForm(ruleFormRef)"> {{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
  <el-dialog :model-value="dialogVisible" width="30%" center :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
    <div style="font-size: 24px; text-align: center">{{ stepMsg || $t('replenish.inProgress') }}</div>
    <template #footer> </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { updateHostName, updateHostNameProcess } from '@/api/hostsApi'
import elConfim from '@/utils/elConfim'
import type { FormInstance, FormRules } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const dialogVisible = ref(false)

const stepMsg = ref('')
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true
  },
  id: {
    type: Number,
    default: NaN
  },
  hostName: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:modelValue', 'refresh'])

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  name: ''
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: t('message.requiredinput'), trigger: 'blur' },
    { pattern: /^[a-z][a-z0-9-]+$/, message: t('replenish.hostnameRule'), trigger: 'blur' }
  ]
})

function submitForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      elConfim
        .confim({
          isCancelButton: true,
          message: t('replenish.confirmModifyHostname', {
            value: props.hostName
          }),
          desc: t('replenish.modifyHostnameWarning')
        })
        .then(() => {
          updateHostName({ id: props.id, hostName: ruleForm.name }).then((res) => {
            handleClose()
            dialogVisible.value = true
            getProcess()
            progressLoop()
          })
        })
    }
  })
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  emit('update:modelValue', false)
  formEl.resetFields()
}

function handleClose() {
  resetForm(ruleFormRef.value)
}
const lock = ref(false)
const timer = ref()
function getProcess() {
  updateHostNameProcess({ id: props.id })
    .then((res: any) => {
      stepMsg.value = res.data.data.stepMsg
      if (res.data.data.stete === 'SUCCESS') closeTimer()
    })
    .finally(() => {
      lock.value = false
    })
}
function progressLoop() {
  if (lock.value) return
  timer.value = setInterval(() => {
    getProcess()
  }, 1000)
}
function closeTimer() {
  if (timer.value) clearInterval(timer.value)
  dialogVisible.value = false
  emit('refresh')
}
</script>
