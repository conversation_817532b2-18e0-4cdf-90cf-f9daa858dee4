export function useJournal() {
  const { t, store } = useBasicTool()
  // 时间段下拉框
  const timeOptions = reactive([
    {
      value: '300000',
      label: t('form.pastMinutes', { number: 5 })
    },
    {
      value: '600000',
      label: t('form.pastMinutes', { number: 10 })
    },
    {
      value: '1800000',
      label: t('form.pastMinutes', { number: 30 })
    }
  ])
  const load = ref(true)
  const total = ref(0)
  const loading = ref(false)
  // 最低日志级别下拉框
  const levelOptions = [
    { label: t('replenish.traceLevel'), value: 'TRACE' },
    { label: t('replenish.debugLevel'), value: 'DEBUG' },
    { label: t('replenish.infoLevel'), value: 'INFO' },
    { label: t('replenish.warnLevel'), value: 'WARN' },
    { label: t('replenish.errorLevel'), value: 'ERROR' },
    { label: t('replenish.fatalLevel'), value: 'FATAL' }
  ]
  // 表格被减高度
  const tableDeleteHeight = ref(344)
  const tableHeight = ref()
  // 判断是否收起改变被减高度
  function wacthActive(value: boolean) {
    value ? (tableDeleteHeight.value = 344) : (tableDeleteHeight.value = 204)
  }
  // 监听滚动事件
  function onScroll(tableData: any, callback: Function, className: string) {
    const dom = document.querySelector(className)
    dom?.addEventListener(
      'scroll',
      (e: any) => {
        const target = e.target
        const clientHeight = target.clientHeight
        const scrollTop = target.scrollTop
        const scrollHeight = target.scrollHeight
        const length = tableData.data.length
        if (clientHeight + scrollTop + 1 > scrollHeight && load.value && total.value > length) {
          load.value = false
          loading.value = true
          callback()
        }
      },
      true
    )
  }
  function tableLevelFormatter(cellValue: string) {
    return levelOptions.find((item) => item.value === cellValue)?.label
  }
  let timer: any
  function download(value: EsApi.IGetAllEs) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      store.dispatch('es/downloadEs', { ...value, searchAfter: new Date().getTime() })
    }, 600)
  }
  function getLevelColor(name: string) {
    return [
      { name: ['FATAL'], value: 'level--fatal' },
      { name: ['ERROR'], value: 'level--warn' },
      { name: ['WARN'], value: 'level--ingeneral' },
      { name: ['INFO', 'DEBUG', 'TRACE'], value: 'level--prompt' }
    ].find((item) => item.name.includes(name))?.value
  }
  return {
    load,
    total,
    loading,
    timeOptions,
    levelOptions,
    tableDeleteHeight,
    tableHeight,
    tableLevelFormatter,
    wacthActive,
    onScroll,
    download,
    getLevelColor
  }
}
