// k8sApi.ts
import store from '@/store'
import Base from '@/utils/Base'
import service from '@/utils/Http'
import { getPath } from '@/utils/Path'
import { LStorage } from '@/utils/storage'
import axios from 'axios'
/**
 * @description 获取k8s列表
 * @param {K8sApi.IGetK8sList} data
 * @return {*}
 */
export const getK8sList = (data: K8sApi.IGetK8sList) => {
  return service.post<ApiResponse<PageList<K8sApi.K8sListRecordsData>>>('/k8s/pageList', data)
}

/**
 * @description 获取k8s列表(不分页)
 * @return {*}
 */
export const getK8sNoList = (data: K8sApi.K8sTenantList) => {
  return service.post<ApiResponse<Array<K8sApi.K8sListRecordsData>>>('/k8s/list', data)
}
/**
 * @description 获取当前可绑定的k8s集群列表
 * @param {K8sApi.IGetUnBoundedK8sList} data
 * @return {*}
 */
export const getUnBoundedK8sList = (data: K8sApi.IGetUnBoundedK8sList) => {
  return service.post<ApiResponse<Array<K8sApi.UnBoundedK8sListData>>>('/k8s/unbounded/list', data)
}

/**
 * @description [注意：接口使用单独的axios实例] k8s通过config方式认证注册
 * @param {K8sApi.IAuth} data
 * @return {*}
 */
export const k8sAuth = (formData: K8sApi.IAuth) => {
  const headers: any = {
    'Manager-Language': store.state.app.languag
  }
  headers[`${Base.cookie}`] = LStorage.get(Base.cookie)
  return axios
    .create({
      baseURL: import.meta.env.VITE_BASE_URL,
      headers
    })
    .post<ApiResponse<any>>(`${getPath('/k8s/auth')}`, formData)
}

/**
 * @description 添加k8s时可选的storageClass列表
 * @param {K8sApi.getStorageClassList} data
 * @return {*}
 */
export const getStorageClassList = (data: K8sApi.IAuth) => {
  const headers: any = {
    'Manager-Language': store.state.app.languag
  }
  headers[`${Base.cookie}`] = LStorage.get(Base.cookie)
  return axios
    .create({
      baseURL: import.meta.env.VITE_BASE_URL,
      headers
    })
    .post<ApiResponse<any>>(`${getPath('/k8s/storageClassList')}`, data)
}
/*
 * @description 获取nodeList
 * @param {K8sApi.GetNodeList} data
 * @return {*}
 */
export const getNodeList = (data: K8sApi.GetNodeList) => {
  return service.post<ApiResponse<string[]>>('/k8s/nodeList', data)
}

/**
 * @description 节点调度
 * @param {K8sApi.NodeScheduling} data
 * @return {*}
 */
export const nodeScheduling = (data: K8sApi.NodeScheduling) => {
  return service.post<ApiResponse<null>>('/service/instance/nodeScheduling', data)
}

/**
 * @description 编辑K8s
 * @param {K8sApi.NodeScheduling} formData
 * @return {*}
 */
export const editK8s = (formData: K8sApi.IAuth) => {
  const headers: any = {
    'Manager-Language': store.state.app.languag
  }
  headers[`${Base.cookie}`] = LStorage.get(Base.cookie)
  return axios
    .create({
      baseURL: import.meta.env.VITE_BASE_URL,
      headers
    })
    .post<ApiResponse<any>>(`${getPath('/k8s/edit')}`, formData)
}

/**
 * @description 删除K8s
 * @param {K8sApi.DeleteK8s} data
 * @return {*}
 */
export const deleteK8s = (data: K8sApi.DeleteK8s) => {
  return service.post<ApiResponse<null>>('/k8s/delete', data)
}

export const k8sTenantList = (data: K8sApi.K8sTenantList) => {
  return service.post<ApiResponse<null>>('/k8s/tenantList', data)
}

export const k8sAssign = (data: K8sApi.K8sAssign) => {
  return service.post<ApiResponse<null>>('/k8s/assign', data)
}

/**
 * @description 区域列表
 * @param {string[]} data
 * @return {*}
 */
export const getK8sRegion = () => {
  return service.post<ApiResponse<string[]>>('/k8s/region')
}

/**
 * @description 集群绑定的k8s列表信息
 * @param {K8sApi.IGetUnBoundedK8sList} data
 * @return {*}
 */
export const K8sInfoList = (data: K8sApi.IGetUnBoundedK8sList) => {
  return service.post<ApiResponse<Array<K8sApi.K8sInfoListRes>>>('/k8s/infoList', data)
}

/**
 * @description k8s维护模式切换
 * @param {HostsApi.HostsMaintenance} data
 * @return {*}
 */

export const K8sMaintenance = (data: HostsApi.HostsMaintenance) => {
  return service.post<ApiResponse<any>>(`/k8s/maintenance`, data)
}

/**
 * @description 根据流水号调用k8s绑定进度查询接口
 * @param {HostsApi.IHostsBindProgress} data
 * @return {*}
 */
export const k8sBindProgress = async (serialNumber: HostsApi.IHostsBindProgress) => {
  return await service.get<ApiResponse<HostsApi.ProgressData>>(`/k8s/bind/progress/${serialNumber}`)
}

/**
 * @description 创建 Nodeclass
 * @param {K8sApi.CreateNodeClassReq} data
 * @return {*}
 */
export const createNodeClass = async (data: K8sApi.CreateNodeClassReq) => {
  return await service.post<ApiResponse<string>>(`/karpenter/createNodeClass`, data)
}

/**
 * @description 分页获取 Nodeclass 列表
 * @param data
 * @return {*}
 */
export const pageNodeClasses = async (
  data: {
    k8sClusterId: stringOrNumber
    tenantId: stringOrNumber
  } & PageArgument
) => {
  return await service.post<ApiResponse<PageList<K8sApi.FindNodeClassRecord>>>(`/karpenter/pageNodeClasses`, data)
}

/**
 * @description Nodeclass 列表
 * @param data
 * @return {*}
 */
export const findNodeClassList = async (data: { k8sClusterId: stringOrNumber; tenantId: stringOrNumber }) => {
  return await service.post<ApiResponse<K8sApi.FindNodeClassRecord[]>>(`/karpenter/findNodeClassList`, data)
}

/**
 * @description 分页获取 NodePool 列表
 * @param data
 * @return {*}
 */
export const pageNodePools = async (
  data: {
    k8sClusterId: stringOrNumber
    tenantId: stringOrNumber
  } & PageArgument
) => {
  return await service.post<ApiResponse<PageList<K8sApi.NodePoolRecord>>>(`/karpenter/pageNodePools`, data)
}

/**
 * @description 查询 NodePool 模板
 * @param data
 * @return {*}
 */
export const queryNodePoolTpls = async () => {
  return await service.post<ApiResponse<Record<string, string>>>(`/karpenter/queryNodePoolTpls`)
}

/**
 * @description 查询 NodePool 模板
 * @param data
 * @return {*}
 */
export const createNodePool = async (data: K8sApi.CreateNodePoolReq) => {
  return await service.post<ApiResponse<string>>(`/karpenter/createNodePool`, data)
}

/**
 * @description 查询模板 NodePool 是否创建, True 已经创建
 * @param data
 * @return {*}
 */
export const templateHasCreated = async (data: { k8sId: stringOrNumber }) => {
  return await service.post<ApiResponse<boolean>>(`/karpenter/templateHasCreated/${data.k8sId}`, data)
}

/**
 * @description 删除 NodePool
 * @param data
 * @return {*}
 */
export const deleteNodePool = async (data: K8sApi.DeleteNodePoolReq) => {
  return await service.post<ApiResponse<string>>(`/karpenter/deleteNodePool`, data)
}

/**
 * @description 更新 NodePool
 * @param data
 * @return {*}
 */
export const updateNodePool = async (data: K8sApi.UpdateNodePoolReq) => {
  return await service.post<ApiResponse<string>>(`/karpenter/updateNodePool`, data)
}

/**
 * @description 更新 NodeClass
 * @param data
 * @return {*}
 */
export const updateNodeClass = async (data: K8sApi.UpdateNodeClassReq) => {
  return await service.post<ApiResponse<string>>(`/karpenter/updateNodeClass`, data)
}

/**
 * @description 更新 NodeClass
 * @param data
 * @return {*}
 */
export const deleteNodeClass = async (data: K8sApi.DeleteNodeClassReq) => {
  return await service.post<ApiResponse<string>>(`/karpenter/deleteNodeClass`, data)
}

/**
 * @description 验证集群是否安装 karpenter 和 nodepool crd
 * @param data
 * @return {*}
 */
export const validK8sCluster = async (data: { k8sId: stringOrNumber }) => {
  return await service.post<ApiResponse<boolean>>(`/karpenter/validK8sCluster/${data.k8sId}`, data)
}
