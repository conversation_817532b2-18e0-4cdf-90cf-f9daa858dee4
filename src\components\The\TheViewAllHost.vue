<template>
  <el-dialog :model-value="modelValue" :title="`${$t('replenish.viewAllSelectedHosts')}(${lenght})`" width="900px" :before-close="handleClose">
    <el-scrollbar height="484px">
      <div class="view-all">
        {{ content }}
      </div>
    </el-scrollbar>
  </el-dialog>
</template>

<script lang="ts" setup>
defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  lenght: {
    type: Number,
    default: 0
  },
  content: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['update:modelValue'])
function handleClose() {
  emit('update:modelValue', false)
}
</script>
<style lang="scss" scoped>
.view-all {
  font-size: 14px;
  line-height: 22px;
  color: var(--ops-secondary-tips-text-color);
}
</style>
