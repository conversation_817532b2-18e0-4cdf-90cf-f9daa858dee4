'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var CryptoJS = require('crypto-js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var CryptoJS__default = /*#__PURE__*/_interopDefaultLegacy(CryptoJS);

const iv = CryptoJS__default["default"].enc.Utf8.parse("70w5zbONA5xXAJ5C");
const KEY = "A39DHJC4x6MAOaHr";
function encrypt(plaintText, key = KEY) {
  if (!plaintText) {
    return "";
  }
  const newKey = CryptoJS__default["default"].enc.Utf8.parse(key);
  const iv2 = CryptoJS__default["default"].enc.Utf8.parse("70w5zbONA5xXAJ5C");
  const newPlaintText = plaintText;
  const options = {
    iv: iv2,
    mode: CryptoJS__default["default"].mode.CBC,
    padding: CryptoJS__default["default"].pad.Pkcs7
  };
  const encryptedData = CryptoJS__default["default"].AES.encrypt(newPlaintText, newKey, options);
  const encryptedBase64Str = encryptedData.toString();
  return encryptedBase64Str;
}
function decrypt(encryptedBase64Str, key = KEY) {
  if (!encryptedBase64Str) {
    return "";
  }
  const options = {
    iv,
    mode: CryptoJS__default["default"].mode.CBC,
    padding: CryptoJS__default["default"].pad.Pkcs7
  };
  const newKey = CryptoJS__default["default"].enc.Utf8.parse(key);
  const decryptedData = CryptoJS__default["default"].AES.decrypt(encryptedBase64Str, newKey, options);
  const decryptedStr = CryptoJS__default["default"].enc.Utf8.stringify(decryptedData);
  return decryptedStr;
}
function getQueryParam(paramName) {
  const queryString = window.location.href.split("?")?.[1] || "";
  const params = parseQueryString(queryString);
  if (typeof paramName !== "string" || !paramName.match(/^[a-zA-Z0-9-_]+$/)) {
    console.warn("Invalid parameter name.");
    return void 0;
  }
  return params[paramName] || void 0;
}
const parseQueryString = (queryString) => {
  const params = {};
  queryString.split("&").forEach((param) => {
    const [key, value = ""] = param.split("=");
    params[decodeURIComponent(key)] = decodeURIComponent(value);
  });
  return params;
};

exports.decrypt = decrypt;
exports.encrypt = encrypt;
exports.getQueryParam = getQueryParam;
exports.parseQueryString = parseQueryString;
//# sourceMappingURL=encryption.js.map
