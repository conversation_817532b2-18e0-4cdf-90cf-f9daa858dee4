<template>
  <div class="configure-box">
    <div class="view-configure">
      <BaseLayout :is-header="false" :is-footer="false">
        <template #content>
          <div class="text-right" :class="'mb-16px'">
            <BaseButton class="relative z-1" v-if="!route.query.clusterServiceInstanceId" type="info" @click="addCustomParameters">
              <i class="iconfont icon-tianjia f-14 mr-5px" />
              <span>{{ $t('replenish.addCustomParameter') }}</span>
            </BaseButton>
            <img
              v-if="isSearch"
              @click="switchSearch"
              class="inline-block relative z-1 ml-10px cursor-pointer"
              src="@/assets/icons/icon_you.svg"
              alt="icon"
            />
            <img
              v-else
              @click="switchSearch"
              class="inline-block relative z-1 ml-10px cursor-pointer"
              src="@/assets/icons/icon_mieyou.svg"
              alt="icon"
            />
          </div>
          <div class="view-configure-main">
            <div v-if="isSearch" class="pl-20px bg-hex-FAFBFC mb-16px">
              <BaseSearch :searchItemData="searchItemData" @on-submit="submitSearch" @reset-search="resetSearch"> </BaseSearch>
            </div>
            <ConfigureList
              v-loading="isLoadList"
              :is-loading="isLoadList"
              :class="isSearch ? 'minHS' : 'minH'"
              :view-data="viewData"
              :edit-records="editRecords"
              :custom-data="customParametersData"
              @change="customChange"
            />
            <BasePagination v-model="pageInfo" class="pagination" @page-change="handlePageChange" />
          </div>
        </template>
      </BaseLayout>
      <div class="change-reason-group">
        <template v-if="$has('namespace-componentsConfiguraOperation')">
          <!-- <span style="margin-right: 20px">{{ $t('form.editValue', { value: configureCacheMap.size }) }}</span>
          <span style="font-size: 16px">{{ $t('form.modifyReasonLabel') + '：' }}&emsp;</span>
          <el-input class="change-reason-group__input" v-model="configRequestFrom.modifyReason" :placeholder="$t('form.modifyReason')" /> -->
          <BaseButton
            style="margin-left: 20px"
            type="primary"
            :loading="isSaveLoading"
            :disabled="(isSaveDisabled && isEditRecords && isAddcustom) || isLoadList"
            @click="onSave"
          >
            {{ $t('button.save') }}
          </BaseButton>
          <!-- <BaseButton
            style="margin-left: 20px"
            type="primary"
            :loading="isReleaseLoading"
            :disabled="(isSaveDisabled && isAddcustom) || isSaveLoading"
            @click="onSaveRelease"
          >
            {{ $t('button.saveAndPublish') }}
          </BaseButton> -->
        </template>
      </div>
    </div>

    <AddCustomParameters
      v-if="addCustomParameter"
      v-model="addCustomParameter"
      :custom-data="customParametersData"
      @submit="reverseGetConfigureList"
    />
    <UpdateInfo
      v-model="updateVisible"
      :cluster-service-id="clusterServiceId"
      :cluster-service-instance-id="clusterServiceInstanceId"
      :list="updateList"
      :config-request-from="configRequestFrom"
      @submit="submitUpdateInfo"
    />
  </div>
</template>

<script lang="ts" setup>
import elConfim from '@/utils/elConfim'
import { bus } from '@/utils/mitt'

import { ElMessage } from 'element-plus'
import AddCustomParameters from './components/AddCustomParameters.vue'
import ConfigureList from './components/ConfigureList.vue'
import { useConfigureCacheMap } from './components/hooks/useConfigureCacheMap'
import { useGetListByPage } from './hooks/useGetListByPage'
import UpdateInfo from './components/UpdateInfo.vue'

const { route, t, router, setBreadList, store, $has } = useBasicTool()
const { configureCacheMap, resetConfigureCacheMap } = useConfigureCacheMap()

const props = defineProps({
  configKey: {
    type: String,
    default: ''
  },
  clusterServiceId: {
    type: String,
    default: ''
  },
  clusterServiceInstanceId: {
    type: String,
    default: ''
  }
})
const addCustomParameter = ref(false)
const {
  viewData,
  pageInfo,
  searchForm,
  editRecords,
  customParametersData,
  customParametersDataCompare,
  resetPageInfo,
  isLoadList,
  handlePageChange,
  getData,
  onSearch,
  onReset
} = useGetListByPage(store, props.clusterServiceId, props.clusterServiceInstanceId)
const isSearch = ref(true)
const namespaceNameText = computed(() => store.state.namespace.namespaceName)
const isSaveDisabled = computed(() => configureCacheMap.size <= 0)
const isEditRecords = computed(() => (editRecords?.value ?? false ? editRecords?.value?.length === 0 : true))
const isAddcustom = computed(() => customParametersData.value.length === 0 && customParametersDataCompare.value.length === 0)
const confList = computed(() => store.state.clusterServiceConfig.confList)
const isSaveLoading = ref(false)
const isReleaseLoading = ref(false)
const configRequestFrom = reactive<{
  configRequestDtos: any
  modifyReason: string
  applied: number
}>({
  configRequestDtos: [],
  modifyReason: '',
  applied: 0
})
const updateList = ref<Record<string, string>[]>([])

// 搜索配置项
const searchItemData = reactive<Array<BaseSearch.SearchItemData>>([
  {
    label: computed(() => t('form.fileName') + '：'),
    type: 'select',
    selectOptions: [
      { name: t('state.online'), value: 1 },
      { name: t('state.offline'), value: 0 }
    ],
    param: 'confName',
    defaultValue: '',
    placeholder: t('replenish.selectFileName')
  },
  {
    label: computed(() => t('form.keywordSearch') + '：'),
    type: 'input',
    param: 'searchKey',
    defaultValue: '',
    placeholder: computed(() => t('form.pleaseEnterKeyWords'))
  },
  {
    label: t('replenish.roleScope') + '：',
    type: 'select',
    selectOptions: [
      { name: t('state.online'), value: 1 },
      { name: t('state.offline'), value: 0 }
    ],
    param: 'roleName',
    defaultValue: '',
    placeholder: computed(() => t('form.pleaseChoose'))
  },
  {
    label: t('replenish.configCategory') + '：',
    type: 'select',
    selectOptions: [
      { name: t('state.online'), value: 1 },
      { name: t('state.offline'), value: 0 }
    ],
    param: 'labelType',
    defaultValue: '',
    placeholder: computed(() => t('form.pleaseChoose'))
  }
])

const updateVisible = ref(false)

// 搜索提交
function submitSearch(value: any) {
  pageInfo.pageNo = 1
  searchForm.confName = value.confName
  searchForm.searchKey = value.searchKey
  searchForm.roleName = value.roleName
  searchForm.labelType = value.labelType
  onSearch()
}
// 重置搜索框
function resetSearch(value: any) {
  resetPageInfo()
  onReset()
}
const recordId = ref()
bus.on('getConfigureList', () => {
  getConfigureList()
  onSearch()
})
function getConfigureList() {
  getData()
}

onMounted(() => {
  getConfigureList()
  store.dispatch('clusterServiceConfig/getConfNameList', {
    clusterServiceId: props.clusterServiceId,
    clusterServiceInstanceId: props.clusterServiceInstanceId
  })
  if (props.configKey) {
    searchForm.searchKey = props.configKey
    handlePageChange()
  }
  store.dispatch('clusterServiceConfig/configTypeList').then((res) => {
    searchItemData[3].selectOptions = res.data.map((item: any) => {
      return { name: item.enumKey, value: item.enumValue }
    })
  })
  store.dispatch('clusterServiceConfig/getRoleNames', { clusterServiceId: props.clusterServiceId }).then((res) => {
    searchItemData[2].selectOptions = res.data.map((item: any) => {
      return { name: item, value: item }
    })
  })
})
watch(
  () => confList.value,
  () => {
    searchItemData[0].selectOptions = confList.value.map((item: string) => {
      return { name: item, value: item }
    })
  }
)
onBeforeUnmount(() => {
  configureCacheMap.clear()
})

// 保存事件
function onSave() {
  if (isSaveLoading.value) return
  // isSaveLoading.value = true
  configureCacheMap.forEach((value) => {
    configRequestFrom.configRequestDtos.push(value)
  })
  configRequestFrom.configRequestDtos.push(...customParametersData.value)
  const updateKey = [] as string[]
  updateList.value = []
  configRequestFrom.configRequestDtos?.forEach((el: { confKey: string; confDefaultValue: any; instanceConfigs: { confValue: any }[] }) => {
    if (!updateKey.includes(el.confKey)) {
      updateKey.push(el.confKey)
      const oldValue =
        editRecords.value?.find(
          (ele: { confKey: string; confDefaultValue: any; instanceConfigs: { confValue: any }[] }) => ele.confKey === el.confKey
        )?.instanceConfigs?.[0]?.confValue || ''
      const defaultValue =
        viewData.value?.find((ele: { confKey: string; confDefaultValue: any; instanceConfigs: { confValue: any }[] }) => ele.confKey === el.confKey)
          ?.instanceConfigs?.[0]?.confValue || ''
      updateList.value.push({
        confKey: el.confKey,
        oldValue: oldValue || defaultValue,
        confValue: el.instanceConfigs?.[0]?.confValue || ''
      })
    }
  })
  updateVisible.value = true

  // store
  //   .dispatch('configure/updateAllConfigsByClusterId', {
  //     ...configRequestFrom,
  //     recordId: recordId.value,
  //     clusterServiceId: props.clusterServiceId,
  //     clusterServiceInstanceId: props.clusterServiceInstanceId
  //   })
  //   .then(() => {
  //     ElMessage({
  //       type: 'success',
  //       message: t('message.saveSuccess')
  //     })
  //     resetConfigureCacheMap()
  //     getConfigureList()
  //     configRequestFrom.configRequestDtos = []
  //     customParametersData.value = []
  //     customParametersDataCompare.value = []
  //     configRequestFrom.modifyReason = ''
  //   })
  //   .catch(() => {
  //     ElMessage({
  //       type: 'error',
  //       message: t('message.saveFailed')
  //     })
  //   })
  //   .finally(() => {
  //     isSaveLoading.value = false
  //   })
}

const submitUpdateInfo = () => {
  // ElMessage({
  //   type: 'success',
  //   message: t('message.saveSuccess')
  // })
  resetConfigureCacheMap()
  getConfigureList()
  configRequestFrom.configRequestDtos = []
  customParametersData.value = []
  customParametersDataCompare.value = []
  configRequestFrom.modifyReason = ''
  updateVisible.value = false
}

// 保存并发布事件
function onSaveRelease() {
  elConfim
    .confim({
      isCancelButton: true,
      title: t('title.confirmTip'),
      message: t('message.isSaveConfigurationFile'),
      desc: t('message.savingAndPublishing')
    })
    .then(() => {
      isReleaseLoading.value = true
      configureCacheMap.forEach((value) => {
        configRequestFrom.configRequestDtos.push(value)
      })
      configRequestFrom.configRequestDtos.push(...customParametersData.value)
      store
        .dispatch('configure/updateAllConfigsByClusterId', {
          ...configRequestFrom,
          applied: 1,
          recordId: recordId.value,
          clusterServiceId: props.clusterServiceId,
          clusterServiceInstanceId: props.clusterServiceInstanceId
        })
        .then(() => {
          ElMessage({
            type: 'success',
            message: t('message.configurationSavesuccess')
          })
          resetConfigureCacheMap()
          getConfigureList()
          configRequestFrom.configRequestDtos = []
          customParametersData.value = []
          customParametersDataCompare.value = []
          configRequestFrom.modifyReason = ''
        })
        .catch(() => {
          ElMessage({
            type: 'error',
            message: t('message.configurationSaveFailed')
          })
        })
        .finally(() => {
          isReleaseLoading.value = false
        })
    })
}

function addCustomParameters() {
  addCustomParameter.value = true
}

function switchSearch() {
  isSearch.value = !isSearch.value
}

function reverseGetConfigureList(formData: any[]) {
  customParametersData.value.push(...formData)
}
function customChange(value: any[]) {
  customParametersData.value = value
}
</script>

<style lang="scss" scoped>
.configure-box {
  box-sizing: border-box;
  height: calc(100% + 30px);
  margin-top: -30px;
  background-color: var(--ops-bg-white-color);
  .configure-box__view-configure {
    height: calc(100% - 72px);
  }
  .view-configure {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    padding-bottom: 80px;
    .view-configure-main {
      display: flex;
      overflow-y: auto;
      flex-direction: column;
      height: calc(100% - 52px);
      .pagination {
        align-self: flex-end;
        padding-bottom: 10px;
      }
    }
  }
  .change-reason-group {
    position: absolute;
    right: 16px;
    bottom: 0px;
    z-index: 2;
    box-sizing: border-box;
    width: calc(100% - 32px);
    height: 60px;
    padding: 0 20px 10px;
    background-color: var(--ops-bg-white-color);
    @include flex(flex-end, center);
    .change-reason-group__input {
      width: 480px;
    }
  }
}
:deep(.el-tabs__content) {
  display: none;
}
:deep(.el-tabs--border-card) {
  border: none;
}
:deep(.base-search) {
  .base-search__input {
    width: 240px;
  }
}
</style>
