'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/icon/style/css');
var iconsVue = require('@element-plus/icons-vue');
require('./Expand.vue_vue_type_style_index_0_scoped_true_lang.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const clsPrefix = "dc-search-filter-expand";
const _sfc_main = vue.defineComponent({
  __name: "Expand",
  props: {
    expandText: {
      type: String,
      default: "\u5C55\u5F00"
    },
    collapseText: {
      type: String,
      default: "\u6536\u8D77"
    }
  },
  emits: ["toggleExpand"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const isExpand = vue.ref(false);
    const toggle = () => {
      isExpand.value = !isExpand.value;
      emits("toggleExpand", isExpand.value);
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(`${clsPrefix}`),
        onClick: toggle
      }, [
        vue.createVNode(vue.unref(elementPlus.ElIcon), null, {
          default: vue.withCtx(() => [
            isExpand.value ? (vue.openBlock(), vue.createBlock(vue.unref(iconsVue.ArrowDown), { key: 0 })) : (vue.openBlock(), vue.createBlock(vue.unref(iconsVue.ArrowUp), { key: 1 }))
          ]),
          _: 1
        }),
        vue.createElementVNode("span", {
          class: vue.normalizeClass(`${clsPrefix}_text`)
        }, vue.toDisplayString(isExpand.value ? props.expandText : props.collapseText), 3)
      ], 2);
    };
  }
});
var Expand = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__scopeId", "data-v-e86dc0c8"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\search-filter\\src\\Expand.vue"]]);

exports["default"] = Expand;
//# sourceMappingURL=Expand.js.map
