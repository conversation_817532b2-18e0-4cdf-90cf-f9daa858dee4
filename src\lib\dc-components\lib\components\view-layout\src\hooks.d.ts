import { type Ref } from "vue";
interface ElementSize {
    width: Ref<number>;
    height: Ref<number>;
}
/**
 * 监听DOM元素尺寸变化的Hook
 * @param target 目标元素的Ref或DOM元素
 * @param options 配置选项
 * @returns 包含width和height的响应式对象
 */
export declare function useElementSize(target: Ref<HTMLElement | null> | HTMLElement | null, options?: {
    initialWidth?: number;
    initialHeight?: number;
    debounce?: number;
}): ElementSize;
export {};
