{"version": 3, "file": "index.js", "sources": ["../../../../../../../../packages/components/form/src/components/Custom/index.tsx"], "sourcesContent": ["import { defineComponent } from \"vue\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcFormCustomRenderFn } from \"../../types\";\r\nimport \"./index.scss\";\r\n\r\nexport default defineComponent({\r\n  name: \"DcFormCustom\",\r\n  props: {\r\n    modelValue: {},\r\n    renderCustom: {\r\n      type: Function as PropType<DcFormCustomRenderFn>,\r\n    },\r\n    rowIndex: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n  },\r\n  emits: {\r\n    \"update:modelValue\": (val: any) => true,\r\n  },\r\n  setup(props, ctx) {\r\n    const onChange = (val: any) => {\r\n      ctx.emit(\"update:modelValue\", val);\r\n    };\r\n\r\n    return {\r\n      onChange,\r\n    };\r\n  },\r\n  render() {\r\n    const vNode = this.$props.renderCustom\r\n      ? this.$props.renderCustom({\r\n          value: this.$props.modelValue,\r\n          onChange: this.onChange,\r\n          rowIndex: this.$props.rowIndex,\r\n        })\r\n      : null;\r\n    return vNode;\r\n  },\r\n});\r\n"], "names": ["defineComponent", "name", "props", "modelValue", "renderCustom", "type", "Function", "rowIndex", "Number", "default", "emits", "val", "setup", "ctx", "onChange", "emit", "render", "vNode", "$props", "value"], "mappings": ";;;;;;;AAKA,mBAAeA,mBAAgB,CAAA;AAAA,EAC7BC,IAAM,EAAA,cAAA;AAAA,EACNC,KAAO,EAAA;AAAA,IACLC,YAAY,EAAC;AAAA,IACbC,YAAc,EAAA;AAAA,MACZC,IAAMC,EAAAA,QAAAA;AAAAA,KACR;AAAA,IACAC,QAAU,EAAA;AAAA,MACRF,IAAMG,EAAAA,MAAAA;AAAAA,MACNC,OAAS,EAAA,CAAA;AAAA,KACX;AAAA,GACF;AAAA,EACAC,KAAO,EAAA;AAAA,IACL,qBAAsBC,CAAa,GAAA,KAAA,IAAA;AAAA,GACrC;AAAA,EACAC,KAAAA,CAAMV,OAAOW,GAAK,EAAA;AAChB,IAAA,MAAMC,WAAYH,CAAa,GAAA,KAAA;AAC7BE,MAAIE,GAAAA,CAAAA,IAAAA,CAAK,qBAAqBJ,GAAG,CAAA,CAAA;AAAA,KACnC,CAAA;AAEA,IAAO,OAAA;AAAA,MACLG,QAAAA;AAAAA,KACF,CAAA;AAAA,GACF;AAAA,EACAE,MAAS,GAAA;AACP,IAAA,MAAMC,QAAQ,IAAKC,CAAAA,MAAAA,CAAOd,YACtB,GAAA,IAAA,CAAKc,OAAOd,YAAa,CAAA;AAAA,MACvBe,KAAAA,EAAO,KAAKD,MAAOf,CAAAA,UAAAA;AAAAA,MACnBW,UAAU,IAAKA,CAAAA,QAAAA;AAAAA,MACfP,QAAAA,EAAU,KAAKW,MAAOX,CAAAA,QAAAA;AAAAA,KACvB,CACD,GAAA,IAAA,CAAA;AACJ,IAAOU,OAAAA,KAAAA,CAAAA;AAAAA,GACT;AACF,CAAC,CAAA;;;;"}