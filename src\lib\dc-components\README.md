<p align="center">dataCreating components - A Vue.js 3 UI-Logical combinations library base element plus</p>

- Vue 3 Composition API
- Written in TypeScript
- Base element plus

## 项目中使用

### 安装

`npm install dc-components`

### 引入

- 入口文件引入

```ts
import DcComponents from "dc-component";

// 导入样式
import "dc-components/index.css";

app.use(DcComponents);
```

- tsconfig 引入 ts 文件

```json
"compilerOptions": {
  "types": [
    ...
    "dc-components/global"
  ]
}
```

- vite.config 配置

```ts
// 配置增加unplugin-element-plus/vite 插件，自动引入使用到的element-plus 组件样式

import UnpluginElementPlus from "unplugin-element-plus/vite";

plugins: [...UnpluginElementPlus({})];
```

### 组件样式

- 每个项目都有单独的 element-plus 样式重置，可以在其中增加对 dc-components 中组件的样式修改，例如

```scss
// reset.scss
html {
  .cd-drawer {
    .el-drawer__header {
      height: 36px;
      @include bgColor("gray-8");
      padding: 0 $spacing-l;
      margin-bottom: 0;
      display: flex;
      align-items: center;

      justify-content: space-between;
      @include borderBottom("border");
      .el-drawer__title {
        font-size: $font-size-l;
        font-weight: 600;
        @include color("title");
      }
      .el-drawer__close-btn {
        padding: 0;
        font-size: $font-size-l;
        @include color("secondary");
        &:hover {
          @include color("blue");
        }
      }
    }
    .el-drawer__footer {
      display: flex;
      height: 44px;
      padding: 0 $spacing-l;
      @include bgColor("interactive-bg");
      @include borderTop("border");
      justify-content: flex-end;
      align-items: center;
    }
    .el-drawer__body {
      padding: 0;
    }
  }
  .cd-search-filter {
    .el-input,
    .el-select {
      width: 160px;
    }
  }
}
```

- 使用组件的时候，class 增加 自定义的类名

```vue
<DcDrawer class="cd-drawer"></DcDrawer>
```

## 使用参考

### 组件

#### button

- props

```ts
debounce: 是否防抖，默认false
debounceInterval: 防抖时间，默认300

```

#### checkbox

- props

```ts
options: 选项组;
// ts

export interface DcCheckboxOpItem {
  label: string | number;
  value: string | number | boolean;
  disabled?: boolean;
}
```

#### dialog

- props

```ts
modelValue: 控制显示隐藏
type: 弹窗类型，ts 定义如下：

export enum DcDialogTypes {
  HEADER_FOOTER = "header_footer",
  FOOTER = "footer",
  HEADER = "header",
  ONLYCONTENT = "ONLYCONTENT",
}

confirmAction： 确认按钮，ts定义如下：

export interface DcDialogAction {
  innerText?: string;
  visible?: boolean | Ref<boolean>;
  componentSlot?: Record<string, RenderVNodeFn>; // 按钮的slot
  props?: Record<string, any>; //ElButton 按钮的属性配置
  config?: DcButtonConfig; // DcButton props 配置
}

cancelAction：取消按钮
footerLeftSlot：类型为函数, footer按钮左侧 补充
footerRightSlot:  类型为函数, footer按钮右侧 补充
closeIsCancel: 点击关闭是否触发取消事件，默认true
class: class 名
```

#### drawer

- props

```ts
modelValue: 控制显示隐藏
type: 弹窗类型，ts 定义如下：

export enum DcDrawerTypes {
  HEADER_FOOTER = "header_footer",
  FOOTER = "footer",
  HEADER = "header",
  ONLYCONTENT = "ONLYCONTENT",
}

confirmAction： 确认按钮，ts定义如下：

export interface DcDialogAction {
  innerText?: string;
  visible?: boolean | Ref<boolean>;
  componentSlot?: Record<string, RenderVNodeFn>; // 按钮的slot
  props?: Record<string, any>; //ElButton 按钮的属性配置
  config?: DcButtonConfig; // DcButton props 配置
}

cancelAction：取消按钮
footerLeftSlot：类型为函数, footer按钮左侧 补充
footerRightSlot:  类型为函数, footer按钮右侧 补充
closeIsCancel: 点击关闭是否触发取消事件，默认true
class: class 名
```

#### table

- props

```ts
columns: 展示列配置，数组， ts定义

export interface DcTableColumnItem {
  type?: string;
  label?: string;
  className?: string;
  labelClassName?: string;
  prop: string;
  property?: string;
  width?: string | number;
  minWidth?: string | number;
  renderHeader?: (data: { column: DcTableColumnItem; $index: number }) => VNode;
  sortable?: boolean | string;
  sortMethod?: (a: any, b: any) => number;
  sortBy?: string | ((row: any, index: number) => string) | string[];
  resizable?: boolean;
  columnKey?: string;
  align?: string;
  headerAlign?: string;
  showOverflowTooltip?: boolean | Record<string, any>;
  fixed?: boolean | string;
  formatter?: (
    row: any,
    column: DcTableColumnItem,
    cellValue: any,
    index: number
  ) => VNode | string;
  selectable?: (row: any, index: number) => boolean;
  reserveSelection?: boolean;
  filterMethod?: (value: any, row: any, column: DcTableColumnItem) => void;
  filteredValue?: string[];
  filters?: Filters;
  filterPlacement?: string;
  filterMultiple?: boolean;
  index?: number | ((index: number) => number);
  sortOrders?: ("ascending" | "descending" | null)[];
  slot?: boolean;
  columnRequired?: boolean;
  columnDefault?: boolean;
}
isColumnFilter： 是否筛选列展示，默认false
append: append slot
filterSubmitText
filterCancelText
filterAllText
filterResetText
height: 字符串，表格的高度
loading： 加载状态
showSelectColumn
selectionWidth
showEmptyImg: 是否展示缺省图片
selectable
locale: 语言
export type DcLocaleType = "zh-CN" | "en-US";
theme: 主题
export type DcThemeType = "light" | "dark";

```

#### edit-table (待完善)

#### form

- props

```ts
config: 表单配置，ts 定义如下

export interface DcFormConfig {
  inline?: boolean;
  labelPosition?: "left" | "right" | "top";
  labelWidth?: string | number;
  showMessage?: boolean;
  inlineMessage?: boolean;
  disabled?: boolean;
  itemMargin?: DcFormItemMargin; //配置表单项的margin
  children: DcFormItem[]; //表单项列表
}

// 表单项配置 ts

export interface DcFormItemBase {
  model: string; // 绑定的属性
  defaultValue: any; //默认值
  rules?: FormItemRule[];
  label?: string;
  labelWidth?: string | number;
  disabled?: boolean;
  component: DcFormComponentEnum; //展示表单项组件
  inlineMessage?: boolean;
  labelSlot?: RenderVNodeFn;
  visible?: DcFormItemVisible; // 展示控制 v-if
  show?: boolean | Ref<boolean>; // 展示控制 v-show
  effect?: DcFormItemEffect; // 绑定的属性值变化时，触发的副作用函数
  props?: Record<string, any>;
  componentSlot?: Record<string, RenderVNodeFn>;
  rightSlot?: RenderVNodeFn;
  margin?: DcFormItemMargin;
  class?: string;
}

export type DcFormItem =
  | DcFormInput
  | DcFormInputNumber
  | DcFormSelect
  | DcFormSelectV2
  | DcFormText
  | DcFormRadio
  | DcFormCheckbox
  | DcFormAutoComplete
  | DcFormColorPicker
  | DcFormDatePicker
  | DcFormCascaderPanel
  | DcFormRate
  | DcFormSlider
  | DcFormSwitch
  | DcFormTimePicker
  | DcFormTimeSelect
  | DcFormTransfer
  | DcFormUpload
  | DcFormTable
  | DcFormCustom
  | DcFormCascader;

// 表单项支持的组件
export enum DcFormComponentEnum {
  INPUT = "input",
  TEXT = "text",
  SELECT = "select",
  RADIO = "radio",
  CHECKBOX = "checkbox",
  AUTOCOMPLETE = "autocomplete",
  CASCADER = "cascader",
  CASCADERPANEL = "cascaderPanel",
  COLORPICKER = "colorPicker",
  DATEPICKER = "datePicker",
  INPUTNUMBER = "inputNumber",
  RATE = "rate",
  SELECTV2 = "selectV2",
  SLIDER = "slider",
  SWITCH = "switch",
  TIMEPICKER = "timePicker",
  TIMESELECT = "timeSelect",
  TRANSFER = "transfer",
  UPLOAD = "upload",
  TABLE = "table",
  CUSTOM = "custom",
}

// select、checkbox、radio 在props 中配置 options, 可以为 ref 类型
// table 组件的列配置 扩展了table column
export interface DcFormTableColumnItem extends DcTableColumnItem {
  isFormItem?: boolean; // 是否为表单项
  formItemConfig?: DcFormItem; // 表单项配置
}


// custom 自定义组件，需要在props 中配置 renderCustom

export type DcFormCustomRenderFn = ({
  value,
  onChange,
  rowIndex,
}: {
  value: any;
  onChange: (val: any) => void;
  rowIndex: number;
}) => VNode;

```

- expose

```ts
validate,
validateField,
resetFields,
scrollToField,
clearValidate,
getValues,
initModel,
reset,
setFieldsValue,
asyncValidate,
```

#### radio

- props

```ts
options: 选项组;
// ts

export interface DcRadioOpItem {
  label: string | number;
  value: string | number | boolean;
  disabled?: boolean;
  defaultSlot?: RenderVNodeFn;
}
```

#### render-vnode

- props

```ts
vnode;
```

#### search-filter

- props

```ts
config: 筛选表单配置 ts定义如下：

export interface DcSearchFilterConfig {
  queryAction?: DcSearchFilterAction; // 查询按钮
  resetAction?: DcSearchFilterAction; // 重置按钮
  refreshAction?: DcSearchFilterAction; //刷新按钮
  actionMargin?: DcFormItemMargin; // 按钮边距
  formConfig: DcFormConfig; // 表单项配置
  expand?: DcSearchFilterExpand; // 展开收起
  extendSlot?: VNode; // 扩展slot
  extendMargin?: DcFormItemMargin;
}

```

- expose

```ts
query,
reset,
refresh,
getValues,

```

#### select

- props

```ts
options: 选项组，可以为 ref 类型

export interface DcSelectOpItem {
  label: string | number;
  value: string | number | boolean;
  disabled?: boolean;
  defaultSlot?: RenderVNodeFn;
}

groups: 选项分组，可以为ref 类型

export interface DcSelectGroupItem {
  label: string;
  disabled?: boolean;
  options: DcSelectOpItem[];
}

```

### empty

- props

```ts
type: 场景类型;
export type DcEmptyType =
  | "list"
  | "unList"
  | "echarts"
  | "file"
  | "power"
  | "feature"
  | "blood"
  | "model"
  | "network"
  | "404"
  | "405"
  | "message"
  | "image"
  | "imagePlaceholder"
  | "api";

locale: 语言;
export type DcLocaleType = "zh-CN" | "en-US";

theme: 主题;
export type DcThemeType = "light" | "dark";

description: 描述文案;
String;

showImg: 是否展示缺省图表;
Boolean;

img: 替换缺省图;
// import img from "./image.jpg";
```

### login

- props

| **属性名**        | **说明**         | **默认值**                                                            | **默认值**                                             |
| ----------------- | ---------------- | --------------------------------------------------------------------- | ------------------------------------------------------ |
| productKey        | 产品名           | "CyberData" &#124; "CyberEngine" &#124; "CyberAI" &#124; "UserCenter" | UserCenter                                             |
| useLocale         | 是否使用国际化   | boolean                                                               | true                                                   |
| baseUrl           | 接口请求 baseUrl | string                                                                | 用户中心开发地址 |
| locale            | 语言             | "zh-CN" &#124; "en-US"                                                | zh-CN                                                  |
| title？           | 展示标题         | string                                                                | -                                                      |
| logo？            | logo             | string                                                                | 原型 logo                                              |
| showLogo？        | 是否展示 logo    | boolean                                                               | true                                                   |
| autoLogin？       | 是否自动登录     | boolean                                                               | false                                                  |
| autoLoginLoading? | 自动登录 loading | boolean                                                               | false                                                  |

- event

| **事件名**     | **说明**     | **回调参数**           |
| -------------- | ------------ | ---------------------- |
| loginSuccess   | 登录成功回调 | loginres               |
| updateLanguage | 切换国际化   | locale（props.locale） |

```
type loginres {
  title: string; 产品名
  jwtToken: string; token
  loginName: string; 用户名
  password: string; 密码（未加密）
}
```

### 工具函数

#### useDialog

- 简化使用 dialog 的 hooks, 使用方式如下

```ts
const {
  showDialog: showDeleteTip,
  onCancel: closeDelete,
  visible: deleteTipVis,
  onConfirm: confirmDelete,
  dialogData: deleteData,
} = useDialog({
  defaultData: {
    id: "",
    name: "",
    ids: [],
  },
  confirm: () => {},
  cancel: () => {},
  show: () => {}
```

#### useDrawer

- 简化使用 drawer 的 hooks, 使用方式如下

```ts
const {
  showDrawer: showRelateTable,
  onCancel: closeRelateTable,
  visible: relateTableVisible,
  drawerData: relateTableData,
} = useDrawer({
  confirm: () => {},
  cancel: () => {},
  show: () => {}
  defaultData: "",
});

```

### 指令

#### copy

- 复制

```vue
<span v-copy="{ text: 'test' }"></span>
```

## 组件开发

### 目录结构

- packages, 组件库原始代码，包括 components(组件)，constants(常量)，utils(工具函数)，directives(指令)
- play dev 调试组件的 vite 项目
- scripts 打包、发布的脚本

### 开发约定

- 一个组件需要在一个单独目录下，并包含入口文件 index.ts、 源代码目录 src
- 入口文件需要对外透出组件和组件的 ts 定义
- src 下需要有自己的 types 文件，types 中需要包含组件实例的类型定义
- 组件中需要拆分的组件放在该组件目录下的 components 里

### 开发注意事项

- props 中的值若是 ref 类型，要用 computed 处理一下
- style 中不要使用 v-bind

## Breaking change list

- 暂无
