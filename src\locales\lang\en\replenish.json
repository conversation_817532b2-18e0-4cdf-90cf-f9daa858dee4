{"Friday": "Friday", "Monday": "Monday", "Saturday": "Saturday", "Sunday": "Sunday", "Thursday": "Thursday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "abnormal": "abnormal", "account": "account number", "accountInfo": "account information", "active": "active", "add": "add to", "addApplication": "Add Application", "addCluster": "Add cluster", "addComponent": "Add new components", "addComponents": "add component", "addCustomParameter": "Add custom parameters", "addFailed": "Add failed", "addHBaseTask": "Add Hbase task", "addHDFTask": "Add HDFS task", "addHiveTask": "Add Hive task", "addHost": "Add Host", "addHostToDefaultCluster": "Add the host to the default cluster", "addHostToExistingCustomCluster": "Add the host to an existing custom cluster", "addHostsToCluster": "Add a group of hosts to form a cluster and install the Agent service.", "addJob": "Add Job", "addK8s": "Add K8s", "addKerberosUser": "Add a Kerberos user", "addKey": "Add new keywords", "addNamespace": "Add Namespace", "addNewDashboard": "Add dashboard", "addNewHost": "Add host", "addParameter": "Add parameters", "addPeople": "Add personnel", "addPrimarySecondaryPlan": "Add primary and backup solutions", "addRack": "Add rack", "addRobot": "Add robot", "addRole": "Add role", "addRoleGroup": "Add role group", "addServiceType": "Add service type", "addStatus": "Add status", "addSuccess": "Added successfully", "addTask": "Add task", "addTime": "Add time", "addUser": "New User", "addUserSuccess": "New user successfully added", "addUsers": "Add User", "added": "Added", "adding": "Adding", "address": "address", "adds": "New", "advancedConfiguration": "Advanced configuration", "advancedSettings": "You can customize cluster allocation through advanced settings function", "alarmCategory": "Alarm category", "alarmContent": "Alarm content", "alarmCount": "Number of alarms", "alarmRecord": "Alarm Log ", "alarmRuleSetting": "Alarm rule setting", "alarmService": "Alarm service", "alarmTime": "Alarm time", "alertAutoRelease": "When the alarm does not require manual elimination, the alarm rule defaults to set the status to Released when it is not triggered after 2 rounds of patrol cycles.", "alertCategory": "Alarm category", "alertContent": "Alarm content", "alertInterval": "Alarm interval", "alertLevel": "Alarm level", "alertNotificationLevel": "Alarm notification level", "alertNotificationSettings": "Alarm notification settings", "alertPushByHighestSeverity": "When multiple levels are selected, the highest level of alarm push is triggered in the order of high risk, warning, danger, and general.", "alertStatus": "Alarm status", "alertTitle": "Alarm Title", "all": "all", "allocatedMemoryInSeconds": "Number of seconds in which memory has been allocated", "allocatedVCoresInSeconds": "Number of seconds that <PERSON><PERSON> has been allocated", "alphanumericCharactersDashUnderscoreDot": "Only for alphanumeric characters, -, _, and. Cannot start with a root.", "amend": "modify", "and": "and", "appliedConfigFileVersion": "The configuration file version for actual application", "applyWhenNoOtherRulesApply": "Apply when other rules are not applicable", "asParentPoolThenUserNameSubPool": "Use root {value} serves as the parent pool, and then uses a child pool that matches the username", "audit": "audit", "auditList": "Audit List", "auditLogAlarm": "If the current cluster performs the selected audit operation of the corresponding level, an audit log alarm is triggered. For the same operation, the alarm will be silenced and then sent again according to the alarm interval.", "auditLogAlert": "Audit log alarm", "authMethod": "Authentication method", "authenticationFile": "authenticated document", "authenticationType": "Certification type", "autoGenerate": "Automatically generated", "autoScaling": "Automatic expansion and contraction capacity", "automaticInspection": "Automatic inspection", "availableHosts": "Available hosts", "availableInstances": "Optional instances", "awaitingDeletion": "To be deleted", "awaitingDeployment": "To be deployed", "awaitingDeployment2": "Waiting for deployment", "awaitingExecution": "Pending execution", "awaitingExpansion": "To be expanded and contracted capacity", "awaitingRestart": "Waiting to restart", "awaitingRollingRestart": "Waiting for rolling restart", "awaitingScheduling": "Pending scheduling", "awaitingStart": "To be started", "awaitingStop": "To be stopped", "awaitingUpdate": "To be updated", "backgroundImage": "background image", "badRequest": "Request error (400)", "bandwidth": "bandwidth", "basicInformation": "Basic information", "batchDelete": "Batch deletion", "batchRestart": "Batch restart", "batchUnbind": "Batch unbinding", "beingDelet": "Deleting", "beingRestart": "Restarting in progress", "beingScal": "Expanding and shrinking capacity", "beingSchedul": "Scheduling in progress", "beingStart": "Starting", "beingStop": "Stopping", "beingUpdat": "Updating", "belongCluster": "Cluster to which it belongs", "belongingCluster": "Cluster to which it belongs", "bind": "binding", "bindHost": "Bind host", "bindK8s": "Bind k8s", "bindTime": "binding time", "bindingSuccess": "Binding successful", "blacklist": "Block", "blacklistSuccess": "Blocked successfully", "blacklistedNodeMessage": "This node is a blacklisted node and cannot be written to", "boundHostCannotBeDeleted": "The cluster that has already been bound to the host, installed components, or bound to K8s cannot be deleted", "browserUpgradeNotice": "The system recognizes that the current browser version is relatively low, and some page displays may be missing. Please upgrade the current browser version to 69 or above", "buildChart": "Build a chart", "button": "<PERSON><PERSON>", "buttonText": "Fill in the button text here", "byTime": "By time", "cancel": "cancel", "cancelScheduledKill": "Cancel scheduled killing", "cannotExceed100": "Cannot be greater than 100", "changesMayNotBeSaved": "Your changes may not be saved.", "charAndSymbolLimit": "Only supports numbers, English upper and lower case letters, underscore _ and English dash \"-\"", "charAndSymbolLimit2": "3-20 characters", "charLimit1To20": "1-20 characters, does not support input of characters other than Chinese and English", "charLimit3To20": "3-20 characters", "character": "character", "charactersLimitedToAlphanumeric": "Alphanumeric characters only", "chart": "chart", "chartQuantity": "Number of charts", "chartType": "Chart type", "checkAllHosts": "Check all hosts", "checkComponentConfig": "Please check if the configuration of each component is correct", "checkHost": "Check the host", "checkStartTime": "Start checking time", "checkStatus": "Check the status", "choosable": "Optional", "chooseTenant": "Select tenant", "clear": "Clear", "clientCertificate": "Client certificate", "clientToDataSource": "Client and Data Source", "clone": "clone", "cloneResourcePool": "Clone resource pool", "close": "closure", "cluster": "colony", "clusterAlertNotificationSettings": "Cluster alarm notification settings", "clusterCannotBeDeleted": "The current cluster (xxxx) cannot be deleted", "clusterDeletionSuccess": "Cluster deletion successful", "clusterHostList": "Cluster host list", "clusterInspection": "Cluster inspection", "clusterList": "Cluster List", "clusterMonitoring": "Cluster monitoring", "clusterName": "Cluster name", "clusterNameLength": "The maximum length of the cluster name should not exceed {number} digits", "clusterOverview": "Cluster Overview", "clusterRenameSuccess": "Cluster rename successful", "clusterResourceUsage": "Cluster resource usage", "clusterState": "Cluster status", "clusterUnbind": "Cluster unbinding", "coldDataClusterDirectory": "Cold data cluster directory", "coldDataHDFSName": "HDFS name where cold data is located", "coldDataList": "Cold Data List", "coldDataStorageCluster": "Cold data storage cluster", "collapse": "close", "collapseAll": "Put it all away", "commandTerminal": "command terminal", "complete": "complete", "completed": "Completed", "component": "assembly", "componentAbnormalSituation": "Component exceptions", "componentAdded": "Component added", "componentCannotBeDeleted": "The current component (xxxx) cannot be deleted", "componentConfigRecord": "Component configuration record", "componentConfiguration": "Component configuration", "componentCount": "Number of components", "componentDependency": "The current component ({value}) is dependent on ({values})", "componentDependency1": "The currently selected component ({value}) depends on ({string}). Please complete the {string} installation before installing {value};", "componentDependencyNotice": "The currently selected component (xxxx) depends on ({string}). Before installing xxxx, please complete the installation of xxx first;", "componentDetails": "Component Details", "componentHasDependencies": "The current component has dependencies and cannot be deleted!", "componentInstallation": "Component installation", "componentInstance": "Component instance", "componentLogAlert": "Component log alarm", "componentMonitoring": "Component monitoring", "componentName": "Component name", "componentNameFormat": "Component names only support lowercase letters and numbers, and must contain lowercase letters", "componentOverview": "Component Overview", "componentRunningStatusAlert": "Component running status alarm", "componentService": "Component Services", "componentServiceInspection": "Component service inspection", "componentStatus": "Component status", "componentStatusAlarm": "Trigger an alarm when the component status is abnormal or faulty within a specified interval.", "componentVersion": "Component version", "concurrency": "Number concurrencies", "configCategory": "Configuration category", "configDownload": "Configure Download", "configOperation": "Configuration operation", "configRollbackSuccess": "Configuration rollback successful", "configSaveAndPublishFailure": "Failed to save and publish configuration", "configSaveAndPublishSuccess": "Configuration saved and published successfully", "configSaveFailure": "Configuration save failed", "configSaveSuccess": "Configuration saved successfully", "configSnapshot": "Configure snapshot", "configurationInformation": "configuration information", "configurationRecord": "Configuration record", "configurationSet": "config set", "configurationSetNameLength1To20": "The configuration set name is 1-20 characters long", "configurationSource": "Configuration source", "configurationView": "Configuration View", "configureComponentParameters": "Configure component parameters", "configureServiceParameters": "Configure service parameters", "confirm": "determine", "confirmAddition": "Confirm Add", "confirmBlacklistDataNode": "Are you sure to block the DataNode node", "confirmCancelScheduledKill": "Are you sure to cancel the scheduled killing of this task", "confirmClearSelectedAlertRecords": "Do you want to choose to clear the selected alarm records?", "confirmComponentInstallation": "Please confirm whether the relevant components have been installed?", "confirmDelete": "Are you sure to delete?", "confirmDeleteCluster": "Are you sure you want to delete the current cluster (xxxx)?", "confirmDeleteComponentService": "Are you sure to delete the current component service (xxxx)?", "confirmDeleteCurrentTask": "Are you sure to delete the current task?", "confirmDeleteHost": "Are you sure to delete the current host (xxxx)?", "confirmDeleteJob": "Are you sure you want to delete the current Job ({value})?", "confirmDeleteKerberosUser": "Are you sure you want to delete the current Kerberos user? \nKeytabs generated in history will become invalid after deletion.", "confirmDeleteKeyword": "Delete keywords?", "confirmDeletePlacementRule": "Do you want to delete this placement rule", "confirmDeleteRetiredNode": "Are you sure to delete this retired node?", "confirmDeleteScheduleMode": "Whether to delete the plan mode", "confirmDeleteSelectedComponent": "Are you sure to delete the selected component?", "confirmDeleteSelectedHost": "Are you sure to delete the selected host?", "confirmDeleteUserLimit": "Do you want to remove the user restriction", "confirmExitEditing": "Are you sure you want to exit the current edit?", "confirmExportKeytabFile": "Do you want to export the Keytab file of the current Kerberos user?", "confirmHostUnbinding": "Are you sure to unbind the selected host?", "confirmInstallation": "Are you sure about installation?", "confirmKillTask": "Do you want to confirm the task of checking and killing?", "confirmLeavePage": "Please confirm whether to leave this page?", "confirmModifyHostname": "Are you sure to modify the hostname {value}?", "confirmNewPassword": "Confirm Password", "confirmRackDeletion": "Are you sure to delete the current rack? (Rack number xxx)", "confirmRollback": "Confirm rollback", "confirmRollbackToVersion": "Are you sure to roll back the configuration to this version (xxxx)?", "confirmRollingRestart": "Do you want to scroll and restart", "confirmSubmission": "Are you sure to submit?", "confirmSubmitForScaling": "After submission, the system will expand and contract, which may take a few minutes.", "confirmToDeleteTenant": "Are you sure to delete the tenant", "confirmUnbindHost": "Are you sure to unbind the host (xxxx) from the cluster?", "confirmUnbindK8sCluster": "Are you sure to unbind k8s {value} of this cluster?", "confirmUnblacklistDataNode": "Are you sure to cancel the blacklist of this DataNode node", "confirmUpdateSelectedComponent": "Do you want to update the selected components?", "confirmationPrompt": "Confirmation prompt", "connectionAlarm": "When the host is unable to establish a network connection with the target host or the connection is interrupted, an alarm is triggered. The values of this item are 0 and 1,0, indicating that the connection has not been established or has been interrupted. 1 indicates that the connection has been established", "connectionError": "Connection error (xxxx)!", "connectionMethod": "Method", "connectivityTestFailure": "Test connection failed, please check the configuration", "connectivityTestSuccess": "Test connection successful, can continue operation", "containerName": "Container name", "copySuccess": "Copy successful", "countSmallFilesInDirectory": "Count the number of small files in this directory, which can be customized.", "cpuResources": "CPU resources", "cpuUsageAlarm": "An alarm is triggered when the host's CPU usage reaches the set threshold.", "create": "newly build", "createClusterOneComponentModifiable": "When creating a cluster, only one component service can be added at a time; the configuration component parameters are all passed in from the backend and pre-filled according to the default values, and support modification", "createNewKerberosUser": "Create a new Kerberos user", "createOrExpandCluster": "Using hosts to create new clusters or expand existing clusters; If the cluster uses Kerberos authentication, please ensure that the Kerberos software package is installed on the new host, otherwise the services on the new host will not run.", "createPermission": "Create permissions", "createPermissions": "create a privilege", "createPlacementRule": "Create placement rules", "createPoolIfNotExist": "If the pool does not exist, create it", "createResourcePool": "Create resource pool", "createScheduleMode": "Create Plan Mode", "createScheduleRule": "Create plan rules", "createSubPool": "Create subpool", "createTenant": "Create tenant", "createTime": "creation time", "createUserLimit": "Create user restrictions", "cruxword": "key word", "currentCluster": "Current cluster", "currentComponent": "current component", "currentComponentVersion": "Current component version vxxx", "currentConfiguration": "currently allocated", "currentCustomVersionNumber": "Current custom version number", "currentHost": "Current host", "currentOperation": "Current operation", "currentRole": "Current role", "currentRoles": "Existing roles", "custom": "custom", "customChart": "Custom charts", "customComponentName": "Custom Component Name", "customComponentNameMaxLength": "The custom component name can be entered up to xxxx characters!", "customConfigExists": "This custom configuration item already exists", "customDeployment": "Custom deployment", "customName": "Custom Name", "customParameter": "Custom Parameters", "customRoleAllocation": "Custom role assignment", "customSize": "by custom size", "customSmallFileSizeSettings": "Custom settings for small files", "dailyInspection": "Once a day", "dailyRepeatFromXxToXxFirstEffectiveAtXxx": "Repeats every day from {beginTime}:00 to {endTimeStr}:00 (CST), first starting at {startTime}.", "dangerous": "danger", "dashboard": "Dashboard", "dashboardName": "Dashboard Name", "dataCollecting": "During data collection", "dataLake": "data lake", "dataPlatform": "Data center", "date": "date", "day": "day", "days": "day", "debugLevel": "DEBUG(hint)", "defaultSchedulingPolicy": "Default scheduling policy", "defaultSettings": "default setting", "defaultUserLimit": "User default restrictions", "defaultValue": "Default value", "delete": "delete", "deleteAccount": "Delete this employee account?", "deleteCurrentCustomParameter": "Whether to delete the current custom parameters", "deleteCurrentPrimarySecondaryPlan": "Whether to delete the current active and backup plans", "deleteCurrentRole": "Do you want to delete the current role", "deleteCustomConfigAfterSave": "After saving and publishing the configuration, delete the custom configuration", "deleteDependentComponentFirst": "Please delete the components that depend on it before deleting", "deleteExistingHistoricalDataInBackupCluster": "Delete the existing historical data of the backup cluster under the plan", "deleteFailed": "Delete failed", "deleteProgressDetails": "Delete progress details", "deleteRoleGroupConfirmation": "Whether to delete the role group", "deleteSuccess": "Delete successfully", "deleted": "Deleted", "deleting": "Deleting in progress", "dependencyCheck": "Dependency checking", "dependentComponents": "Dependent components", "deployFailed": "Deployment failed", "deployImmediately": "Deploy now", "deployedHostCount": "Number of deployed hosts", "deploying": "In deployment", "deploymentMethod": "Deployment method", "deploymentProgressDetails": "Deployment progress details", "description": "describe", "designation": "name", "diagnosticReport": "Diagnostic report", "dingding": "DingTalk DingTalk", "dingdingRobot": "DingTalk Robot", "directoryAddress": "Directory Address", "directoryKeywords": "Catalog keywords", "disable": "deactivate", "disableMaintenanceMode": "Close maintenance mode", "disabledTenantCannotLoginSystemNoOperationPermissions": "Disabled tenants will not be able to log in to the system and do not have operational permissions on the system", "disasterRecoveryTaskCheck": "Disaster recovery task inspection", "disasterRecoveryTaskInspection": "Disaster recovery task inspection", "disk": "disk", "diskReadSpeedAlarm": "When the disk read rate of the host reaches the set threshold, an alarm is triggered in MB/s", "diskSystem": "disk system", "diskUsage": "Disk usage", "diskWriteSpeedAlarm": "An alarm is triggered when the host's disk write rate reaches the set threshold, in MB/s.", "doNotRecommendClearIfUnprocessed": "If the alarm is not processed, it is not recommended to clear it", "dockerDeployment": "Docker deployment", "download": "download", "downloadLog": "Download logs", "downloadNow": "Download Now", "downloadTemplate": "Download Template", "dropdown": "Drop down box", "dueToKubernetesBinding": "Due to the binding of cluster {value} on Kubernetes, please unbind and delete again", "duration": "duration", "dynamicResourceAllocation": "Dynamic resource allocation", "edit": "edit", "editDashboard": "Edit dashboard", "editJob": "Edit Job", "editK8s": "Edit k8s", "editPlacementRule": "Edit placement rules", "editRack": "<PERSON>", "editResourcePool": "Edit resource pool", "editRoleGroup": "Edit role group", "editScheduleMode": "Edit Plan Mode", "editSuccess": "Edited successfully", "editTenant": "Edit tenant", "editUser": "Edit User", "editUserLimit": "Edit user restrictions", "editUserSuccess": "Successfully edited user", "editValue": "xx edit value", "email": "Mail", "emailAddress": "Email address:", "emailConfigurationTestResult": "Email configuration test results", "emailForTestResult": "The test results will be sent to your email in the form of an email. Please enter your email address.", "emailMaxLength": "Email length not exceeding 30 characters", "emailSaved": "Email address saved", "employeeName": "Employee name", "enable": "Enable", "enableMaintenanceMode": "Activate maintenance mode", "enableTenantWillRestoreLoginSystemAndOperationPermissions": "The enabled tenant will restore login and operational permissions to the system", "enabledState": "Open status", "end": "end", "endDate": "end date", "endTime": "End time", "enterSecretKey": "Please enter the key", "enterVerification": "Please enter the verification code", "enterVerificationCode": "To prevent accidental operations, please enter the verification code into the input box", "equalTo": "equal", "errorLevel": "ERROR (Danger)", "errorLog": "error log", "eventTime": "Event Time", "everyDay": "Every day", "everyday": "everyday", "example": "Example", "executionCycle": "Execution cycle", "executionFailure": "Execution failed", "executionInProgress": "In progress", "executionResult": "Execution result", "executionSuccess": "Executed successfully", "executionTime": "Execution time", "executor": "Execute user", "existentialDependence": "There are component dependencies (xxxx)", "existingConfigurationSet": "There is already a configuration set", "existingResources": "Existing resources", "exitWithoutSaving": "After exiting, the current modified content will not be saved", "expand": "open", "explain": "explain", "export": "Export", "exportKeytab": "Export Keytab", "failure": "fail", "fairSchedulingBasedOnCpuAndMemory": "Fairly schedule resources based on CPU and memory. \n(suggestion)", "fairSchedulingBasedOnMemory": "Only allocate resources based on memory fairness.", "fairShareDurationBeforePreemption": "The number of seconds a resource pool is in fair share state before it attempts to preempt a container to obtain resources from other resource pools.", "fairSharePreemptionThreshold": "Fair share preemption threshold", "fairSharePreemptionTimeout": "Fair share preemption timeout", "fatalLevel": "FATAL (High Risk)", "fault": "fault", "feishu": "anonymous letter", "fetchingInformation": "Getting information", "fifo": "fifo.", "fileDesignation": "File Name", "fileName": "file name", "fileSize": "file size", "fileSizeLimit": "The file size cannot exceed 100kb", "fileStatus": "File status", "firstSyncOfTargetTableOnTaskStartup": "Perform full synchronization of the target table upon initial task initiation", "format2To16": "2-16 bits", "format2To16ChineseAndEnglishLetters": "2-16 characters, composed of Chinese and English letters, supporting uppercase and lowercase letters as well as spaces", "format6To16ChineseAndEnglishLetters": "6-16 characters, composed of Chinese and English letters, supporting uppercase and lowercase letters as well as spaces", "format6To16DigitsLettersAndEnglish": "The 6-16 characters consist of numbers, letters, and English. The characters support _ - and cannot start with _", "format6To20DigitsAndEnglishLetters": "6-20 digits, composed of numbers and English letters, supporting uppercase and lowercase English letters", "freezeAccount": "Do you want to freeze the employee's account?", "freezeSuccess": "Freeze successful", "friday": "Friday", "frozen": "frozen", "fullLog": "Complete log", "fullTable": "full scale", "general": "commonly", "gettingSyncTables": "Retrieve synchronization table", "globalException": "[Global exception]", "goToClusterList": "Go to cluster list", "good": "good", "greaterThan": "greater than", "hbaseTask": "Hbase task", "hdfsTask": "HDFS task", "hideAllDescriptions": "Hide all descriptions", "highRisk": "high risk", "hint": "Prompt language", "historicalInspectionReports": "Historical Inspection Report", "hiveTask": "Hive tasks", "host": "host", "hostAdditionFailure": "Host addition failed, please check the relevant parameters", "hostAdditionProgress": "Host addition progress", "hostAdditionStatus": "Host added status", "hostAlias": "host alias", "hostAliasCharacterLimit": "Host alias supports 1-50 characters", "hostAliasSupport": "Host alias supports 1", "hostBinding": "host binding", "hostBindingFailure": "Host binding failed", "hostConfigurationIssue": "Host configuration issues", "hostConnectivityAlert": "Host connectivity alarm", "hostCount": "Number of hosts", "hostCounts": "Number of hosts", "hostCpuAlert": "Host CPU alarm (%)", "hostDetail": "Host details", "hostDiskInAlert": "Host disk I alarm", "hostDiskOutAlert": "Host disk O alarm", "hostInfo": "Host information", "hostInspection": "Host inspection", "hostIp": "Host IP", "hostIpAndPort": "host ip port", "hostList": "Host list", "hostMemoryAlert": "Host memory alarm (%)", "hostMonitoring": "Host monitoring", "hostName": "Host name", "hostNetworkIn": "Host network I (MB) alarm", "hostNetworkOut": "Host network O (MB) alarm", "hostOccupiedCannotDelete": "The currently selected host is occupied and cannot be deleted!", "hostResources": "Host resources", "hostRole": "Host role", "hostRoleList": "Host role list", "hostSearch": "Host Search", "hostUnbindingResult": "After unbinding, the host returns to the default resource pool and can be deleted", "hostUnbound": "The host has been unbound", "hostnameRule": "The host name starts with a lowercase letter and consists of lowercase letters, numbers, and \"-\"", "hostsSelected": "Selected host", "hotColdDataGrading": "Cold and hot data classification", "hotDataClusterDirectory": "Hot data cluster directory", "hotDataHDFSName": "HDFS name where the hot data is located", "hotDataList": "Hot Data List", "hotStorageCluster": "Hot data storage cluster", "hours": "hour", "httpVersionNotSupported": "HTTP version not supported (505)", "ifPoolDoesNotExistCreateIt": "If the pool does not exist, create it.", "inPoolAttemptsToPreemptContainers": "The number of seconds a pool remains in its minimum share before the pool attempts to preempt a container to obtain resources from other pools. \n(optional)", "inProgress": "have in hand", "infoLevel": "INFO (prompt)", "initializeFrom": "Initialize from", "inputClusterName": "Please enter the cluster name", "inputClusterToDelete": "Please enter the cluster name to be deleted", "inputConfigInfo": "Please enter configuration information", "inputCustomComponentName": "Please enter the custom component name", "inputCustomComponentToDelete": "Please enter the name of the custom component you want to delete", "inputEmail": "Please enter email address", "inputEmployeeName": "Please enter the employee's name", "inputField": "Input box", "inputHostToDelete": "Please enter the host name to delete", "inputHostToUnbind": "Please enter the name of the host to unbind", "inputNewPassword": "Please enter new password", "inputNodeCount": "Please enter the number of nodes", "inputNumberOfHosts": "Please enter the number of hosts", "inputOriginalPassword": "Please enter the original password", "inputPassword": "Please input a password", "inputPhoneNumber": "Please enter your phone number", "inputPrivilegedUser": "Please enter privileged user", "inputQuery": "Enter query", "inputRackNumber": "Please enter the rack number", "inputRoleNodeCount": "Please enter the number of role nodes", "inputSSHPortNumber": "Please enter the SSH port number", "inputUserName": "enter one user name", "inspectionConfiguration": "Inspection configuration", "inspectionCycle": "Inspection cycle", "inspectionDescription": "Inspection Item Description", "inspectionDuration": "Inspection duration", "inspectionDurationLimit": "minutes, 0 means unlimited", "inspectionDurationLimitMinutes": "Continuous inspection restrictions", "inspectionItemDescription": "Inspection item description", "inspectionItemResult": "Inspection item results", "inspectionItemSuggestion": "Suggestions for inspection items", "inspectionItems": "Inspection matters", "inspectionProgress": "Inspection progress", "inspectionReport": "Inspection Report", "inspectionStartTime": "Inspection start time", "inspectionStatus": "Inspection status", "installAgentOnNewHost": "Allow you to install the agent on the new host. You can keep the new host for future addition to the cluster, or you can add the new host to an existing cluster.", "installComplete": "Installation completed", "installComponent": "Install components", "installFailed": "Installation failed", "installImmediately": "Install now", "installationInformation": "Installation information", "installationOrder": "Installation sequence", "installing": "Installing", "instance": "example", "instanceAddress": "Instance address", "instanceConfiguration": "Instance configuration", "instanceInformation": "Instance information", "instanceName": "Instance name", "instanceStatusHistory": "Instance Status History", "instancesWithinRoleGroup": "Instance within role group", "integratedDataLakes": "Integrated data lake", "interval10Min": "Every 10 minutes", "interval12Hours": "12 hours", "interval15Min": "Every 15 minutes", "interval1Day": "1 day", "interval1Hour": "1 hour", "interval2Hours": "2 hours", "interval30Min": "Every 30 minutes", "interval30Minutes": "30 Minutes", "interval3Hours": "Every 3 hours", "interval5Min": "Every 5 minutes", "interval6Hours": "6 hours", "interval7Days": "7 days", "invalidEmail": "Email address is wrong, please re-enter", "invalidPhoneNumber": "The format of the phone number is incorrect", "ipAddress": "IP address", "isAdmin": "Do you have admin privileges?", "isClusterAlertAndHealthCheckDisabled": "When enabled, the current cluster does not perform alarm rules and health inspection verification items.", "isDelete": "Do you want to delete {value}?", "isDisableThisTenantAccount": "Do you want to deactivate the tenant account", "isEnableThisTenant": "Whether to enable this tenant", "isResetTheTenantLoginAccountPassword": "Do you want to reset the tenant's login account password", "isSelected": "selected", "jobConfiguration": "Job configuration", "k8SCreatePermission": "K8S creation permission:", "k8SList": "K8S List", "k8sAdditionSuccess": "K8s added successfully", "k8sInfo": "K8s information", "k8sName": "K8s Name", "k8sNamespace": "K8s namespace", "keyword": "keyword", "keywordSearch": "Keyword search", "keywordSetting": "Keyword settings", "keywords": "key word", "kill": "Check and kill", "kubernetesClusterAddress": "Kubernetes cluster address", "kubernetesDeployment": "Kubernetes deployment", "kubernetesName": "Kubernetes Name", "lastDay": "In the past day", "lastHour": "Nearly an hour", "lastInspection": "last inspection", "lastModifiedTime": "last modified time", "lastRestartTime": "Recent restart time", "lastTaskDetectionTime": "Last task detection time", "lastWeek": "In the past week", "latelyModifiedTime": "Last modified time", "latestConfiguration": "Latest configuration", "latestVersion": "Current latest version", "lessThan": "less than", "lessThan001": "Less than 0.01", "lessThanBlockSize": "smaller than block size", "limitOfApplicationMaster": "Limits the proportion of the fair share of the resource pool that can be used to run the ApplicationMaster. \nFor example, if set to 1.0, the ApplicationMaster in the leaf pool can use up to 100% of its fair share of memory and CPU. \nA value of -1.0 disables monitoring of ApplicationMaster shares. \nThe default value is 0.5.", "lineChart": "Line chart", "link": "Link", "list": "list", "logCollection": "Log collection", "logKeywordAlarm": "If the current cluster log matches the selected keyword, a log alarm will be triggered. The same keyword will be silenced and then sent again according to the alarm interval.", "logKeywordAlert": "Log keyword alarm", "logLevel": "log level", "logLevelAlarm": "Trigger an alarm when a component generates logs of a specified level within a specified interval (the same interval may contain multiple logs).", "logList": "Log List", "logTimeRange": "Log time period", "login": "Sign in", "loginAccount": "Login account", "loginPassword": "Login password", "logout": "Log out", "logs": "log", "lowercaseLettersNumbersOnly": "Contains only lowercase letters, numbers, hyphens -, and dots Left slash/, maximum length limit of 20", "mail": "mail", "maintenanceMode": "maintenance mode", "maintenanceModeActivationFailed": "Maintenance mode activation failed", "maintenanceModeExemption": "When maintenance mode is turned on, this component will not be used as a check item for alarm rules and health inspections.", "maintenanceModeNote": "When maintenance mode is enabled, the host will not be used as an alarm rule or a verification item for health checks.", "manageAllClustersOrSingleCluster": "You can manage all clusters within your permission range uniformly, or choose a single cluster for easy and efficient management.", "manageNamespace": "Namespace management", "management": "manage", "mandatorySelection": "This is a mandatory option", "manualInspection": "Manual inspection", "max": "maximum", "maxInput20Characters": "Up to 20 characters can be entered", "maximumNumberOfCpuAndMemory": "The maximum number of CPUs and memory available for this pool. This has priority over weight based sharing. (Optional)", "maximumResources": "Maximum number of resources", "maximumRunningApplications": "Maximum number of running applications", "maximumRunningApplicationsPerUser": "Maximum number of running applications", "memory": "Memory", "memoryUsage": "Memory usage", "memoryUsageAlarm": "Trigger an alarm when the host's memory usage reaches the set threshold", "message": "news", "migration": "migrate", "migrationBackup": "Migration backup", "migrationSuccessful": "Migration successful", "minimumLogLevel": "Minimum log level", "minimumNumberOfCpuAndMemory": "The minimum amount of CPU and memory available for this pool. This takes priority over weight based sharing. (Optional)", "minimumResources": "Minimum number of resources", "minimumSharedPriorityTimeout": "Minimum sharing priority timeout", "minutes": "minute", "missingOrInvalidServiceParameters": "The configuration service parameter list has mandatory fields that are not filled in or do not meet the requirements!", "mobile": "mobile phone", "modifyEmailAddress": "Modify email address", "modifyEmailFailure": "Failed to modify email address", "modifyHostnameWarning": "Modifying the host name will cause the running components to become unusable, and after modifying the host name, the components need to be restarted!", "modifyMobileNumber": "Change phone number", "modifyParametersWhenDeployingCluster": "Modify parameters when deploying a cluster", "modifyPassword": "Change Password", "modifyPermission": "Modify permissions", "modifyPhoneNumberFailure": "Failed to modify phone number", "modifyPhoneNumberSuccess": "Mobile phone number modified successfully", "modifyReason": "Reason for modification", "monday": "Monday", "monitorChart": "Monitoring chart", "monitorNodeCount": "Number of monitoring nodes", "monitoring": "monitor", "monthly": "per month", "monthlyInspection": "once a month", "monthlyRepeatFromDayXxToDayXxFromXxToXxFirstEffectiveAtXx": "Repeats from {beginTime}:00 to {endTimeStr}:00 (CST) on day {dayOfMonth0} to {dayOfMonth1} of each month, taking effect first at {startTime}.", "more": "more", "most": "to", "mustNotStartWithRoot": "Cannot start with a root.", "mutualTrustHelpDocument": "Mutual trust help document", "mysqlMasterSlaveConfigurationPrompt": "Tip: Please confirm whether the primary and backup databases have completed the MySQL primary and backup configuration, such as enabling binlog and specifying the server-side", "name": "full name", "namespaceList": "Namespace list", "namespaceName": "namespace name", "namespaceResource": "Namespace resource", "namingRules": "The name consists of Chinese characters, letters, numbers, underscores, and the hyphen '-'. It cannot start or end with a hyphen or underline.", "networkError": "Network error(502)", "networkInputAlarm": "When the network input rate of the host reaches the set threshold, an alarm is triggered in MB/s", "networkOutputAlarm": "An alarm is triggered when the network output rate of the host reaches the set threshold, the unit is MB/s", "networkTimeout": "Network timeout (504)", "newConfigFileVersion": "The latest configuration file version requires component restart to take effect", "newCustomConfig": "This new custom configuration", "newPassword": "New Password", "nextDayAtMidnight": "The next day 0", "nextStep": "Next step", "no": "no", "noChartsAvailable": "No charts available at the moment", "noChineseOrEmojis": "Not supporting Chinese and emoticons", "noContent": "No content available at the moment", "noData": "No data yet", "noDatas": "No data available", "noLeadingOrTrailingSpaces": "Cannot start and end with spaces", "noLimit": "X (currently unrestricted)", "noPermission": "No permission yet", "noRequired": "Not mandatory", "nodeCount": "Number of nodes", "nodeName": "Node Name", "nodeScheduling": "Node scheduling", "nodeStillHasReplicaData": "There are still {value} replica data in this node that have not been migrated yet. Are you sure you want to delete this retired node?", "nodeType": "Node type", "none": "Currently unavailable", "normal": "normal", "notDeployed": "Not deployed", "notExecuted": "unexecuted", "notStarted": "Not started yet", "notes": "Remark", "notificationMethod": "Notification method", "offline": "off-line", "oneClickDeployment": "One-click deployment", "oneMonthDataLimit": "Only data from the past month can be filtered and queried", "online": "on-line", "onlyEnglishInputAllowed": "Only English, spaces, and numbers can be entered, with a maximum length of 50 characters", "onlyEnglishNumbersBreveUnderscore": "Only English numerical characters, dashes, and underscores", "onlyPositiveIntegersGreaterThanOrEqualToZero": "Only supports positive integers greater than or equal to 0", "onlyPositiveIntegersGreaterThanZero": "Only supports positive integers greater than 0", "onlyPositiveIntegersSupported": "Only supports input of positive integers", "open": "turn on", "operation": "operation", "operationAttribute": "Operation attribute", "operationContent": "Operation content", "operationHistory": "Operation history", "operationHistoryList": "Operation history list", "operationName": "Operation name", "operationStatus": "Operation status", "operationSuccess": "Operation successful", "operationTime": "Operating time", "operationType": "Operation type", "operator": "Operator", "optional": "Optional", "optionalSameAsSourceIfEmpty": "Optional, the empty time directory is consistent with the main cluster directory", "originalPassword": "Original password", "parameterConfiguration": "Parameter configuration", "parameterName": "Parameter Name", "parameterSetting": "Parameter settings", "password": "password", "passwordConnection": "Account password", "passwordLength6To20": "Password length is 6 to 20 characters", "passwordMinimumLength8": "The minimum password length is 8", "passwordMismatch": "The password entered twice is inconsistent", "passwordModificationFailed": "Failed to change password Server error", "passwordModificationSuccess": "Password changed successfully, please log in again", "passwordsDoNotMatch": "The passwords entered twice do not match. Please re-enter", "pastXMinutes": "Past xx minutes", "pendingRun": "To be run", "percentageOfBlockSize": "Percentage by block size", "perceptionStatus": "Perceptual state", "permissionControl": "Permission control", "permissionEdit": "Permission modification", "permissionModificationSuccess": "Permissions modified successfully", "permissionTenants": "Permissions tenant", "phone": "Telephone", "phoneNumber": "phone number", "phoneNumbers": "Phone number:", "physicalSpace": "Physical space", "plSelectCluster": "Please select a cluster", "placementRules": "placement rules", "platformBasicEnvironment": "Platform infrastructure environment", "platformInspection": "Platform intelligent inspection", "pleaseAddRobot": "Please add a robot", "pleaseEnter": "Please enter", "pleaseEnterAddress": "Please enter the address, such as 192.68.12 [1-200]", "pleaseEnterBandwidth": "Please enter bandwidth", "pleaseEnterConcurrency": "Please enter the concurrent number", "pleaseEnterConfigurationName": "Please enter configuration name", "pleaseEnterConfigurationSetName": "Please enter the configuration set name", "pleaseEnterCorrectFormat": "Please enter the correct format", "pleaseEnterCorrectHostToUnbind": "Please enter the correct host name to unbind", "pleaseEnterCorrectVerificationCode": "Please enter the correct verification code", "pleaseEnterDashboardName": "Please enter the dashboard name", "pleaseEnterDescription": "Please enter a description, limited to 200 characters", "pleaseEnterDurationLimit": "Please enter a persistent limit", "pleaseEnterHDFTargetDirectory": "Please enter the HDFS sync directory", "pleaseEnterIPAddress": "Please enter IP address", "pleaseEnterK8sName": "Please enter the name of K8s", "pleaseEnterKeyword": "Please enter keywords", "pleaseEnterLoginAccount": "Please enter your login account", "pleaseEnterLoginPassword": "Please enter your login password", "pleaseEnterName": "Please enter a name", "pleaseEnterNamePhoneNumberOrAccount": "Please enter your name/phone number/account", "pleaseEnterNamespaceName": "Please enter namespace name", "pleaseEnterPoolName": "Please enter a pool name", "pleaseEnterQuerySQL": "Please enter the query SQL", "pleaseEnterServiceAccount": "Please enter serviceAccount", "pleaseEnterServiceType": "Please enter the service type", "pleaseEnterTaskName": "Please enter the task name", "pleaseEnterTenantName": "Please enter tenant name", "pleaseEnterTenantResponsiblePerson": "Please enter the tenant responsible person", "pleaseEnterTheCorrectIPAddress": "Please enter the correct ip address", "pleaseEnterThresholdValue": "Please enter the corresponding threshold", "pleaseEnterUserName": "Please enter the user name", "pleaseFillInConfigurationSet": "Please fill in the configuration set", "pleaseInput": "Please enter", "pleaseSelect": "Please choose", "pleaseSelectAutoClear": "Please choose whether to automatically clear", "pleaseSelectBelongingCluster": "Please select the cluster to which you belong", "pleaseSelectConfigurationSet": "Please select a configuration set", "pleaseSelectCurrentRole": "Please select the current role", "pleaseSelectDataSource": "Please select a data source", "pleaseSelectExistingConfigurationSet": "Please select an existing configuration set", "pleaseSelectInitializationConfigurationSet": "Please select the initialization configuration set", "pleaseSelectIntervalTime": "Please select interval", "pleaseSelectMonthlyTriggerTime": "Please select the monthly trigger time", "pleaseSelectNamespace": "Please select namespace", "pleaseSelectPlacementRule": "Please select a placement rule", "pleaseSelectPrimaryBackupScheme": "Please provide the primary and backup plans", "pleaseSelectScheduledKillTime": "Please select the task scheduled killing time", "pleaseSelectStatus": "Please select the status", "pleaseSelectTime": "Please select a time", "pleaseSelectUpgradeVersion": "Please select the upgraded version", "pleaseSelectWeeklyTriggerTime": "Please select the weekly trigger time", "pleaseUploadKubernetesConfigFileAndSizeMustNotExceed100kb": "Please upload the kubernetes config file, and the size must not exceed 100kb", "podName": "pod name", "pool": "pool", "poolName": "Pool name", "positiveIntegerOnly": "[xxx] Node can only input positive integers", "preemption": "seize", "previousStep": "Previous step", "primaryBackupClusterScheme": "Belonging to the primary and standby cluster solution", "primaryBackupScheme": "Primary and backup plans", "primaryClusterName": "Main cluster name", "primaryClusterTitle": "Primary cluster name", "primarySecondaryManagement": "Primary and backup management", "primarySecondaryPlanName": "Main and backup plan name", "primarySecondaryPlanTitle": "Main and backup plan name", "primarySecondarySwitch": "Switching", "priority": "priority", "priorityExists": "Priority already exists", "privilegedUser": "privileged user", "processCpuUsage": "CPU usage of the process", "processMemoryUsage": "Memory usage of the process", "productDoc": "Product documentation", "productTechnicalWhitePaper": "Product Technical White Paper", "productUserManual": "Product User Manual", "progress": "schedule", "publicKeyConnection": "Public key", "publicKeyConnectionInstructions": "To add a host using the public key link method, you need to paste the current system key into the host certification file you are about to add. The host certification file is usually \"~/.ssh/authorized_keys\". Please make sure to complete the above operations before adding the host; currently \nSystem key download", "qiyeweixin": "WeCom", "qiyeweixinRobot": "Enterprise WeChat Robot", "query": "query", "queueNameExists": "The queue name already exists", "rack": "frame", "rackDeletionWarning": "After deleting a rack, the deployment relationship between the original rack and the host will be released.", "rackLabel": "frame", "rackManagement": "Rack management", "rackNumber": "Rack number", "reAdd": "re-add", "reAddAfterDeletion": "After deletion, the primary and backup schemes can be re added.", "reExecute": "Re-execute", "reasonForModification": "Reason for modification", "recentlyUsed": "Recently used", "recipient": "addressee", "recommendChrome": "For a better user experience, it is recommended to use Google Chrome browser", "recommended": "proposal", "reenterClusterName": "To prevent accidental operations, please enter the cluster name again", "reenterCustomComponentName": "To prevent accidental operation, please enter the custom component name again", "reenterHostName": "To prevent accidental operation, please enter the host name again", "reenterPassword": "Please enter password again", "reenterRackNumber": "To prevent accidental operation, please enter the rack number", "refreshingDynamicResourcePools": "Refresh dynamic resource pool", "relatedCluster": "Related clusters", "relatedToSharingWithOtherPools": "Resource sharing related to other pools", "released": "Lift", "remark": "remarks", "rename": "rename", "reportDetails": "Report details", "requestNotFound": "Request error (404)", "requestTimeout": "Request timeout (408)", "requiredField": "This is a required field", "requiresManualClear": "Do you need to manually delete it?", "reset": "reset", "resetEmployeePassword": "Do you want to reset this employee's password?", "resetPassword": "reset password", "resetPasswordSuccess": "Password reset successful", "resourceAllocation": "resource allocation ", "resourceAllocationLimit": "Resource allocation restrictions:", "resources": "resources", "responsiblePerson": "person in charge", "restart": "<PERSON><PERSON>", "restartBeforeStopDependencies": "It is recommended to stop the components that depend on it before restarting", "restartComponentForChangesToTakeEffect": "After saving and publishing, the component needs to be restarted for configuration to take effect", "restartComponentService": "Do you want to restart the current component (xxxx) service?", "restartFailed": "Restart failed", "restartList": "Restart List", "restartProgressDetails": "Restart progress details", "restartRoleService": "Do you want to restart the current role (xxxx) service?", "restartSelectedRoleService": "Do you want to restart the selected role service?", "restartSyncTask": "Do you want to restart the selected components?", "restartableAfterTermination": "After termination, the synchronization task can be restarted", "restarting": "Restarting in progress", "restoreDefaultValues": "Restore defaults", "return": "return", "returnToClusterList": "Return cluster list", "returnToComponentList": "Return component list", "returnToHostList": "Return host list", "returnToList": "Return to List", "rexNamespaceName": "The name consists of Chinese characters, letters, numbers, underscores, and the hyphen '-'. It cannot start or end with a hyphen or underline.", "role": "role", "roleAllocationInstructions": "You can customize role assignments for new services here, but be aware that performance will be affected if assignments are incorrect (for example, too many roles assigned to a host).", "roleConfiguration": "Role configuration", "roleGroup": "Role group", "roleGroupName": "Role group name", "roleInstance": "Role instance", "roleList": "Role List", "roleLog": "Character Log", "roleLogFile": "role log file", "roleName": "Character name", "roleScope": "Role scope", "roleType": "Role type", "rollingRestart": "Rolling restart", "rollingRestartComponent": "Do you want to scroll and restart the {value} component", "rollingRestartDetails": "Rolling restart details", "rollingRestartProgressDetails": "Rolling restart progress details", "rollingRestarting": "Rolling restart in progress", "running": "Running", "runningComponentCannotBeDeleted": "Due to the current component being running, it cannot be deleted temporarily", "runtime": "Running time", "runtimes": "Running time", "saturday": "Saturday", "save": "save", "saveAndPublish": "Save and publish", "saveAndPublishCurrentConfig": "Do you want to save and publish the current configuration file?", "saveSuccess": "Saved successfully", "scaleFailed": "Expansion and contraction failed", "scaleUpOrDown": "Expansion and contraction capacity", "scaleUpOrDownProgressDetails": "Details of expansion and contraction progress", "scaling": "Expanding and shrinking", "scheduleMode": "Planning mode", "scheduledKill": "Regularly investigate and kill", "scheduledKillTime": "Time for scheduled inspections and killings", "scheduling": "Scheduling", "schedulingFailed": "Scheduling failed", "schedulingPolicy": "Planning strategy", "schedulingProgressDetails": "Scheduling progress details", "secondaryClusterName": "Standby cluster name", "secondaryClusterTitle": "Backup cluster name", "select": "choose", "selectAll": "Select All", "selectAllSelectedRoles": "View all selected roles (values)", "selectAtLeastOneHost": "Select at least one host", "selectAuthenticationType": "Please select certification type", "selectCluster": "Select cluster", "selectComponent": "Please select a component", "selectComponentName": "Please select a component name", "selectCustomComponentName": "Please select the name of the custom component you need to expand or shrink.", "selectDataRange": "Select data range", "selectDataSource": "Select data source", "selectDeploymentMethod": "Choose deployment method", "selectFile": "Please select a file", "selectFileName": "Please choose a file name", "selectHost": "Select Host", "selectInspectionCycle": "Please select the inspection cycle", "selectInstance": "Select instance", "selectK8s": "Please select K8s", "selectK8sCluster": "Please select K8s cluster", "selectManualInspectionType": "Please select the manual inspection execution type", "selectNamespaceForClusterInKubernetes": "Select the namespace used by the current cluster in this Kubernetes (this option can be empty, if it is empty, the system will automatically create a new namespace in Kubernetes). Please ensure that the selected namespace exists and is not in use", "selectNode": "Please select node", "selectNodeType": "Select node type", "selectRoleName": "Please select a role name", "selectServiceType": "Please select the service type you want to add. Only one component can be installed at a time", "selectServiceTypeToAdd": "Please select the type of service to add", "selectStartTime": "Please choose a start time", "selectTime": "Select time", "selectUpgradeVersion": "Select upgrade version", "selectVersion": "Please select version", "selectXToYHosts": "Select at least x units, select at most x units", "selected": "Selected", "selectedComponent": "Selected components", "selectedComponents": "Selected components", "selectedConfigurationSet": "Selected configuration set", "selectedHost": "Selected host", "selectedRole": "Selected role", "selectedTablesOnly": "Only check the table", "selectedXHosts": "Channel xx has been selected", "sendTestMessage": "Send test message", "serialNumber": "Serial number", "serious": "serious", "serverError": "Server error (500)", "serverException": "Server abnormality, please contact the administrator", "service": "service", "serviceComponents": "Service components", "serviceNameAndPort": "ServiceName+port", "serviceNotImplemented": "Service not implemented (501)", "serviceType": "Service type", "serviceUnavailable": "Service Unavailable (503)", "serviceVersion": "Service version", "sessionExpired": "Login expired", "setScale": "Set scale", "setToFalseProhibitsPreemptionFromOtherPools": "Setting it to false will prevent other pools from preempting from this pool. The default value is true. On high priority pools or SLA pools, this value should be set to false.", "showAllDescriptions": "Display all descriptions", "showOnly": "Only display {value} configuration error issues, click to collapse", "smallFileCountMonitoringDelay": "There is a T 1 delay in monitoring the number of small files", "smallFileDefinition": "Small file definition", "smallFileDefinitionPolicy": "Small file definition strategy", "smallFileManager": "Small file management", "sourceClusterDataDirectory": "Main cluster source data directory", "sourceClusterHBaseCluster": "Source cluster HBase cluster", "sourceClusterSourceDataDirectory": "Main cluster source data directory", "sourceDatabaseName": "Source database name", "sourceHiveCluster": "Source hive cluster", "sourceList": "Source List", "specifiedHost": "Specify host", "specifyPoolName": "Specify pool name", "sshPortNumber": "SSH port number", "stackedAreaChart": "stacking area ", "start": "start-up", "startComponentService": "Do you want to start the current component (xxxx) service?", "startCurrentPrimaryBackupScheme": "Should the current primary and backup solutions be activated", "startDate": "Start date", "startDateCannotBeGreaterThanEndDate": "The start date cannot be greater than the end date", "startFailed": "Startup failed", "startInstallationConfirmation": "After starting the installation, the current configuration will only support viewing. Please confirm that it is correct and click OK to start.", "startSyncTask": "Do you want to start the current synchronization task", "startTime": "start time", "startTimeMustBeLessThanEndTime": "The start time must be less than the end time", "starting": "Starting up", "startupProgressDetails": "Startup progress details", "status": "state", "statusDelayMinutes": "Unhealthy (with minute level delay)", "stepContent": "Step content", "steps": "step", "stop": "stop", "stopComponentService": "Do you want to stop the current component (xxxx) service?", "stopDependenciesBeforeRestart": "It is recommended to stop the above dependent components before restarting", "stopFailed": "Stop failed", "stopProgressDetails": "Stop progress details", "stopSyncTask": "Do you want to terminate the current synchronization task", "stopped": "Stopped", "stopping": "Stopping", "storageAndComputationIntegration": "Storage-Compute Separation", "storageAndComputationSeparation": "Storage-Compute Integration", "storageSystem": "storage system", "subdirectoryCount": "Number of subdirectories", "submit": "Submit", "success": "success", "suggestStoppingDependentComponentsBeforeUpdate": "It is recommended to stop components that depend on it before updating.", "suggestion": "suggestion", "sunday": "Sunday", "supportS3": "Support S3", "switchPrimarySecondaryRelation": "Do you want to switch the primary backup relationship in the current plan?", "switchToAbsoluteValue": "Switch to absolute value", "switchToPercentage": "switch to percentage", "syncStrategy": "Synchronization strategy", "syncTableName": "Synchronization table name", "syncTableSelection": "Synchronization table selection", "syncTableType": "Synchronization table type", "syncTaskCount": "Number of synchronized tasks", "syncTaskManagement": "Synchronized task management", "table": "form", "targetClusterHBaseCluster": "Target cluster hbase cluster", "targetClusterTargetDirectory": "Standby cluster target directory", "targetDatabase": "Target database", "targetDatabaseName": "Target database name", "targetDatabaseNameShouldBeSameAsSource": "The target database name should be the same as the source database name", "targetHiveCluster": "Target Hive Cluster", "targetList": "Target List", "task": "Task", "taskManagement": "task management", "taskMonitoring": "Task monitoring", "taskName": "Task Name", "taskNameKeyword": "Task Name", "taskStatus": "Task status", "taskTime": "Task Time", "tenant": "Owned tenant", "tenantAvailableResources": "Tenant available resources", "tenantBelonging": "tenant", "tenantHasK8SCreatePermission": "This tenant has the permission to create K8S", "tenantList": "Tenant List", "tenantName": "Tenant name:", "tenantPermissions": "Tenant Permissions", "tenantResponsiblePerson": "Tenant Manager:", "tenantStatus": "Tenant status:", "tenantWillNotAppearInSystemAfterDeletion": "After deletion, tenants will not appear in the system", "tenantsWithPermissions": "Tenants with permissions can customize the namespace management of k8s.", "terminate": "termination", "testConnectivity": "Test connectivity", "testConnectivityFirst": "Please test the connectivity first", "testEmail": "Email testing", "testEmailRequestSentSuccessfully": "Test email request sent successfully", "testing": "During testing", "thursday": "Thursday", "tieredStoragePolicy": "Storage classification strategy", "time": "time", "timePeriod": "time slot", "timeSelection": "Time selection", "timeoutInSeconds": "Timeout (seconds)", "timeoutOrServerException": "The request timed out or the server was abnormal. Please check the network or contact the administrator.", "tip": "Tips", "to": "to", "traceLevel": "TRACE (prompt)", "tryingToReconnect": "Attempt to reconnect", "tuesday": "Tuesday", "type": "type", "unbind": "Unbind", "unbindAndDeleteHostBoundToCluster": "The selected host has already been bound to the cluster, and we will perform a cluster unbinding operation for you before deleting it", "unbindHostDefaultCluster": "After unbinding, the host will be placed in the default cluster and can be added for future use", "unbindK8s": "Unbind k8s", "unblacklist": "Unblock", "unblacklistSuccess": "Cancel block successfully", "unbound": "Unbound", "undo": "revoke", "unfreeze": "thaw", "unfreezeAccount": "Do you want to unfreeze the employee's account?", "unfreezeSuccess": "Thawing successful", "unhealthy": "unhealthy", "uninstalled": "Uninstalled", "unknownReason": "unknown reason", "unlimitedResources": "Unrestricted resources", "unrecognizedIdentifier": "Unrecognized ID", "unsavedChangesWarning": "After leaving, the system may not save your actions", "unselected": "Not selected", "update": "renew", "updateComponent": "Update component", "updateFailed": "Update failed", "updateProgressDetails": "Update progress details", "updating": "Updated", "uploadFile": "Upload files", "usage": "Dosage", "usePrimaryGroupPool": "Use a resource pool that matches the user's primary group", "usePrimaryGroupPoolThenUserNameSubPool": "Use the resource pool that matches the user's primary group, and then use the sub pool that matches the username", "useRuntimeSpecifiedPool": "Use the resource pool specified at runtime", "useSecondaryGroupPool": "Use a resource pool that matches one of the user's secondary groups.", "useSecondaryGroupPoolThenUserNameSubPool": "Use a resource pool that matches one of the user's secondary groups, and then use a subpool that matches the user's name", "useSpecifiedPool": "Use the specified resource pool.", "useSpecifiedPoolAsParent": "Use the resource pool that matches the specified pool as the parent pool, and then use the child pool that matches the username.", "useSpecifiedPoolRootXxx": "Use the specified resource pool, root. {value}", "useUserNamePool": "Use a resource pool that matches the username. (Not recommended)", "user": "user", "userCount": "Number of users", "userKeywords": "User keywords", "userLimit": "user limit", "userLimits": "user limit", "userList": "User list", "userManager": "user management ", "userName": "Username", "username": "user name", "validEmailRequired": "Please enter the correct email address", "validPhoneNumberRequired": "Please enter the correct mobile phone number", "validSSHPortRequired": "Please fill in the correct SSH port number", "validateHostRoleAssignment": "Please check if the hosts assigned to each role meet the standards", "value": "value", "valueBetweenZeroAndOne": "A value between 0 and 1. If the value is set to x and the fair share of the resource pool is F, then when the allocation is less than (x * F), resources will begin to be preempted from other resource pools.", "version": "edition", "versionNumber": "Version number", "versionRollback": "Version rollback", "versionSelection": "Version selection", "view": "see", "viewAlarms": "View alarms", "viewAll": "View all", "viewAllSelectedHosts": "View all selected hosts", "viewComponent": "View Components", "viewDataSource": "View data source", "viewDetails": "View details", "viewDiagnosisReport": "View diagnostic report", "viewEdit": "View modifications", "viewEvent": "View events", "viewInstance": "View instances", "viewJob": "View Job", "viewK8s": "View K8s", "viewLog": "View log", "viewModify": "View modifications", "viewProgress": "View progress", "viewProgressAfterDeletion": "After deletion, progress details can be viewed in more operations", "viewProgressAfterStart": "After startup, you can view progress details in more operations", "viewProgressAfterStop": "After stopping, progress details can be viewed in more operations", "viewReport": "View report", "viewRestartProgress": "After restarting, progress details can be viewed in more operations", "viewVersion": "View version", "virtualCores": "virtual kernel", "warnLevel": "Warning (general)", "warning": "warning", "warningIssues": "There are {value} warning issues, click to display", "webSocketConnectionClosed": "WebSocket connection closed", "webSocketConnectionEstablished": "WebSocket connection established", "webSocketError": "WebSocket error", "webTerminal": "Web terminal", "wednesday": "Wednesday", "weekly": "weekly", "weeklyInspection": "Once a week", "weeklyRepeatOnXxFromXxToXxFirstEffectiveAtXx": "Repeats every week on {dayOfWeek} from {beginTime}:00 to {endTimeStr}:00 (CST), first starting at {startTime}.", "weight": "power", "welcomeBack": "welcome back", "yarnResourcePoolConfiguration": "Yarn resource pool configuration", "yes": "yes", "belongingRegion": "Belonging region", "clusterManagement": "Cluster management", "alreadyBound": "Already bound", "unbound2": "Unbound", "bindResources": "Bind resources", "standardEdition": "Standard Edition", "lightweightVersion": "Lightweight version", "deployModeTip": "Kubernetes: All components are installed on the Kubernetes cluster; Docker: All components are installed in container mode on the host", "isDeleteCurrentRole": "Do you want to delete the current role ($name)?", "isDeleteCurrentRoleGroup": "Do you want to delete the role group ($name)?", "hideAllAlarmDetails": "Hide all alarm details", "viewAllAlarmDetails": "View all alarm details", "addAlarmItems": "Add alarm items", "alarmComponent": "Alarm component", "alarmOperation": "Alarm operation", "warningKeywords": "Warning keywords", "licenseTip1": "The license will be effective on $date", "licenseTip2": "The license has expired on $date", "licenseTip3": "Your license will expire in $date days, please renew it as soon as possible!", "namespacePermissionTip": "The serviceaccount account account belonging to the k8s cluster does not have the permission to add namespace. Please contact the k8s cluster administrator to add it", "selectDeploymentMode": "Select deployment mode", "memoryTip": "Input as an integer, such as 1024, representing 1024Mb", "nodePoolConfiguration": "Node Pool Configuration", "installCarpenter": "Install Carpenter", "createNodePool": "Create node pool", "nodePoolName": "Node Pool Name", "expansionStatus": "Expansion status", "nodeNum": "Number of nodes", "availableNodeNum": "Number of available nodes", "allocatedAndMaximumCpu": "CPU (allocated/maximum)", "allocatedAndMaximumMemory": "Memory (allocated/maximum)", "updateTime": "Update time", "createNodeClass": "Create Nodeclass", "nodeclassName": "Nodeclass name", "scalableEvent": "Scalable event", "timeID": "Time ID", "nodePool": "Node Pool", "totalInstancesAfterChanges": "Total instances after changes", "selectionMethod": "Selection method", "selectionMethodTip": "Template based creation: The following three types of node pools will be created, suitable for basic tasks such as Spark and Flink. They can only be created once and cannot be deleted after creation. Custom creation: Node pool naming and parameters can be customized, with no limit on the number of times they can be created, and the naming cannot be repeated.", "createBasedTemplate": "Create based on template", "nodePoolYAML": "Node Pool YAML", "namespaceAllocation": "Namespace allocation", "submitParameters": "Submit parameters", "reasonExplanation": "Reason explanation", "parameterType": "Parameter type", "beforeModification": "Before modification", "modify": "Modify", "batchConfiguration": "Batch configuration"}