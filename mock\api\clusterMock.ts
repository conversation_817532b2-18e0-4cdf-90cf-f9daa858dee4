export default [
    {
        url: '/api/ops/cluster/service/page',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: {
                records: [{
                    clusterName: '自定义名称',
                    name: '组件001',
                    deployWay: 1,
                    version: '1.3.6',
                    state: 4,
                    description: '组件说明'

                }],
                total: 1
            }
        })
    },
    {
        url: '/api/ops/cluster/service/addrole',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: `******************************`
        })
    },
    {
        url: '/api/ops/cluster/service/componentList',
        method: 'POST',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: [
                {
                    id: 1,
                    name: 'HDFS'
                }
            ]
        })
    },
    {
        url: '/api/ops/cluster/service/deleteByClusterServiceId/******************************',
        method: 'GET',
        response: () => ({
            code: 0,
            msg: 'ok',
            data: true
        })
    }
]