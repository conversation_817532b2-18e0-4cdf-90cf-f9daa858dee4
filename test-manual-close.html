<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Close Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #fef0f0;
            border: 1px solid #fde2e2;
            color: #f56c6c;
            padding: 15px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .close-btn {
            background: none;
            border: none;
            color: #f56c6c;
            cursor: pointer;
            font-size: 18px;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .close-btn:hover {
            background-color: rgba(245, 108, 108, 0.1);
            border-radius: 50%;
        }
        .test-controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #337ecc;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>手动关闭测试</h1>
    
    <div class="test-controls">
        <button onclick="showMessage()">显示消息</button>
        <button onclick="autoCloseMessage()">自动关闭消息</button>
        <button onclick="clearLog()">清除日志</button>
        <button onclick="checkStorage()">检查localStorage</button>
        <button onclick="clearStorage()">清除localStorage</button>
    </div>
    
    <div id="log" class="log">测试说明：
1. 点击"显示消息"会显示一个带关闭按钮的消息
2. 点击消息上的×按钮（手动关闭）会保存数据到localStorage
3. 点击"自动关闭消息"（模拟程序关闭）不会保存数据到localStorage
4. 消息会在3秒后自动消失（模拟Element Plus的默认行为）

开始测试...
</div>

    <script>
        let currentMessage = null;
        let currentOnClose = null;
        let autoCloseTimer = null;

        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `\n[${timestamp}] ${message}`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function showMessage() {
            // 清除之前的消息
            if (currentMessage) {
                currentMessage.remove();
                clearTimeout(autoCloseTimer);
            }

            log('显示新消息...');
            
            // 模拟useLicense中的逻辑
            const messageId = `license-message-${Date.now()}`;
            let isManualClose = false;
            
            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = `message base-message ${messageId}`;
            messageEl.innerHTML = `
                <div class="el-message__content">License将在6天后过期，请及时续费</div>
                <button class="close-btn el-message__closeBtn" onclick="closeMessage(true)">×</button>
            `;
            
            document.body.appendChild(messageEl);
            currentMessage = messageEl;
            
            // 模拟onClose回调
            currentOnClose = () => {
                log('onClose回调被调用');
                if (isManualClose) {
                    const licenseData = {
                        startTime: '2024-01-01T00:00:00Z',
                        endTime: '2024-12-31T23:59:59Z',
                        date: new Date().toISOString().split('T')[0]
                    };
                    localStorage.setItem('licenseMarkObj', JSON.stringify(licenseData));
                    log('✅ 手动关闭：已保存到localStorage');
                } else {
                    log('❌ 非手动关闭：未保存到localStorage');
                }
            };
            
            // 使用更精确的选择器监听关闭按钮点击事件
            setTimeout(() => {
                const closeBtn = document.querySelector(`.${messageId} .el-message__closeBtn`);
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        isManualClose = true;
                        log('检测到手动点击关闭按钮');
                    }, { once: true });
                    log('已设置关闭按钮监听器');
                } else {
                    log('❌ 未找到关闭按钮');
                }
            }, 100);
            
            // 模拟Element Plus的自动关闭（3秒后）
            autoCloseTimer = setTimeout(() => {
                if (currentMessage) {
                    log('消息自动关闭（3秒超时）');
                    closeMessage(false);
                }
            }, 3000);
            
            log('消息已显示，将在3秒后自动关闭，或者您可以点击×按钮手动关闭');
        }

        function closeMessage(isManual) {
            if (currentMessage) {
                currentMessage.remove();
                currentMessage = null;
                clearTimeout(autoCloseTimer);
                
                if (currentOnClose) {
                    currentOnClose();
                    currentOnClose = null;
                }
                
                log(isManual ? '消息已手动关闭' : '消息已自动关闭');
            }
        }

        function autoCloseMessage() {
            if (currentMessage) {
                log('程序触发自动关闭...');
                closeMessage(false);
            } else {
                log('没有消息需要关闭');
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '日志已清除\n';
        }

        function checkStorage() {
            const data = localStorage.getItem('licenseMarkObj');
            if (data) {
                log('localStorage内容: ' + data);
            } else {
                log('localStorage为空');
            }
        }

        function clearStorage() {
            localStorage.removeItem('licenseMarkObj');
            log('localStorage已清除');
        }
    </script>
</body>
</html>
