<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item v-for="(item, index) in collapseList" :key="index" :name="index">
      <template #title>
        <div class="el-collapse-item-title">
          <div class="el-collapse-item-title__left">
            <span class="turn"><i class="iconfont icon-icon_xiala iconColor" /></span>
            <span>{{ $t('replenish.host') }}{{ index + 1 }}</span>
          </div>
          <i class="iconfont icon-icon_shanchu1" @click="deleteCollapse(index)" />
        </div>
      </template>
      <template #default>
        <el-form ref="ruleFormRef" :model="collapseList[index]" inline :rules="rules" label-position="right" label-width="140px">
          <el-form-item :label="$t('form.ipAddress') + '：'" prop="ips">
            <el-input v-model="item.ips" class="input-width" size="default" :placeholder="$t('form.ipsPlaceholder')" />
          </el-form-item>
          <el-form-item :label="$t('form.SSHPortNumber') + '：'" prop="sshPort">
            <el-input v-model="item.sshPort" class="input-width" size="default" :placeholder="$t('form.pleaseEnterTheSSHPortNumber')" />
          </el-form-item>
          <el-form-item :label="$t('replenish.connectionMethod') + ':'" prop="loginType">
            <el-radio-group v-model="item.loginType" class="input-width" @change="chengGroup(index)">
              <el-radio :label="1">{{ $t('replenish.passwordConnection') }}</el-radio>
              <el-radio :label="2">{{ $t('replenish.publicKeyConnection') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="item.loginType === 1">
            <el-form-item :label="$t('form.privilegedUser') + '：'" prop="sudoUser">
              <el-input v-model="item.sudoUser" class="input-width" size="default" :placeholder="$t('form.pleaseEnterPrivilegedUser')" />
            </el-form-item>
            <el-form-item :label="$t('form.password') + '：'" prop="userPwd">
              <el-input
                v-model="item.userPwd"
                type="password"
                class="input-width"
                autocomplete="new-password"
                :placeholder="$t('form.pleaseEnterPassword')"
                show-password
              />
            </el-form-item>
          </template>
          <template v-else>
            <div class="secret-key">
              <span>{{ $t('replenish.publicKeyConnectionInstructions') }}: </span>
              <BaseButton link class="down" @click="down">{{ $t('replenish.downloadNow') }}</BaseButton>
            </div>
          </template>
          <div class="choose-ds" v-if="props.showDsChoose">
            <div class="choose-ds-title">{{ $t('replenish.clientToDataSource') }}</div>
            <el-form-item prop="namespaceId" :label="$t('replenish.selectCluster') + ':'">
              <el-select v-model="item.namespaceId" @change="getClusterService($event, item)">
                <el-option v-for="c in clusterList" :key="c.id" :value="c.id" :label="c.name"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="components" :label="$t('replenish.selectDataSource') + ':'" label-width="160px">
              <div class="choose-ds-components">
                <div class="choose-ds-component" v-for="c in componentList" :key="c.value">
                  <div>
                    <el-checkbox :modelValue="item.components.includes(c.value)" @change="checkComponent(item, c.value, c.dsList)">{{
                      c.label
                    }}</el-checkbox>
                  </div>
                  <div>
                    <el-radio-group :modelValue="item.dsList[c.value]" @change="checkDs(item, c.value, $event)">
                      <el-radio v-for="d in c.dsList" :key="d.value" :label="d.value">{{ d.label }}</el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item :label="$t('replenish.rackNumber') + '：'">
            <InputDropdown v-model="item.rackNumber" />
          </el-form-item>
        </el-form>
      </template>
    </el-collapse-item>
  </el-collapse>
</template>
<script lang="ts" setup>
import InputDropdown from '@/components/The/TheHostsAddInputDropdown.vue'
import { HostAddForm, useHostAddFrom } from '@/hooks/useAddHost'
import cloneDeep from 'lodash-es/cloneDeep'
import { encrypt } from '@/utils/Auth'
const { router, store } = useBasicTool()
const activeNames = ref([0])
const { hostAddForm, rules } = useHostAddFrom()
const ruleFormRef = ref()
const collapseList = ref<HostAddForm[]>([])
const props = defineProps({
  list: {
    type: Array as any,
    default: () => {
      return []
    }
  },
  namespaceId: {
    type: Number as any,
    default: -1
  },
  showDsChoose: {
    type: Boolean,
    default: false
  },
  hostType: {
    type: Number,
    default: 0
  }
})

const checkComponent = (item: any, val: any, dsList: any) => {
  const index = item.components.indexOf(val)
  if (index === -1) {
    item.components.push(val)
    item.dsList[val] = dsList[0].value
  } else {
    item.components.splice(index, 1)
    item.dsList[val] = ''
  }
}

const checkDs = (item: any, componentVal: any, val: any) => {
  const index = item.components.indexOf(componentVal)
  if (index === -1) {
    item.components.push(componentVal)
  }
  item.dsList[componentVal] = val
}

const getNamespaceList = () => {
  store.dispatch('namespace/namespaceGatewayList', { name: '', searchAudit: true }).then((res) => {
    clusterList.value = res.data
  })
}

const getClusterService = (id: any, collapseItem: any) => {
  collapseItem.components = []
  collapseItem.dsList = {}
  store.dispatch('cluster/getClusterServiceNode', { id }).then((res: any) => {
    componentList.value = Object.keys(res.data).map((item) => {
      return {
        value: item,
        label: item,
        dsList: res.data[item].map((s: any) => {
          return {
            value: s.clusterServiceId,
            label: s.clusterServiceName
          }
        })
      }
    })
  })
}
onMounted(() => {
  collapseList.value.push(cloneDeep(hostAddForm))
  getNamespaceList()
})

const clusterList = ref<any[]>([])

const componentList = ref<any[]>([])

// 下载
function down() {
  store.dispatch('hosts/downPublicKey')
}

async function submitForm() {
  return new Promise((resolve, reject) => {
    isSubmit(ruleFormRef.value).then(async (valid: Boolean) => {
      if (valid) {
        store
          .dispatch('hosts/batchAddHosts', {
            namespaceId: props.namespaceId,
            hostType: props.hostType,
            hostBatchAddOriginRequest: collapseList.value.map((item) => {
              const clusterIds: string[] = []
              Object.keys(item.dsList).forEach((c) => {
                if (item.dsList[c]) {
                  clusterIds.push(item.dsList[c])
                }
              })
              return {
                ...item,
                userPwd: encrypt(item.userPwd),
                clusterIds
              }
            })
          })
          .then((res) => {
            if (res.code === 0) {
              router.push({
                name: 'HostAddStateList',
                query: { serialNumber: res.data, hostType: props.hostType }
              })
            }
          })
          .finally(() => {
            resolve(false)
          })
      } else {
        resolve(valid)
      }
    })
  })
}
function chengGroup(index: number) {
  if (collapseList.value[index].loginType === 2) collapseList.value[index].sudoUser = 'root'
  else collapseList.value[index].sudoUser = ''
}
async function isSubmit(arr: Array<any>): Promise<Boolean> {
  const verificationArr: Boolean[] = []
  for (let i = 0; i < arr.length; i++) {
    verificationArr.push(await arr[i].validate((valid: Boolean) => valid))
  }

  if (verificationArr.includes(false)) return false
  else return true
}
function addCollapseList() {
  collapseList.value.push(cloneDeep(hostAddForm))
  const activeNumber = collapseList.value.length - 1
  if (!activeNames.value.includes(activeNumber)) {
    activeNames.value.push(activeNumber)
  }
}
function deleteCollapse(index: number) {
  collapseList.value.splice(index, 1)
}
defineExpose({
  addCollapseList,
  submitForm
})
</script>
<style lang="scss" scoped>
.el-collapse-item-title {
  width: 100%;
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  background-color: var(--ops-table-bg-heder-color);
  @include flex(space-between, center);
  .el-collapse-item-title__left {
    position: relative;
    bottom: 1px;
    height: 40px;
    line-height: 40px;
  }
}
:deep(.el-collapse) {
  border: none;
}
:deep(.el-collapse-item__header) {
  height: 40px;
  border: 1px solid var(--ops-border-color);
  font-size: 16px;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}
:deep(.el-collapse-item__arrow) {
  display: none;
}
:deep(.el-collapse-item__content) {
  padding: 20px;
  border-right: 1px solid var(--ops-border-color);
  border-left: 1px solid var(--ops-border-color);
}
.turn {
  display: inline-block;
  transform: rotate(-90deg);
  transition: all 0.5s;
}
:deep(.el-collapse-item__header.is-active) {
  .turn {
    transform: rotate(0deg);
  }
}
.input-width {
  width: 280px;
}
.icon-icon_shanchu1,
.secret-key {
  color: #99a0b5;
}
.secret-key {
  position: relative;
  top: -20px;
  padding-left: 512px;
  font-size: 14px;
  line-height: 24px;
}
:deep(.el-form--inline .el-form-item) {
  margin-right: 100px;
}
.down {
  height: 16px !important;
  font-size: 14px !important;
}
.choose-ds {
  padding: 20px 20px 0;
  background: #fafbfc;
  &-form {
    margin-right: 0 !important;
  }
  &-title {
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 600;
    color: rgba(51, 57, 76, 1);
  }
  .el-form-item {
    align-items: flex-start;
    width: 100%;
    .el-select {
      width: 260px;
      margin-right: 20px;
    }
  }
}
</style>
