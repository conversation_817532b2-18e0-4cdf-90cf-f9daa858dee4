{"version": 3, "file": "make-installer.js", "sources": ["../../../../packages/dc-components/make-installer.ts"], "sourcesContent": ["import { INSTALLED_KEY } from \"@dc-components/constants\";\r\nimport directives from \"@dc-components/directives\";\r\nimport type { Plugin } from \"vue\";\r\n\r\nexport const makeInstaller = (components: Plugin[] = []) => {\r\n  const install = (app: any) => {\r\n    if (app[INSTALLED_KEY]) return;\r\n    app[INSTALLED_KEY] = true;\r\n    components.forEach((c) => app.use(c));\r\n    app.use(directives);\r\n  };\r\n\r\n  return {\r\n    install,\r\n  };\r\n};\r\n"], "names": ["INSTALLED_KEY", "directives"], "mappings": ";;;;;;;;;;;AAIO,MAAM,aAAgB,GAAA,CAAC,UAAuB,GAAA,EAAO,KAAA;AAC1D,EAAM,MAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC5B,IAAA,IAAI,GAAI,CAAAA,sBAAA,CAAA;AAAgB,MAAA,OAAA;AACxB,IAAA,GAAA,CAAIA,sBAAiB,CAAA,GAAA,IAAA,CAAA;AACrB,IAAA,UAAA,CAAW,QAAQ,CAAC,CAAA,KAAM,GAAI,CAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AACpC,IAAA,GAAA,CAAI,IAAIC,8BAAU,CAAA,CAAA;AAAA,GACpB,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,OAAA;AAAA,GACF,CAAA;AACF;;;;"}