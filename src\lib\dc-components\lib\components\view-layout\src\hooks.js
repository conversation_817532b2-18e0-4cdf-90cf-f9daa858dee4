'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');

function useElementSize(target, options = {}) {
  const width = vue.ref(options.initialWidth || 0);
  const height = vue.ref(options.initialHeight || 0);
  let observer = null;
  let timeoutId = null;
  const updateSize = (entry) => {
    if (options.debounce) {
      if (timeoutId) {
        window.clearTimeout(timeoutId);
      }
      timeoutId = window.setTimeout(() => {
        width.value = entry.contentRect.width;
        height.value = entry.contentRect.height;
      }, options.debounce);
    } else {
      width.value = entry.contentRect.width;
      height.value = entry.contentRect.height;
    }
  };
  const observeElement = (element) => {
    observer = new ResizeObserver((entries) => {
      if (entries[0]) {
        updateSize(entries[0]);
      }
    });
    observer.observe(element);
  };
  vue.onMounted(() => {
    const element = target instanceof HTMLElement ? target : target?.value;
    if (element) {
      width.value = element.clientWidth;
      height.value = element.clientHeight;
      observeElement(element);
    }
  });
  vue.onUnmounted(() => {
    if (observer) {
      observer.disconnect();
    }
    if (timeoutId) {
      window.clearTimeout(timeoutId);
    }
  });
  return {
    width,
    height
  };
}

exports.useElementSize = useElementSize;
//# sourceMappingURL=hooks.js.map
