'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const isObject = (val) => val !== null && typeof val === "object";
const hasOwnProperty = Object.prototype.hasOwnProperty;
const hasOwn = (val, key) => hasOwnProperty.call(val, key);
const fromPairs = (pairs) => {
  const result = {};
  if (pairs == null) {
    return result;
  }
  for (const pair of pairs) {
    result[pair[0]] = pair[1];
  }
  return result;
};

exports.fromPairs = fromPairs;
exports.hasOwn = hasOwn;
exports.isObject = isObject;
//# sourceMappingURL=index.js.map
