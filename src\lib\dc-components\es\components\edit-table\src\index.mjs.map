{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/edit-table/src/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form ref=\"form\" :model=\"formModel\">\r\n      <el-table v-bind=\"$attrs\" :data=\"tableData\">\r\n        <slot />\r\n      </el-table>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script lang=\"ts\" setup>\r\nimport { ElForm, ElTable } from \"element-plus\";\r\nimport type { PropType, Ref } from \"vue\";\r\n// eslint-disable-next-line import/order\r\nimport { computed, provide, ref, watchEffect } from \"vue\";\r\nimport type {\r\n  DcEditTableEditActions,\r\n  DcEditTableFormModel,\r\n  DcEditTableFormModelItem,\r\n  DcEditTableFormProps,\r\n  DcEditTableRequestFunc,\r\n} from \"./types\";\r\ndefineOptions({\r\n  name: \"DcEditTable\",\r\n});\r\n\r\nconst props = defineProps({\r\n  dataSource: {\r\n    type: Array as PropType<any[]>,\r\n  },\r\n  request: {\r\n    type: Object as PropType<DcEditTableRequestFunc<any>>,\r\n  },\r\n});\r\n\r\nconst formModel = ref<DcEditTableFormModel>({\r\n  model: [],\r\n});\r\n\r\nconst form = ref();\r\n\r\nconst formProps = ref<DcEditTableFormProps>(new Set());\r\n\r\nconst tableData = computed(() => formModel.value.model.map(({ data }) => data));\r\n\r\nconst resultData = computed(() => {\r\n  return formModel.value.model.reduce(\r\n    (resultData: any[], model: DcEditTableFormModelItem) => {\r\n      if (model.isNew) {\r\n        return resultData;\r\n      }\r\n      resultData.push({\r\n        ...model.data,\r\n      });\r\n      return resultData;\r\n    },\r\n    []\r\n  );\r\n});\r\n\r\nconst convertFormModel = (data: any[]): DcEditTableFormModelItem[] =>\r\n  data.map(\r\n    (row: any): DcEditTableFormModelItem => ({\r\n      data: { ...row },\r\n      formData: { ...row },\r\n      isEditing: false,\r\n      isNew: false,\r\n    })\r\n  );\r\n\r\nwatchEffect(async () => {\r\n  const model = [...(props?.dataSource ?? [])];\r\n  if (typeof props.request === \"function\") {\r\n    model.push(...(await Promise.resolve(props.request())));\r\n  }\r\n  formModel.value.model = convertFormModel(model);\r\n});\r\n\r\nconst generateValidateFields = (index: number) =>\r\n  Array.from(formProps.value).map((prop) => `model.${index}.formData.${prop}`);\r\n\r\nconst startEditable = (index: number) => {\r\n  formModel.value.model[index].isEditing = true;\r\n};\r\n\r\nconst deleteRow = (index: number) => {\r\n  formModel.value.model.splice(index, 1);\r\n};\r\n\r\nconst addRow = (row: Record<any, any> = {}) => {\r\n  formModel.value.model.push({\r\n    data: { ...row },\r\n    formData: { ...row },\r\n    isEditing: true,\r\n    isNew: true,\r\n  });\r\n};\r\n\r\nconst cancelEditable = (index: number) => {\r\n  if (!form.value) {\r\n    return;\r\n  }\r\n\r\n  form.value.resetFields &&\r\n    form.value.resetFields(generateValidateFields(index));\r\n  const formModelItem = formModel.value.model[index];\r\n  formModelItem.formData = { ...formModelItem.data };\r\n  if (formModelItem.isNew) {\r\n    formModel.value.model.splice(index, 1);\r\n  } else {\r\n    formModelItem.isEditing = false;\r\n  }\r\n};\r\n\r\nconst saveEditable = (index: number) => {\r\n  if (!form.value) {\r\n    return;\r\n  }\r\n\r\n  form.value.validateField &&\r\n    form.value.validateField(\r\n      generateValidateFields(index),\r\n      (validated: boolean) => {\r\n        if (!validated) {\r\n          return;\r\n        }\r\n        const formModelItem = formModel.value.model[index];\r\n        formModelItem.data = { ...formModelItem.formData };\r\n        formModelItem.isEditing = false;\r\n        formModelItem.isNew = false;\r\n      }\r\n    );\r\n};\r\n\r\nconst editActions: DcEditTableEditActions = {\r\n  addRow,\r\n  deleteRow,\r\n  startEditable,\r\n  cancelEditable,\r\n  saveEditable,\r\n};\r\n\r\nprovide<Ref<DcEditTableFormModel>>(\"formModel\", formModel);\r\n\r\nprovide<Ref<DcEditTableFormProps>>(\"formProps\", formProps);\r\n\r\nprovide<DcEditTableEditActions>(\"editActions\", editActions);\r\n\r\ndefineExpose({\r\n  resultData,\r\n  editActions,\r\n});\r\n</script>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;AAqBc,EAAA,IAAA,EAAA,aAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,SAAQ,GAAA,GAAA,CAAA;AASd,MAAA;AAA4C,KAAA,CAC1C;AAAQ,IACV,MAAC,IAAA,GAAA,GAAA,EAAA,CAAA;AAED,IAAA,MAAM,SAAW,GAAA,GAAA,iBAAA,IAAA,GAAA,EAAA,CAAA,CAAA;AAEjB,IAAA,MAAM,SAAY,GAAA,QAA8B,CAAA,MAAA,SAAA,CAAA,KAAK,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,EAAA,KAAA,IAAA,CAAA,CAAA,CAAA;AAErD,IAAM,MAAA,UAAA,GAAqB,QAAA,CAAA,MAAgB;AAE3C,MAAM,OAAA,SAAA,CAAa,WAAe,CAAA,MAAA,CAAA,CAAA,WAAA,EAAA,KAAA,KAAA;AAChC,QAAA,IAAA,WAAiB,EAAM;AAEnB,UAAA,kBAAiB,CAAA;AACf,SAAO;AAAA,QACT,WAAA,CAAA,IAAA,CAAA;AACA,UAAA,GAAA,KAAA,CAAA,IAAgB;AAAA,SAAA,CACd;AAAS,QACX,OAAC,WAAA,CAAA;AACD,OAAO,EAAA,EAAA,CAAA,CAAA;AAAA,KACT,CAAA,CAAA;AAEF,IACF,MAAC,gBAAA,GAAA,CAAA,IAAA,KAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAA;AAED,MAAA,IAAM;AAEuC,MACvC,QAAQ,EAAA,EAAO,GAAA,GAAA,EAAA;AAAA,MACf,SAAA,EAAY,KAAO;AAAA,MACnB,KAAW,EAAA,KAAA;AAAA,KAAA,CACX,CAAO,CAAA;AAAA,IACT,WACF,CAAA,YAAA;AAEF,MAAA,MAAA,KAAwB,GAAA,CAAA,GAAA,KAAA,EAAA,UAAA,IAAA,EAAA,CAAA,CAAA;AACtB,MAAA,IAAA,YAAe,CAAA,OAAW,KAAA,UAAc,EAAG;AAC3C,QAAI,KAAA,CAAA,IAAa,CAAA,GAAA,MAAA,OAAY,CAAY,OAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AACvC,OAAM;AAAgD,MACxD,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAU,CAAA,CAAA;AAAoC,IAChD,MAAC,sBAAA,GAAA,CAAA,KAAA,KAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,UAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAED,IAAA,MAAM,aAAyB,GAAA,CAAA,KAAA,KAC7B;AAEF,MAAM,SAAA,CAAA,KAAA,CAAA,KAAiB,CAAkB,KAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA;AACvC,KAAU,CAAA;AAA+B,IAC3C,MAAA,SAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,SAAA,CAAA,KAAA,CAAY,KAAmB,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACnC,KAAA,CAAA;AAAqC,IACvC,MAAA,MAAA,GAAA,CAAA,GAAA,GAAA,EAAA,KAAA;AAEA,MAAA,SAAe,CAAA,KAAyB,CAAA,KAAC,CAAM,IAAA,CAAA;AAC7C,QAAU,IAAA,EAAA,EAAA,GAAA,GAAM;AAAW,QACzB,QAAQ,EAAA,EAAO,GAAA,GAAA,EAAA;AAAA,QACf,SAAA,EAAY,IAAG;AAAI,QACnB,KAAW,EAAA,IAAA;AAAA,OAAA,CACX,CAAO;AAAA,KAAA,CACT;AAAC,IACH,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACJ,QAAI;AACF,OAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,WAAA,IAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,sBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAEA,MAAA,mBACE,GAAA,SAAK,MAAM,CAAY,KAAA,CAAA,KAAA,CAAA,CAAA;AACzB,MAAM,aAAA,CAAA,QAAgB,GAAU,EAAA,GAAA,aAAY,CAAA,IAAA,EAAA,CAAA;AAC5C,MAAA,IAAA,aAAyB,CAAA,KAAA,EAAA;AACzB,QAAA,gBAAkB,KAAO,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACvB,OAAA,MAAA;AAAqC,QAChC,aAAA,CAAA,SAAA,GAAA,KAAA,CAAA;AACL,OAAA;AAA0B,KAC5B,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AACJ,QAAI;AACF,OAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,aAAA,IAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,sBAAA,CAAA,KAAA,CAAA,EAAA,CAAA,SAAA,KAAA;AAEA,QAAK,IAAA,CAAA;AAIC,UAAA,OAAgB;AACd,SAAA;AAAA,QACF,MAAA,aAAA,GAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACA,QAAM,aAAA,CAAA,IAAA,GAAA,EAA0B,GAAA,aAAY,CAAA,QAAA,EAAA,CAAA;AAC5C,QAAA,aAAA,CAAc,SAAS,GAAG,KAAA,CAAA;AAC1B,QAAA,aAAA,CAAc,KAAY,GAAA,KAAA,CAAA;AAC1B,OAAA,CAAA,CAAA;AAAsB,KAAA,CACxB;AACF,IACJ,MAAA,WAAA,GAAA;AAEA,MAAA,MAA4C;AAAA,MAC1C,SAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,MACA,YAAA;AAAA,KACA,CAAA;AAAA,IACF,OAAA,CAAA,WAAA,EAAA,SAAA,CAAA,CAAA;AAEA,IAAA,OAAA,CAAmC,aAAa,SAAS,CAAA,CAAA;AAEzD,IAAA,OAAA,CAAmC,aAAa,EAAS,WAAA,CAAA,CAAA;AAEzD,IAAA,QAAgC;AAEhC,MAAa,UAAA;AAAA,MACX,WAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;"}