<template>
  <div class="login-container">
    <div class="login-left">
      <div class="logo-text">{{ $t('replenish.dataPlatform') }}</div>
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large" class="login-form" label-position="left">
        <div class="title-container">
          <div>
            Hello!
            <span>{{ $t('title.welcomeBack') }}</span>
          </div>
        </div>
        <el-form-item class="fromItem" prop="loginName">
          <el-input
            v-model="loginForm.loginName"
            :placeholder="$t('form.pleaseEnterUserName')"
            name="username"
            type="text"
            tabindex="1"
            autocomplete="on"
          >
            <template #prefix>
              <i class="iconfont icon-icon_dengluye_daohang_yonghuming" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="fromItem" prop="password">
          <el-input
            v-model="loginForm.password"
            :placeholder="$t('form.pleaseEnterPassword')"
            name="password"
            tabindex="2"
            autocomplete="on"
            show-password
            @keyup.enter="handleLogin(loginFormRef)"
          >
            <template #prefix>
              <i class="iconfont icon-icon_dengluye_mima" />
            </template>
          </el-input>
        </el-form-item>
        <el-button class="submitBtn" size="large" :loading="loading" type="primary" @click.prevent="handleLogin(loginFormRef)">
          {{ $t('replenish.login') }}
        </el-button>
      </el-form>
      <!-- <p class="login-left-bottom-text">浙江数新网络有限公司</p> -->
    </div>
    <div class="login-bg">
      <!-- <span class="login-title">Data Creating Manager</span> -->
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getClusterInfo } from '@/api/clusterApi'
import { getRecords, login } from '@/api/userApi'
import { encrypt } from '@/utils/Auth'
import Base from '@/utils/Base'
import { bus } from '@/utils/mitt'
import { LStorage } from '@/utils/storage'
import type { FormRules } from 'element-plus'
const { t, router, store } = useBasicTool()
const loading = false
const loginFormRef = ref()
const loginRules = reactive<FormRules>({
  loginName: [
    {
      required: true,
      message: t('form.pleaseEnterUserName'),
      trigger: 'blur'
    }
  ],
  password: [
    {
      required: true,
      message: t('form.pleaseEnterPassword'),
      trigger: 'blur'
    }
  ]
})
const loginForm = reactive({
  loginName: '',
  password: '',
  rememberMe: true
})
let timer: any
function loginUser() {
  if (timer) clearTimeout(timer)
  timer = setTimeout(() => {
    handleLogin(loginFormRef.value)
  }, 300)
}
function handleLogin(formEl: any) {
  if (!formEl) return
  formEl.validate((valid: any) => {
    if (valid) {
      login({ ...loginForm, password: encrypt(loginForm.password) }).then((response) => {
        const data = response.data
        if (data.code === '200') {
          store.commit('user/setUserName', loginForm.loginName)
          const { jwtToken } = data.data
          Base.setCookie(Base.cookie, jwtToken, Base.default_exp_value)
          LStorage.set(Base.cookie, jwtToken)
          store.dispatch('user/getUserInfo').then(() => {
            store.dispatch('app/getNamespaceType')
            isLastCluster()
            bus.emit('connectWebSocket')
          })
        }
      })
    }
  })
}
function isLastCluster() {
  getRecords().then((res) => {
    if (res.data.data) {
      const { namespaceId, namespaceTitle, deployMode } = res.data.data
      if (!namespaceId) {
        router.push({ name: 'namespace' })
        return
      }
      getClusterInfo({ id: namespaceId }).then((resT) => {
        if (resT.data.data) {
          store.commit('namespace/setNamespaceName', namespaceTitle)
          store.commit('component/setNamespaceId', namespaceId)
          store.commit('namespace/setMeunCurrentNamespaceTitle', namespaceTitle)
          store.commit('component/setDeployMode', deployMode)
          router.push({
            name: 'namespaceOverview',
            query: { namespaceId, namespaceTitle }
          })
        } else {
          router.push({ name: 'namespace' })
        }
      })
    } else {
      router.push({ name: 'namespace' })
    }
  })
}
</script>
<style lang="scss" scoped>
$bg: #202020;
$dark_gray: #f2f4f9;
$light_gray: #ffffff;
.login-container {
  overflow: hidden;
  width: 1920px;
  height: 1080px;
  /* reset element-ui css */
  .el-form-item {
    border-radius: var(--radius-l);
    background-color: #121724;
  }
  .el-input__inner {
    border: 1px solid $dark_gray;
    color: #61646e;
    background-color: $dark_gray;
  }
  .el-input--small .el-input__inner {
    border-radius: var(--radius-l);
    line-height: 60px;
  }
  .el-input__prefix {
    top: 5px;
    left: 8px;
  }
}
// 修复chrome浏览器对用户名密码的自动填充使用的样式input:-webkit-autofill
:deep(.el-input) {
  .el-input__inner {
    font-size: 16px;
  }
  input:-webkit-autofill,
  textarea:-webkit-autofill,
  select:-webkit-autofill {
    // -webkit-text-fill-color: #121724 !important;
    background-color: transparent;
    transition: background-color 500000s ease-in-out 0s; //背景色透明  生效时长  过渡效果  启用时延迟的时间
  }
  /* stylelint-disable-next-line selector-pseudo-element-no-unknown */
  input::input-placeholder {
    color: #c0c4cf;
  }
  input:placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #c0c4cf;
  }
  input::placeholder {
    /* Mozilla Firefox 19+ */
    color: #c0c4cf;
  }
}
.fromItem {
  width: 385px;
}
</style>

<style lang="scss" scoped>
.login-container {
  display: flex;
  width: 100%;
  height: 100vh;
  color: var(--ops-text-color);
  // background: #fdfdff;
  background-color: #171e32;
  .login-left {
    position: relative;
    width: 70%;
    .login-left-icon {
      position: absolute;
      top: 40px;
      left: 52px;
      width: 140px;
    }
    .logo-text {
      margin-top: 22px;
      margin-left: 30px;
      font-family: PingFangSC-Semibold, 'PingFang SC';
      font-size: 24px;
      line-height: 33px;
      font-weight: 600;
      color: #6283ff;
    }
    .title-container {
      // width: 96px;
      height: 40px;
      margin-bottom: 41px;
      font-size: 28px;
      line-height: 40px;
      font-weight: 600;
      // color: #33394c;
      color: var(--ops-text-white-color);
      div {
        font-size: 28px;
        span {
          font-size: 20px;
        }
      }
    }
    .login-form {
      width: 340px;
      margin: 0 auto;
      margin-top: 240px;
      .el-button {
        width: 100%;
      }
      ::v-deep(.el-input) {
        width: 385px;
        height: 60px;
      }
      .iconfont {
        margin-left: 16px;
        font-size: 20px;
        line-height: 60px;
        color: #6283ff;
      }
      ::v-deep(.el-input__icon) {
        line-height: 60px;
      }
      ::v-deep(.el-input__inner) {
        padding-left: 8px;
        border-radius: var(--radius-m);
        font-size: 16px;
      }
      ::v-deep(.el-input__wrapper) {
        padding-left: 0;
        background-color: #f2f4f9;
        box-shadow: none;
      }
      ::v-deep(.el-input__wrapper.is-focus) {
        box-shadow: none;
      }
      ::v-deep(.el-form-item.is-error .el-input__wrapper) {
        box-shadow: none;
      }
      .submitBtn {
        width: 385px;
        height: 48px;
        border-radius: var(--radius-m);
        margin-top: 18px;
        background: var(--ops-primary-color);
      }
    }
    .login-left-bottom-text {
      position: absolute;
      bottom: 60px;
      left: 50%;
      font-size: 16px;
      letter-spacing: 14px;
      color: #c5c8d3;
      transform: translate(-50%, -50%);
    }
  }
  .login-bg {
    width: 38%;
    height: 100vh;
    padding-top: 60px;
    background: url('./img/ShangFa_bg.png') no-repeat;
    background-size: 100% 103%;
    .login-title {
      margin-left: 54px;
      font-size: 28px;
      line-height: 40px;
      color: var(--ops-text-color);
    }
  }
}
</style>
