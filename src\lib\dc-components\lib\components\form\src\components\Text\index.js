'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('./index2.js');

var DcFormText = vue.defineComponent({
  name: "DcFormText",
  props: {
    modelValue: {
      type: [String, Number, Boolean]
    }
  },
  render() {
    return vue.createVNode("div", {
      "class": "dc-form-text"
    }, [this.$props.modelValue]);
  }
});

exports["default"] = DcFormText;
//# sourceMappingURL=index.js.map
