import './images/index.mjs';
import img from './images/list-light.mjs';
import img$1 from './images/unList-light.mjs';
import img$2 from './images/echarts.mjs';
import img$3 from './images/file.mjs';
import img$4 from './images/power-light.mjs';
import img$5 from './images/feature.mjs';
import img$6 from './images/blood-light.mjs';
import img$7 from './images/model.mjs';
import img$8 from './images/network-light.mjs';
import img$9 from './images/404-light.mjs';
import img$a from './images/405-light.mjs';
import img$b from './images/message.mjs';
import img$c from './images/image.mjs';
import img$d from './images/imagePlaceholder.mjs';
import img$e from './images/api.mjs';
import img$f from './images/list-dark.mjs';
import img$g from './images/unList-dark.mjs';
import img$h from './images/power-dark.mjs';
import img$i from './images/blood-dark.mjs';
import img$j from './images/network-dark.mjs';
import img$k from './images/404-dark.mjs';
import img$l from './images/405-dark.mjs';

const imgConfig = {
  light: {
    list: img,
    unList: img$1,
    echarts: img$2,
    file: img$3,
    power: img$4,
    feature: img$5,
    blood: img$6,
    model: img$7,
    network: img$8,
    "404": img$9,
    "405": img$a,
    message: img$b,
    image: img$c,
    imagePlaceholder: img$d,
    api: img$e
  },
  dark: {
    list: img$f,
    unList: img$g,
    echarts: img$2,
    file: img$3,
    power: img$h,
    feature: img$5,
    blood: img$i,
    model: img$7,
    network: img$j,
    "404": img$k,
    "405": img$l,
    message: img$b,
    image: img$c,
    imagePlaceholder: img$d,
    api: img$e
  }
};
const descConfig = {
  "zh-CN": {
    list: "\u6682\u65E0\u6570\u636E",
    unList: "\u6682\u65E0\u5185\u5BB9",
    echarts: "\u6682\u65E0\u56FE\u8868",
    file: "\u6682\u672A\u6DFB\u52A0\u6587\u4EF6",
    power: "\u6682\u65E0\u6743\u9650",
    feature: "\u6682\u65E0\u8BE5\u529F\u80FD",
    blood: "\u6682\u65E0\u8840\u7F18\u4FE1\u606F",
    model: "\u6682\u65E0\u6A21\u578B\uFF0C\u8BF7\u5148\u65B0\u5EFA",
    network: "\u7F51\u7EDC\u4E2D\u65AD\uFF0C\u8BF7\u5237\u65B0\u91CD\u8BD5",
    "404": "404",
    "405": "405",
    message: "\u6682\u65E0\u6D88\u606F",
    image: "\u6682\u65E0\u56FE\u7247\uFF0C\u8BF7\u5148\u6DFB\u52A0",
    imagePlaceholder: "\u56FE\u7247\u5360\u4F4D",
    api: "\u6682\u65E0API\uFF0C\u8BF7\u5148\u65B0\u5EFA"
  },
  "en-US": {
    list: "No data",
    unList: "No content",
    echarts: "No chart",
    file: "No files added",
    power: "No permission",
    feature: "This function is not available",
    blood: "No bloodline information",
    model: "There is no model, please create a new one first",
    network: "The network is interrupted, please refresh and try again.",
    "404": "404",
    "405": "405",
    message: "No news",
    image: "There are no pictures, please add them first",
    imagePlaceholder: "Picture placeholder",
    api: "There is no API, please create a new one first"
  }
};

export { descConfig, imgConfig };
//# sourceMappingURL=constants.mjs.map
