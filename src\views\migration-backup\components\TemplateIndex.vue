<template>
  <div>
    <div
      :style="{
        overflow: 'hidden',
        width: '100%',
        height: route.path.includes('migrationBackup') ? 'calc(100vh - 56px)' : 'calc(100vh - 104px)',
        'background-color': 'var(--ops-bg-white-color)'
      }"
    >
      <BaseElTabs :model-value="isTabs" @tab-change="tabsChange">
        <el-tab-pane v-for="(item, index) in tabData?.filter((item) => item?.isTabs ?? true)" :key="index" :label="item.label" :name="item.name" />
      </BaseElTabs>
      <div :style="{ height: route.path.includes('migrationBackup') ? '100%' : 'calc(100% - 56px)' }">
        <router-view />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { TabPaneName } from 'element-plus'
defineProps({
  tabData: {
    type: Array<{ label: string; name: string; isTabs?: boolean }>
  }
})
const { route, t, router } = useBasicTool()
const isTabs = computed<any>(() => route.name)

function switchRouter(routerName: any): void {
  router.push({
    name: routerName
  })
}
// tabs切换事件
function tabsChange(name: TabPaneName) {
  switchRouter(name)
}
</script>
