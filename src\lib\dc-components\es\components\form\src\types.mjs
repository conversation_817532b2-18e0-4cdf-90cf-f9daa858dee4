var DcFormComponentEnum = /* @__PURE__ */ ((DcFormComponentEnum2) => {
  DcFormComponentEnum2["INPUT"] = "input";
  DcFormComponentEnum2["TEXT"] = "text";
  DcFormComponentEnum2["SELECT"] = "select";
  DcFormComponentEnum2["RADIO"] = "radio";
  DcFormComponentEnum2["CHECKBOX"] = "checkbox";
  DcFormComponentEnum2["AUTOCOMPLETE"] = "autocomplete";
  DcFormComponentEnum2["CASCADER"] = "cascader";
  DcFormComponentEnum2["CASCADERPANEL"] = "cascaderPanel";
  DcFormComponentEnum2["COLORPICKER"] = "colorPicker";
  DcFormComponentEnum2["DATEPICKER"] = "datePicker";
  DcFormComponentEnum2["INPUTNUMBER"] = "inputNumber";
  DcFormComponentEnum2["RATE"] = "rate";
  DcFormComponentEnum2["SELECTV2"] = "selectV2";
  DcFormComponentEnum2["SLIDER"] = "slider";
  DcFormComponentEnum2["SWITCH"] = "switch";
  DcFormComponentEnum2["TIMEPICKER"] = "timePicker";
  DcFormComponentEnum2["TIMESELECT"] = "timeSelect";
  DcFormComponentEnum2["TRANSFER"] = "transfer";
  DcFormComponentEnum2["UPLOAD"] = "upload";
  DcFormComponentEnum2["TABLE"] = "table";
  DcFormComponentEnum2["CUSTOM"] = "custom";
  return DcFormComponentEnum2;
})(DcFormComponentEnum || {});

export { DcFormComponentEnum };
//# sourceMappingURL=types.mjs.map
