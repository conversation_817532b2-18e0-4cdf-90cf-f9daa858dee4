'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var index$1 = require('../../button/src/index.js');
var index = require('../../render-vnode/src/index.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const _hoisted_1 = { class: "dc-dialog-footer" };
const _sfc_main = vue.defineComponent({
  __name: "Footer",
  props: {
    confirmAction: {
      type: Object,
      default: () => ({})
    },
    cancelAction: {
      type: Object,
      default: () => ({})
    },
    footerLeftSlot: {
      type: Object
    },
    footerRightSlot: {
      type: Object
    }
  },
  emits: ["cancel", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const defaultConfirmAction = {
      innerText: "\u786E\u5B9A",
      props: {
        type: "primary"
      },
      config: {
        debounce: true
      }
    };
    const confirmAction = vue.computed(() => props.confirmAction ? {
      ...defaultConfirmAction,
      ...props.confirmAction,
      props: {
        ...defaultConfirmAction.props,
        ...props.confirmAction.props || {}
      },
      config: {
        ...defaultConfirmAction.config,
        ...props.confirmAction.config || {}
      }
    } : defaultConfirmAction);
    const defaultCancelAction = {
      innerText: "\u53D6\u6D88"
    };
    const cancelAction = vue.computed(() => props.cancelAction ? { ...defaultCancelAction, ...props.cancelAction } : defaultCancelAction);
    const onConfirm = () => {
      emits("confirm");
    };
    const onCancel = () => {
      emits("cancel");
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", _hoisted_1, [
        props.footerLeftSlot ? (vue.openBlock(), vue.createBlock(vue.unref(index["default"]), {
          key: 0,
          vnode: props.footerLeftSlot
        }, null, 8, ["vnode"])) : vue.createCommentVNode("v-if", true),
        confirmAction.value.visible !== false ? (vue.openBlock(), vue.createBlock(index$1["default"], vue.mergeProps({ key: 1 }, { ...confirmAction.value.props }, {
          config: confirmAction.value.config,
          onClick: onConfirm
        }), vue.createSlots({
          default: vue.withCtx(() => [
            vue.createTextVNode(" " + vue.toDisplayString(confirmAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          vue.renderList(Object.keys(confirmAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: vue.withCtx((scope) => [
                vue.createVNode(vue.unref(index["default"]), {
                  vnode: (confirmAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : vue.createCommentVNode("v-if", true),
        cancelAction.value.visible !== false ? (vue.openBlock(), vue.createBlock(index$1["default"], vue.mergeProps({ key: 2 }, { ...cancelAction.value.props }, {
          config: cancelAction.value.config,
          onClick: onCancel
        }), vue.createSlots({
          default: vue.withCtx(() => [
            vue.createTextVNode(" " + vue.toDisplayString(cancelAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          vue.renderList(Object.keys(cancelAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: vue.withCtx((scope) => [
                vue.createVNode(vue.unref(index["default"]), {
                  vnode: (cancelAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : vue.createCommentVNode("v-if", true),
        props.footerRightSlot ? (vue.openBlock(), vue.createBlock(vue.unref(index["default"]), {
          key: 3,
          vnode: props.footerRightSlot
        }, null, 8, ["vnode"])) : vue.createCommentVNode("v-if", true)
      ]);
    };
  }
});
var Footer = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\dialog\\src\\Footer.vue"]]);

exports["default"] = Footer;
//# sourceMappingURL=Footer.js.map
