// monitorApi.ts
import service from '@/utils/Http'

/**
 * @description 根据标签获取图表面板列表
 * @param {PanelApi.IGetPanelQueryByLabel} data
 * @return {*}
 */

export const getPanelQueryByLabel = async (data: PanelApi.IGetPanelQueryByLabel) => {
  return service.post<ApiResponse<Array<PanelApi.PanelQueryByLabel>>>('/panel/queryByLabel', data)
}

/**
 * @description
 * @return {*}
 */
export const getPanelInstanceCondition = async () => {
  return service.get<ApiResponse<Array<PanelApi.PanelInstanceConditionData>>>('/panel/service/condition')
}

/**
 * @description 集群监控筛选条件
 * @return {*}
 */
export const getPanelNamespaceCondition = async () => {
  return service.get<ApiResponse<Array<PanelApi.PanelNamespaceConditionData>>>('/panel/namespace/condition')
}

/**
 * @description 主机状态统计
 * @param {PanelApi.getStatusList} data
 * @return {*}
 */
export const getHostsStatus = async (data: PanelApi.getStatusList) => {
  return service.get<ApiResponse<PanelApi.HostsStatusData>>(`/hosts/status/static/${data.namespaceId}`)
}

/**
 * @description 组件状态统计
 * @param {PanelApi.getStatusList} data
 * @return {*}
 */
export const getClusterServiceStatus = async (data: PanelApi.getStatusList) => {
  return service.get<ApiResponse<PanelApi.ClusterServiceStatusData>>(`/cluster/service/status/static/${data.namespaceId}`)
}
/**
 * @description 告警数量统计
 * @param {PanelApi.getStatusList} data
 * @return {*}
 */
export const getWarnStatus = async (data: PanelApi.getStatusList) => {
  return service.get<ApiResponse<PanelApi.WarnStatusData>>(`/warn/status/static/${data.namespaceId}`)
}

/**
 * @description 监控节点统计
 * @param {PanelApi.getStatusList} data
 * @return {*}
 */
export const getNodesStatus = async (data: PanelApi.getStatusList) => {
  return service.get<ApiResponse<PanelApi.NodeStatusData>>(`/cluster/service/nodes/static/${data.namespaceId}`)
}

/**
 * @description 监控节点统计
 * @param {PanelApi.getClusterMonitorJumpList} data
 * @return {*}
 */
export const getClusterMonitorJumpList = async (data: PanelApi.getClusterMonitorJumpList) => {
  return service.get<ApiResponse<PanelApi.getClusterMonitorJumpListData>>(`/panel/clusterMonitorJumpList/?namespaceId=${data.namespaceId}`)
}

/**
 * @description 监控节点统计
 * @param {PanelApi.GetPanelLink} data
 * @return {*}
 */
export const getPanelLink = async (data: PanelApi.GetPanelLink) => {
  return service.get<ApiResponse<string>>(`/panel/link?monitorTarget=${data.monitorTarget}&label=${data.label}`)
}
