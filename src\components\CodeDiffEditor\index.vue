<template>
  <div class="container-code read-only" v-if="showCode" ref="containerRef">
    <div :id="editorId" :key="editorId" style="height: 100%"></div>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
import { initEditor } from '../MonacoEditor/init'
import { debounce } from 'lodash-es'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'

const props = defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  originCode: {
    type: Object as PropType<{
      code: string
      language: string
    }>,
    default: () => ({
      language: '',
      code: ''
    })
  },
  modifiedCode: {
    type: Object as PropType<{
      code: string
      language: string
    }>,
    default: () => ({
      language: '',
      code: ''
    })
  },
  miniMap: {
    type: <PERSON>olean,
    default: false
  },
  lineWrapping: {
    type: Boolean,
    default: false
  },
  folding: {
    type: Boolean,
    default: true
  },
  lineDecorationsWidth: {
    type: Number,
    default: 10
  },
  showLine: {
    type: Boolean,
    default: true
  },
  readOnly: {
    type: Boolean,
    default: false
  }
})

const editorId = ref(`diff-editor_${Date.now()}`)
const showCode = ref(false)

const containerRef = ref()

let diffEditor: monaco.editor.IStandaloneDiffEditor | null = null
let originalModel: monaco.editor.ITextModel | null = null
let modifiedModel: monaco.editor.ITextModel | null = null

let codeObserver: ResizeObserver | null = null
let lastCodeSize = {
  width: '0',
  height: '0'
}

const init = () => {
  nextTick(() => {
    try {
      const container = document.getElementById(editorId.value)
      if (!container) {
        console.error('CodeDiffEditor: Container not found', editorId.value)
        return
      }

      // 确保 Monaco Editor 已经初始化
      if (!monaco || !monaco.editor) {
        console.error('Monaco Editor not loaded')
        return
      }

      originalModel = monaco.editor.createModel(props.originCode.code, props.originCode.language)
      modifiedModel = monaco.editor.createModel(props.modifiedCode.code, props.modifiedCode.language)

    // 确保容器有明确的尺寸
    if (container.offsetHeight === 0) {
      container.style.height = '400px'
    }

    // 使用最基础的配置，避免兼容性问题
    diffEditor = monaco.editor.createDiffEditor(container, {
      renderSideBySide: true,
      readOnly: props.readOnly,
      automaticLayout: true
    })

    diffEditor.setModel({
      original: originalModel,
      modified: modifiedModel
    })

    // 简单的布局刷新
    setTimeout(() => {
      diffEditor?.layout()
    }, 100)
    codeObserver = new ResizeObserver(
      debounce(() => {
        if (!containerRef.value) {
          return
        }
        const width = getComputedStyle(containerRef?.value).getPropertyValue('width')
        const height = getComputedStyle(containerRef?.value).getPropertyValue('height')
        if (width === lastCodeSize.width && height === lastCodeSize.height) return
        lastCodeSize.height = height
        lastCodeSize.width = width
        resizeCoder()
      }, 300)
    )

    if (containerRef.value) {
      codeObserver.observe(containerRef.value)
    }
    } catch (error) {
      console.error('CodeDiffEditor initialization error:', error)
    }
  })
}

function resizeCoder() {
  diffEditor?.layout()
}

function getLineChanges() {
  return diffEditor?.getLineChanges()
}

onBeforeMount(() => {
  // 确保 Monaco Editor 主题已初始化
  try {
    initEditor()
  } catch (error) {
    console.warn('Monaco Editor theme initialization failed:', error)
  }
})

watch(
  () => props.isShow,
  (isShow) => {
    if (!isShow || diffEditor) return
    showCode.value = true
    init()
  },
  { immediate: true }
)

watch(
  () => props.originCode.code,
  (code) => {
    originalModel?.setValue(code)
  },
  { immediate: true }
)

watch(
  () => props.modifiedCode.code,
  (code) => {
    modifiedModel?.setValue(code)
  },
  { immediate: true }
)

watch(
  () => props.originCode.language,
  (lang) => {
    if (originalModel) {
      monaco.editor.setModelLanguage(originalModel, lang)
    }
  }
)

watch(
  () => props.modifiedCode.language,
  (lang) => {
    if (modifiedModel) {
      monaco.editor.setModelLanguage(modifiedModel, lang)
    }
  }
)

onBeforeUnmount(() => {
  if (diffEditor) {
    modifiedModel?.dispose()
    originalModel?.dispose()
    diffEditor.dispose()
    diffEditor = null
    originalModel = null
    modifiedModel = null
    codeObserver = null
    lastCodeSize = {
      width: '0',
      height: '0'
    }
  }
})

onBeforeMount(() => {
  initEditor()
})

defineExpose({
  getLineChanges
})
</script>
<style lang="scss" scoped>
.container-code {
  height: 100%;
  width: 100%;
}

:deep(.select-run-line) {
  background: #3bbb6e;
  margin-left: -38px;
  width: 3px !important;
}

:deep(.run-widget) {
  left: 3px;
  width: 62px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-flow: row nowrap;
  box-sizing: border-box;
  background: #fff;
  border-color: #fff;
  cursor: pointer;
  font-size: 12px;
  color: #567aff;

  &.disabled {
    cursor: not-allowed;
  }
  .iconfont {
    position: relative;
    top: 1px;
  }
}
</style>
<style lang="scss">
html {
  .container-code {
    .margin {
      background: #f7f9fc;
    }
    .monaco-editor-background {
      background: #fff;
    }
    .minimap-slider-horizontal {
      background-color: transparent;
    }
    .minimap-slider {
      display: block !important;
    }
    .minimap-slider:hover {
      .minimap-slider-horizontal {
        background: #eef2fe;
        border-color: #ccd7ff;
        box-sizing: border-box;
        display: block;
      }
    }
    &.read-only {
      .monaco-editor-background {
        background-color: #f7f9fc;
      }
    }
  }
}
</style>
