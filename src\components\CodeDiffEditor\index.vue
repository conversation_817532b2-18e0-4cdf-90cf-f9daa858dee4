<template>
  <div class="container-code read-only" v-if="showCode" ref="containerRef">
    <div :id="editorId" :key="editorId" style="height: 100%"></div>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
import { initEditor } from '../MonacoEditor/init'
import { debounce } from 'lodash-es'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'

const props = defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  originCode: {
    type: Object as PropType<{
      code: string
      language: string
    }>,
    default: () => ({
      language: '',
      code: ''
    })
  },
  modifiedCode: {
    type: Object as PropType<{
      code: string
      language: string
    }>,
    default: () => ({
      language: '',
      code: ''
    })
  },
  miniMap: {
    type: <PERSON>olean,
    default: false
  },
  lineWrapping: {
    type: Boolean,
    default: false
  },
  folding: {
    type: Boolean,
    default: true
  },
  lineDecorationsWidth: {
    type: Number,
    default: 10
  },
  showLine: {
    type: Boolean,
    default: true
  },
  readOnly: {
    type: Boolean,
    default: false
  }
})

const editorId = ref(`diff-editor_${Date.now()}`)
const showCode = ref(false)

const containerRef = ref()

let diffEditor: monaco.editor.IStandaloneDiffEditor | null = null
let originalModel: monaco.editor.ITextModel | null = null
let modifiedModel: monaco.editor.ITextModel | null = null

let codeObserver: ResizeObserver | null = null
let lastCodeSize = {
  width: '0',
  height: '0'
}

const init = () => {
  nextTick(() => {
    const container = document.getElementById(editorId.value)
    if (!container) {
      console.error('CodeDiffEditor: Container not found', editorId.value)
      return
    }

    // 确保有有效的代码内容，并处理换行符问题
    let originalCode = props.originCode.code || ''
    let modifiedCode = props.modifiedCode.code || ''
    const language = props.originCode.language || props.modifiedCode.language || 'text'

    // 统一换行符，避免 \r\n 和 \n 混用导致的差异检测问题
    originalCode = originalCode.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
    modifiedCode = modifiedCode.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

    console.log('CodeDiffEditor init:', {
      originalCode: originalCode.substring(0, 50) + '...',
      modifiedCode: modifiedCode.substring(0, 50) + '...',
      language,
      originalLength: originalCode.length,
      modifiedLength: modifiedCode.length,
      hasCarriageReturn: props.originCode.code.includes('\r') || props.modifiedCode.code.includes('\r'),
      contentsDiffer: originalCode !== modifiedCode
    })

    originalModel = monaco.editor.createModel(originalCode, language)
    modifiedModel = monaco.editor.createModel(modifiedCode, language)

    // 确保容器有明确的尺寸
    if (container.offsetHeight === 0) {
      container.style.height = '400px'
    }
    if (container.offsetWidth === 0) {
      container.style.width = '100%'
    }

    // 创建 diff 编辑器，使用优化配置确保差异能正确显示
    diffEditor = monaco.editor.createDiffEditor(container, {
      renderSideBySide: true, // 并排显示
      readOnly: props.readOnly,
      automaticLayout: true, // 自动布局
      minimap: { enabled: false }, // 禁用 minimap 避免布局问题
      lineNumbers: props.showLine ? 'on' : 'off',
      wordWrap: props.lineWrapping ? 'on' : 'off',
      folding: props.folding,
      theme: 'vs',
      // 关键差异显示配置
      ignoreTrimWhitespace: false, // 不忽略空白字符差异
      renderOverviewRuler: true, // 显示概览标尺
      renderIndicators: true, // 显示差异指示器
      enableSplitViewResizing: true, // 允许调整面板大小
      // 强制使用基础差异算法，确保能检测到差异
      diffAlgorithm: 'legacy', // 使用传统算法，更稳定
      // 确保差异装饰器正确显示
      renderLineHighlight: 'all',
      // 显示空白字符，有助于发现差异
      renderWhitespace: 'boundary'
    })
    // 设置模型
    diffEditor.setModel({
      original: originalModel,
      modified: modifiedModel
    })

    console.log('DiffEditor created, models set')

    // 延迟布局刷新，确保容器已经渲染完成
    setTimeout(() => {
      if (diffEditor) {
        diffEditor.layout()
        console.log('Layout refreshed')

        // 再次刷新确保差异正确显示
        setTimeout(() => {
          if (diffEditor) {
            diffEditor.layout()

            // 强制重新计算差异
            diffEditor.updateOptions({})

            // 获取差异信息用于调试
            const changes = diffEditor.getLineChanges()
            console.log('Line changes detected:', changes?.length || 0)

            // 如果没有检测到差异但内容明显不同，尝试其他方法
            if ((!changes || changes.length === 0) && originalCode !== modifiedCode) {
              console.warn('Content differs but no line changes detected, trying to force refresh...')

              // 尝试重新设置模型
              setTimeout(() => {
                if (diffEditor && originalModel && modifiedModel) {
                  diffEditor.setModel({
                    original: originalModel,
                    modified: modifiedModel
                  })

                  // 再次检查差异
                  setTimeout(() => {
                    if (diffEditor) {
                      const newChanges = diffEditor.getLineChanges()
                      console.log('After model reset, line changes:', newChanges?.length || 0)

                      if (newChanges && newChanges.length > 0) {
                        diffEditor.revealLineInCenter(newChanges[0].modifiedStartLineNumber || 1)
                      }
                    }
                  }, 200)
                }
              }, 100)
            } else if (changes && changes.length > 0) {
              // 如果有差异，滚动到第一个差异
              diffEditor.revealLineInCenter(changes[0].modifiedStartLineNumber || 1)
            }
          }
        }, 300)
      }
    }, 100)
    codeObserver = new ResizeObserver(
      debounce(() => {
        if (!containerRef.value) {
          return
        }
        const width = getComputedStyle(containerRef?.value).getPropertyValue('width')
        const height = getComputedStyle(containerRef?.value).getPropertyValue('height')
        if (width === lastCodeSize.width && height === lastCodeSize.height) return
        lastCodeSize.height = height
        lastCodeSize.width = width
        resizeCoder()
      }, 300)
    )

    if (containerRef.value) {
      codeObserver.observe(containerRef.value)
    }
  })
}

function resizeCoder() {
  diffEditor?.layout()
}

function getLineChanges() {
  return diffEditor?.getLineChanges()
}

watch(
  () => props.isShow,
  (isShow) => {
    if (!isShow || diffEditor) return
    showCode.value = true
    init()
  },
  { immediate: true }
)

watch(
  () => props.originCode.code,
  (code) => {
    originalModel?.setValue(code)
  },
  { immediate: true }
)

watch(
  () => props.modifiedCode.code,
  (code) => {
    modifiedModel?.setValue(code)
  },
  { immediate: true }
)

watch(
  () => props.originCode.language,
  (lang) => {
    if (originalModel) {
      monaco.editor.setModelLanguage(originalModel, lang)
    }
  }
)

watch(
  () => props.modifiedCode.language,
  (lang) => {
    if (modifiedModel) {
      monaco.editor.setModelLanguage(modifiedModel, lang)
    }
  }
)

onBeforeUnmount(() => {
  if (diffEditor) {
    modifiedModel?.dispose()
    originalModel?.dispose()
    diffEditor.dispose()
    diffEditor = null
    originalModel = null
    modifiedModel = null
    codeObserver = null
    lastCodeSize = {
      width: '0',
      height: '0'
    }
  }
})

onBeforeMount(() => {
  initEditor()
})

defineExpose({
  getLineChanges
})
</script>
<style lang="scss" scoped>
.container-code {
  width: 100%;
  height: 100%;
}
:deep(.select-run-line) {
  width: 3px !important;
  margin-left: -38px;
  background: #3bbb6e;
}
:deep(.run-widget) {
  left: 3px;
  display: inline-flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 62px;
  height: 20px;
  border-color: #ffffff;
  font-size: 12px;
  color: #567aff;
  background: #ffffff;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }
  .iconfont {
    position: relative;
    top: 1px;
  }
}
</style>
<style lang="scss">
html {
  .container-code {
    .margin {
      background: #f7f9fc;
    }
    .monaco-editor-background {
      background: #ffffff;
    }
    .minimap-slider-horizontal {
      background-color: transparent;
    }
    .minimap-slider {
      display: block !important;
    }
    .minimap-slider:hover {
      .minimap-slider-horizontal {
        display: block;
        box-sizing: border-box;
        border-color: #ccd7ff;
        background: #eef2fe;
      }
    }
    &.read-only {
      .monaco-editor-background {
        background-color: #f7f9fc;
      }
    }
  }
}
</style>
