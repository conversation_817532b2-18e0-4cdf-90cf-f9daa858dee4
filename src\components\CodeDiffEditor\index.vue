<template>
  <div class="container-code read-only" v-if="showCode" ref="containerRef">
    <div :id="editorId" :key="editorId" style="height: 100%"></div>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
import { initEditor } from '../MonacoEditor/init'
import { debounce } from 'lodash-es'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'

const props = defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  originCode: {
    type: Object as PropType<{
      code: string
      language: string
    }>,
    default: () => ({
      language: '',
      code: ''
    })
  },
  modifiedCode: {
    type: Object as PropType<{
      code: string
      language: string
    }>,
    default: () => ({
      language: '',
      code: ''
    })
  },
  miniMap: {
    type: <PERSON>olean,
    default: false
  },
  lineWrapping: {
    type: Boolean,
    default: false
  },
  folding: {
    type: Boolean,
    default: true
  },
  lineDecorationsWidth: {
    type: Number,
    default: 10
  },
  showLine: {
    type: Boolean,
    default: true
  },
  readOnly: {
    type: Boolean,
    default: false
  }
})

const editorId = ref(`diff-editor_${Date.now()}`)
const showCode = ref(false)

const containerRef = ref()

let diffEditor: monaco.editor.IStandaloneDiffEditor | null = null
let originalModel: monaco.editor.ITextModel | null = null
let modifiedModel: monaco.editor.ITextModel | null = null

let codeObserver: ResizeObserver | null = null
let lastCodeSize = {
  width: '0',
  height: '0'
}

const init = () => {
  nextTick(() => {
    const container = document.getElementById(editorId.value)
    if (!container) {
      console.error('CodeDiffEditor: Container not found', editorId.value)
      return
    }

    originalModel = monaco.editor.createModel(props.originCode.code, props.originCode.language)
    modifiedModel = monaco.editor.createModel(props.modifiedCode.code, props.modifiedCode.language)

    // 确保容器有明确的尺寸
    if (container.offsetHeight === 0) {
      container.style.height = '400px'
    }

    diffEditor = monaco.editor.createDiffEditor(container, {
      enableSplitViewResizing: false,
      renderSideBySide: true, // 确保并排显示
      renderOverviewRuler: true, // 显示概览标尺
      ignoreTrimWhitespace: false, // 不忽略空白字符差异
      minimap: {
        enabled: props.miniMap // 是否启用预览图
      },
      lineNumbers: props.showLine ? 'on' : 'off',
      wordWrap: props.lineWrapping ? 'on' : 'off',
      folding: props.folding,
      lineDecorationsWidth: props.lineDecorationsWidth,
      readOnly: props.readOnly,
      automaticLayout: false, // 手动控制布局
      theme: 'vs', // 使用默认主题
      diffCodeLens: true, // 显示代码镜头
      diffAlgorithm: 'advanced' // 使用高级差异算法
    })

    diffEditor.setModel({
      original: originalModel,
      modified: modifiedModel
    })

    // 强制刷新布局，确保 diff 正确显示
    setTimeout(() => {
      diffEditor?.layout()
      // 再次确保布局正确
      setTimeout(() => {
        diffEditor?.layout()
        // 强制重新计算差异
        diffEditor?.revealLineInCenter(1)
      }, 200)
    }, 100)
    codeObserver = new ResizeObserver(
      debounce(() => {
        if (!containerRef.value) {
          return
        }
        const width = getComputedStyle(containerRef?.value).getPropertyValue('width')
        const height = getComputedStyle(containerRef?.value).getPropertyValue('height')
        if (width === lastCodeSize.width && height === lastCodeSize.height) return
        lastCodeSize.height = height
        lastCodeSize.width = width
        resizeCoder()
      }, 300)
    )

    if (containerRef.value) {
      codeObserver.observe(containerRef.value)
    }
  })
}

function resizeCoder() {
  diffEditor?.layout()
}

function getLineChanges() {
  return diffEditor?.getLineChanges()
}

watch(
  () => props.isShow,
  (isShow) => {
    if (!isShow || diffEditor) return
    showCode.value = true
    init()
  },
  { immediate: true }
)

watch(
  () => props.originCode.code,
  (code) => {
    originalModel?.setValue(code)
  },
  { immediate: true }
)

watch(
  () => props.modifiedCode.code,
  (code) => {
    modifiedModel?.setValue(code)
  },
  { immediate: true }
)

watch(
  () => props.originCode.language,
  (lang) => {
    if (originalModel) {
      monaco.editor.setModelLanguage(originalModel, lang)
    }
  }
)

watch(
  () => props.modifiedCode.language,
  (lang) => {
    if (modifiedModel) {
      monaco.editor.setModelLanguage(modifiedModel, lang)
    }
  }
)

onBeforeUnmount(() => {
  if (diffEditor) {
    modifiedModel?.dispose()
    originalModel?.dispose()
    diffEditor.dispose()
    diffEditor = null
    originalModel = null
    modifiedModel = null
    codeObserver = null
    lastCodeSize = {
      width: '0',
      height: '0'
    }
  }
})

onBeforeMount(() => {
  initEditor()
})

defineExpose({
  getLineChanges
})
</script>
<style lang="scss" scoped>
.container-code {
  width: 100%;
  height: 100%;
}
:deep(.select-run-line) {
  width: 3px !important;
  margin-left: -38px;
  background: #3bbb6e;
}
:deep(.run-widget) {
  left: 3px;
  display: inline-flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 62px;
  height: 20px;
  border-color: #ffffff;
  font-size: 12px;
  color: #567aff;
  background: #ffffff;
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
  }
  .iconfont {
    position: relative;
    top: 1px;
  }
}
</style>
<style lang="scss">
html {
  .container-code {
    .margin {
      background: #f7f9fc;
    }
    .monaco-editor-background {
      background: #ffffff;
    }
    .minimap-slider-horizontal {
      background-color: transparent;
    }
    .minimap-slider {
      display: block !important;
    }
    .minimap-slider:hover {
      .minimap-slider-horizontal {
        display: block;
        box-sizing: border-box;
        border-color: #ccd7ff;
        background: #eef2fe;
      }
    }
    &.read-only {
      .monaco-editor-background {
        background-color: #f7f9fc;
      }
    }
  }
}
</style>
