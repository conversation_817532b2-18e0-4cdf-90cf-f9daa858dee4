{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/login/src/constants.ts"], "sourcesContent": ["export default {\n  \"zh-CN\": {\n    CyberEngine: {\n      desc: \"一站式云原生大数据平台\",\n    },\n    CyberData: {\n      desc: \"一站式多云大数据研发治理平台\",\n    },\n    CyberAI: {\n      desc: \"一站式机器学习生态服务平台\",\n    },\n    welcome: \"欢迎回来\",\n    usernameP: \"请输入用户名\",\n    passwordP: \"请输入密码\",\n    codeP: \"请输入验证码\",\n    usernameR: \"请输入用户名\",\n    passwordR: \"请输入密码\",\n    codeR: \"请输入验证码\",\n    login: \"登录\",\n    dialogTitle: \"请选择您要进入的平台\",\n    recentlyUsed: \"最近使用\",\n    register: \"注册\",\n    registerText: \"没有账号？立即注册\",\n    loginText: \"已有账号，立即登录\",\n    phoneP: \"请输入手机号\",\n    phoneR: \"请输入合法的电话号码\",\n    emailP: \"请输入邮箱地址\",\n    emailR: \"请输入合法的邮箱地址\",\n    registerSu: \"注册成功，即将跳转登录\",\n    usernameRule: \"长度6~16个字符，仅支持包含字母、数字、-或_\",\n  },\n  \"en-US\": {\n    CyberEngine: {\n      desc: \"One-stop cloud-native big data platform\",\n    },\n    CyberData: {\n      desc: \"One-stop multi-cloud big data R&D management platform\",\n    },\n    CyberAI: {\n      desc: \"One-stop machine learning ecological service platform\",\n    },\n    welcome: \"Welcome back\",\n    usernameP: \"User name\",\n    passwordP: \"Password\",\n    codeP: \"Verification code\",\n    usernameR: \"Please enter user name\",\n    passwordR: \"Please enter password\",\n    codeR: \"Please enter the verification code\",\n    login: \"Log in\",\n    dialogTitle: \"Please select the platform you want to enter\",\n    recentlyUsed: \"Recently Used\",\n    register: \"Register\",\n    registerText: \"Don't have an account? Register Now\",\n    loginText: \"Already have an account, log in now\",\n    phoneP: \"Please enter your phone number\",\n    phoneR: \"Please enter a valid phone number\",\n    emailP: \"Please enter your email address\",\n    emailR: \"Please enter a valid email address\",\n    registerSu: \"Registration successful, will be redirected to login soon\",\n    usernameRule: \"6~16 characters, only letters, numbers, - and _ are allowed\",\n  },\n  \"ja-JP\": {\n    CyberEngine: {\n      desc: \"ワンストップクラウドネイティブビッグデータプラットフォーム\",\n    },\n    CyberData: {\n      desc: \"ワンストップ型マルチクラウドビッグデータ研究開発管理プラットフォーム\",\n    },\n    CyberAI: {\n      desc: \"ワンストップ型マルチクラウドビッグデータ研究開発管理プラットフォーム\",\n    },\n    welcome: \"お帰りなさい\",\n    usernameP: \"ユーザー名\",\n    passwordP: \"パスワード\",\n    codeP: \"認証コード\",\n    usernameR: \"ユーザー名を入力してください\",\n    passwordR: \"パスワードを入力してください\",\n    codeR: \"認証コードを入力してください\",\n    login: \"ログイン\",\n    dialogTitle: \"アクセスするプラットフォームを選択してください\",\n    recentlyUsed: \"最近使用した\",\n    register: \"登録\",\n    registerText: \"アカウントはありませんか？今すぐ登録\",\n    loginText: \"既存のアカウント、すぐにログイン\",\n    phoneP: \"携帯番号を入力してください\",\n    phoneR: \"合法的な電話番号を入力してください\",\n    emailP: \"メールアドレスを入力してください\",\n    emailR: \"適切なメールアドレスを入力してください\",\n    registerSu: \"登録に成功しました。ジャンプログインしようとしています\",\n    usernameRule: \"文字、数字、-または_\",\n  },\n};\n"], "names": [], "mappings": "AAAA,aAAe;AACf,EAAE,OAAO,EAAE;AACX,IAAI,WAAW,EAAE;AACjB,MAAM,IAAI,EAAE,oEAAoE;AAChF,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE,sFAAsF;AAClG,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,gFAAgF;AAC5F,KAAK;AACL,IAAI,OAAO,EAAE,0BAA0B;AACvC,IAAI,SAAS,EAAE,sCAAsC;AACrD,IAAI,SAAS,EAAE,gCAAgC;AAC/C,IAAI,KAAK,EAAE,sCAAsC;AACjD,IAAI,SAAS,EAAE,sCAAsC;AACrD,IAAI,SAAS,EAAE,gCAAgC;AAC/C,IAAI,KAAK,EAAE,sCAAsC;AACjD,IAAI,KAAK,EAAE,cAAc;AACzB,IAAI,WAAW,EAAE,8DAA8D;AAC/E,IAAI,YAAY,EAAE,0BAA0B;AAC5C,IAAI,QAAQ,EAAE,cAAc;AAC5B,IAAI,YAAY,EAAE,wDAAwD;AAC1E,IAAI,SAAS,EAAE,wDAAwD;AACvE,IAAI,MAAM,EAAE,sCAAsC;AAClD,IAAI,MAAM,EAAE,8DAA8D;AAC1E,IAAI,MAAM,EAAE,4CAA4C;AACxD,IAAI,MAAM,EAAE,8DAA8D;AAC1E,IAAI,UAAU,EAAE,oEAAoE;AACpF,IAAI,YAAY,EAAE,oHAAoH;AACtI,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,WAAW,EAAE;AACjB,MAAM,IAAI,EAAE,yCAAyC;AACrD,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE,uDAAuD;AACnE,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,uDAAuD;AACnE,KAAK;AACL,IAAI,OAAO,EAAE,cAAc;AAC3B,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,KAAK,EAAE,mBAAmB;AAC9B,IAAI,SAAS,EAAE,wBAAwB;AACvC,IAAI,SAAS,EAAE,uBAAuB;AACtC,IAAI,KAAK,EAAE,oCAAoC;AAC/C,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,WAAW,EAAE,8CAA8C;AAC/D,IAAI,YAAY,EAAE,eAAe;AACjC,IAAI,QAAQ,EAAE,UAAU;AACxB,IAAI,YAAY,EAAE,qCAAqC;AACvD,IAAI,SAAS,EAAE,qCAAqC;AACpD,IAAI,MAAM,EAAE,gCAAgC;AAC5C,IAAI,MAAM,EAAE,mCAAmC;AAC/C,IAAI,MAAM,EAAE,iCAAiC;AAC7C,IAAI,MAAM,EAAE,oCAAoC;AAChD,IAAI,UAAU,EAAE,2DAA2D;AAC3E,IAAI,YAAY,EAAE,6DAA6D;AAC/E,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,WAAW,EAAE;AACjB,MAAM,IAAI,EAAE,gLAAgL;AAC5L,KAAK;AACL,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE,8MAA8M;AAC1N,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,8MAA8M;AAC1N,KAAK;AACL,IAAI,OAAO,EAAE,sCAAsC;AACnD,IAAI,SAAS,EAAE,gCAAgC;AAC/C,IAAI,SAAS,EAAE,gCAAgC;AAC/C,IAAI,KAAK,EAAE,gCAAgC;AAC3C,IAAI,SAAS,EAAE,sFAAsF;AACrG,IAAI,SAAS,EAAE,sFAAsF;AACrG,IAAI,KAAK,EAAE,sFAAsF;AACjG,IAAI,KAAK,EAAE,0BAA0B;AACrC,IAAI,WAAW,EAAE,4IAA4I;AAC7J,IAAI,YAAY,EAAE,sCAAsC;AACxD,IAAI,QAAQ,EAAE,cAAc;AAC5B,IAAI,YAAY,EAAE,8GAA8G;AAChI,IAAI,SAAS,EAAE,kGAAkG;AACjH,IAAI,MAAM,EAAE,gFAAgF;AAC5F,IAAI,MAAM,EAAE,wGAAwG;AACpH,IAAI,MAAM,EAAE,kGAAkG;AAC9G,IAAI,MAAM,EAAE,oHAAoH;AAChI,IAAI,UAAU,EAAE,oKAAoK;AACpL,IAAI,YAAY,EAAE,0DAA0D;AAC5E,GAAG;AACH,CAAC;;;;"}