'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var _default = require('./default.js');
var index_js = require('./components/index.js');
var index_js$1 = require('./constants/index.js');
var index_js$2 = require('./utils/index.js');
var makeInstaller = require('./make-installer.js');

const install = _default["default"].install;

exports["default"] = _default["default"];
exports.makeInstaller = makeInstaller.makeInstaller;
exports.install = install;
Object.keys(index_js).forEach(function (k) {
	if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return index_js[k]; }
	});
});
Object.keys(index_js$1).forEach(function (k) {
	if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return index_js$1[k]; }
	});
});
Object.keys(index_js$2).forEach(function (k) {
	if (k !== 'default' && !exports.hasOwnProperty(k)) Object.defineProperty(exports, k, {
		enumerable: true,
		get: function () { return index_js$2[k]; }
	});
});
//# sourceMappingURL=index.js.map
