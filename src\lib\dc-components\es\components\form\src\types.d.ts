import type { CascaderOption, FormItemRule, Options } from "element-plus";
import type { Ref, VNode } from "vue";
import type { DcCheckboxOptions } from "../../checkbox/src/types";
import type { DcRadioOptions } from "../../radio/src/types";
import type { RenderVNodeFn } from "../../render-vnode/src/types";
import type { DcSelectGroups, DcSelectOptions } from "../../select/src/types";
import type { DcFormTableColumnItem } from "./components/Table/types";
import type DcForm from "./index.vue";
export interface DcFormVisibleControl {
    [k: string]: boolean | Ref<boolean>;
}
export declare type DcFormModel = Record<string, any>;
export declare enum DcFormComponentEnum {
    INPUT = "input",
    TEXT = "text",
    SELECT = "select",
    RADIO = "radio",
    CHECKBOX = "checkbox",
    AUTOCOMPLETE = "autocomplete",
    CASCADER = "cascader",
    CASCADERPANEL = "cascaderPanel",
    COLORPICKER = "colorPicker",
    DATEPICKER = "datePicker",
    INPUTNUMBER = "inputNumber",
    RATE = "rate",
    SELECTV2 = "selectV2",
    SLIDER = "slider",
    SWITCH = "switch",
    TIMEPICKER = "timePicker",
    TIMESELECT = "timeSelect",
    TRANSFER = "transfer",
    UPLOAD = "upload",
    TABLE = "table",
    CUSTOM = "custom"
}
export declare type DcFormItemVisible = (model: DcFormModel) => boolean;
export declare type DcFormItemEffect = (model: DcFormModel, changeModel: (model: DcFormModel) => void, rowIndex?: number) => void;
export interface DcFormItemMargin {
    right?: string;
    bottom?: string;
    top?: string;
    left?: string;
}
export interface DcFormItemBase {
    model: string;
    defaultValue: any;
    rules?: FormItemRule[];
    label?: string;
    labelWidth?: string | number;
    disabled?: boolean;
    component: DcFormComponentEnum;
    inlineMessage?: boolean;
    labelSlot?: RenderVNodeFn;
    visible?: DcFormItemVisible;
    effect?: DcFormItemEffect;
    props?: Record<string, any>;
    componentSlot?: Record<string, RenderVNodeFn>;
    rightSlot?: RenderVNodeFn;
    margin?: DcFormItemMargin;
    class?: string;
}
export declare type DcFormInput = DcFormItemBase;
export declare type DcFormInputNumber = DcFormItemBase;
export declare type DcFormText = DcFormItemBase;
export declare type DcFormCustomRenderFn = ({ value, onChange, rowIndex, }: {
    value: any;
    onChange: (val: any) => void;
    rowIndex: number;
}) => VNode;
export interface DcFormSelect extends DcFormItemBase {
    props: {
        groups?: DcSelectGroups;
        options?: DcSelectOptions;
        [k: string]: any;
    };
}
export interface DcFormRadio extends DcFormItemBase {
    props: {
        options: DcRadioOptions;
        [k: string]: any;
    };
}
export interface DcFormCheckbox extends DcFormItemBase {
    props: {
        options: DcCheckboxOptions;
        [k: string]: any;
    };
}
export declare type DcFormAutoComplete = DcFormItemBase;
export interface DcFormCascader extends DcFormItemBase {
    props: {
        options: CascaderOption[] | Ref<CascaderOption[]>;
        [k: string]: any;
    };
}
export interface DcFormCascaderPanel extends DcFormItemBase {
    props: {
        options: CascaderOption[] | Ref<CascaderOption[]>;
        [k: string]: any;
    };
}
export declare type DcFormColorPicker = DcFormItemBase;
export declare type DcFormDatePicker = DcFormItemBase;
export declare type DcFormRate = DcFormItemBase;
export interface DcFormTable extends DcFormItemBase {
    props: {
        columns: DcFormTableColumnItem[];
        topSlot: RenderVNodeFn;
        bottomSlot: RenderVNodeFn;
        [k: string]: any;
    };
}
export interface DcFormSelectV2 extends DcFormItemBase {
    props: {
        options: Options;
        [k: string]: any;
    };
}
export declare type DcFormSlider = DcFormItemBase;
export declare type DcFormSwitch = DcFormItemBase;
export declare type DcFormTimePicker = DcFormItemBase;
export declare type DcFormTimeSelect = DcFormItemBase;
export declare type DcFormTransfer = DcFormItemBase;
export declare type DcFormUpload = DcFormItemBase;
export interface DcFormCustom extends DcFormItemBase {
    props: {
        renderCustom: DcFormCustomRenderFn;
        [k: string]: any;
    };
}
export declare type DcFormItem = DcFormInput | DcFormInputNumber | DcFormSelect | DcFormSelectV2 | DcFormText | DcFormRadio | DcFormCheckbox | DcFormAutoComplete | DcFormColorPicker | DcFormDatePicker | DcFormCascaderPanel | DcFormRate | DcFormSlider | DcFormSwitch | DcFormTimePicker | DcFormTimeSelect | DcFormTransfer | DcFormUpload | DcFormTable | DcFormCustom | DcFormCascader;
export interface DcFormConfig {
    inline?: boolean;
    labelPosition?: "left" | "right" | "top";
    labelWidth?: string | number;
    showMessage?: boolean;
    inlineMessage?: boolean;
    disabled?: boolean;
    itemMargin?: DcFormItemMargin;
    children: DcFormItem[];
}
export declare type DcFormInstance = InstanceType<typeof DcForm>;
