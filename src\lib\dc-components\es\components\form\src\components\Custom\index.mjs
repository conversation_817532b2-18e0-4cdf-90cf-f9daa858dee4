import { defineComponent } from 'vue';
import './index2.mjs';

var DcFormCustom = defineComponent({
  name: "DcFormCustom",
  props: {
    modelValue: {},
    renderCustom: {
      type: Function
    },
    rowIndex: {
      type: Number,
      default: 0
    }
  },
  emits: {
    "update:modelValue": (val) => true
  },
  setup(props, ctx) {
    const onChange = (val) => {
      ctx.emit("update:modelValue", val);
    };
    return {
      onChange
    };
  },
  render() {
    const vNode = this.$props.renderCustom ? this.$props.renderCustom({
      value: this.$props.modelValue,
      onChange: this.onChange,
      rowIndex: this.$props.rowIndex
    }) : null;
    return vNode;
  }
});

export { DcFormCustom as default };
//# sourceMappingURL=index.mjs.map
