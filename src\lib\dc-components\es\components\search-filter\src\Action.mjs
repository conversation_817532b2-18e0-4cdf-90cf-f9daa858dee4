import { defineComponent, h, computed, openBlock, createElementBlock, normalizeClass, createBlock, mergeProps, createSlots, withCtx, createTextVNode, toDisplayString, renderList, createVNode, unref, createCommentVNode } from 'vue';
import <PERSON>c<PERSON><PERSON>on from '../../button/src/index.mjs';
import RenderVNode from '../../render-vnode/src/index.mjs';
import Refresh from './Refresh.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';

const _sfc_main = defineComponent({
  __name: "Action",
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    clsPrefix: {
      type: String,
      default: ""
    }
  },
  emits: ["query", "reset", "refresh"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const defaultQueryAction = {
      innerText: "\u67E5\u8BE2",
      props: {
        type: "primary"
      }
    };
    const defaultResetAction = {
      innerText: "\u91CD\u7F6E"
    };
    const defaultRefreshAction = {
      innerText: "",
      componentSlot: {
        default: () => h(Refresh)
      }
    };
    const queryAction = computed(() => props.config.queryAction ? {
      ...defaultQueryAction,
      ...props.config.queryAction,
      props: {
        ...defaultQueryAction.props,
        ...props.config.queryAction.props || {}
      }
    } : defaultQueryAction);
    const resetAction = computed(() => props.config.resetAction ? { ...defaultResetAction, ...props.config.resetAction } : defaultResetAction);
    const refreshAction = computed(() => props.config.refreshAction ? { ...defaultRefreshAction, ...props.config.refreshAction } : defaultRefreshAction);
    const onQuery = () => {
      emits("query");
    };
    const onReset = () => {
      emits("reset");
    };
    const onRefresh = () => {
      emits("refresh");
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(`${props.clsPrefix}-operate`)
      }, [
        queryAction.value.visible !== false ? (openBlock(), createBlock(DcButton, mergeProps({ key: 0 }, { ...queryAction.value.props }, {
          config: queryAction.value.config,
          onClick: onQuery
        }), createSlots({
          default: withCtx(() => [
            createTextVNode(" " + toDisplayString(queryAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          renderList(Object.keys(queryAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: withCtx((scope) => [
                createVNode(unref(RenderVNode), {
                  vnode: (queryAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : createCommentVNode("v-if", true),
        resetAction.value.visible !== false ? (openBlock(), createBlock(DcButton, mergeProps({ key: 1 }, { ...resetAction.value.props }, {
          config: resetAction.value.config,
          onClick: onReset
        }), createSlots({
          default: withCtx(() => [
            createTextVNode(" " + toDisplayString(resetAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          renderList(Object.keys(resetAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: withCtx((scope) => [
                createVNode(unref(RenderVNode), {
                  vnode: (resetAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : createCommentVNode("v-if", true),
        refreshAction.value.visible !== false ? (openBlock(), createBlock(DcButton, mergeProps({ key: 2 }, { ...refreshAction.value.props }, {
          config: refreshAction.value.config,
          onClick: onRefresh
        }), createSlots({
          default: withCtx(() => [
            createTextVNode(" " + toDisplayString(refreshAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          renderList(Object.keys(refreshAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: withCtx((scope) => [
                createVNode(unref(RenderVNode), {
                  vnode: (refreshAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : createCommentVNode("v-if", true)
      ], 2);
    };
  }
});
var Action = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\search-filter\\src\\Action.vue"]]);

export { Action as default };
//# sourceMappingURL=Action.mjs.map
