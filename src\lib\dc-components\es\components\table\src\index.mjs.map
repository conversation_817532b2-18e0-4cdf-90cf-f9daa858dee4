{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/table/src/index.vue"], "sourcesContent": ["<template>\r\n  <el-table\r\n    ref=\"$refs\"\r\n    class=\"dc-table\"\r\n    v-bind=\"$attrs\"\r\n    :style=\"{ height: props.height }\"\r\n  >\r\n    <el-table-column v-if=\"isColumnFilter\" width=\"48px\" align=\"center\">\r\n      <template #header>\r\n        <ColumnsFilter\r\n          style=\"position: relative; top: 3px\"\r\n          :columns=\"columns\"\r\n          :filter-submit-text=\"filterSubmitText\"\r\n          :filter-cancel-text=\"filterCancelText\"\r\n          :filter-all-text=\"filterAllText\"\r\n          :filter-reset-text=\"filterResetText\"\r\n          @change=\"onColumnsFilterChange\"\r\n        />\r\n      </template>\r\n    </el-table-column>\r\n    <slot name=\"before\" />\r\n    <el-table-column\r\n      v-if=\"props.showSelectColumn\"\r\n      type=\"selection\"\r\n      align=\"center\"\r\n      :width=\"props.selectionWidth\"\r\n      :selectable=\"props.selectable\"\r\n    />\r\n    <template v-for=\"(col, index) in columnsData\">\r\n      <el-table-column v-if=\"col?.slot\" v-bind=\"col\" :key=\"index\">\r\n        <template #default=\"{ row, column, $index }\">\r\n          <!--  eslint-disable-next-line vue/valid-attribute-name -->\r\n          <slot :name=\"col.prop\" :row=\"row\" :column=\"column\" :$index=\"$index\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        v-if=\"col.type === 'expand' && !col?.slot\"\r\n        v-bind=\"col\"\r\n        :key=\"index\"\r\n      >\r\n        <template #default=\"{ row, column, $index }\">\r\n          <component :is=\"col.formatter(row, column, row[col.prop], $index)\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        v-if=\"!col?.slot && col.type !== 'expand'\"\r\n        v-bind=\"col\"\r\n        :key=\"index\"\r\n      />\r\n    </template>\r\n    <slot name=\"after\" />\r\n    <template #append>\r\n      <slot name=\"append\">\r\n        <template v-if=\"append && typeof append() === 'string'\">\r\n          {{ append() }}\r\n        </template>\r\n        <template v-else-if=\"append\">\r\n          <component :is=\"append\" />\r\n        </template>\r\n      </slot>\r\n    </template>\r\n    <template #empty>\r\n      <DcEmpty :show-img=\"showEmptyImg\" :locale=\"locale\" :theme=\"theme\" />\r\n    </template>\r\n  </el-table>\r\n</template>\r\n\r\n<script lang=\"ts\" setup>\r\n// eslint-disable-next-line import/order\r\nimport { ElLoading, ElTable, ElTableColumn } from \"element-plus\";\r\nimport DcEmpty from \"../../empty/src/index.vue\";\r\nimport ColumnsFilter from \"./components/ColumnsFilter.vue\";\r\n// eslint-disable-next-line import/order\r\nimport { computed, nextTick, ref, watch } from \"vue\";\r\n// eslint-disable-next-line import/order\r\nimport type { PropType, VNode } from \"vue\";\r\nimport type { DcTableColumnItem } from \"./types\";\r\nimport type { DcLocaleType, DcThemeType } from \"../../types\";\r\n// import { cloneDeep } from \"lodash\";\r\ndefineOptions({\r\n  name: \"DcTable\",\r\n});\r\n\r\nconst props = defineProps({\r\n  columns: {\r\n    type: Array as PropType<DcTableColumnItem[] | any>,\r\n  },\r\n  isColumnFilter: {\r\n    type: Boolean,\r\n  },\r\n  append: {\r\n    type: Function as PropType<() => VNode | string>,\r\n  },\r\n  filterSubmitText: {\r\n    type: String,\r\n  },\r\n  filterCancelText: {\r\n    type: String,\r\n  },\r\n  filterAllText: {\r\n    type: String,\r\n  },\r\n  filterResetText: {\r\n    type: String,\r\n  },\r\n  height: {\r\n    type: String,\r\n    default: \"100%\",\r\n  },\r\n  loading: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  showSelectColumn: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  selectable: {\r\n    type: Function as PropType<(row: any, index: number) => boolean>,\r\n    default: () => true,\r\n  },\r\n  selectionWidth: {\r\n    type: Number,\r\n    default: 40,\r\n  },\r\n  showEmptyImg: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  locale: {\r\n    type: String as PropType<DcLocaleType>,\r\n    default: \"zh-CN\",\r\n  },\r\n  theme: {\r\n    type: String as PropType<DcThemeType>,\r\n    default: \"light\",\r\n  },\r\n});\r\n\r\n// 表格的ref\r\nconst $refs = ref<InstanceType<typeof ElTable>>();\r\n\r\nconst columnsFilter = ref<any>([]);\r\n\r\nlet loadingInstance: any;\r\n\r\nconst columnsData = computed(() => {\r\n  if (props?.isColumnFilter) {\r\n    return props?.columns?.filter(\r\n      (column: DcTableColumnItem) =>\r\n        (column?.columnDefault\r\n          ? columnsFilter.value.includes(column.label) && column?.columnDefault\r\n          : columnsFilter.value.includes(column.label)) ||\r\n        column?.columnRequired ||\r\n        !column.label\r\n    );\r\n  } else {\r\n    return props.columns;\r\n  }\r\n});\r\n\r\nconst doLoading = (loading: boolean) => {\r\n  if (loading) {\r\n    nextTick(() => {\r\n      if ($refs.value?.$el) {\r\n        loadingInstance = ElLoading.service({\r\n          target: $refs.value.$el,\r\n          background: \"transparent\",\r\n          body: false,\r\n          fullscreen: false,\r\n        });\r\n      }\r\n    });\r\n  } else {\r\n    loadingInstance?.close();\r\n  }\r\n};\r\n\r\nwatch(\r\n  () => props.loading,\r\n  (loading) => {\r\n    doLoading(loading);\r\n  },\r\n  { immediate: true }\r\n);\r\n\r\nfunction onColumnsFilterChange(checkList: any) {\r\n  columnsFilter.value = checkList;\r\n}\r\n// 把$ref暴露出去方便父组件使用\r\ndefineExpose({ $refs });\r\n</script>\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;;;;AA+Ec,EAAA,IAAA,EAAA,SAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,GAAA,EAAA,CAAA;AAyDd,IAAA,MAAM,aAA0C,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AAEhD,IAAM,IAAA,eAAA,CAAA;AAEN,IAAI,MAAA,WAAA,GAAA,QAAA,CAAA,MAAA;AAEJ,MAAM,IAAA,KAAA,EAAA,gBAAuB;AAC3B,QAAA,YAA2B,EAAA,OAAA,EAAA,MAAA,CAAA,CAAA,MAAA,KAAA,CAAA,MAAA,EAAA,aAAA,GAAA,aAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,MAAA,EAAA,aAAA,GAAA,aAAA,CAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,CAAA,KAAA,MAAA,EAAA,cAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACzB,OAAO,MAAA;AAOP,QACK,OAAA,KAAA,CAAA,OAAA,CAAA;AACL,OAAA;AAAa,KACf,CAAA,CAAA;AAAA,IACF,MAAC,SAAA,GAAA,CAAA,OAAA,KAAA;AAED,MAAM,IAAA,OAAA,EAAA;AACJ,QAAA,QAAa,CAAA,MAAA;AACX,UAAA,IAAA,KAAe,CAAA,KAAA,EAAA,GAAA,EAAA;AACb,YAAI,eAAa,GAAK,SAAA,CAAA,OAAA,CAAA;AACpB,cAAA,MAAA,EAAA,KAAA,CAAA;AAAoC,cAClC,yBAAoB;AAAA,cACpB,IAAY,EAAA,KAAA;AAAA,cACZ,UAAM,EAAA,KAAA;AAAA,aAAA,CACN,CAAY;AAAA,WAAA;AACb,SACH,CAAA,CAAA;AAAA,OAAA,MACD;AAAA,QACI,eAAA,EAAA,KAAA,EAAA,CAAA;AACL,OAAA;AAAuB,KACzB,CAAA;AAAA,IACF,KAAA,CAAA,MAAA,KAAA,CAAA,OAAA,EAAA,CAAA,OAAA,KAAA;AAEA,MAAA,SACQ,CAAA,OACN,CAAA,CAAA;AACE,KAAA,EAAA,EAAA,SAAiB,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IACnB,SACa,qBACf,CAAA,SAAA,EAAA;AAEA,MAAA,aAAA,CAAA,KAAA,GAAA,SAA+C,CAAA;AAC7C,KAAA;AAAsB,IACxB,QAAA,CAAA,EAAA,KAAA,EAAA,CAAA,CAAA;AAEA,IAAa,OAAA,CAAA,YAAS,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}