import { ElMessage } from 'element-plus'
import type { <PERSON>Fn, MessageHandler, MessageOptions, MessageParams } from 'element-plus'
import type { SFCInstallWithContext } from 'element-plus/es/utils'
import type { AppContext } from 'vue'

type MessageType = 'success' | 'warning' | 'info' | 'error'
type MessageParam = string | { type?: MessageType; message?: string; [key: string]: any }

type ExtendedHandler = (options: MessageParam) => any

interface ExtendedMessageFn extends MessageFn {
  success: ExtendedHandler
  warning: ExtendedHandler
  info: ExtendedHandler
  error: ExtendedHandler
  _context: AppContext | null
}

const createMessageOptions = (options: MessageParam, type?: MessageType): MessageOptions => {
  const baseOptions: MessageOptions = {
    showClose: true,
    duration: 3000,
    customClass: 'base-message'
  }

  const msgOptions = typeof options === 'string' ? { message: options } : options
  const messageType = type || msgOptions.type

  return {
    ...baseOptions,
    ...msgOptions,
    showClose: messageType !== 'success',
    duration: messageType === 'success' ? 2000 : 3000,
    type: messageType
  }
}

const BaseMessage = ((options?: MessageParam, appContext?: AppContext | null) =>
  ElMessage(createMessageOptions(options || ''), appContext)) as ExtendedMessageFn

BaseMessage._context = null

const messageTypes = ['success', 'warning', 'info', 'error'] as const
messageTypes.forEach((type) => {
  BaseMessage[type] = ((options: MessageParam) => ElMessage(createMessageOptions(options, type))) as unknown as ExtendedHandler
})

export default BaseMessage as unknown as SFCInstallWithContext<ExtendedMessageFn>
