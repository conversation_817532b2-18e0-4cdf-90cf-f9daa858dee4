'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/option/style/css');
require('element-plus/es/components/option-group/style/css');
require('element-plus/es/components/select/style/css');
var index = require('../../render-vnode/src/index.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const __default__ = vue.defineComponent({
  name: "DcSelect"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: [String, Number, Boolean, Array]
    },
    options: {
      type: [Array, Object],
      default: () => []
    },
    groups: {
      type: [Array, Object],
      default: () => []
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const attrs = vue.useAttrs();
    const options = vue.computed(() => {
      return vue.isRef(props.options) ? props.options.value : props.options;
    });
    const groups = vue.computed(() => {
      return vue.isRef(props.groups) ? props.groups.value : props.groups;
    });
    const handleChange = (val) => {
      emits("update:modelValue", val);
      if (typeof attrs.onChange === "function") {
        attrs.onChange(val);
      }
    };
    ;
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElSelect), vue.mergeProps({ ..._ctx.$attrs }, {
        "model-value": props.modelValue,
        onChange: handleChange
      }), {
        default: vue.withCtx(() => [
          groups.value.length ? (vue.openBlock(true), vue.createElementBlock(vue.Fragment, { key: 0 }, vue.renderList(groups.value, (group) => {
            return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElOptionGroup), {
              key: group.label,
              label: group.label,
              disabled: group.disabled
            }, {
              default: vue.withCtx(() => [
                (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(group.options, (item, index$1) => {
                  return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElOption), {
                    key: index$1,
                    label: item.label,
                    value: item.value,
                    disabled: item.disabled
                  }, vue.createSlots({ _: 2 }, [
                    item.defaultSlot ? {
                      name: "default",
                      fn: vue.withCtx((scope) => [
                        vue.createVNode(vue.unref(index["default"]), {
                          vnode: item.defaultSlot,
                          scope,
                          "extra-scope": item
                        }, null, 8, ["vnode", "scope", "extra-scope"])
                      ]),
                      key: "0"
                    } : void 0
                  ]), 1032, ["label", "value", "disabled"]);
                }), 128))
              ]),
              _: 2
            }, 1032, ["label", "disabled"]);
          }), 128)) : (vue.openBlock(true), vue.createElementBlock(vue.Fragment, { key: 1 }, vue.renderList(options.value, (item, index$1) => {
            return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElOption), {
              key: index$1,
              label: item.label,
              value: item.value
            }, vue.createSlots({ _: 2 }, [
              item.defaultSlot ? {
                name: "default",
                fn: vue.withCtx((scope) => [
                  vue.createVNode(vue.unref(index["default"]), {
                    vnode: item.defaultSlot,
                    scope,
                    "extra-scope": item
                  }, null, 8, ["vnode", "scope", "extra-scope"])
                ]),
                key: "0"
              } : void 0
            ]), 1032, ["label", "value"]);
          }), 128))
        ]),
        _: 1
      }, 16, ["model-value"]);
    };
  }
});
var Select = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\select\\src\\index.vue"]]);

exports["default"] = Select;
//# sourceMappingURL=index.js.map
