<template>
  <div>
    <div class="flex justify-between items-center px-20px pt-19px pb-20px">
      <TheTitle class="mb-0px!" :content="$t('replenish.inspectionConfiguration')" />
      <div>
        <template v-if="isEdit">
          <BaseButton type="primary" @click="submitForm(ruleFormRef)"> {{ $t('button.save') }} </BaseButton>
          <BaseButton type="info" @click="resetForm(ruleFormRef)">{{ $t('button.cancel') }}</BaseButton>
        </template>
        <template v-else>
          <BaseButton v-if="$has('inspection-config-update')" type="info" class="w-72px" @click="edit">
            <div class="w-50px lh-32px">
              <img src="@/assets/icons/icon-bianji.svg" class="mr-4px mb-4px inline-block" alt="icon" />
              <span>{{ $t('replenish.edit') }}</span>
            </div>
          </BaseButton>
        </template>
      </div>
    </div>
    <div class="px-20px">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="left" label-width="120px" class="demo-ruleForm">
        <el-form-item :label="$t('replenish.automaticInspection')" :required="isEdit" prop="enabled">
          <template v-if="!isEdit" #label="scope">
            <span class="label">{{ scope.label }}</span>
          </template>
          <el-switch :disabled="!isEdit" v-model="ruleForm.enabled" />
        </el-form-item>
        <el-form-item :label="$t('replenish.inspectionCycle')" :required="isEdit" prop="period">
          <template v-if="!isEdit" #label="scope">
            <span class="label">{{ scope.label }}</span>
          </template>
          <span v-if="!isEdit" class="value">{{ ruleForm.period ?? '-' }}</span>
          <el-select v-if="isEdit" class="w-100%" v-model="ruleForm.period" :placeholder="$t('replenish.selectInspectionCycle')">
            <el-option :label="$t('replenish.dailyInspection')" value="每天一次" />
            <el-option :label="$t('replenish.weeklyInspection')" value="每周一次" />
            <el-option :label="$t('replenish.monthlyInspection')" value="每月一次" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('replenish.inspectionStartTime')" :required="isEdit" prop="time">
          <template v-if="!isEdit" #label="scope">
            <span class="label">{{ scope.label }}</span>
          </template>
          <span v-if="!isEdit" class="value">{{ ruleForm.time ?? '-' }}</span>
          <el-time-picker v-if="isEdit" v-model="ruleForm.time" value-format="HH:mm" format="HH:mm" :placeholder="$t('replenish.selectStartTime')" />
        </el-form-item>
        <el-form-item :label="$t('replenish.inspectionDurationLimitMinutes')" :required="isEdit" prop="timeout">
          <template v-if="!isEdit" #label="scope">
            <span class="label">{{ scope.label }}</span>
          </template>
          <span v-if="!isEdit" class="value">{{ ruleForm.timeout ?? '-' }}</span>
          <el-input-number
            v-if="isEdit"
            class="w-100%!"
            v-model="ruleForm.timeout"
            :min="0"
            :max="1440"
            :controls="false"
            :precision="0"
            :placeholder="$t('replenish.inspectionDurationLimit')"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
const { t, store, $has } = useBasicTool()
const isEdit = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  period: '',
  time: '03:00',
  timeout: 0,
  enabled: false
})

const rules = reactive<FormRules>({
  period: [{ required: true, message: t('replenish.selectInspectionCycle'), trigger: 'change' }],
  time: [{ required: true, message: t('replenish.selectStartTime'), trigger: 'change' }],
  timeout: [{ required: true, message: t('replenish.pleaseEnterDurationLimit'), trigger: 'blur' }],
  enabled: [{ required: true, message: '', trigger: 'change' }]
})

async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      store.dispatch('inspection/inspectionConfigSave', { ...ruleForm }).then((res) => {
        isEdit.value = false
        getRuleForm()
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}

function resetForm(formEl: FormInstance | undefined) {
  if (!formEl) return
  formEl.resetFields()
  getRuleForm()
  isEdit.value = false
}

function edit() {
  isEdit.value = true
}
onMounted(() => {
  getRuleForm()
})

function getRuleForm() {
  store.dispatch('inspection/inspectionConfig').then((res) => {
    ruleForm.enabled = res.data?.enabled ?? false
    ruleForm.period = res.data?.period ?? ''
    ruleForm.timeout = res.data?.timeout
    ruleForm.time = res.data?.time ?? '03:00'
  })
}
</script>
<style lang="scss" scoped>
.label {
  font-size: 14px;
  // line-height: 22px;
  font-weight: 400;
  color: #99a0b5;
}
.value {
  font-size: 14px;
  line-height: 22px;
}
:deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}
:deep(.el-date-editor .el-input__wrapper) {
  width: 175px !important;
}
</style>
