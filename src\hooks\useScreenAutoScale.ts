// 大屏自适应
// import { ElMessage } from 'element-plus';
import throttle from 'lodash-es/throttle'
// import screenfull from 'screenfull';
export function useScreenAutoScale(scaleDom: HTMLElement | null, isSpreadOut?: boolean) {
  // 画布宽高
  const width = 1920
  const height = 1080
  /* 根据浏览器大小推断缩放比例 */
  function getScale() {
    const ww = window.innerWidth / width
    const wh = window.innerHeight / height
    return ww < wh ? ww : wh
  }
  function resize() {
    if (scaleDom) {
      if (!isSpreadOut) {
        scaleDom.style.transform = `scale(${getScale()}) translate(-50%, -50%)`
      } else {
        scaleDom.style.transform = `scale(${parseFloat((window.innerWidth / width).toFixed(5))}, ${parseFloat(
          (window.innerHeight / height).toFixed(5)
        )})`
      }
    }
  }
  function onResize() {
    resize()
    return throttle(resize, 15)
  }

  function onListen() {
    window.addEventListener('resize', onResize())
  }
  function unResize() {
    window.removeEventListener('resize', resize)
  }
  const initScreen = () => {
    onResize()
    onListen()
  }
  return {
    initScreen,
    unResize
  }
}

// // 切换全屏
// export function fullscreen() {
//   if (!screenfull.isEnabled) ElMessage.error('当前您的浏览器不支持全屏');
//   screenfull.toggle();
// }

// // 判断是否是全屏
// export function isFullscreen(): boolean {
//   return screenfull.isFullscreen;
// }

// // 退出全屏

// export function exitFullscreen() {
//   screenfull.exit();
// }
