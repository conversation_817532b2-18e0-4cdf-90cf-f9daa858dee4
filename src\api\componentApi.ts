// componentApi.ts
import service from '@/utils/Http'

/**
 * @description 获取所有的组件
 * @param {stringOrNumber} namespaceId
 * @return {*}
 */
export const getComponentListAll = async (namespaceId: stringOrNumber) => {
  return service.get<ApiResponse<Array<ComponentApi.ComponentListAllDataItem>>>(`/component/list/all?namespaceId=${namespaceId}`)
}

/**
 * @description 根据组件ID获取组件子模块信息
 * @param {ComponentApi.getComponentListModelById} data
 * @return {*}
 */
export const getComponentListModelById = async (data: ComponentApi.IGetComponentListModelById) => {
  return service.get<ApiResponse<Array<ComponentApi.ComponentListModelDataItem>>>(`/component/list/module/${data.id}/${data.deployWay}`)
}

/**
 * @description 根据组件ID获取组件子模块信息
 * @param {ComponentApi.GetComponentListModelByIdRoleList} data
 * @return {*}
 */
export const getComponentListModelByIdRoleList = async (data: ComponentApi.GetComponentListModelByIdRoleList) => {
  return service.get<ApiResponse<Array<ComponentApi.ComponentListModelByIdRoleListDataItem>>>(
    `/component/list/module/role?moduleId=${data.moduleId}&componentName=${data.componentName}&deployMode=${data.deployMode}`
  )
}

/**
 * @description 根据命名空间id获取绑定的K8S集群
 * @param {ComponentApi.IGetBindK8sNamespace} data
 * @return {*}
 */
export const getBindK8sNamespaceList = async (data: ComponentApi.IGetBindK8sNamespaceList) => {
  return service.get<ApiResponse<Array<ComponentApi.BindK8sNamespaceListDataItem>>>(`/component/list/kubernetes/${data.namespaceId}`)
}

/**
 * @description 组件安装版本列表获取
 * @param {stringOrNumber} id
 * @return {*}
 */
export const getComponentListModule = async (id?: any) => {
  return service.get<ApiResponse<Array<any>>>(`/component/list/module/${id}`)
}
