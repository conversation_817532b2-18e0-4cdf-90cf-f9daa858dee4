<template>
  <div class="base-table-toolbar">
    <BaseButton v-for="(item, index) in getButtonData" link :key="index" :disabled="item.disabled" @click="item.click">
      <slot v-if="item.slotName" :name="item.slotName"></slot>
      <template v-else> {{ item.buttonName }} </template>
    </BaseButton>
    <el-dropdown v-if="isDropDown" trigger="click" size="large" placement="bottom-end" class="dropdown" popper-class="component-popper">
      <BaseButton link class="h-0px! relative">
        <i class="iconfont icon-icon_gengduo1" />
      </BaseButton>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="(item, index) in getDropdownData" :key="index">
            <BaseButton link :disabled="item.disabled" @click="item.click" class="buttonOverspread px-10px!">
              <slot v-if="item.slotName" :name="item.slotName"></slot>
              <template v-else> {{ item.buttonName }} </template>
            </BaseButton>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script lang="ts" setup>
interface Props {
  buttonData?: BaseToolbar.ButtonItem[]
  dropdownData?: BaseToolbar.ButtonItem[]
}
const props = withDefaults(defineProps<Props>(), {
  buttonData: () => [
    {
      buttonName: '按钮',
      disabled: false,
      click: () => {},
      isButton: true
    }
  ],
  dropdownData: () => []
})
const getButtonData = computed(() => props.buttonData.filter((item: BaseToolbar.ButtonItem) => item.isButton))
const getDropdownData = computed(() => props.dropdownData.filter((item: BaseToolbar.ButtonItem) => item.isButton))
const isDropDown = computed(() => getDropdownData.value.length !== 0)
</script>
<style lang="scss" scoped>
.base-table-toolbar {
  display: flex;
  align-items: center;
  .dropdown {
    margin-left: 10px;
  }
  .el-dropdown-menu__item {
    .el-button {
      padding: 0px 12px;
    }
  }
}
</style>
