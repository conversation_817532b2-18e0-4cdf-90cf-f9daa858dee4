/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-08-08 13:47:15
 * @LastEditTime: 2025-08-09 16:45:55
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description:
 */
import service from '@/utils/Http'

//创建队列
export const createQuene = async (data: QueneApi.CreateQueneRequest) => {
  return await service.post<ApiResponse<boolean>>('/queue/create', data)
}

//删除队列
export const deleteQuene = async (data: { id: string }) => {
  return await service.post<ApiResponse<boolean>>('/queue/delete', data)
}

//编辑队列
export const editQuene = async (data: QueneApi.CreateQueneRequest) => {
  return await service.post<ApiResponse<boolean>>('/queue/edit', data)
}

//查询当前集群的资源使用和全部资源信息
export const getQueneOverView = async (data: { id: string }) => {
  return await service.post<ApiResponse<any>>('/queue/getResources', data)
}

//查询队列列表
export const getQueneList = async (data: QueneApi.queneListReq) => {
  return await service.post('/queue/page', data)
}
