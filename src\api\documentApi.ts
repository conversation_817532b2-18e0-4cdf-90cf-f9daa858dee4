import { downService } from '@/utils/Http'
import { getPath } from '@/utils/Path'
import axios from 'axios'

/**
 * @description 获取文档
 * @param {'white-paper' | 'white-paper-watermark' | 'operation-manual' | ' operation-manual-watermark'} lable
 * @return {*}
 */
// export const getDocument = async (label: 'white-paper' | 'white-paper-watermark' | 'operation-manual' | ' operation-manual-watermark') => {
//   return await downService().get(getPath(`/templateFile/downloadDocument/?label=${label}`));
// };

export const getDocument = async (label: 'white-paper' | 'white-paper-watermark' | 'operation-manual' | 'operation-manual-watermark') => {
  let url = ''
  switch (label) {
    case 'white-paper':
      url = '/assets/white_paper_online.pdf'
      break
    case 'white-paper-watermark':
      url = '/assets/white_paper_offline.pdf'
      break
    case 'operation-manual':
      url = '/assets/operation_manual_online.pdf'
      break
    case 'operation-manual-watermark':
      url = '/assets/operation_manual_offline.pdf'
      break
    default:
      break
  }
  return await axios.get(url, { responseType: 'blob' })
}

/**
 * @description 获取kerberos-trust文档
 * @return {*}
 */
export const getKerberosTemplateFile = async () => {
  return await downService().post(getPath(`/templateFile/download`), { label: 'kerberos-trust' })
}
