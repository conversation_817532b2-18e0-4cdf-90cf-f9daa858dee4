{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../../packages/components/form/src/components/Table/index.vue"], "sourcesContent": ["<template>\r\n  <DcRenderVNode :vnode=\"props.topSlot\" />\r\n  <DcTable\r\n    v-bind=\"{ ...tableProps }\"\r\n    :data=\"props.modelValue\"\r\n    :columns=\"columns\"\r\n    class=\"dc-form-table\"\r\n  />\r\n  <DcRenderVNode :vnode=\"props.bottomSlot\" />\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, h } from \"vue\";\r\nimport DcRenderVNode from \"../../../../render-vnode/src/index\";\r\nimport DcTable from \"../../../../table/src/index.vue\";\r\nimport DcFormItem from \"../FormItem/index.vue\";\r\nimport type { RenderVNodeFn } from \"../../../../render-vnode/src/types\";\r\nimport type { DcFormItem as DcFormItemI } from \"../../types\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcFormTableColumnItem } from \"./types\";\r\n\r\ndefineOptions({\r\n  name: \"DcFormTable\",\r\n});\r\n\r\nconst props = defineProps({\r\n  columns: {\r\n    type: Array as PropType<DcFormTableColumnItem[]>,\r\n    default: () => [],\r\n  },\r\n  modelValue: {\r\n    type: Array as PropType<Record<string, any>[]>,\r\n    default: () => [],\r\n  },\r\n  topSlot: {\r\n    type: Object as PropType<RenderVNodeFn>,\r\n    default: null,\r\n  },\r\n  bottomSlot: {\r\n    type: Object as PropType<RenderVNodeFn>,\r\n    default: null,\r\n  },\r\n  tableProps: {\r\n    type: Object,\r\n    default: () => ({}),\r\n  },\r\n  fileList: {\r\n    type: Object,\r\n    default: () => ({}),\r\n  },\r\n  rowIndex: {\r\n    type: Number,\r\n    default: 0,\r\n  },\r\n  disabled: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\", \"triggerEffect\", \"blur\"]);\r\n\r\nconst columns = computed(() => {\r\n  return (\r\n    props.columns?.map((item) => {\r\n      const config = { ...item };\r\n      if (config.isFormItem && config.formItemConfig) {\r\n        config.formatter = (row, columns, cellVal, index) => {\r\n          const model = (config.formItemConfig as DcFormItemI).model.replace(\r\n            \"[index]\",\r\n            `[${index}]`\r\n          );\r\n          return h(\r\n            DcFormItem,\r\n            {\r\n              config: {\r\n                ...(config.formItemConfig as DcFormItemI),\r\n                model,\r\n              },\r\n              visible: true,\r\n              modelValue: props.modelValue[index][config.prop],\r\n              rowIndex: index,\r\n              margin: config.formItemConfig?.margin,\r\n              onTriggerEffect: triggerEffect,\r\n              [\"onUpdate:modelValue\"]: (val) =>\r\n                updateModelValue(val, index, config.prop),\r\n            },\r\n            () => \"\"\r\n          );\r\n        };\r\n      }\r\n      return config;\r\n    }) || []\r\n  );\r\n});\r\n\r\nconst triggerEffect = (...args: any) => {\r\n  emits(\"triggerEffect\", ...args);\r\n};\r\n\r\nconst updateModelValue = (val: any, index: number, columnProp: string) => {\r\n  const newVal = props.modelValue;\r\n  newVal[index][columnProp] = val;\r\n  emits(\"update:modelValue\", newVal);\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.dc-form-table {\r\n  .el-table__row {\r\n    &:has(.cell .el-form-item.is-error) {\r\n      td.el-table__cell .cell {\r\n        padding-bottom: 16px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["DO_defineComponent"], "mappings": ";;;;;;;AAqBc,MAAA,WAAA,GAAAA,eAAA,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAmCd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAA,OACE,KAAM,CAAA,OAAA,EAAS,GAAI,CAAA,CAAC,IAAS,KAAA;AAC3B,QAAM,MAAA,MAAA,GAAS,EAAE,GAAG,IAAK,EAAA,CAAA;AACzB,QAAI,IAAA,MAAA,CAAO,UAAc,IAAA,MAAA,CAAO,cAAgB,EAAA;AAC9C,UAAA,MAAA,CAAO,SAAY,GAAA,CAAC,GAAK,EAAA,QAAA,EAAS,SAAS,KAAU,KAAA;AACnD,YAAA,MAAM,QAAS,MAAO,CAAA,cAAA,CAA+B,MAAM,OACzD,CAAA,SAAA,EACA,IAAI,KACN,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,YAAA,OAAO,EACL,UACA,EAAA;AAAA,cACE,MAAQ,EAAA;AAAA,gBACN,GAAI,MAAO,CAAA,cAAA;AAAA,gBACX,KAAA;AAAA,eACF;AAAA,cACA,OAAS,EAAA,IAAA;AAAA,cACT,UAAY,EAAA,KAAA,CAAM,UAAW,CAAA,KAAA,CAAA,CAAO,MAAO,CAAA,IAAA,CAAA;AAAA,cAC3C,QAAU,EAAA,KAAA;AAAA,cACV,MAAA,EAAQ,OAAO,cAAgB,EAAA,MAAA;AAAA,cAC/B,eAAiB,EAAA,aAAA;AAAA,cACjB,CAAC,wBAAwB,CAAC,GAAA,KACxB,iBAAiB,GAAK,EAAA,KAAA,EAAO,OAAO,IAAI,CAAA;AAAA,aAC5C,EACA,MAAM,EACR,CAAA,CAAA;AAAA,WACF,CAAA;AAAA,SACF;AACA,QAAO,OAAA,MAAA,CAAA;AAAA,OACR,KAAK,EAAC,CAAA;AAAA,KAEV,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgB,IAAI,IAAc,KAAA;AACtC,MAAM,KAAA,CAAA,eAAA,EAAiB,GAAG,IAAI,CAAA,CAAA;AAAA,KAChC,CAAA;AAEA,IAAA,MAAM,gBAAmB,GAAA,CAAC,GAAU,EAAA,KAAA,EAAe,UAAuB,KAAA;AACxE,MAAA,MAAM,SAAS,KAAM,CAAA,UAAA,CAAA;AACrB,MAAA,MAAA,CAAO,OAAO,UAAc,CAAA,GAAA,GAAA,CAAA;AAC5B,MAAA,KAAA,CAAM,qBAAqB,MAAM,CAAA,CAAA;AAAA,KACnC,CAAA;;;;;;;;;;;;;;;;;;;;;;"}