{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/empty/src/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dc-empty\">\r\n    <ElEmpty :description=\"desc\">\r\n      <template #image>\r\n        <ElImage v-if=\"showImg\" :src=\"image\" />\r\n        <div v-else />\r\n      </template>\r\n      <template #default><slot /> </template>\r\n      <template v-if=\"$slots.description\" #description\r\n        ><slot name=\"description\"\r\n      /></template>\r\n    </ElEmpty>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed } from \"vue\";\r\nimport { ElEmpty, ElImage } from \"element-plus\";\r\nimport { descConfig, imgConfig } from \"./constants\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcLocaleType, DcThemeType } from \"../../types\";\r\nimport type { DcEmptyType } from \"./types\";\r\n\r\ndefineOptions({\r\n  name: \"DcEmpty\",\r\n});\r\n\r\nconst props = defineProps({\r\n  locale: {\r\n    type: String as PropType<DcLocaleType>,\r\n    default: \"zh-CN\",\r\n  },\r\n  type: {\r\n    type: String as PropType<DcEmptyType>,\r\n    default: \"list\",\r\n  },\r\n  theme: {\r\n    type: String as PropType<DcThemeType>,\r\n    default: \"light\",\r\n  },\r\n  description: {\r\n    type: String,\r\n    default: \"\",\r\n  },\r\n  showImg: {\r\n    type: Boolean,\r\n    default: true,\r\n  },\r\n  img: {\r\n    type: String,\r\n    default: \"\",\r\n  },\r\n});\r\n\r\nconst image = computed(() => props.img || imgConfig[props.theme]?.[props.type]);\r\nconst desc = computed(\r\n  () => props.description || descConfig[props.locale]?.[props.type]\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.dc-empty {\r\n  height: 100%;\r\n  width: 100%;\r\n  .el-empty {\r\n    height: 100%;\r\n    .el-empty__image {\r\n      line-height: 0;\r\n      .el-image {\r\n        height: 120px;\r\n        width: 120px;\r\n      }\r\n    }\r\n    .el-empty__description {\r\n      margin-top: 8px;\r\n      font-size: 12px;\r\n      font-family: PingFangSC, PingFang SC;\r\n      font-weight: 400;\r\n      color: rgba(41, 51, 78, 0.65);\r\n      line-height: 20px;\r\n      p {\r\n        font-size: 12px;\r\n      }\r\n    }\r\n    .el-empty__bottom {\r\n      margin-top: 16px;\r\n    }\r\n    div:first-child {\r\n      margin-top: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["DO_defineComponent", "computed", "imgConfig", "descConfig"], "mappings": ";;;;;;;;;;;;;;AAuBc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AA2BR,IAAA,MAAA,KAAA,GAAQC,aAAS,MAAM,KAAA,CAAM,OAAOC,mBAAU,CAAA,KAAA,CAAM,KAAS,CAAA,GAAA,KAAA,CAAM,IAAK,CAAA,CAAA,CAAA;AACxE,IAAA,MAAA,IAAA,GAAOD,aACX,MAAM,KAAA,CAAM,eAAeE,oBAAW,CAAA,KAAA,CAAM,MAAU,CAAA,GAAA,KAAA,CAAM,IAC9D,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}