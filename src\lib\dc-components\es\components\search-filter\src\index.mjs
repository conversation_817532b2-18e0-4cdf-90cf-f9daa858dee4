import { defineComponent, ref, computed, h, openBlock, createElementBlock, normalizeClass, createVNode, createCommentVNode } from 'vue';
import '../../form/index.mjs';
import DcForm from '../../form/src/index.mjs';
import Action from './Action.mjs';
import Expand from './Expand.mjs';
import './index.vue_vue_type_style_index_0_scoped_true_lang.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';
import { DcFormComponentEnum } from '../../form/src/types.mjs';

const clsPrefix = "dc-search-filter";
const actionModel = "dc-search-filter-model";
const extendModel = "dc-search-filter-extend";
const __default__ = defineComponent({
  name: "DcSearchFilter"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["query", "reset", "refresh"],
  setup(__props, { expose: __expose, emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const formRef = ref();
    const isExpand = ref(false);
    const expandConfig = computed(() => {
      return props.config.expand || { showExpand: false, expandFields: [] };
    });
    const formConfigChildren = computed(() => {
      return (props.config.formConfig?.children || []).map((item) => {
        return {
          ...item,
          visible: (modelValue) => {
            if (isExpand.value) {
              return !expandConfig.value.expandFields.includes(item.model);
            }
            return item.visible ? item.visible(modelValue) : true;
          }
        };
      });
    });
    const formConfig = computed(() => {
      const children = formConfigChildren.value;
      children.push({
        model: actionModel,
        label: "",
        labelWidth: 0,
        defaultValue: "",
        visible: () => true,
        component: DcFormComponentEnum.CUSTOM,
        margin: props.config.actionMargin,
        props: {
          renderCustom: () => {
            return h(Action, {
              config: props.config,
              clsPrefix,
              onQuery,
              onRefresh,
              onReset
            });
          }
        }
      });
      if (props.config.extendSlot) {
        children.push({
          model: extendModel,
          label: "",
          labelWidth: 0,
          defaultValue: "",
          visible: () => true,
          component: DcFormComponentEnum.CUSTOM,
          margin: props.config.extendMargin,
          props: {
            renderCustom: () => {
              return props.config.extendSlot;
            }
          }
        });
      }
      return props.config.formConfig ? {
        ...props.config.formConfig,
        children
      } : { children, labelWidth: "auto" };
    });
    const toggleExpand = (val) => {
      isExpand.value = val;
    };
    const getValues = () => {
      const query = formRef.value.getValues();
      query[actionModel] = void 0;
      query[extendModel] = void 0;
      return query;
    };
    const setValues = (modelValue) => {
      formRef.value.initModel(modelValue);
    };
    const onQuery = () => {
      const query = getValues();
      emits("query", query);
    };
    const onReset = () => {
      formRef.value.reset();
      const query = getValues();
      emits("reset", query);
    };
    const onRefresh = () => {
      const query = getValues();
      emits("refresh", query);
    };
    __expose({
      query: onQuery,
      reset: onReset,
      refresh: onRefresh,
      getValues,
      setValues,
      formRef
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(`${clsPrefix} ${expandConfig.value.showExpand ? "with-expand" : ""}`)
      }, [
        createVNode(DcForm, {
          ref_key: "formRef",
          ref: formRef,
          config: formConfig.value,
          inline: "",
          class: normalizeClass(`${clsPrefix}-form `)
        }, null, 8, ["config", "class"]),
        expandConfig.value.showExpand ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(`${clsPrefix}-expand`)
        }, [
          createVNode(Expand, {
            "collapse-text": expandConfig.value.collapseText,
            "expand-text": expandConfig.value.expandText,
            onToggleExpand: toggleExpand
          }, null, 8, ["collapse-text", "expand-text"])
        ], 2)) : createCommentVNode("v-if", true)
      ], 2);
    };
  }
});
var SearchFilter = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-7cdc81e0"], ["__file", "D:\\DataCyber\\dc-components\\packages\\components\\search-filter\\src\\index.vue"]]);

export { SearchFilter as default };
//# sourceMappingURL=index.mjs.map
