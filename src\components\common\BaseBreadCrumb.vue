<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item v-for="(item, index) in props.list" :key="index" :class="{ canRouter: item.to }" @click="onClick(item)">
      {{ item.name }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script lang="ts" setup>
import { RouteLocationRaw, useRouter } from 'vue-router'

const router = useRouter()

interface Bread {
  name: string
  to?: RouteLocationRaw
}
interface Props {
  list?: Array<Bread>
}
const props = withDefaults(defineProps<Props>(), {
  list: () => []
})

function onClick(bread: Bread) {
  if (bread.to) {
    router.push(bread.to)
  }
}
</script>

<style scoped lang="scss">
.el-breadcrumb {
  .el-breadcrumb__item {
    :deep(.el-breadcrumb__inner) {
      font-size: 14px;
      color: var(--ops-disabled-color);
    }
  }
  .el-breadcrumb__item {
    :deep(.el-breadcrumb__inner) {
      font-size: 14px;
      color: var(--ops-disabled-color);
    }
  }
  .el-breadcrumb__item:last-child {
    :deep(.el-breadcrumb__inner) {
      font-weight: 550;
      color: var(--ops-text-color);
    }
  }
  :deep(.canRouter) {
    .el-breadcrumb__inner {
      cursor: pointer;
    }
    .el-breadcrumb__inner:hover {
      color: var(--ops-primary-color);
    }
  }
}
</style>
