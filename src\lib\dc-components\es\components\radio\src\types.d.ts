import type { Ref } from "vue";
import type { RenderVNodeFn } from "../../render-vnode/src/types";
import type DcRadio from "./index.vue";
export interface DcRadioOpItem {
    label: string | number;
    value: string | number | boolean;
    disabled?: boolean;
    defaultSlot?: RenderVNodeFn;
}
export declare type DcRadioOptions = DcRadioOpItem[] | Ref<DcRadioOpItem[]>;
export declare type DcRadioInstance = InstanceType<typeof DcRadio>;
