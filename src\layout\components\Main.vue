<template>
  <div style="height: 100%">
    <BaseTitle v-if="pageTitle && !route.path.includes('migrationBackup')" :title="pageTitle"></BaseTitle>
    <el-scrollbar class="scrollbar">
      <router-view />
    </el-scrollbar>
  </div>
</template>
<script lang="ts" setup>
const { store, route } = useBasicTool()
const breadList = computed(() => store.state.app.breadList)

const pageTitle = computed(() => {
  return breadList.value.length ? breadList.value[breadList.value.length - 1].name : ''
})
const scrollbarHeight = computed(() => {
  console.log(pageTitle.value, 'route.path', route.path, breadList.value)
  if (pageTitle.value && !route.path.includes('migrationBackup')) {
    return 'calc(100% - 48px)'
  } else {
    return '100%'
  }
})
</script>
<style lang="scss" scoped>
.scrollbar {
  height: v-bind(scrollbarHeight);
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
}
</style>
