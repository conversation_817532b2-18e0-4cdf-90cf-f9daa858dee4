{"version": 3, "file": "index.mjs", "sources": ["../../../../packages/dc-components/index.ts"], "sourcesContent": ["import installer from \"./default\";\r\n\r\nexport * from \"@dc-components/components\";\r\nexport * from \"@dc-components/constants\";\r\nexport * from \"@dc-components/utils\";\r\n\r\nexport * from \"./make-installer\";\r\n\r\nexport const install = installer.install;\r\n\r\nexport default installer;\r\n"], "names": [], "mappings": ";;;;;;;AAKY,MAAC,OAAO,GAAG,SAAS,CAAC;;;;"}