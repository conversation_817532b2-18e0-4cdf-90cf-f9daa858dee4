{"version": 3, "file": "encryption.mjs", "sources": ["../../../../../../packages/components/login/src/encryption.ts"], "sourcesContent": ["import CryptoJS from \"crypto-js\";\n\nconst iv = CryptoJS.enc.Utf8.parse(\"70w5zbONA5xXAJ5C\"); // 偏移量\nconst KEY = \"A39DHJC4x6MAOaHr\";\n// 加密\nexport function encrypt(plaintText: string, key = KEY) {\n  if (!plaintText) {\n    return \"\";\n  }\n  // 秘钥\n  const newKey = CryptoJS.enc.Utf8.parse(key);\n  const iv = CryptoJS.enc.Utf8.parse(\"70w5zbONA5xXAJ5C\");\n  const newPlaintText = plaintText;\n  const options = {\n    iv,\n    mode: CryptoJS.mode.CBC,\n    padding: CryptoJS.pad.Pkcs7,\n  };\n  const encryptedData = CryptoJS.AES.encrypt(newPlaintText, newKey, options);\n  const encryptedBase64Str = encryptedData.toString();\n  // encryptedBase64Str = encryptedBase64Str.replace(/\\+/g,\"-\");\n  return encryptedBase64Str;\n}\n\n// 解密\nexport function decrypt(encryptedBase64Str: string, key = KEY) {\n  if (!encryptedBase64Str) {\n    return \"\";\n  }\n  // var vals = encryptedBase64Str.replace(/\\-/g, '+').replace(/_/g, '/');\n  const options = {\n    iv,\n    mode: CryptoJS.mode.CBC,\n    padding: CryptoJS.pad.Pkcs7,\n  };\n  const newKey = CryptoJS.enc.Utf8.parse(key);\n  const decryptedData = CryptoJS.AES.decrypt(\n    encryptedBase64Str,\n    newKey,\n    options\n  );\n  const decryptedStr = CryptoJS.enc.Utf8.stringify(decryptedData);\n  return decryptedStr;\n}\n\n// 解析路由url参数\nexport function getQueryParam(paramName) {\n  // 安全地解析查询字符串\n  const queryString = window.location.href.split(\"?\")?.[1] || \"\";\n  const params = parseQueryString(queryString);\n\n  // 确保 paramName 是安全的，避免参数注入\n  if (typeof paramName !== \"string\" || !paramName.match(/^[a-zA-Z0-9-_]+$/)) {\n    console.warn(\"Invalid parameter name.\");\n    return undefined;\n  }\n\n  // 返回指定的参数值，如果不存在则返回 undefined\n  return params[paramName] || undefined;\n}\n\n// 解析查询字符串的辅助函数\nexport const parseQueryString = (queryString) => {\n  const params = {};\n  queryString.split(\"&\").forEach((param) => {\n    const [key, value = \"\"] = param.split(\"=\");\n    params[decodeURIComponent(key)] = decodeURIComponent(value);\n  });\n  return params;\n};\n"], "names": [], "mappings": ";;AACA,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACvD,MAAM,GAAG,GAAG,kBAAkB,CAAC;AACxB,SAAS,OAAO,CAAC,UAAU,EAAE,GAAG,GAAG,GAAG,EAAE;AAC/C,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC1D,EAAE,MAAM,aAAa,GAAG,UAAU,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,EAAE,EAAE,GAAG;AACX,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG;AAC3B,IAAI,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC7E,EAAE,MAAM,kBAAkB,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;AACtD,EAAE,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AACM,SAAS,OAAO,CAAC,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE;AACvD,EAAE,IAAI,CAAC,kBAAkB,EAAE;AAC3B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,EAAE;AACN,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG;AAC3B,IAAI,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAClF,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAClE,EAAE,OAAO,YAAY,CAAC;AACtB,CAAC;AACM,SAAS,aAAa,CAAC,SAAS,EAAE;AACzC,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACjE,EAAE,MAAM,MAAM,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC;AAC/C,EAAE,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE;AAC7E,IAAI,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;AAC5C,IAAI,OAAO,KAAK,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,CAAC;AACrC,CAAC;AACW,MAAC,gBAAgB,GAAG,CAAC,WAAW,KAAK;AACjD,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC5C,IAAI,MAAM,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChE,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}