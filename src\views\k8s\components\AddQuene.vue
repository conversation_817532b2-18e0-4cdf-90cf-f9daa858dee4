<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-08-07 20:37:03
 * @LastEditTime: 2025-08-13 19:13:30
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON>
 * @Description:
-->
<template>
  <el-dialog :title="title" :model-value="dialogVisible" width="30%" :before-close="handleClose">
    <el-form class="quenen-form" ref="formRef" :model="ruleForm" :rules="rules" label-width="auto">
      <el-form-item :label="`${$t('form.queneName')}：`" prop="name">
        <el-input
          :disabled="rowItem.name"
          :style="{ width: '240px' }"
          v-model="ruleForm.name"
          :placeholder="$t('form.queneNamePlaceholder')"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item :label="`${$t('form.queneDesc')}：`" prop="description">
        <el-input
          :style="{ width: '240px' }"
          type="textarea"
          :rows="4"
          v-model="ruleForm.description"
          :placeholder="$t('form.queneDescPlaceholder')"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item :label="`${$t('form.weight')}：`" prop="weight">
        <el-input-number :style="{ width: '240px' }" v-model="ruleForm.weight" :min="0" :placeholder="$t('form.weightPlaceholder')" />
      </el-form-item>
      <el-form-item :label="`${$t('form.allocateCpu')}核：`" prop="cpuAllocated">
        <el-input-number
          :style="{ width: '240px' }"
          v-model="ruleForm.cpuAllocated"
          :min="0"
          :max="100"
          :placeholder="$t('form.pleaseEnterAllocateCpu')"
        />
      </el-form-item>
      <el-form-item :label="`${$t('form.allocateMemory')}GB：`" prop="memoryAllocated">
        <el-input-number
          :style="{ width: '240px' }"
          v-model="ruleForm.memoryAllocated"
          :min="0"
          :max="50"
          :placeholder="$t('form.pleaseEnterAllocateMemory')"
        />
      </el-form-item>
      <el-form-item :label="`${$t('form.allocateGpuNum')}：`" prop="gpuAllocated">
        <el-input-number
          :style="{ width: '240px' }"
          v-model="ruleForm.gpuAllocated"
          :min="0"
          :max="10"
          :placeholder="$t('form.pleaseEnterAllocateGpuNum')"
        />
      </el-form-item>
      <!-- <el-form-item :label="`${$t('form.allocateGpuMemory')}：`" prop="gpuMemoryAllocated">
        <el-input-number
          :style="{ width: '240px' }"
          v-model="ruleForm.gpuMemoryAllocated"
          :min="0"
          :max="50"
          :placeholder="$t('form.pleaseEnterAllocateGpuMemory')"
        />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="handleClose">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { createQuene, editQuene } from '@/api/queneApi'
const { t, store, $has, setBreadList, route, hasBusinessAuthority, router } = useBasicTool()

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  rowItem: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: ''
  }
})
const formRef = ref()

const ruleForm = reactive({
  name: '',
  description: '',
  weight: 0,
  cpuAllocated: 0,
  memoryAllocated: 0,
  gpuAllocated: 0,
  gpuMemoryAllocated: 0
})
const rules = reactive({
  name: [{ required: true, message: '请输入队列名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入队列描述', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入权重', trigger: 'blur' }],
  cpuAllocated: [{ required: true, message: '请输入分配CPU', trigger: 'blur' }],
  memoryAllocated: [{ required: true, message: '请输入分配内存', trigger: 'blur' }],
  gpuAllocated: [{ required: true, message: '请输入分配GPU数量', trigger: 'blur' }],
  gpuMemoryAllocated: [{ required: true, message: '请输入分配GPU内存', trigger: 'blur' }]
})
const isLoading = ref(false)
const emit = defineEmits(['close', 'confirm'])
const k8sId = computed(() => store.state.k8s.k8sId || route.query.id)

const handleClose = () => {
  emit('close')
  resetForm()
}
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      isLoading.value = true
      const params = {
        ...ruleForm,
        kubernetesId: k8sId.value
      }
      if (props.rowItem.id) {
        editQuene(params)
          .then((res: any) => {
            if (res.data.code === 0) {
              isLoading.value = false
              emit('confirm')
              resetForm()
            }
          })
          .finally(() => {
            isLoading.value = false
          })
      } else {
        createQuene(params)
          .then((res: any) => {
            if (res.data.code === 0) {
              isLoading.value = false
              emit('confirm')
              resetForm()
            }
          })
          .finally(() => {
            isLoading.value = false
          })
      }
      // setTimeout(() => {
      //   isLoading.value = false
      //   emit('confirm')
      //   resetForm()
      // }, 1000)
    }
  })
}
const resetForm = () => {
  formRef.value.resetFields()
  ruleForm.name = ''
  ruleForm.description = ''
  ruleForm.weight = 0
  ruleForm.cpuAllocated = 0
  ruleForm.memoryAllocated = 0
  ruleForm.gpuAllocated = 0
  ruleForm.gpuMemoryAllocated = 0
}
watch(
  () => props.dialogVisible,
  (val) => {
    if (val && props.rowItem.id) {
      ruleForm.name = props.rowItem.name
      ruleForm.description = props.rowItem.description
      ruleForm.weight = props.rowItem.weight
      ruleForm.cpuAllocated = props.rowItem.totalCpuCapacity
      ruleForm.memoryAllocated = props.rowItem.totalMemoryCapacity
      ruleForm.gpuAllocated = props.rowItem.totalGpuCapacity
    }
  }
)
</script>

<style scoped lang="scss"></style>
