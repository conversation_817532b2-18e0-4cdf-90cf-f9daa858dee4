<template>
  <el-sub-menu index="3">
    <template #title>
      <div class="inline-block lang-switch bg-bgWhite rd-50%">
        <span class="iconfont-box">
          <i v-if="language === 'en'" class="iconfont icon-yingwen cursor" />
          <i v-else-if="language === 'zh-CN'" class="iconfont icon-zhongwen1 cursor" />
          <img v-else class="w-18px h-18px mt-3px ml-2px" :src="ja" alt="icon_jp" />
        </span>
      </div>
    </template>
    <el-menu-item @click="load('zh-CN')">中文简体</el-menu-item>
    <el-menu-item @click="load('en')">English</el-menu-item>
    <el-menu-item @click="load('ja-JP')">日本語です</el-menu-item>
  </el-sub-menu>
</template>
<script lang="ts" setup>
import ja from '@/assets/icons/icon_jp.svg'
import useLocale from '@/locales/useLocale'
import { computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const language = computed(() => store.state.app.language)
const { setLocale } = useLocale()
function load(value: string) {
  if (setLocale(value)) window.location.reload()
}
</script>
<style lang="scss" scoped>
.lang-switch {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}
</style>
