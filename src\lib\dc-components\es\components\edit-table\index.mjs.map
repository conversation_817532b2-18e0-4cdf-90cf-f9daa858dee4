{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/edit-table/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport EditTable from \"./src/index.vue\";\r\n\r\nexport const DcEditTable = withInstall(EditTable);\r\n\r\nexport default DcEditTable;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC,SAAS;;;;"}