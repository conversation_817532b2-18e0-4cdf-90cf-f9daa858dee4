<template>
  <el-dialog :model-value="modelValue" :title="$t('replenish.rename')" width="440px" :before-close="resetForm">
    <el-form ref="passForm" :model="ruleForm" :rules="rules" label-width="120px">
      <el-form-item :label="$t('replenish.hostAlias')" prop="alias">
        <el-input v-model="ruleForm.alias" class="w-280px!" />
      </el-form-item>
    </el-form>
    <template #footer>
      <BaseButton type="primary" :loading="isLoading" @click="submitForm()">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="resetForm()">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
const { t, router, store } = useBasicTool()
const isLoading = ref(false)
const passForm = ref()
const emit = defineEmits(['update:modelValue', 'submit'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  id: {
    type: Number,
    default: 0
  },
  alias: {
    type: String,
    default: ''
  }
})
const ruleForm = reactive({ alias: '' })

watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      ruleForm.alias = props.alias
    }
  }
)

const rules = {
  alias: [
    {
      pattern: /^.{1,50}$/,
      message: t('replenish.hostAliasCharacterLimit')
    }
  ]
}
// 提交
function submitForm() {
  passForm.value.validate((valid: any) => {
    if (valid) {
      isLoading.value = true
      store
        .dispatch('hosts/editAlias', { id: props.id, alias: ruleForm.alias })
        .then((res) => {
          resetForm()
          emit('submit')
        })
        .finally(() => {
          isLoading.value = false
        })
    }
  })
}
// 重置
function resetForm() {
  passForm.value.resetFields()
  emit('update:modelValue', false)
}
</script>
