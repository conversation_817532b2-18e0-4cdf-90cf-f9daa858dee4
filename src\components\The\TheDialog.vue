<template>
  <div class="baseDialog">
    <el-dialog
      :model-value="dialogVisible"
      width="30%"
      center
      :show-close="isShow"
      :close-on-click-modal="isClick"
      :close-on-press-escape="isEscClose"
      :before-close="handleClose"
    >
      <el-progress v-bind="$attrs" type="circle" :percentage="percentage" class="progress" :status="status" />
      <template #footer>
        <span class="dialog-footer">{{ stepMsg }}</span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
const isClick = ref(false)
const isEscClose = ref(false)
// let isShow = ref(false)
type Status = '' | 'success' | 'warning' | 'exception'
interface Props {
  dialogVisible: boolean
  percentage: number
  stepMsg: string
  status: Status
  isShow: boolean
}
withDefaults(defineProps<Props>(), {
  dialogVisible: false,
  percentage: 0,
  stepMsg: '',
  status: '',
  isShow: false
})

const emit = defineEmits(['cancelClick'])

// 取消展示添加进度弹框
const handleClose = () => {
  emit('cancelClick')
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  text-align: center;
}
</style>
