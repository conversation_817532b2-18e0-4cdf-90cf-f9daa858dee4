{"name": "engine_manager", "version": "3.1.0", "scripts": {"dev": "vite", "dev:mock": "vite --mode mock", "login-build": "vite build --mode login", "build": "vite build", "ts-check": "tsc", "staging": "vite build --mode staging", "build:datacenter": "vite build --mode datacenter", "build:login": "vite build --mode login", "serve": "vite preview", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx,.vue src", "prepare": "husky install", "lint-staged": "lint-staged", "lintcss": "stylelint --fix \"src/**/*.{vue,scss,sass}\" ", "commit": "czg", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:push": "vitest run"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "@pureadmin/utils": "^1.9.3", "animate.css": "^4.1.1", "axios": "^0.21.4", "codemirror": "^5.65.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.8", "diff": "^5.1.0", "element-plus": "^2.3.6", "html2canvas": "^1.4.1", "json-bigint": "^1.0.0", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "monaco-editor": "0.34.0", "monaco-sql-languages": "^0.12.2", "qs": "^6.11.2", "v-code-diff": "^1.13.1", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-pdf-viewer-stars": "^0.2.9", "vue-router": "^4.2.2", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "xlsx": "^0.18.5", "xterm": "^4.19.0", "xterm-addon-attach": "^0.6.0", "xterm-addon-fit": "^0.5.0"}, "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@commitlint/cz-commitlint": "^16.3.0", "@commitlint/format": "^16.2.1", "@edge-runtime/vm": "1.1.0-beta.31", "@types/codemirror": "^5.60.8", "@types/crypto-js": "^4.1.1", "@types/diff": "^5.0.5", "@types/json-bigint": "^1.0.1", "@types/lodash-es": "^4.17.7", "@types/mockjs": "^1.0.7", "@types/node": "^16.18.34", "@typescript-eslint/eslint-plugin": "^5.59.9", "@typescript-eslint/parser": "^5.59.9", "@unocss/reset": "^0.51.13", "@vitejs/plugin-vue": "^4.4.0", "@vitest/coverage-c8": "^0.23.4", "@vitest/ui": "^0.23.4", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/compiler-sfc": "^3.3.4", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.3.2", "@vueuse/core": "^9.13.0", "autoprefixer": "^10.4.14", "chalk": "^5.2.0", "conventional-changelog-atom": "^2.0.8", "cz-git": "^1.6.1", "czg": "^1.6.1", "dotenv": "^16.1.4", "dotenv-expand": "^10.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.14.1", "happy-dom": "^6.0.4", "husky": "^7.0.4", "jsdom": "^20.0.3", "lint-staged": "^12.5.0", "mockjs": "^1.1.0", "picocolors": "^1.0.0", "postcss": "^8.4.24", "postcss-flexbugs-fixes": "^5.0.2", "postcss-html": "^1.5.0", "postcss-modules": "^5.0.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.8", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.63.2", "stylelint": "^14.16.1", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^3.1.0", "stylelint-config-recommended": "^9.0.0", "stylelint-config-recommended-scss": "^7.0.0", "stylelint-config-standard": "^28.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^4.7.0", "typescript": "^4.9.5", "unocss": "^0.51.13", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "vite": "4.2.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-environment": "^1.1.3", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^0.23.4", "vue-eslint-parser": "^8.3.0"}, "lint-staged": {"*.{ts,tsx,js,jsx,vue}": ["npm run lint"], "*.{sass,scss,vue}": ["npm run lintcss"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "engines": {"node": ">=16.x", "pnpm": ">=8"}}