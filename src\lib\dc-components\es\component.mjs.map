{"version": 3, "file": "component.mjs", "sources": ["../../../../packages/dc-components/component.ts"], "sourcesContent": ["import {\r\n  DcButton,\r\n  DcCheckbox,\r\n  DcDialog,\r\n  DcDrawer,\r\n  DcEditTable,\r\n  DcEditTableColumn,\r\n  DcEmpty,\r\n  DcForm,\r\n  DcLogin,\r\n  DcRadio,\r\n  DcSearchFilter,\r\n  DcSelect,\r\n  DcTable,\r\n  DcViewLayout,\r\n} from \"@dc-components/components\";\r\n\r\nimport type { Plugin } from \"vue\";\r\n\r\nexport default [\r\n  DcButton,\r\n  DcEditTable,\r\n  DcEditTableColumn,\r\n  DcSelect,\r\n  DcForm,\r\n  DcRadio,\r\n  DcCheckbox,\r\n  DcTable,\r\n  DcSearchFilter,\r\n  DcDialog,\r\n  DcDrawer,\r\n  DcEmpty,\r\n  DcLogin,\r\n  DcViewLayout,\r\n] as Plugin[];\r\n"], "names": [], "mappings": ";;AAgBA,iBAAe;AACf,EAAE,QAAQ;AACV,EAAE,WAAW;AACb,EAAE,iBAAiB;AACnB,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,OAAO;AACT,EAAE,cAAc;AAChB,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,YAAY;AACd,CAAC;;;;"}