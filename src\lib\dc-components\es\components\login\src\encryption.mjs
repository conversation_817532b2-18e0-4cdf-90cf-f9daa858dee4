import CryptoJS from 'crypto-js';

const iv = CryptoJS.enc.Utf8.parse("70w5zbONA5xXAJ5C");
const KEY = "A39DHJC4x6MAOaHr";
function encrypt(plaintText, key = KEY) {
  if (!plaintText) {
    return "";
  }
  const newKey = CryptoJS.enc.Utf8.parse(key);
  const iv2 = CryptoJS.enc.Utf8.parse("70w5zbONA5xXAJ5C");
  const newPlaintText = plaintText;
  const options = {
    iv: iv2,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  };
  const encryptedData = CryptoJS.AES.encrypt(newPlaintText, newKey, options);
  const encryptedBase64Str = encryptedData.toString();
  return encryptedBase64Str;
}
function decrypt(encryptedBase64Str, key = KEY) {
  if (!encryptedBase64Str) {
    return "";
  }
  const options = {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  };
  const newKey = CryptoJS.enc.Utf8.parse(key);
  const decryptedData = CryptoJS.AES.decrypt(encryptedBase64Str, newKey, options);
  const decryptedStr = CryptoJS.enc.Utf8.stringify(decryptedData);
  return decryptedStr;
}
function getQueryParam(paramName) {
  const queryString = window.location.href.split("?")?.[1] || "";
  const params = parseQueryString(queryString);
  if (typeof paramName !== "string" || !paramName.match(/^[a-zA-Z0-9-_]+$/)) {
    console.warn("Invalid parameter name.");
    return void 0;
  }
  return params[paramName] || void 0;
}
const parseQueryString = (queryString) => {
  const params = {};
  queryString.split("&").forEach((param) => {
    const [key, value = ""] = param.split("=");
    params[decodeURIComponent(key)] = decodeURIComponent(value);
  });
  return params;
};

export { decrypt, encrypt, getQueryParam, parseQueryString };
//# sourceMappingURL=encryption.mjs.map
