<template>
  <div class="hosts">
    <BaseLayout>
      <template #header>
        <BaseSearch :searchItemData="searchItemData" @on-submit="submitSearch" @reset-search="resetSearch">
          <template #button>
            <div class="append-btn">
              <BaseButton class="base-add-button" type="primary" :disabled="tableData.data.length === 0" @click="openTheCheckHost">
                {{ $t('replenish.checkAllHosts') }}
              </BaseButton>
              <BaseAddButton :disabled="addHostDisabled" class="base-add-button" :is-button="$has('hosts-add')" @click="addHost">{{
                $t('button.addHost')
              }}</BaseAddButton>
            </div>
          </template>
        </BaseSearch>
        <div class="hosts-type">
          <div @click="toggleType(HostType.HOST)" :class="`${hostType === HostType.HOST ? 'active' : ''} hosts-type-item`">
            {{ $t('replenish.hostList') }}
          </div>
          <div @click="toggleType(HostType.GATEWAY)" :class="`${hostType === HostType.GATEWAY ? 'active' : ''} hosts-type-item`">
            gateway{{ $t('replenish.list') }}
          </div>
        </div>
      </template>
      <template #content>
        <BaseTable
          ref="baseTable"
          v-loading="tableLoading"
          :columns="hostType === HostType.HOST ? tableData.column : gatewayTableData.column"
          :data="hostType === HostType.HOST ? tableData.data : gatewayTableData.data"
          :row-key="getRowKey"
          @select="handleSelect"
          @select-all="handleSelect"
        >
          <template #onlineState="scope">
            <BaseState :state-code="scope.row.onlineState"></BaseState>
          </template>
          <template #name="scope">
            <BaseButton link @click="openDetails(scope.row.id, scope.row.ip)">
              {{ scope.row.name }}
            </BaseButton>
            <!-- 用于清除element-plus按钮之间的间距 -->
            <span></span>
            <TheWebTerminalbutton :url="routeData(scope)" />
          </template>
          <template #alias="scope">
            <span>{{ scope.row?.alias ?? '-' }}</span>
            <img
              v-if="$has('hosts-alias-rename')"
              class="inline-block cursor-pointer ml-2px mb-4px"
              src="@/assets/icons/icon-bianjihost.svg"
              alt="icon"
              @click="openRename(scope.row.id, scope.row.alias)"
            />
          </template>
          <template #rolesNum="scope">
            <BaseButton link :disabled="scope.row?.rolesNum === 0" @click="openRoleList(scope.row.id, scope.row.ip)">
              {{ scope.row?.rolesNum }}
            </BaseButton>
          </template>
          <template #rackNumber="scope">
            {{ scope.row.rackNumber ?? '-' }}
          </template>
          <template #cpuTotal="scope">
            <TheHostTotal :utilization="scope.row.cpuUtilization" :used="scope.row.cpuUsed" :total="scope.row.cpuTotal" company="Core" />
          </template>
          <template #memoryTotal="scope">
            <TheHostTotal :utilization="scope.row.memoryUtilization" :used="scope.row.memoryUsed" :total="scope.row.memoryTotal" company="Gi" />
          </template>
          <template #diskTotal="scope">
            <TheHostTotal :utilization="scope.row.diskUtilization" :used="scope.row.diskUsage" :total="scope.row.diskTotal" company="Gi" />
          </template>
          <template #maintenanceStatusHeader="{ column }">
            {{ column.label }}
            <BaseElTooltip :content="$t('replenish.maintenanceModeNote')" placement="top">
              <img src="@/assets/icons/icon_tishi.svg" class="inline-block relative top--1px" alt="icon" />
            </BaseElTooltip>
          </template>
          <template #maintenanceStatus="scope">
            <el-switch
              class="h-22px"
              :model-value="scope.row.maintenanceStatus ?? '0'"
              :active-value="'1'"
              :inactive-value="'0'"
              @click="maintenance(scope)"
            />
          </template>
          <template #manageState="scope">
            <BaseTableToolbar :button-data="tableToolbar(scope).buttonData" />
          </template>
          <template #addState="scope">
            <div
              :class="`add-status ${getCurrentAddStateColor(scope.row.addState)}`"
              @click="showAddDetail(scope.row.executeRecordBoList || [], scope.row.addState, scope.row.id, scope.row)"
            >
              {{ getCurrentAddState(scope.row.addState) }}
            </div>
          </template>
        </BaseTable>
      </template>
      <template #footer>
        <BasePagination v-model="pageInfo" @page-change="handlePageChange" />
        <div v-if="$has('hosts-batch-delete') || $has('hosts-batch-unbind')">
          <BaseButton v-if="$has('hosts-batch-delete')" link :disabled="isBatchOperat" @click="batchDelete">
            {{ $t('button.batchDelete') }}
          </BaseButton>
          <span class="interval--vertical" v-if="hostType === HostType.HOST"></span>
          <BaseButton v-if="$has('hosts-batch-unbind') && hostType === HostType.HOST" link :disabled="isBatchOperat" @click="batchUnbind">
            {{ $t('button.batchUnbind') }}
          </BaseButton>
        </div>
      </template>
    </BaseLayout>
    <TheBatchCofirm v-model="batchOpen" :params="params" @submit="submit">
      <template #button>
        <BaseButton type="primary" style="padding-left: 16px" link @click="viewAll"> {{ $t('button.viewAll') }} </BaseButton>
      </template>
    </TheBatchCofirm>
    <TheHostAliasRename v-model="theHostOpenRename" :id="hostEditData.id" :alias="hostEditData.name" @submit="getTableList" />
    <TheViewAllHost v-model="isViewAllHost" :lenght="selectionList.length" :content="viewAllHostcontent" />
    <TheHostEdit v-model="hostEditData.isOpen" :id="hostEditData.id" :host-name="hostEditData.name" />
    <TheCheckHost v-model="theCheckHost" :check-uid="checkUid" />
    <HoseAddDetail
      :visible="addDetailVisible"
      @close="closeAddDetail"
      :executeRecordBoList="executeRecordBoList"
      :is-fail="currentIsFail"
      :hostId="currentHostId"
      :hostType="hostType"
      :hostData="currentHost"
    />
    <ViewDataSource :visible="dataSourceDrawerVisible" @close="closeDSDrawer" :host-info="dsDrawerHostInfo" />
  </div>
</template>

<script lang="ts" setup>
import { useBaseCommonUtils } from '@/hooks/useBaseCommonUtils'
import useTableSelect from '@/hooks/useTableSelect'
import getRandomString from '@/utils/GetRandomString'
import Loop from '@/utils/Loop'
import { handleCofirm } from '@/utils/The/TheConfirmDelete'
import elConfim from '@/utils/elConfim'
import { ElMessage } from 'element-plus'
import HoseAddDetail from './components/HostAddDetail.vue'
import ViewDataSource from './components/ViewDataSource.vue'
import { useHostListConfig } from './hooks/useHostListConfig'
import { HostType } from './types'

const { route, t, router, store, $has, setBreadList } = useBasicTool()
const isHostDelete = ref(false)
const theHostOpenRename = ref(false)
const { searchItemData, tableData, gatewayTableData } = useHostListConfig()
const { selectionList, handleSelect, getRowKey, isBatchOperat } = useTableSelect()
const { pageInfo, resetPageInfo } = usePage()
const { getSelectOptions } = useBaseCommonUtils()
const baseTable = ref()
const theCheckHost = ref(false)
const isViewAllHost = ref(false)
const checkUid = ref()
const viewAllHostcontent = ref('')
const batchOpen = ref(false)
let timer: any
const lock = ref<boolean>(false)
const params = reactive({
  message: t('message.unbindSelectedHost'),
  desc: t('message.afterTheHostIsUnbound'),
  name: '',
  preventMessage: '为防止意外操作，请输入该验证码到输入框：',
  inputPlaceholder: t('replenish.enterVerification'),
  inputErrorMessage: t('replenish.pleaseEnterCorrectVerificationCode'),
  batchLable: t('replenish.selectedHost'),
  itemList: ''
})
const hostEditData = reactive({ isOpen: false, id: 0, name: '' })
// 是否显示table加载动画
const tableLoading = ref<boolean>(true)
interface SearchModelObj {
  [key: string]: string
}
const hostType = ref<HostType>(HostType.HOST)
const searchModelObj: SearchModelObj = reactive({
  onlineState: '', // 状态
  namespaceId: '', // 所属集群
  ip: '',
  rackNumber: '',
  alias: '',
  requestTenantId: '' // IP
})

const addDetailVisible = ref(false)

const dataSourceDrawerVisible = ref(false)

const executeRecordBoList = ref<any[]>([])
const currentIsFail = ref(false)
const currentHostId = ref('')
const currentHost = ref()

const dsDrawerHostInfo = ref({
  id: '',
  name: ''
})

function toggleType(type: HostType) {
  hostType.value = type
  selectionList.value = []
  baseTable.value.$refs.clearSelection()
  getTableList()
}

function showAddDetail(list: any[], addState: number, id: string, row: any) {
  executeRecordBoList.value = list
  currentIsFail.value = addState === 2
  currentHostId.value = id
  currentHost.value = row
  addDetailVisible.value = true
}

function closeAddDetail() {
  addDetailVisible.value = false
}

function showDSDrawer() {
  dataSourceDrawerVisible.value = true
}

function closeDSDrawer() {
  dataSourceDrawerVisible.value = false
}

// 判断是否可以选中
function selectable(row: any) {
  return true
}

function getCurrentAddState(state: number) {
  switch (state) {
    case 0:
      return t('state.completeAdd')
    case 1:
      return t('state.preAdded')
    case 2:
      return t('state.addFailed')
  }
  return '-'
}

function getCurrentAddStateColor(state: number) {
  const statusColor: Record<string, string> = {
    '0': 'green',
    '1': 'orange',
    '2': 'red'
  }
  return statusColor[state]
}

// 表格操作栏数据
function tableToolbar(scope: any): BaseToolbar.ToolbarData {
  if (hostType.value === HostType.HOST) {
    return {
      buttonData: [
        {
          buttonName: t('table.clusterUnbinding'),
          disabled: false,
          click: () => {
            onRemoveClick(scope)
          },
          isButton: !isDefaultNamespace(scope) && $has('hosts-Unbind')
        },
        {
          buttonName: t('table.delete'),
          disabled: scope.row.onlineState === '2',
          click: () => {
            onDeleteClick(scope)
          },
          isButton: isDefaultNamespace(scope) && $has('hosts-delete')
        },
        {
          buttonName: t('table.edit'),
          disabled: false,
          click: () => {
            hostEditData.isOpen = true
            hostEditData.id = scope.row.id
            hostEditData.name = scope.row.name
          },
          isButton: $has('hosts-edit')
        }
      ]
    }
  } else {
    return {
      buttonData: [
        {
          buttonName: t('replenish.viewDataSource'),
          disabled: false,
          click: () => {
            dsDrawerHostInfo.value = {
              id: scope.row.id,
              name: scope.row.hostName
            }
            showDSDrawer()
          },
          isButton: $has('hosts-view-datasource')
        },
        {
          buttonName: t('table.delete'),
          disabled: scope.row.onlineState === '2',
          click: () => {
            onDeleteClick(scope)
          },
          isButton: $has('hosts-delete')
        }
      ]
    }
  }
}
onMounted(() => {
  if (route.query.rackNumber) {
    searchItemData[3].defaultValue = route.query.rackNumber
    searchModelObj.rackNumber = searchItemData[3].defaultValue
  }
  if ($has('hosts-batch-delete') || $has('hosts-batch-unbind')) {
    tableData.column.unshift({ type: 'selection', 'reserve-selection': true, selectable: selectable })
    gatewayTableData.column.unshift({ type: 'selection', 'reserve-selection': true, selectable: selectable })
  }
  store.dispatch('user/getAllTenantInfo').then((res) => {
    searchItemData[2].selectOptions = getSelectOptions('tenantName', 'tenantId', res.data)
  })
  setBreadList([
    // {
    //   name: t('menu.hostList')
    // }
  ])
  getResourceLimitConfig()
  // 获取表格数据请求
  getTableList()
  // 获取所属集群请求
  getNamespaceList()
  timer = new Loop(() => {
    getTableList(false)
  }, 10000)
})
onUnmounted(() => {
  timer.clearLoop()
})

// 搜索提交
function submitSearch(value: any) {
  pageInfo.pageNo = 1
  searchOpera(value)
}
// 重置搜索框
function resetSearch(value: any) {
  searchItemData[3].defaultValue = ''
  value.rackNumber = ''
  resetPageInfo()
  searchOpera(value)
}
// 搜索操作
function searchOpera(value: any) {
  tableLoading.value = true
  Object.keys(searchModelObj).forEach((key) => {
    searchModelObj[key] = value[key]
  })
  getTableList()
}

function getNamespaceList() {
  store.dispatch('hosts/getNamespaceList').then((res: any) => {
    searchItemData[1].selectOptions = getSelectOptions('name', 'id', res.data)
  })
}

function getTableList(searchAudit?: boolean) {
  if (lock.value) return
  lock.value = true
  const params = {
    ...pageInfo,
    ...searchModelObj,
    searchAudit,
    hostType: hostType.value
  }
  store
    .dispatch('hosts/getHostList', params)
    .then((res: any) => {
      if (params.hostType !== hostType.value) {
        return
      }
      if (hostType.value === HostType.HOST) {
        tableData.data = res.data.records
      } else {
        gatewayTableData.data = res.data.records
      }
      pageInfo.total = res.data.total
      tableLoading.value = false
    })
    .finally(() => {
      lock.value = false
    })
}
// 添加主机
function addHost() {
  router.push({
    name: 'hostsAdd',
    query: {
      type: hostType.value
    }
  })
}

function handlePageChange() {
  getTableList()
}

// 显示弹框
function onRemoveClick(scope: any) {
  const hostId = scope.row.id
  const namespaceId = scope.row.namespaceId
  const hostName = scope.row.name
  const configs = {
    message: t('message.ubindHost', { value: hostName }),
    desc: t('message.defaultClusterHost'),
    name: hostName,
    preventMessage: t('message.accidentalHostname'),
    inputPlaceholder: t('message.hostName'), //输入框默认信息
    inputErrorMessage: t('replenish.pleaseEnterCorrectHostToUnbind') //检验信息
  }
  handleCofirm(configs).then(() => {
    hostsUnBind([hostId], namespaceId)
  })
}
function hostsUnBind(hostIds: number[], namespaceId?: number) {
  store
    .dispatch('hosts/hostsUnBind', {
      hostIds: hostIds,
      namespaceId
    })
    .then((res: any) => {
      getTableList()
      ElMessage({
        message: t('message.hostUnbundled'),
        type: 'success'
      })
      baseTable.value.$refs.clearSelection()
    })
}
//删除主机
function onDeleteClick(scope: any) {
  const configs = {
    message: scope.row.name ? t('message.isDeleteHost', { value: scope.row.name }) : '是否确定删除当前主机？',
    desc: '',
    showInput: !!scope.row.name,
    showRemind: !!scope.row.name,
    name: scope.row.name,
    preventMessage: t('message.accidentalHostname'),
    inputPlaceholder: t('message.deletedHostName'), //输入框默认信息
    inputErrorMessage: t('message.deletedHostName') //检验信息
  }
  handleCofirm(configs).then(() => {
    if (hostType.value === HostType.HOST) {
      store.dispatch('hosts/deleteHost', { id: scope.row?.id }).then((res) => {
        getTableList()
      })
    } else {
      store.dispatch('hosts/deleteEdgeHost', { id: scope.row?.id }).then((res) => {
        getTableList()
      })
    }
  })
}
function isDefaultNamespace(scope: any): boolean {
  const namespaceId = scope.row?.namespaceId
  return namespaceId === -1
}
function routeData(scope?: any): string {
  return router.resolve({
    name: 'webTerminal',
    query: { host: scope.row.ip }
  }).href
}
function batchDelete() {
  const hostIds = selectionList.value.map((item) => item.id)
  store.dispatch('hosts/batchDeleteHostCheck', { ids: hostIds }).then((res) => {
    if (!res.data) {
      elConfim.confim({
        isCancelButton: true,
        message: t('replenish.hostOccupiedCannotDelete')
      })
    } else {
      params.message = t('replenish.confirmDeleteSelectedHost')
      params.desc = t('replenish.unbindAndDeleteHostBoundToCluster')
      batch()
      isHostDelete.value = true
    }
  })
}

function batchUnbind() {
  params.message = t('message.unbindSelectedHost')
  params.desc = t('message.afterTheHostIsUnbound')
  batch()
  isHostDelete.value = false
}
function batch() {
  batchOpen.value = true
  const itemList = selectionList.value.map((item) => `${item.name}(${item.ip})`).join('；')
  params.itemList = itemList
  params.batchLable = `${t('replenish.hostsSelected')}(${selectionList.value.length}):`
  params.name = getRandomString()
}
function submit() {
  const hostIds = selectionList.value.map((item) => item.id)
  if (isHostDelete.value) {
    store.dispatch('hosts/batchDeleteHost', { ids: hostIds }).then((res) => {
      getTableList()
      baseTable.value.$refs.clearSelection()
    })
  } else {
    hostsUnBind(hostIds)
  }
}
function viewAll() {
  isViewAllHost.value = true
  viewAllHostcontent.value = selectionList.value.map((item) => `${item.name}(${item.ip})`).join('；')
}
function openDetails(hostId: number, hostIp: string) {
  router.push({
    name: 'hostDetails',
    query: { hostId, hostIp }
  })
}
function openRoleList(hostId: number, ip: string) {
  router.push({
    name: 'hostDetails',
    query: { hostId, hostIp: ip, tab: 'role' }
  })
}
function openTheCheckHost() {
  store.dispatch('hosts/hostsCheck', '').then((res) => {
    theCheckHost.value = true
    checkUid.value = res.data
  })
}

function maintenance(scope: any) {
  store.dispatch('hosts/hostsMaintenance', { id: scope.row.id, maintenanceStatus: scope.row.maintenanceStatus === '1' ? 0 : 1 }).then((res) => {
    getTableList()
    if (!res.data) {
      ElMessage.error(t('replenish.maintenanceModeActivationFailed'))
    }
  })
}
const userInfo = computed(() => store.state.user.userInfo)
const addHostDisabled = ref(true)
//获取是否有添加主机权限
function getResourceLimitConfig() {
  store.dispatch('hosts/getResourceLimitConfig', { id: userInfo.value.tenantId }).then((res) => {
    addHostDisabled.value = res.data ? (Number(res.data?.type) === 1 ? false : true) : false
  })
}

function openRename(id: number, alias: string) {
  theHostOpenRename.value = true
  hostEditData.id = id
  hostEditData.name = alias
}
</script>

<style lang="scss" scoped>
.hosts {
  height: 100%;
  :deep(.layout) {
    .layout-header {
      flex-flow: column;
      align-items: flex-start;
    }
  }
  :deep(.base-search) {
    flex: 1;
    min-width: 100%;
    .el-form {
      display: inline-flex;
      flex-flow: row wrap;
      width: 100%;
    }
    .search-button {
      flex: 1;
      min-width: 400px;
      margin-right: 0;
      .el-form-item__content {
        display: inline-flex;
        flex-flow: row;
        width: 100%;
      }
      .append-btn {
        flex: 1;
        text-align: right;
      }
    }
  }
  &-type {
    display: inline-flex;
    align-items: center;
    width: 100%;
    margin-bottom: 12px;
    &-item {
      padding: 6px 18px;
      border: 1px solid #f4f5f9;
      border-radius: 16px;
      margin-right: 20px;
      font-size: 14px;
      line-height: 20px;
      color: rgba(153, 160, 181, 1);
      background-color: #f4f5f9;
      cursor: pointer;
      &.active {
        border: 1px solid #4777ff;
        color: rgba(71, 119, 255, 1);
        background: rgba(71, 119, 255, 0.1);
      }
    }
  }
  .add-status {
    position: relative;
    padding-left: 12px;
    cursor: pointer;
    &:before {
      content: '';
      position: absolute;
      top: 9px;
      left: 0px;
      display: block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #0bb060;
    }
    &.green {
      &:before {
        background-color: #0bb060;
      }
    }
    &.orange {
      &:before {
        background-color: #f29200;
      }
    }
    &.red {
      &:before {
        background-color: #f66660;
      }
    }
  }
  .interval--vertical {
    position: relative;
    top: 5px;
  }
  :deep(.el-switch__core) {
    height: 22px !important;
  }
}
</style>
