<template>
  <textarea ref="codeEditor" :placeholder="$t('replenish.pleaseInput')"></textarea>
</template>

<script lang="ts" setup>
// codemirror基础资源引入
import _CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/mode/xml/xml' // xml编辑器模式
import 'codemirror/theme/idea.css' // 主题
// 折叠资源引入:开始
import 'codemirror/addon/fold/brace-fold.js'
import 'codemirror/addon/fold/comment-fold.js'
import 'codemirror/addon/fold/foldcode.js'
import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/addon/fold/foldgutter.js'
import 'codemirror/addon/fold/indent-fold.js'
// 折叠资源引入:结束

// 搜索资源引入:开始
import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/scroll/annotatescrollbar.js'
import 'codemirror/addon/search/jump-to-line.js'
import 'codemirror/addon/search/match-highlighter.js'
import 'codemirror/addon/search/matchesonscrollbar.js'

import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/search/search.js'
import 'codemirror/addon/search/searchcursor.js'
// 搜索资源引入:结束

// 启用placeholder
import 'codemirror/addon/display/placeholder.js'
// 代码高亮
import 'codemirror/addon/hint/xml-hint.js'
import 'codemirror/addon/selection/active-line.js' //光标行背景高亮，配置里面也需要styleActiveLine设置为true
// import '@/utils/xml-lint.js';
const CodeMirror = window.CodeMirror || _CodeMirror
const props = defineProps({
  modelValue: String,
  defaultValue: String,
  readOnly: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'input'])
const codeEditor = ref()
let editor: any
watch(
  () => props.modelValue,
  () => {
    if (null != editor && props.modelValue && props.modelValue !== editor.getValue()) {
      // 触发v-model的双向绑定
      editor.setValue(props.modelValue)
    }
  }
)
watch(
  () => props.readOnly,
  () => {
    if (null != editor) {
      editor.setOption('readOnly', props.readOnly)
    }
  }
)
onMounted(() => {
  nextTick(() => {
    editor = CodeMirror.fromTextArea(codeEditor.value, {
      mode: 'application/xml',
      value: props.modelValue,
      mime: 'application/xml',
      theme: 'idea', // 主题
      indentWithTabs: false, // 在缩进时，是否需要把 n*tab宽度个空格替换成n个tab字符，默认为false
      smartIndent: true, // 自动缩进，设置是否根据上下文自动缩进（和上一行相同的缩进量）。默认为true
      lineNumbers: true, // 是否在编辑器左侧显示行号
      matchBrackets: true, // 括号匹配
      readOnly: props.readOnly,
      // 启用代码折叠相关功能:开始
      foldGutter: true,
      lineWrapping: true,
      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
      // 启用代码折叠相关功能:结束
      styleActiveLine: true // 光标行高亮
    })
    // 监听编辑器的change事件
    editor.on('change', () => {
      // 触发v-model的双向绑定
      emit('update:modelValue', editor.getValue())
      // 触发input事件
      emit('input', editor.getValue())
    })
    if (props.defaultValue) {
      editor.setValue(props.defaultValue)
    }
  })
})
onBeforeUnmount(() => {
  if (null !== editor) {
    editor.toTextArea()
    editor = null
  }
})
</script>

<style lang="scss" scoped></style>
