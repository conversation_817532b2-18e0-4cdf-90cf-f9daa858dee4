<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeDiffEditor Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #337ecc;
        }
        .info {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .diff-container {
            display: flex;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .diff-side {
            flex: 1;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            overflow: auto;
        }
        .diff-original {
            background-color: #fff5f5;
            border-right: 1px solid #ddd;
        }
        .diff-modified {
            background-color: #f0fff4;
        }
        .diff-line-removed {
            background-color: #fecaca;
            text-decoration: line-through;
        }
        .diff-line-added {
            background-color: #bbf7d0;
        }
    </style>
</head>
<body>
    <h1>CodeDiffEditor 测试</h1>
    
    <div class="info">
        <h3>问题诊断</h3>
        <p>如果 CodeDiffEditor 组件显示内容但没有 diff 效果，可能的原因：</p>
        <ul>
            <li><strong>数据问题</strong>：originCode 和 modifiedCode 的数据太相似或为空</li>
            <li><strong>语言设置</strong>：language 属性设置不正确</li>
            <li><strong>Monaco Editor 配置</strong>：diff editor 的配置选项有问题</li>
            <li><strong>容器高度</strong>：容器没有明确的高度</li>
            <li><strong>初始化时机</strong>：组件初始化时数据还没有准备好</li>
        </ul>
    </div>

    <div class="controls">
        <button onclick="testSimpleDiff()">测试简单差异</button>
        <button onclick="testComplexDiff()">测试复杂差异</button>
        <button onclick="testYamlDiff()">测试 YAML 差异</button>
        <button onclick="testJsonDiff()">测试 JSON 差异</button>
    </div>

    <h3>模拟的 Diff 显示（用于对比）</h3>
    <div class="diff-container">
        <div class="diff-side diff-original" id="originalContent">
            <div>原始内容将显示在这里...</div>
        </div>
        <div class="diff-side diff-modified" id="modifiedContent">
            <div>修改后内容将显示在这里...</div>
        </div>
    </div>

    <script>
        const testCases = {
            simple: {
                original: `line 1
line 2
line 3
line 4`,
                modified: `line 1
line 2 modified
line 3
line 4
line 5 added`
            },
            complex: {
                original: `function calculateTotal(items) {
  let total = 0;
  for (let i = 0; i < items.length; i++) {
    total += items[i].price;
  }
  return total;
}`,
                modified: `function calculateTotal(items, tax = 0) {
  let total = 0;
  for (const item of items) {
    total += item.price * (1 + item.discount || 0);
  }
  return total * (1 + tax);
}`
            },
            yaml: {
                original: `# 原始配置文件
server:
  port: 8080
  host: localhost
  
database:
  url: ********************************
  username: root
  password: password123
  
logging:
  level: INFO
  file: /var/log/app.log`,
                modified: `# 修改后的配置文件
server:
  port: 9090
  host: 0.0.0.0
  
database:
  url: ****************************************
  username: admin
  password: newPassword456
  pool:
    max-connections: 20
  
logging:
  level: DEBUG
  file: /var/log/app.log
  
cache:
  enabled: true
  ttl: 3600`
            },
            json: {
                original: `{
  "name": "test-app",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  },
  "scripts": {
    "start": "node index.js"
  }
}`,
                modified: `{
  "name": "test-app",
  "version": "1.1.0",
  "dependencies": {
    "express": "^4.19.0",
    "lodash": "^4.17.21",
    "axios": "^1.6.0"
  },
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest"
  },
  "devDependencies": {
    "nodemon": "^3.0.0",
    "jest": "^29.0.0"
  }
}`
            }
        };

        function showDiff(testCase) {
            const originalEl = document.getElementById('originalContent');
            const modifiedEl = document.getElementById('modifiedContent');
            
            originalEl.textContent = testCase.original;
            modifiedEl.textContent = testCase.modified;
            
            console.log('Test case data:', testCase);
        }

        function testSimpleDiff() {
            showDiff(testCases.simple);
        }

        function testComplexDiff() {
            showDiff(testCases.complex);
        }

        function testYamlDiff() {
            showDiff(testCases.yaml);
        }

        function testJsonDiff() {
            showDiff(testCases.json);
        }

        // 默认显示 YAML 差异
        testYamlDiff();
    </script>
</body>
</html>
