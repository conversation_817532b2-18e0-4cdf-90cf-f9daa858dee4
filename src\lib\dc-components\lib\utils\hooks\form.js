'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');

function useDependModel(defaultModelData = {}) {
  const dependModel = vue.reactive({ ...defaultModelData });
  const changeDependModel = (val) => {
    Object.keys(val).forEach((item) => {
      dependModel[item] = val[item];
    });
  };
  return {
    dependModel,
    changeDependModel
  };
}

exports.useDependModel = useDependModel;
//# sourceMappingURL=form.js.map
