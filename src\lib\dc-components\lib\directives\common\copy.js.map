{"version": 3, "file": "copy.js", "sources": ["../../../../../packages/directives/common/copy.ts"], "sourcesContent": ["/**\r\n * v-copy\r\n * 复制某个值至剪贴板\r\n * 接收参数：string类型/Ref<string>类型/Reactive<string>类型\r\n */\r\nimport { ElMessage } from \"element-plus\";\r\nimport type { Directive, DirectiveBinding } from \"vue\";\r\n\r\ninterface DirectiveBindingValue {\r\n  text: string;\r\n  showTip?: boolean;\r\n  tips?: string;\r\n}\r\ninterface ElType extends HTMLElement {\r\n  copyValue: DirectiveBindingValue;\r\n  __handleClick__: any;\r\n}\r\n\r\nconst defaultCopyValue = {\r\n  showTip: true,\r\n  text: \"\",\r\n  tips: \"复制成功\",\r\n};\r\n\r\nconst copy: Directive = {\r\n  mounted(el: ElType, binding: DirectiveBinding<DirectiveBindingValue>) {\r\n    el.copyValue = { ...defaultCopyValue, ...binding.value };\r\n    el.addEventListener(\"click\", handleClick);\r\n  },\r\n  updated(el: ElType, binding: DirectiveBinding<DirectiveBindingValue>) {\r\n    el.copyValue = { ...defaultCopyValue, ...binding.value };\r\n  },\r\n  beforeUnmount(el: ElType) {\r\n    el.removeEventListener(\"click\", el.__handleClick__);\r\n  },\r\n};\r\n\r\nfunction handleClick(this: any) {\r\n  const input = document.createElement(\"input\");\r\n  input.value = this.copyValue.text.toLocaleString();\r\n  document.body.appendChild(input);\r\n  input.select();\r\n  document.execCommand(\"Copy\");\r\n  document.body.removeChild(input);\r\n  if (this.copyValue.showTip) {\r\n    ElMessage({\r\n      type: \"success\",\r\n      message: this.copyValue.tips,\r\n    });\r\n  }\r\n}\r\n\r\nexport default copy;\r\n"], "names": ["ElMessage"], "mappings": ";;;;;;;AAkBA,MAAM,gBAAmB,GAAA;AAAA,EACvB,OAAS,EAAA,IAAA;AAAA,EACT,IAAM,EAAA,EAAA;AAAA,EACN,IAAM,EAAA,0BAAA;AACR,CAAA,CAAA;AAEA,MAAM,IAAkB,GAAA;AAAA,EACtB,OAAA,CAAQ,IAAY,OAAkD,EAAA;AACpE,IAAA,EAAA,CAAG,YAAY,EAAE,GAAG,gBAAkB,EAAA,GAAG,QAAQ,KAAM,EAAA,CAAA;AACvD,IAAG,EAAA,CAAA,gBAAA,CAAiB,SAAS,WAAW,CAAA,CAAA;AAAA,GAC1C;AAAA,EACA,OAAA,CAAQ,IAAY,OAAkD,EAAA;AACpE,IAAA,EAAA,CAAG,YAAY,EAAE,GAAG,gBAAkB,EAAA,GAAG,QAAQ,KAAM,EAAA,CAAA;AAAA,GACzD;AAAA,EACA,cAAc,EAAY,EAAA;AACxB,IAAG,EAAA,CAAA,mBAAA,CAAoB,OAAS,EAAA,EAAA,CAAG,eAAe,CAAA,CAAA;AAAA,GACpD;AACF,EAAA;AAEA,SAAgC,WAAA,GAAA;AAC9B,EAAM,MAAA,KAAA,GAAQ,QAAS,CAAA,aAAA,CAAc,OAAO,CAAA,CAAA;AAC5C,EAAA,KAAA,CAAM,KAAQ,GAAA,IAAA,CAAK,SAAU,CAAA,IAAA,CAAK,cAAe,EAAA,CAAA;AACjD,EAAS,QAAA,CAAA,IAAA,CAAK,YAAY,KAAK,CAAA,CAAA;AAC/B,EAAA,KAAA,CAAM,MAAO,EAAA,CAAA;AACb,EAAA,QAAA,CAAS,YAAY,MAAM,CAAA,CAAA;AAC3B,EAAS,QAAA,CAAA,IAAA,CAAK,YAAY,KAAK,CAAA,CAAA;AAC/B,EAAI,IAAA,IAAA,CAAK,UAAU,OAAS,EAAA;AAC1B,IAAUA,qBAAA,CAAA;AAAA,MACR,IAAM,EAAA,SAAA;AAAA,MACN,OAAA,EAAS,KAAK,SAAU,CAAA,IAAA;AAAA,KACzB,CAAA,CAAA;AAAA,GACH;AACF;;;;"}