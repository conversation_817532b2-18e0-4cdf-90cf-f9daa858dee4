'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
require('element-plus/es/components/dialog/style/css');
var Footer = require('./Footer.js');
var types = require('./types.js');
require('./index.vue_vue_type_style_index_0_lang.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const _hoisted_1 = /* @__PURE__ */ vue.createElementVNode("div", { style: { "display": "none" } }, null, -1);
const _hoisted_2 = /* @__PURE__ */ vue.createElementVNode("div", { style: { "display": "none" } }, null, -1);
const __default__ = vue.defineComponent({
  name: "DcDialog"
});
const _sfc_main = vue.defineComponent({
  ...__default__,
  props: {
    modelValue: {
      type: Boolean
    },
    type: {
      type: String,
      default: types.DcDialogTypes.HEADER_FOOTER
    },
    confirmAction: {
      type: Object,
      default: () => ({})
    },
    cancelAction: {
      type: Object,
      default: () => ({})
    },
    footerLeftSlot: {
      type: Object
    },
    footerRightSlot: {
      type: Object
    },
    closeIsCancel: {
      type: Boolean,
      default: true
    },
    class: {
      type: String,
      default: ""
    }
  },
  emits: ["update:modelValue", "cancel", "confirm", "close"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const slots = vue.useSlots();
    const thisFooter = vue.computed(() => {
      return props.type !== types.DcDialogTypes.HEADER && !slots.footer;
    });
    const noHeader = vue.computed(() => {
      return [types.DcDialogTypes.FOOTER, types.DcDialogTypes.ONLYCONTENT].includes(props.type);
    });
    const noFooter = vue.computed(() => {
      return [types.DcDialogTypes.HEADER, types.DcDialogTypes.ONLYCONTENT].includes(props.type);
    });
    const close = () => {
      emits("update:modelValue", false);
      emits("close");
      props.closeIsCancel && emits("cancel");
    };
    const onCancel = () => {
      emits("cancel");
    };
    const onConfirm = () => {
      emits("confirm");
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElDialog), vue.mergeProps(_ctx.$attrs, {
        "model-value": props.modelValue,
        class: `dc-dialog ${noFooter.value ? "no-footer" : ""} ${noHeader.value ? "no-header" : ""} ${props.class}`,
        onClose: close
      }), vue.createSlots({
        default: vue.withCtx(() => [
          vue.renderSlot(_ctx.$slots, "default")
        ]),
        _: 2
      }, [
        props.type === vue.unref(types.DcDialogTypes).FOOTER ? {
          name: "header",
          fn: vue.withCtx(() => [
            _hoisted_1
          ]),
          key: "0"
        } : _ctx.$slots.header ? {
          name: "header",
          fn: vue.withCtx(() => [
            vue.renderSlot(_ctx.$slots, "header")
          ]),
          key: "1"
        } : void 0,
        props.type === vue.unref(types.DcDialogTypes).HEADER ? {
          name: "footer",
          fn: vue.withCtx(() => [
            _hoisted_2
          ]),
          key: "2"
        } : _ctx.$slots.footer ? {
          name: "footer",
          fn: vue.withCtx(() => [
            vue.renderSlot(_ctx.$slots, "footer")
          ]),
          key: "3"
        } : void 0,
        thisFooter.value ? {
          name: "footer",
          fn: vue.withCtx(() => [
            vue.createVNode(Footer["default"], {
              "confirm-action": props.confirmAction,
              "cancel-action": props.cancelAction,
              "footer-left-slot": props.footerLeftSlot,
              "footer-right-slot": props.footerRightSlot,
              onCancel,
              onConfirm
            }, null, 8, ["confirm-action", "cancel-action", "footer-left-slot", "footer-right-slot"])
          ]),
          key: "4"
        } : void 0
      ]), 1040, ["model-value", "class"]);
    };
  }
});
var Dialog = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\dialog\\src\\index.vue"]]);

exports["default"] = Dialog;
//# sourceMappingURL=index.js.map
