<template>
  <div class="f-14 lh-20px mb-10px">{{ data.inspectionName ?? '-' }}</div>
  <div class="view-table">
    <div class="flex items-center">
      <div class="w-115px py-14px px-17px">{{ $t('replenish.inspectionDescription') }}</div>
      <div class="py-14px px-17px flex-1 border-left white-space">{{ data?.inspectionInfo?.replace(/\\n/g, '\n') ?? '-' }}</div>
    </div>
    <div class="flex border-y items-center">
      <div class="w-115px py-14px px-17px">{{ $t('replenish.inspectionItemResult') }}</div>
      <div class="py-14px px-17px flex-1 border-left white-space">
        {{ data?.inspectionResult?.replace(/\\n/g, '\n') ?? '-' }}
      </div>
    </div>
    <div class="flex items-center">
      <div class="w-115px py-14px px-17px">{{ $t('replenish.inspectionItemSuggestion') }}</div>
      <div class="py-14px px-17px flex-1 border-left white-space">
        {{ data.inspectionAdvice?.replace(/\\n/g, '\n') ?? '-' }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
</script>
<style lang="scss" scoped>
.view-table {
  border: 1px solid var(--ops-border-color);
  margin-bottom: 20px;
  font-size: 14px;
  .border-y {
    border-top: 1px solid var(--ops-border-color);
    border-bottom: 1px solid var(--ops-border-color);
  }
  .border-left {
    border-left: 1px solid var(--ops-border-color);
  }
  .white-space {
    white-space: pre-wrap;
  }
}
</style>
