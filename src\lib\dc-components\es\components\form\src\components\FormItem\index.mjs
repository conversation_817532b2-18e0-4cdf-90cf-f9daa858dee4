import { defineComponent, ref, computed, watch, withDirectives, openBlock, createBlock, unref, normalizeClass, normalizeStyle, createSlots, withCtx, resolveDynamicComponent, mergeProps, renderList, createVNode, createCommentVNode, vShow } from 'vue';
import { ElFormItem } from 'element-plus';
import 'element-plus/es/components/form-item/style/css';
import RenderVNode from '../../../../render-vnode/src/index.mjs';
import { Component } from './constants.mjs';
import _export_sfc from '../../../../../_virtual/plugin-vue_export-helper.mjs';

const __default__ = defineComponent({
  name: "DcFormItem"
});
const _sfc_main = defineComponent({
  ...__default__,
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    config: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: [String, Number, Boolean, Array, Object]
    },
    margin: {
      type: Object,
      default: () => ({})
    },
    isInline: {
      type: <PERSON>olean,
      default: false
    },
    rowIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ["update:modelValue", "triggerEffect"],
  setup(__props, { emit: __emit }) {
    ;
    const props = __props;
    const emits = __emit;
    const formItemRef = ref();
    const marginBottom = computed(() => props.margin.bottom ? props.margin.bottom : "18px");
    const marginRight = computed(() => props.margin.right ? props.margin.right : props.isInline ? "32px" : "0px");
    const marginLeft = computed(() => props.margin.left ? props.margin.left : "unset");
    const marginTop = computed(() => props.margin.top ? props.margin.top : "unset");
    const ruleTrigger = computed(() => {
      return (props.config.rules || []).map((item) => item.trigger);
    });
    const handleBlur = () => {
      if (ruleTrigger.value.includes("blur")) {
        formItemRef.value?.validate("blur", () => {
        });
      }
    };
    const triggerEffect = (effect, rowIndex) => {
      emits("triggerEffect", effect, rowIndex);
    };
    const itemChange = (val) => {
      emits("update:modelValue", val, props.config);
      if (props.config.effect && typeof props.config.effect === "function") {
        triggerEffect(props.config.effect, props.rowIndex);
      }
    };
    watch(() => props.modelValue, () => {
      if (ruleTrigger.value.includes("change")) {
        formItemRef.value?.validate("change", () => {
        });
      }
    });
    return (_ctx, _cache) => {
      return withDirectives((openBlock(), createBlock(unref(ElFormItem), {
        ref_key: "formItemRef",
        ref: formItemRef,
        prop: props.config.model,
        label: props.config.label,
        "label-width": props.config.labelWidth,
        rules: props.visible ? props.config.rules : [],
        "inline-message": props.config.inlineMessage,
        class: normalizeClass(`dc-form-item ${props.config.class || ""}`),
        style: normalizeStyle({
          marginRight: marginRight.value,
          marginBottom: marginBottom.value,
          marginTop: marginTop.value,
          marginLeft: marginLeft.value
        })
      }, createSlots({
        default: withCtx(() => [
          (openBlock(), createBlock(resolveDynamicComponent(unref(Component)[props.config.component]), mergeProps({ ...props.config.props || {} }, {
            "model-value": props.modelValue,
            "file-list": props.modelValue,
            "row-index": props.rowIndex,
            disabled: props.config.disabled,
            "onUpdate:modelValue": itemChange,
            onBlur: handleBlur,
            onTriggerEffect: triggerEffect
          }), createSlots({ _: 2 }, [
            renderList(Object.keys(props.config.componentSlot || {}), (slotI) => {
              return {
                name: slotI,
                fn: withCtx((scope) => [
                  createVNode(unref(RenderVNode), {
                    vnode: (props.config.componentSlot || {})[slotI],
                    scope
                  }, null, 8, ["vnode", "scope"])
                ])
              };
            })
          ]), 1040, ["model-value", "file-list", "row-index", "disabled"])),
          props.config.rightSlot ? (openBlock(), createBlock(unref(RenderVNode), {
            key: 0,
            vnode: props.config.rightSlot
          }, null, 8, ["vnode"])) : createCommentVNode("v-if", true)
        ]),
        _: 2
      }, [
        props.config.labelSlot ? {
          name: "label",
          fn: withCtx((scope) => [
            createVNode(unref(RenderVNode), {
              vnode: props.config.labelSlot,
              scope
            }, null, 8, ["vnode", "scope"])
          ]),
          key: "0"
        } : void 0
      ]), 1032, ["prop", "label", "label-width", "rules", "inline-message", "class", "style"])), [
        [vShow, props.visible]
      ]);
    };
  }
});
var DcFormItem = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\form\\src\\components\\FormItem\\index.vue"]]);

export { DcFormItem as default };
//# sourceMappingURL=index.mjs.map
