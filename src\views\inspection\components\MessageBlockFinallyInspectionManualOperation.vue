<template>
  <el-dialog :model-value="modelValue" :title="$t('title.confirmTip')" width="600px" :before-close="handleClose">
    <div class="ml-20px">
      <div class="mb-10px">
        <i class="iconfont icon-icon_jinggaoo c-#f38922" />
        <span class="f-16 ml-5px c-#404C33">{{ $t('replenish.selectManualInspectionType') }}</span>
      </div>

      <el-checkbox v-model="checkedCities.platform">
        <span class="c-hex-666D80">{{ $t('replenish.platformBasicEnvironment') }}</span>
      </el-checkbox>
      <el-checkbox v-model="checkedCities.cluster">
        <span class="c-hex-666D80">{{ $t('replenish.clusterInspection') }}</span>
      </el-checkbox>
      <el-checkbox v-model="checkedCities.component">
        <span class="c-hex-666D80">{{ $t('replenish.componentServiceInspection') }}</span>
      </el-checkbox>
      <el-checkbox v-model="checkedCities.host">
        <span class="c-hex-666D80">{{ $t('replenish.hostInspection') }}</span>
      </el-checkbox>
      <el-checkbox v-model="checkedCities.task">
        <span class="c-hex-666D80">{{ $t('replenish.disasterRecoveryTaskInspection') }}</span>
      </el-checkbox>
    </div>
    <template #footer>
      <BaseButton type="primary" :disabled="isSubmit" @click="sure">{{ $t('button.sure') }}</BaseButton>
      <BaseButton type="info" @click="clone">{{ $t('button.cancel') }}</BaseButton>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
const { store } = useBasicTool()
const checkedCities = reactive({
  platform: false,
  cluster: false,
  component: false,
  host: false,
  task: false
})
defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})
const isSubmit = computed(() => {
  const { platform, cluster, component, host, task } = checkedCities
  return !(platform || cluster || component || host || task)
})
const emit = defineEmits(['update:modelValue', 'submit'])
function handleClose() {
  checkedCities.cluster = false
  checkedCities.platform = false
  checkedCities.component = false
  checkedCities.host = false
  checkedCities.task = false
  emit('update:modelValue', false)
}
function sure() {
  store
    .dispatch('inspection/inspectionManual', {
      ...checkedCities
    })
    .then(() => {
      emit('submit')
      clone()
    })
}
function clone() {
  handleClose()
}
</script>
<style lang="scss" scoped>
:deep(.el-checkbox) {
  margin-right: 10px !important;
}
</style>
