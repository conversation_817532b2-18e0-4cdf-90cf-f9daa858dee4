{"version": 3, "file": "index.js", "sources": ["../../../../../../packages/components/search-filter/src/index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"`${clsPrefix} ${expandConfig.showExpand ? 'with-expand' : ''}`\">\r\n    <DcForm\r\n      ref=\"formRef\"\r\n      :config=\"formConfig\"\r\n      inline\r\n      :class=\"`${clsPrefix}-form `\"\r\n    />\r\n    <div v-if=\"expandConfig.showExpand\" :class=\"`${clsPrefix}-expand`\">\r\n      <Expand\r\n        :collapse-text=\"expandConfig.collapseText\"\r\n        :expand-text=\"expandConfig.expandText\"\r\n        @toggle-expand=\"toggleExpand\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, h, ref } from \"vue\";\r\n\r\nimport { DcFormComponentEnum } from \"../../form\";\r\nimport DcForm from \"../../form/src/index.vue\";\r\nimport Action from \"./Action.vue\";\r\nimport Expand from \"./Expand.vue\";\r\nimport type { DcFormModel } from \"../../form\";\r\nimport type { PropType, VNode } from \"vue\";\r\n\r\nimport type { DcSearchFilterConfig } from \"./types\";\r\n\r\ndefineOptions({\r\n  name: \"DcSearchFilter\",\r\n});\r\n\r\nconst clsPrefix = \"dc-search-filter\";\r\nconst actionModel = \"dc-search-filter-model\";\r\nconst extendModel = \"dc-search-filter-extend\";\r\n\r\nconst props = defineProps({\r\n  config: {\r\n    type: Object as PropType<DcSearchFilterConfig>,\r\n    default: () => ({}),\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"query\", \"reset\", \"refresh\"]);\r\n\r\nconst formRef = ref();\r\n\r\nconst isExpand = ref(false);\r\n\r\nconst expandConfig = computed(() => {\r\n  return props.config.expand || { showExpand: false, expandFields: [] };\r\n});\r\n\r\nconst formConfigChildren = computed(() => {\r\n  return (props.config.formConfig?.children || []).map((item) => {\r\n    return {\r\n      ...item,\r\n      visible: (modelValue: DcFormModel) => {\r\n        if (isExpand.value) {\r\n          return !expandConfig.value.expandFields.includes(item.model);\r\n        }\r\n        return item.visible ? item.visible(modelValue) : true;\r\n      },\r\n    };\r\n  });\r\n});\r\n\r\nconst formConfig = computed(() => {\r\n  const children = formConfigChildren.value;\r\n  children.push({\r\n    model: actionModel,\r\n    label: \"\",\r\n    labelWidth: 0,\r\n    defaultValue: \"\",\r\n    visible: () => true,\r\n    component: DcFormComponentEnum.CUSTOM,\r\n    margin: props.config.actionMargin,\r\n    props: {\r\n      renderCustom: () => {\r\n        return h(Action, {\r\n          config: props.config,\r\n          clsPrefix,\r\n          onQuery,\r\n          onRefresh,\r\n          onReset,\r\n        });\r\n      },\r\n    },\r\n  });\r\n  if (props.config.extendSlot) {\r\n    children.push({\r\n      model: extendModel,\r\n      label: \"\",\r\n      labelWidth: 0,\r\n      defaultValue: \"\",\r\n      visible: () => true,\r\n      component: DcFormComponentEnum.CUSTOM,\r\n      margin: props.config.extendMargin,\r\n      props: {\r\n        renderCustom: () => {\r\n          return props.config.extendSlot as VNode;\r\n        },\r\n      },\r\n    });\r\n  }\r\n\r\n  return props.config.formConfig\r\n    ? {\r\n        ...props.config.formConfig,\r\n        children,\r\n      }\r\n    : { children, labelWidth: \"auto\" };\r\n});\r\n\r\nconst toggleExpand = (val: boolean) => {\r\n  isExpand.value = val;\r\n};\r\n\r\nconst getValues = () => {\r\n  const query = formRef.value.getValues();\r\n  query[actionModel] = undefined;\r\n  query[extendModel] = undefined;\r\n  return query;\r\n};\r\n\r\nconst setValues = (modelValue: DcFormModel) => {\r\n  formRef.value.initModel(modelValue);\r\n};\r\n\r\nconst onQuery = () => {\r\n  const query = getValues();\r\n  emits(\"query\", query);\r\n};\r\nconst onReset = () => {\r\n  formRef.value.reset();\r\n  const query = getValues();\r\n  emits(\"reset\", query);\r\n};\r\n\r\nconst onRefresh = () => {\r\n  const query = getValues();\r\n  emits(\"refresh\", query);\r\n};\r\n\r\ndefineExpose({\r\n  query: onQuery,\r\n  reset: onReset,\r\n  refresh: onRefresh,\r\n  getValues,\r\n  setValues,\r\n  formRef,\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dc-search-filter {\r\n  &.with-expand {\r\n    display: inline-flex;\r\n    width: 100%;\r\n    flex-flow: row nowrap;\r\n    .dc-search-filter-form {\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  &-form {\r\n  }\r\n\r\n  &-expand {\r\n  }\r\n}\r\n</style>\r\n"], "names": ["DO_defineComponent", "ref", "computed", "DcFormComponentEnum", "h", "Action"], "mappings": ";;;;;;;;;;;;;;;;AA8Bc,MAAA,cAAAA,mBAAA,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR;;;;;;;;;;;AAAE,IAAA,CAAA;AAMF,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAOd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAA,MAAM,UAAUC,OAAI,EAAA,CAAA;AAEd,IAAA,MAAA,QAAA,GAAWA,QAAI,KAAK,CAAA,CAAA;AAEpB,IAAA,MAAA,YAAA,GAAeC,aAAS,MAAM;AAC3B,MAAA,OAAA,KAAA,CAAM,OAAO,MAAU,IAAA,EAAE,YAAY,KAAO,EAAA,YAAA,EAAc,EAAG,EAAA,CAAA;AAAA,KACrE,CAAA,CAAA;AAEK,IAAA,MAAA,kBAAA,GAAqBA,aAAS,MAAM;AAChC,MAAA,OAAA,CAAA,KAAA,CAAM,OAAO,UAAY,EAAA,QAAA,IAAY,EAAI,EAAA,GAAA,CAAI,CAAC,IAAS,KAAA;AACtD,QAAA,OAAA;AAAA,UACL,GAAG,IAAA;AAAA,UACH,OAAA,EAAS,CAAC,UAA4B,KAAA;AACpC,YAAA,IAAI,SAAS,KAAO,EAAA;AAClB,cAAA,OAAO,CAAC,YAAa,CAAA,KAAA,CAAM,YAAa,CAAA,QAAA,CAAS,KAAK,KAAK,CAAA,CAAA;AAAA,aAC7D;AACA,YAAA,OAAO,IAAK,CAAA,OAAA,GAAU,IAAK,CAAA,OAAA,CAAQ,UAAU,CAAI,GAAA,IAAA,CAAA;AAAA,WACnD;AAAA,SACF,CAAA;AAAA,OACD,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEK,IAAA,MAAA,UAAA,GAAaA,aAAS,MAAM;AAChC,MAAA,MAAM,WAAW,kBAAmB,CAAA,KAAA,CAAA;AACpC,MAAA,QAAA,CAAS,IAAK,CAAA;AAAA,QACZ,KAAO,EAAA,WAAA;AAAA,QACP,KAAO,EAAA,EAAA;AAAA,QACP,UAAY,EAAA,CAAA;AAAA,QACZ,YAAc,EAAA,EAAA;AAAA,QACd,SAAS,MAAM,IAAA;AAAA,QACf,WAAWC,yBAAoB,CAAA,MAAA;AAAA,QAC/B,MAAA,EAAQ,MAAM,MAAO,CAAA,YAAA;AAAA,QACrB,KAAO,EAAA;AAAA,UACL,cAAc,MAAM;AAClB,YAAA,OAAOC,MAAEC,iBAAQ,EAAA;AAAA,cACf,QAAQ,KAAM,CAAA,MAAA;AAAA,cACd,SAAA;AAAA,cACA,OAAA;AAAA,cACA,SAAA;AAAA,cACA,OAAA;AAAA,aACD,CAAA,CAAA;AAAA,WACH;AAAA,SACF;AAAA,OACD,CAAA,CAAA;AACG,MAAA,IAAA,KAAA,CAAM,OAAO,UAAY,EAAA;AAC3B,QAAA,QAAA,CAAS,IAAK,CAAA;AAAA,UACZ,KAAO,EAAA,WAAA;AAAA,UACP,KAAO,EAAA,EAAA;AAAA,UACP,UAAY,EAAA,CAAA;AAAA,UACZ,YAAc,EAAA,EAAA;AAAA,UACd,SAAS,MAAM,IAAA;AAAA,UACf,WAAWF,yBAAoB,CAAA,MAAA;AAAA,UAC/B,MAAA,EAAQ,MAAM,MAAO,CAAA,YAAA;AAAA,UACrB,KAAO,EAAA;AAAA,YACL,cAAc,MAAM;AAClB,cAAA,OAAO,MAAM,MAAO,CAAA,UAAA,CAAA;AAAA,aACtB;AAAA,WACF;AAAA,SACD,CAAA,CAAA;AAAA,OACH;AAEO,MAAA,OAAA,KAAA,CAAM,OAAO,UAChB,GAAA;AAAA,QACE,GAAG,MAAM,MAAO,CAAA,UAAA;AAAA,QAChB,QAAA;AAAA,OAEF,GAAA,EAAE,QAAU,EAAA,UAAA,EAAY,MAAO,EAAA,CAAA;AAAA,KACpC,CAAA,CAAA;AAEK,IAAA,MAAA,YAAA,GAAe,CAAC,GAAiB,KAAA;AACrC,MAAA,QAAA,CAAS,KAAQ,GAAA,GAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAA,MAAM,YAAY,MAAM;AAChB,MAAA,MAAA,KAAA,GAAQ,OAAQ,CAAA,KAAA,CAAM,SAAU,EAAA,CAAA;AACtC,MAAA,KAAA,CAAM,WAAe,CAAA,GAAA,KAAA,CAAA,CAAA;AACrB,MAAA,KAAA,CAAM,WAAe,CAAA,GAAA,KAAA,CAAA,CAAA;AACd,MAAA,OAAA,KAAA,CAAA;AAAA,KACT,CAAA;AAEM,IAAA,MAAA,SAAA,GAAY,CAAC,UAA4B,KAAA;AACrC,MAAA,OAAA,CAAA,KAAA,CAAM,UAAU,UAAU,CAAA,CAAA;AAAA,KACpC,CAAA;AAEA,IAAA,MAAM,UAAU,MAAM;AACpB,MAAA,MAAM,QAAQ,SAAU,EAAA,CAAA;AACxB,MAAA,KAAA,CAAM,SAAS,KAAK,CAAA,CAAA;AAAA,KACtB,CAAA;AACA,IAAA,MAAM,UAAU,MAAM;AACpB,MAAA,OAAA,CAAQ,MAAM,KAAM,EAAA,CAAA;AACpB,MAAA,MAAM,QAAQ,SAAU,EAAA,CAAA;AACxB,MAAA,KAAA,CAAM,SAAS,KAAK,CAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAA,MAAM,YAAY,MAAM;AACtB,MAAA,MAAM,QAAQ,SAAU,EAAA,CAAA;AACxB,MAAA,KAAA,CAAM,WAAW,KAAK,CAAA,CAAA;AAAA,KACxB,CAAA;AAEa,IAAA,QAAA,CAAA;AAAA,MACX,KAAO,EAAA,OAAA;AAAA,MACP,KAAO,EAAA,OAAA;AAAA,MACP,OAAS,EAAA,SAAA;AAAA,MACT,SAAA;AAAA,MACA,SAAA;AAAA,MACA,OAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}