'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var index = require('../../button/src/index.js');
var index$1 = require('../../render-vnode/src/index.js');
var Refresh = require('./Refresh.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');

const _sfc_main = vue.defineComponent({
  __name: "Action",
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    clsPrefix: {
      type: String,
      default: ""
    }
  },
  emits: ["query", "reset", "refresh"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const defaultQueryAction = {
      innerText: "\u67E5\u8BE2",
      props: {
        type: "primary"
      }
    };
    const defaultResetAction = {
      innerText: "\u91CD\u7F6E"
    };
    const defaultRefreshAction = {
      innerText: "",
      componentSlot: {
        default: () => vue.h(Refresh["default"])
      }
    };
    const queryAction = vue.computed(() => props.config.queryAction ? {
      ...defaultQueryAction,
      ...props.config.queryAction,
      props: {
        ...defaultQueryAction.props,
        ...props.config.queryAction.props || {}
      }
    } : defaultQueryAction);
    const resetAction = vue.computed(() => props.config.resetAction ? { ...defaultResetAction, ...props.config.resetAction } : defaultResetAction);
    const refreshAction = vue.computed(() => props.config.refreshAction ? { ...defaultRefreshAction, ...props.config.refreshAction } : defaultRefreshAction);
    const onQuery = () => {
      emits("query");
    };
    const onReset = () => {
      emits("reset");
    };
    const onRefresh = () => {
      emits("refresh");
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(`${props.clsPrefix}-operate`)
      }, [
        queryAction.value.visible !== false ? (vue.openBlock(), vue.createBlock(index["default"], vue.mergeProps({ key: 0 }, { ...queryAction.value.props }, {
          config: queryAction.value.config,
          onClick: onQuery
        }), vue.createSlots({
          default: vue.withCtx(() => [
            vue.createTextVNode(" " + vue.toDisplayString(queryAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          vue.renderList(Object.keys(queryAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: vue.withCtx((scope) => [
                vue.createVNode(vue.unref(index$1["default"]), {
                  vnode: (queryAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : vue.createCommentVNode("v-if", true),
        resetAction.value.visible !== false ? (vue.openBlock(), vue.createBlock(index["default"], vue.mergeProps({ key: 1 }, { ...resetAction.value.props }, {
          config: resetAction.value.config,
          onClick: onReset
        }), vue.createSlots({
          default: vue.withCtx(() => [
            vue.createTextVNode(" " + vue.toDisplayString(resetAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          vue.renderList(Object.keys(resetAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: vue.withCtx((scope) => [
                vue.createVNode(vue.unref(index$1["default"]), {
                  vnode: (resetAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : vue.createCommentVNode("v-if", true),
        refreshAction.value.visible !== false ? (vue.openBlock(), vue.createBlock(index["default"], vue.mergeProps({ key: 2 }, { ...refreshAction.value.props }, {
          config: refreshAction.value.config,
          onClick: onRefresh
        }), vue.createSlots({
          default: vue.withCtx(() => [
            vue.createTextVNode(" " + vue.toDisplayString(refreshAction.value.innerText), 1)
          ]),
          _: 2
        }, [
          vue.renderList(Object.keys(refreshAction.value.componentSlot || {}), (slotI) => {
            return {
              name: slotI,
              fn: vue.withCtx((scope) => [
                vue.createVNode(vue.unref(index$1["default"]), {
                  vnode: (refreshAction.value.componentSlot || {})[slotI],
                  scope
                }, null, 8, ["vnode", "scope"])
              ])
            };
          })
        ]), 1040, ["config"])) : vue.createCommentVNode("v-if", true)
      ], 2);
    };
  }
});
var Action = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "D:\\DataCyber\\dc-components\\packages\\components\\search-filter\\src\\Action.vue"]]);

exports["default"] = Action;
//# sourceMappingURL=Action.js.map
