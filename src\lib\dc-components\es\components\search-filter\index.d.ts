export declare const DcSearchFilter: import("dc-components/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    config: {
        type: import("vue").PropType<import("./src/types").DcSearchFilterConfig>;
        default: () => {};
    };
}, {
    clsPrefix: string;
    actionModel: string;
    extendModel: string;
    props: import("@vue/shared").LooseRequired<any>;
    emits: (event: "reset" | "query" | "refresh", ...args: any[]) => void;
    formRef: import("vue").Ref<any>;
    isExpand: import("vue").Ref<boolean>;
    expandConfig: import("vue").ComputedRef<any>;
    formConfigChildren: import("vue").ComputedRef<any>;
    formConfig: import("vue").ComputedRef<any>;
    toggleExpand: (val: boolean) => void;
    getValues: () => any;
    setValues: (modelValue: import("..").DcFormModel) => void;
    onQuery: () => void;
    onReset: () => void;
    onRefresh: () => void;
    DcForm: import("vue").DefineComponent<{
        config: {
            type: import("vue").PropType<import("..").DcFormConfig>;
            default: () => {
                children: never[];
            };
        };
    }, {
        props: import("@vue/shared").LooseRequired<any>;
        emits: (event: "validate", ...args: any[]) => void;
        formRef: import("vue").Ref<any>;
        model: import("vue").Ref<import("..").DcFormModel>;
        visibleControl: import("vue").Ref<import("..").DcFormVisibleControl>;
        inline: import("vue").ComputedRef<any>;
        handleValidate: (prop: import("element-plus").FormItemProp, isValid: boolean, message: string) => void;
        validate: (callback?: import("element-plus").FormValidateCallback | undefined) => any;
        validateField: (props?: import("element-plus").FormItemProp | import("element-plus").FormItemProp[] | undefined, callback?: import("element-plus").FormValidateCallback | undefined) => any;
        resetFields: (props?: import("element-plus").FormItemProp | import("element-plus").FormItemProp[] | undefined) => void;
        scrollToField: (prop: import("element-plus").FormItemProp) => void;
        clearValidate: (props?: import("element-plus").FormItemProp | import("element-plus").FormItemProp[] | undefined) => void;
        initModel: (initVal: import("..").DcFormModel) => void;
        getValues: () => {
            [x: string]: any;
        };
        setFieldsValue: (fn: (val: import("..").DcFormModel) => import("..").DcFormModel) => void;
        reset: (clearValidate?: boolean) => void;
        itemChange: (effect?: import("..").DcFormItemEffect | undefined, rowIndex?: number | undefined) => void;
        asyncValidate: (errorInfo: any) => Promise<unknown>;
        readonly ElForm: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: import("vue").PropType<Partial<Record<string, import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }, {
            COMPONENT_NAME: string;
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: import("vue").PropType<Partial<Record<string, import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
                readonly requireAsteriskPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
                readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly labelSuffix: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly validateOnRuleChange: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: import("element-plus").FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }>>;
            emit: (event: "validate", prop: import("element-plus").FormItemProp, isValid: boolean, message: string) => void;
            fields: import("element-plus").FormItemContext[];
            formSize: import("vue").ComputedRef<"" | "default" | "small" | "large">;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string | undefined) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            formClasses: import("vue").ComputedRef<(string | {
                [x: string]: boolean | import("element-plus/es/utils").EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            })[]>;
            addField: (field: import("element-plus").FormItemContext) => void;
            removeField: (field: import("element-plus").FormItemContext) => void;
            resetFields: (props?: import("element-plus/es/utils").Arrayable<import("element-plus").FormItemProp> | undefined) => void;
            clearValidate: (props?: import("element-plus/es/utils").Arrayable<import("element-plus").FormItemProp> | undefined) => void;
            isValidatable: import("vue").ComputedRef<boolean>;
            obtainValidateFields: (props: import("element-plus/es/utils").Arrayable<import("element-plus").FormItemProp>) => import("element-plus").FormItemContext[];
            validate: (callback?: import("element-plus").FormValidateCallback | undefined) => import("element-plus").FormValidationResult;
            doValidateField: (props?: import("element-plus/es/utils").Arrayable<import("element-plus").FormItemProp> | undefined) => Promise<boolean>;
            validateField: (props?: import("element-plus/es/utils").Arrayable<import("element-plus").FormItemProp> | undefined, callback?: import("element-plus").FormValidateCallback | undefined) => import("element-plus").FormValidationResult;
            scrollToField: (prop: import("element-plus").FormItemProp) => void;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
            validate: (prop: import("element-plus").FormItemProp, isValid: boolean, message: string) => boolean;
        }, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: import("vue").PropType<Partial<Record<string, import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: import("element-plus/es/utils").EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: import("element-plus").FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            readonly disabled: boolean;
            readonly labelPosition: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            readonly requireAsteriskPosition: import("element-plus/es/utils").EpPropMergeType<StringConstructor, "right" | "left", unknown>;
            readonly labelWidth: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
            readonly labelSuffix: string;
            readonly showMessage: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly validateOnRuleChange: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly inline: boolean;
            readonly inlineMessage: boolean;
            readonly statusIcon: boolean;
            readonly hideRequiredAsterisk: boolean;
            readonly scrollToError: boolean;
        }, {}>> & {
            FormItem: import("vue").DefineComponent<{
                readonly label: StringConstructor;
                readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly prop: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
                readonly rules: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly error: StringConstructor;
                readonly validateStatus: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly for: StringConstructor;
                readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
                readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }, {
                props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                    readonly label: StringConstructor;
                    readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                    readonly prop: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
                    readonly rules: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly error: StringConstructor;
                    readonly validateStatus: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly for: StringConstructor;
                    readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
                    readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                    readonly size: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                }>> & {
                    [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
                }>>;
                slots: Readonly<{
                    [name: string]: import("vue").Slot<any> | undefined;
                }>;
                formContext: import("element-plus").FormContext | undefined;
                parentFormItemContext: import("element-plus").FormItemContext | undefined;
                _size: import("vue").ComputedRef<"" | "default" | "small" | "large">;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string | undefined) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                labelId: string;
                inputIds: import("vue").Ref<string[]>;
                validateState: import("vue").Ref<"" | "success" | "error" | "validating">;
                validateStateDebounced: Readonly<import("vue").Ref<"" | "success" | "error" | "validating">>;
                validateMessage: import("vue").Ref<string>;
                formItemRef: import("vue").Ref<HTMLDivElement | undefined>;
                initialValue: any;
                isResettingField: boolean;
                labelStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
                contentStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
                formItemClasses: import("vue").ComputedRef<(string | {
                    [x: string]: boolean | undefined;
                })[]>;
                _inlineMessage: import("vue").ComputedRef<boolean>;
                validateClasses: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                propString: import("vue").ComputedRef<string>;
                hasLabel: import("vue").ComputedRef<boolean>;
                labelFor: import("vue").ComputedRef<string | undefined>;
                isGroup: import("vue").ComputedRef<boolean>;
                isNested: boolean;
                fieldValue: import("vue").ComputedRef<any>;
                normalizedRules: import("vue").ComputedRef<import("element-plus").FormItemRule[]>;
                validateEnabled: import("vue").ComputedRef<boolean>;
                getFilteredRule: (trigger: string) => import("async-validator").RuleItem[];
                isRequired: import("vue").ComputedRef<boolean>;
                shouldShowError: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
                currentLabel: import("vue").ComputedRef<string>;
                setValidationState: (state: "" | "success" | "error" | "validating") => void;
                onValidationFailed: (error: import("element-plus").FormValidateFailure) => void;
                onValidationSucceeded: () => void;
                doValidate: (rules: import("async-validator").RuleItem[]) => Promise<true>;
                validate: (trigger: string, callback?: import("element-plus").FormValidateCallback | undefined) => import("element-plus").FormValidationResult;
                clearValidate: () => void;
                resetField: () => void;
                addInputId: (id: string) => void;
                removeInputId: (id: string) => void;
                context: import("element-plus").FormItemContext;
                FormLabelWrap: import("vue").DefineComponent<{
                    isAutoWidth: BooleanConstructor;
                    updateAll: BooleanConstructor;
                }, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                    isAutoWidth: BooleanConstructor;
                    updateAll: BooleanConstructor;
                }>>, {
                    isAutoWidth: boolean;
                    updateAll: boolean;
                }, {}>;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly label: StringConstructor;
                readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly prop: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
                readonly rules: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly error: StringConstructor;
                readonly validateStatus: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly for: StringConstructor;
                readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
                readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>>, {
                readonly required: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
                readonly labelWidth: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
                readonly showMessage: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
                readonly inlineMessage: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, BooleanConstructor], unknown, unknown>;
            }, {}>;
        };
        DcFormItem: import("vue").DefineComponent<{
            visible: {
                type: import("vue").PropType<boolean | import("vue").Ref<boolean>>;
                default: boolean;
            };
            config: {
                type: import("vue").PropType<import("..").DcFormItem>;
                default: () => {};
            };
            modelValue: {
                type: import("vue").PropType<any>;
            };
            margin: {
                type: import("vue").PropType<import("..").DcFormItemMargin>;
                default: () => {};
            };
            isInline: {
                type: BooleanConstructor;
                default: boolean;
            };
            rowIndex: {
                type: NumberConstructor;
                default: number;
            };
        }, {
            props: import("@vue/shared").LooseRequired<any>;
            emits: (event: "update:modelValue" | "triggerEffect", ...args: any[]) => void;
            formItemRef: import("vue").Ref<any>;
            marginBottom: import("vue").ComputedRef<any>;
            marginRight: import("vue").ComputedRef<any>;
            marginLeft: import("vue").ComputedRef<any>;
            marginTop: import("vue").ComputedRef<any>;
            ruleTrigger: import("vue").ComputedRef<any>;
            handleBlur: () => void;
            triggerEffect: (effect: import("..").DcFormItemEffect, rowIndex?: number | undefined) => void;
            itemChange: (val: any) => void;
            readonly ElFormItem: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
                readonly label: StringConstructor;
                readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly prop: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
                readonly rules: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly error: StringConstructor;
                readonly validateStatus: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly for: StringConstructor;
                readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
                readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }, {
                props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                    readonly label: StringConstructor;
                    readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                    readonly prop: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
                    readonly rules: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly error: StringConstructor;
                    readonly validateStatus: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly for: StringConstructor;
                    readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
                    readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                    readonly size: {
                        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                }>> & {
                    [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
                }>>;
                slots: Readonly<{
                    [name: string]: import("vue").Slot<any> | undefined;
                }>;
                formContext: import("element-plus").FormContext | undefined;
                parentFormItemContext: import("element-plus").FormItemContext | undefined;
                _size: import("vue").ComputedRef<"" | "default" | "small" | "large">;
                ns: {
                    namespace: import("vue").ComputedRef<string>;
                    b: (blockSuffix?: string | undefined) => string;
                    e: (element?: string | undefined) => string;
                    m: (modifier?: string | undefined) => string;
                    be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                    em: (element?: string | undefined, modifier?: string | undefined) => string;
                    bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                    bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                    is: {
                        (name: string, state: boolean | undefined): string;
                        (name: string): string;
                    };
                    cssVar: (object: Record<string, string>) => Record<string, string>;
                    cssVarName: (name: string) => string;
                    cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                    cssVarBlockName: (name: string) => string;
                };
                labelId: string;
                inputIds: import("vue").Ref<string[]>;
                validateState: import("vue").Ref<"" | "success" | "error" | "validating">;
                validateStateDebounced: Readonly<import("vue").Ref<"" | "success" | "error" | "validating">>;
                validateMessage: import("vue").Ref<string>;
                formItemRef: import("vue").Ref<HTMLDivElement | undefined>;
                initialValue: any;
                isResettingField: boolean;
                labelStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
                contentStyle: import("vue").ComputedRef<import("vue").CSSProperties>;
                formItemClasses: import("vue").ComputedRef<(string | {
                    [x: string]: boolean | undefined;
                })[]>;
                _inlineMessage: import("vue").ComputedRef<boolean>;
                validateClasses: import("vue").ComputedRef<(string | {
                    [x: string]: boolean;
                })[]>;
                propString: import("vue").ComputedRef<string>;
                hasLabel: import("vue").ComputedRef<boolean>;
                labelFor: import("vue").ComputedRef<string | undefined>;
                isGroup: import("vue").ComputedRef<boolean>;
                isNested: boolean;
                fieldValue: import("vue").ComputedRef<any>;
                normalizedRules: import("vue").ComputedRef<import("element-plus").FormItemRule[]>;
                validateEnabled: import("vue").ComputedRef<boolean>;
                getFilteredRule: (trigger: string) => import("async-validator").RuleItem[];
                isRequired: import("vue").ComputedRef<boolean>;
                shouldShowError: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>>;
                currentLabel: import("vue").ComputedRef<string>;
                setValidationState: (state: "" | "success" | "error" | "validating") => void;
                onValidationFailed: (error: import("element-plus").FormValidateFailure) => void;
                onValidationSucceeded: () => void;
                doValidate: (rules: import("async-validator").RuleItem[]) => Promise<true>;
                validate: (trigger: string, callback?: import("element-plus").FormValidateCallback | undefined) => import("element-plus").FormValidationResult;
                clearValidate: () => void;
                resetField: () => void;
                addInputId: (id: string) => void;
                removeInputId: (id: string) => void;
                context: import("element-plus").FormItemContext;
                FormLabelWrap: import("vue").DefineComponent<{
                    isAutoWidth: BooleanConstructor;
                    updateAll: BooleanConstructor;
                }, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                    isAutoWidth: BooleanConstructor;
                    updateAll: BooleanConstructor;
                }>>, {
                    isAutoWidth: boolean;
                    updateAll: boolean;
                }, {}>;
            }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                readonly label: StringConstructor;
                readonly labelWidth: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly prop: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp) | ((new (...args: any[]) => import("element-plus").FormItemProp & {}) | (() => import("element-plus").FormItemProp))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly required: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, undefined, boolean>;
                readonly rules: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>) | ((new (...args: any[]) => import("element-plus").FormItemRule | import("element-plus").FormItemRule[]) | (() => import("element-plus/es/utils").Arrayable<import("element-plus").FormItemRule>))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly error: StringConstructor;
                readonly validateStatus: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "success" | "error" | "validating", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly for: StringConstructor;
                readonly inlineMessage: import("element-plus/es/utils").EpPropFinalized<readonly [StringConstructor, BooleanConstructor], unknown, unknown, "", boolean>;
                readonly showMessage: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>>, {
                readonly required: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
                readonly labelWidth: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
                readonly showMessage: import("element-plus/es/utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
                readonly inlineMessage: import("element-plus/es/utils").EpPropMergeType<readonly [StringConstructor, BooleanConstructor], unknown, unknown>;
            }, {}>>;
            readonly DcRenderVNode: import("vue").DefineComponent<{
                vnode: {
                    type: import("vue").PropType<import("../render-vnode").RenderVNodeFn>;
                    default: () => null;
                };
                scope: {
                    type: import("vue").PropType<Record<string, any>>;
                    default: () => {};
                };
                extraScope: {
                    type: import("vue").PropType<Record<string, any>>;
                    default: () => {};
                };
            }, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
                vnode: {
                    type: import("vue").PropType<import("../render-vnode").RenderVNodeFn>;
                    default: () => null;
                };
                scope: {
                    type: import("vue").PropType<Record<string, any>>;
                    default: () => {};
                };
                extraScope: {
                    type: import("vue").PropType<Record<string, any>>;
                    default: () => {};
                };
            }>>, {
                vnode: import("../render-vnode").RenderVNodeFn;
                scope: Record<string, any>;
                extraScope: Record<string, any>;
            }, {}>;
            readonly Component: any;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("update:modelValue" | "triggerEffect")[], "update:modelValue" | "triggerEffect", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            visible: {
                type: import("vue").PropType<boolean | import("vue").Ref<boolean>>;
                default: boolean;
            };
            config: {
                type: import("vue").PropType<import("..").DcFormItem>;
                default: () => {};
            };
            modelValue: {
                type: import("vue").PropType<any>;
            };
            margin: {
                type: import("vue").PropType<import("..").DcFormItemMargin>;
                default: () => {};
            };
            isInline: {
                type: BooleanConstructor;
                default: boolean;
            };
            rowIndex: {
                type: NumberConstructor;
                default: number;
            };
        }>> & {
            "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
            onTriggerEffect?: ((...args: any[]) => any) | undefined;
        }, {
            visible: boolean | import("vue").Ref<boolean>;
            config: import("..").DcFormItem;
            margin: import("..").DcFormItemMargin;
            isInline: boolean;
            rowIndex: number;
        }, {}>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "validate"[], "validate", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        config: {
            type: import("vue").PropType<import("..").DcFormConfig>;
            default: () => {
                children: never[];
            };
        };
    }>> & {
        onValidate?: ((...args: any[]) => any) | undefined;
    }, {
        config: import("..").DcFormConfig;
    }, {}>;
    Expand: import("vue").DefineComponent<{
        expandText: {
            type: StringConstructor;
            default: string;
        };
        collapseText: {
            type: StringConstructor;
            default: string;
        };
    }, {
        clsPrefix: string;
        props: import("@vue/shared").LooseRequired<any>;
        emits: (event: "toggleExpand", ...args: any[]) => void;
        isExpand: import("vue").Ref<boolean>;
        toggle: () => void;
        readonly ElIcon: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string | undefined) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            style: import("vue").ComputedRef<import("vue").CSSProperties>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}, {}>> & Record<string, any>;
        readonly ArrowDown: import("vue").DefineComponent<{}, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}>;
        readonly ArrowUp: import("vue").DefineComponent<{}, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "toggleExpand"[], "toggleExpand", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        expandText: {
            type: StringConstructor;
            default: string;
        };
        collapseText: {
            type: StringConstructor;
            default: string;
        };
    }>> & {
        onToggleExpand?: ((...args: any[]) => any) | undefined;
    }, {
        expandText: string;
        collapseText: string;
    }, {}>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("reset" | "query" | "refresh")[], "reset" | "query" | "refresh", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    config: {
        type: import("vue").PropType<import("./src/types").DcSearchFilterConfig>;
        default: () => {};
    };
}>> & {
    onReset?: ((...args: any[]) => any) | undefined;
    onQuery?: ((...args: any[]) => any) | undefined;
    onRefresh?: ((...args: any[]) => any) | undefined;
}, {
    config: import("./src/types").DcSearchFilterConfig;
}, {}>> & Record<string, any>;
export default DcSearchFilter;
export * from "./src/types";
