{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/login/src/constants.ts"], "sourcesContent": ["export default {\n  \"zh-CN\": {\n    CyberEngine: {\n      desc: \"一站式云原生大数据平台\",\n    },\n    CyberData: {\n      desc: \"一站式多云大数据研发治理平台\",\n    },\n    CyberAI: {\n      desc: \"一站式机器学习生态服务平台\",\n    },\n    welcome: \"欢迎回来\",\n    usernameP: \"请输入用户名\",\n    passwordP: \"请输入密码\",\n    codeP: \"请输入验证码\",\n    usernameR: \"请输入用户名\",\n    passwordR: \"请输入密码\",\n    codeR: \"请输入验证码\",\n    login: \"登录\",\n    dialogTitle: \"请选择您要进入的平台\",\n    recentlyUsed: \"最近使用\",\n    register: \"注册\",\n    registerText: \"没有账号？立即注册\",\n    loginText: \"已有账号，立即登录\",\n    phoneP: \"请输入手机号\",\n    phoneR: \"请输入合法的电话号码\",\n    emailP: \"请输入邮箱地址\",\n    emailR: \"请输入合法的邮箱地址\",\n    registerSu: \"注册成功，即将跳转登录\",\n    usernameRule: \"长度6~16个字符，仅支持包含字母、数字、-或_\",\n  },\n  \"en-US\": {\n    CyberEngine: {\n      desc: \"One-stop cloud-native big data platform\",\n    },\n    CyberData: {\n      desc: \"One-stop multi-cloud big data R&D management platform\",\n    },\n    CyberAI: {\n      desc: \"One-stop machine learning ecological service platform\",\n    },\n    welcome: \"Welcome back\",\n    usernameP: \"User name\",\n    passwordP: \"Password\",\n    codeP: \"Verification code\",\n    usernameR: \"Please enter user name\",\n    passwordR: \"Please enter password\",\n    codeR: \"Please enter the verification code\",\n    login: \"Log in\",\n    dialogTitle: \"Please select the platform you want to enter\",\n    recentlyUsed: \"Recently Used\",\n    register: \"Register\",\n    registerText: \"Don't have an account? Register Now\",\n    loginText: \"Already have an account, log in now\",\n    phoneP: \"Please enter your phone number\",\n    phoneR: \"Please enter a valid phone number\",\n    emailP: \"Please enter your email address\",\n    emailR: \"Please enter a valid email address\",\n    registerSu: \"Registration successful, will be redirected to login soon\",\n    usernameRule: \"6~16 characters, only letters, numbers, - and _ are allowed\",\n  },\n  \"ja-JP\": {\n    CyberEngine: {\n      desc: \"ワンストップクラウドネイティブビッグデータプラットフォーム\",\n    },\n    CyberData: {\n      desc: \"ワンストップ型マルチクラウドビッグデータ研究開発管理プラットフォーム\",\n    },\n    CyberAI: {\n      desc: \"ワンストップ型マルチクラウドビッグデータ研究開発管理プラットフォーム\",\n    },\n    welcome: \"お帰りなさい\",\n    usernameP: \"ユーザー名\",\n    passwordP: \"パスワード\",\n    codeP: \"認証コード\",\n    usernameR: \"ユーザー名を入力してください\",\n    passwordR: \"パスワードを入力してください\",\n    codeR: \"認証コードを入力してください\",\n    login: \"ログイン\",\n    dialogTitle: \"アクセスするプラットフォームを選択してください\",\n    recentlyUsed: \"最近使用した\",\n    register: \"登録\",\n    registerText: \"アカウントはありませんか？今すぐ登録\",\n    loginText: \"既存のアカウント、すぐにログイン\",\n    phoneP: \"携帯番号を入力してください\",\n    phoneR: \"合法的な電話番号を入力してください\",\n    emailP: \"メールアドレスを入力してください\",\n    emailR: \"適切なメールアドレスを入力してください\",\n    registerSu: \"登録に成功しました。ジャンプログインしようとしています\",\n    usernameRule: \"文字、数字、-または_\",\n  },\n};\n"], "names": [], "mappings": ";;;;AAAA,aAAe;AAAA,EACb,OAAS,EAAA;AAAA,IACP,WAAa,EAAA;AAAA,MACX,IAAM,EAAA,oEAAA;AAAA,KACR;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,sFAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA;AAAA,MACP,IAAM,EAAA,gFAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA,0BAAA;AAAA,IACT,SAAW,EAAA,sCAAA;AAAA,IACX,SAAW,EAAA,gCAAA;AAAA,IACX,KAAO,EAAA,sCAAA;AAAA,IACP,SAAW,EAAA,sCAAA;AAAA,IACX,SAAW,EAAA,gCAAA;AAAA,IACX,KAAO,EAAA,sCAAA;AAAA,IACP,KAAO,EAAA,cAAA;AAAA,IACP,WAAa,EAAA,8DAAA;AAAA,IACb,YAAc,EAAA,0BAAA;AAAA,IACd,QAAU,EAAA,cAAA;AAAA,IACV,YAAc,EAAA,wDAAA;AAAA,IACd,SAAW,EAAA,wDAAA;AAAA,IACX,MAAQ,EAAA,sCAAA;AAAA,IACR,MAAQ,EAAA,8DAAA;AAAA,IACR,MAAQ,EAAA,4CAAA;AAAA,IACR,MAAQ,EAAA,8DAAA;AAAA,IACR,UAAY,EAAA,oEAAA;AAAA,IACZ,YAAc,EAAA,oHAAA;AAAA,GAChB;AAAA,EACA,OAAS,EAAA;AAAA,IACP,WAAa,EAAA;AAAA,MACX,IAAM,EAAA,yCAAA;AAAA,KACR;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,uDAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA;AAAA,MACP,IAAM,EAAA,uDAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA,cAAA;AAAA,IACT,SAAW,EAAA,WAAA;AAAA,IACX,SAAW,EAAA,UAAA;AAAA,IACX,KAAO,EAAA,mBAAA;AAAA,IACP,SAAW,EAAA,wBAAA;AAAA,IACX,SAAW,EAAA,uBAAA;AAAA,IACX,KAAO,EAAA,oCAAA;AAAA,IACP,KAAO,EAAA,QAAA;AAAA,IACP,WAAa,EAAA,8CAAA;AAAA,IACb,YAAc,EAAA,eAAA;AAAA,IACd,QAAU,EAAA,UAAA;AAAA,IACV,YAAc,EAAA,qCAAA;AAAA,IACd,SAAW,EAAA,qCAAA;AAAA,IACX,MAAQ,EAAA,gCAAA;AAAA,IACR,MAAQ,EAAA,mCAAA;AAAA,IACR,MAAQ,EAAA,iCAAA;AAAA,IACR,MAAQ,EAAA,oCAAA;AAAA,IACR,UAAY,EAAA,2DAAA;AAAA,IACZ,YAAc,EAAA,6DAAA;AAAA,GAChB;AAAA,EACA,OAAS,EAAA;AAAA,IACP,WAAa,EAAA;AAAA,MACX,IAAM,EAAA,gLAAA;AAAA,KACR;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,8MAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA;AAAA,MACP,IAAM,EAAA,8MAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA,sCAAA;AAAA,IACT,SAAW,EAAA,gCAAA;AAAA,IACX,SAAW,EAAA,gCAAA;AAAA,IACX,KAAO,EAAA,gCAAA;AAAA,IACP,SAAW,EAAA,sFAAA;AAAA,IACX,SAAW,EAAA,sFAAA;AAAA,IACX,KAAO,EAAA,sFAAA;AAAA,IACP,KAAO,EAAA,0BAAA;AAAA,IACP,WAAa,EAAA,4IAAA;AAAA,IACb,YAAc,EAAA,sCAAA;AAAA,IACd,QAAU,EAAA,cAAA;AAAA,IACV,YAAc,EAAA,8GAAA;AAAA,IACd,SAAW,EAAA,kGAAA;AAAA,IACX,MAAQ,EAAA,gFAAA;AAAA,IACR,MAAQ,EAAA,wGAAA;AAAA,IACR,MAAQ,EAAA,kGAAA;AAAA,IACR,MAAQ,EAAA,oHAAA;AAAA,IACR,UAAY,EAAA,oKAAA;AAAA,IACZ,YAAc,EAAA,0DAAA;AAAA,GAChB;AACF,CAAA;;;;"}