{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../../packages/components/form/src/components/Custom/index.tsx"], "sourcesContent": ["import { defineComponent } from \"vue\";\r\nimport type { PropType } from \"vue\";\r\nimport type { DcFormCustomRenderFn } from \"../../types\";\r\nimport \"./index.scss\";\r\n\r\nexport default defineComponent({\r\n  name: \"DcFormCustom\",\r\n  props: {\r\n    modelValue: {},\r\n    renderCustom: {\r\n      type: Function as PropType<DcFormCustomRenderFn>,\r\n    },\r\n    rowIndex: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n  },\r\n  emits: {\r\n    \"update:modelValue\": (val: any) => true,\r\n  },\r\n  setup(props, ctx) {\r\n    const onChange = (val: any) => {\r\n      ctx.emit(\"update:modelValue\", val);\r\n    };\r\n\r\n    return {\r\n      onChange,\r\n    };\r\n  },\r\n  render() {\r\n    const vNode = this.$props.renderCustom\r\n      ? this.$props.renderCustom({\r\n          value: this.$props.modelValue,\r\n          onChange: this.onChange,\r\n          rowIndex: this.$props.rowIndex,\r\n        })\r\n      : null;\r\n    return vNode;\r\n  },\r\n});\r\n"], "names": ["defineComponent", "name", "props", "modelValue", "renderCustom", "type", "Function", "rowIndex", "Number", "default", "emits", "val", "setup", "ctx", "onChange", "emit", "render", "vNode", "$props", "value"], "mappings": ";;;AAKA,mBAAeA,eAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,cAAc;AACpBC,EAAAA,KAAK,EAAE;IACLC,UAAU,EAAE,EAAE;AACdC,IAAAA,YAAY,EAAE;AACZC,MAAAA,IAAI,EAAEC,QAAAA;KACP;AACDC,IAAAA,QAAQ,EAAE;AACRF,MAAAA,IAAI,EAAEG,MAAM;AACZC,MAAAA,OAAO,EAAE,CAAA;AACX,KAAA;GACD;AACDC,EAAAA,KAAK,EAAE;IACL,mBAAmB,EAAGC,IAAa,KAAA,IAAA;GACpC;AACDC,EAAAA,KAAKA,CAACV,KAAK,EAAEW,GAAG,EAAE;IAChB,MAAMC,QAAQ,GAAIH,CAAAA,GAAa,KAAA;AAC7BE,MAAAA,GAAG,CAACE,IAAI,CAAC,mBAAmB,EAAEJ,GAAG,CAAC,CAAA;KACnC,CAAA;IAED,OAAO;AACLG,MAAAA,QAAAA;KACD,CAAA;GACF;AACDE,EAAAA,MAAMA,GAAG;AACP,IAAA,MAAMC,KAAK,GAAG,IAAI,CAACC,MAAM,CAACd,YAAY,GAClC,IAAI,CAACc,MAAM,CAACd,YAAY,CAAC;AACvBe,MAAAA,KAAK,EAAE,IAAI,CAACD,MAAM,CAACf,UAAU;MAC7BW,QAAQ,EAAE,IAAI,CAACA,QAAQ;AACvBP,MAAAA,QAAQ,EAAE,IAAI,CAACW,MAAM,CAACX,QAAAA;KACvB,CAAC,GACF,IAAI,CAAA;AACR,IAAA,OAAOU,KAAK,CAAA;AACd,GAAA;AACF,CAAC,CAAC;;;;"}