import service from '@/utils/Http'
/**
 * @description Region列表
 * @param {HBaseApi.GetRegionList} data
 * @return {*}
 */

export const getRegionList = (data: HBaseApi.GetRegionList) => {
  return service.post<ApiResponse<any>>('/hbase/region/list', data)
}

/**
 * @description Region迁移选择下拉列表
 * @param {HBaseApi.ChoiceList} data
 * @return {*}
 */

export const choiceList = (data: HBaseApi.ChoiceList) => {
  return service.post<ApiResponse<any>>('/hbase/region/choice/list', data)
}

/**
 * @description Region迁移
 * @param {HBaseApi.RegionMove} data
 * @return {*}
 */

export const regionMove = (data: HBaseApi.RegionMove) => {
  return service.post<ApiResponse<any>>('/hbase/region/move', data)
}

/**
 * @description Hbase RegionServer列表
 * @param {HBaseApi.RegionServerList} data
 * @return {*}
 */

export const regionServerList = (data: HBaseApi.RegionServerList) => {
  return service.post<ApiResponse<any>>('/hbase/region/server/list', data)
}
