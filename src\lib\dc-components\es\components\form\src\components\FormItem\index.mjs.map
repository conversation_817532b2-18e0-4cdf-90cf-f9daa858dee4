{"version": 3, "file": "index.mjs", "sources": ["../../../../../../../../packages/components/form/src/components/FormItem/index.vue"], "sourcesContent": ["<template>\r\n  <el-form-item\r\n    v-show=\"props.visible\"\r\n    ref=\"formItemRef\"\r\n    :prop=\"props.config.model\"\r\n    :label=\"props.config.label\"\r\n    :label-width=\"props.config.labelWidth\"\r\n    :rules=\"props.visible ? props.config.rules : []\"\r\n    :inline-message=\"props.config.inlineMessage\"\r\n    :class=\"`dc-form-item ${props.config.class || ''}`\"\r\n    :style=\"{\r\n      marginRight,\r\n      marginBottom,\r\n      marginTop,\r\n      marginLeft,\r\n    }\"\r\n  >\r\n    <template v-if=\"props.config.labelSlot\" #label=\"scope\">\r\n      <DcRenderVNode :vnode=\"props.config.labelSlot\" :scope=\"scope\" />\r\n    </template>\r\n\r\n    <component\r\n      v-bind=\"{ ...(props.config.props || {}) }\"\r\n      :is=\"Component[props.config.component]\"\r\n      :model-value=\"props.modelValue\"\r\n      :file-list=\"props.modelValue\"\r\n      :row-index=\"props.rowIndex\"\r\n      :disabled=\"props.config.disabled\"\r\n      @update:modelValue=\"itemChange\"\r\n      @blur=\"handleBlur\"\r\n      @triggerEffect=\"triggerEffect\"\r\n    >\r\n      <template\r\n        v-for=\"slotI in Object.keys(props.config.componentSlot || {})\"\r\n        #[slotI]=\"scope\"\r\n        :key=\"slotI\"\r\n      >\r\n        <DcRenderVNode\r\n          :vnode=\"(props.config.componentSlot || {})[slotI]\"\r\n          :scope=\"scope\"\r\n        />\r\n      </template>\r\n    </component>\r\n    <template v-if=\"props.config.rightSlot\">\r\n      <DcRenderVNode :vnode=\"props.config.rightSlot\" />\r\n    </template>\r\n  </el-form-item>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, ref, watch } from \"vue\";\r\nimport { ElFormItem } from \"element-plus\";\r\n\r\nimport DcRenderVNode from \"../../../../render-vnode/src/index\";\r\n\r\nimport { Component } from \"./constants\";\r\nimport type { PropType, Ref } from \"vue\";\r\nimport type {\r\n  DcFormItem,\r\n  DcFormItemEffect,\r\n  DcFormItemMargin,\r\n} from \"../../types\";\r\n\r\ndefineOptions({\r\n  name: \"DcFormItem\",\r\n});\r\n\r\nconst props = defineProps({\r\n  visible: {\r\n    type: Boolean as PropType<boolean | Ref<boolean>>,\r\n    default: true,\r\n  },\r\n  config: {\r\n    type: Object as PropType<DcFormItem>,\r\n    default: () => ({}),\r\n  },\r\n  modelValue: {\r\n    type: [String, Number, Boolean, Array, Object] as PropType<any>,\r\n  },\r\n  margin: {\r\n    type: Object as PropType<DcFormItemMargin>,\r\n    default: () => ({}),\r\n  },\r\n  isInline: {\r\n    type: Boolean,\r\n    default: false,\r\n  },\r\n  rowIndex: {\r\n    type: Number,\r\n    default: 0,\r\n  },\r\n});\r\n\r\nconst emits = defineEmits([\"update:modelValue\", \"triggerEffect\"]);\r\n\r\nconst formItemRef = ref();\r\n\r\nconst marginBottom = computed(() =>\r\n  props.margin.bottom ? props.margin.bottom : \"18px\"\r\n);\r\n\r\nconst marginRight = computed(() =>\r\n  props.margin.right ? props.margin.right : props.isInline ? \"32px\" : \"0px\"\r\n);\r\n\r\nconst marginLeft = computed(() =>\r\n  props.margin.left ? props.margin.left : \"unset\"\r\n);\r\n\r\nconst marginTop = computed(() =>\r\n  props.margin.top ? props.margin.top : \"unset\"\r\n);\r\n\r\nconst ruleTrigger = computed(() => {\r\n  return (props.config.rules || []).map((item) => item.trigger);\r\n});\r\nconst handleBlur = () => {\r\n  if (ruleTrigger.value.includes(\"blur\")) {\r\n    formItemRef.value?.validate(\"blur\", () => {});\r\n  }\r\n};\r\n\r\nconst triggerEffect = (effect: DcFormItemEffect, rowIndex?: number) => {\r\n  emits(\"triggerEffect\", effect, rowIndex);\r\n};\r\n\r\nconst itemChange = (val: any) => {\r\n  emits(\"update:modelValue\", val, props.config);\r\n  if (props.config.effect && typeof props.config.effect === \"function\") {\r\n    triggerEffect(props.config.effect, props.rowIndex);\r\n  }\r\n};\r\n\r\nwatch(\r\n  () => props.modelValue,\r\n  () => {\r\n    if (ruleTrigger.value.includes(\"change\")) {\r\n      formItemRef.value?.validate(\"change\", () => {});\r\n    }\r\n  }\r\n);\r\n</script>\r\n\r\n<style lang=\"scss\" scoped></style>\r\n"], "names": ["_defineComponent"], "mappings": ";;;;;;;;AA+Dc,EAAA,IAAA,EAAA,YAAA;AAAA,CAAA,CACZ,CAAM;AACR,MAAA,SAAA,GAAAA,eAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAE,IAAA,MAAA,KAAA,GAAA,OAAA,CAAA;AAEF,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AA0Bd,IAAA,MAAM,WAAQ,GAAA,GAAA,EAAA,CAAA;AAEd,IAAA,MAAM,eAAkB,QAAA,CAAA,MAAA,KAAA,CAAA,MAAA,CAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,GAAA,MAAA,CAAA,CAAA;AAExB,IAAM,MAAA,WAAA,GAAA,eACJ,KAAA,CAAA,YAAsB,GAAA,KAAA,CAAA,MAAa,CAAA,KAAA,GAAA,KACrC,CAAA,QAAA,GAAA,MAAA,GAAA,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,UAAc,GAAA,QAAA,CAAA,MAClB,KAAA,CAAA,MAAa,CAAA,IAAA,GAAA,KAAc,CAAA,MAAA,CAAA,IAAe,GAAA,OAAA,CAAA,CAAM;AAGlD,IAAM,MAAA,SAAA,GAAA,eACJ,KAAA,CAAA,UAAoB,GAAA,KAAA,CAAA,MAAa,CAAA,GAAA,GAAA,OACnC,CAAA,CAAA;AAEA,IAAM,MAAA,WAAA,WACJ,CAAA,MAAA;AAGF,MAAM,OAAA,CAAA,KAAA,CAAA,gBAA6B,EAAA,EAAA,GAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,OAAA,CAAA,CAAA;AACjC,KAAQ,CAAA,CAAA;AAAoD,IAC9D,MAAC,UAAA,GAAA,MAAA;AACD,MAAA,IAAM,iBAAmB,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AACvB,QAAA,WAAgB,CAAA,KAAA,EAAA,QAAe,CAAA,MAAA,EAAS,MAAA;AACtC,SAAY,CAAA,CAAA;AAA8B,OAAA;AAAE,KAC9C,CAAA;AAAA,IACF,MAAA,aAAA,GAAA,CAAA,MAAA,EAAA,QAAA,KAAA;AAEA,MAAM,KAAA,CAAA,eAAiB,EAAA,MAA0B,EAAsB,QAAA,CAAA,CAAA;AACrE,KAAM,CAAA;AAAiC,IACzC,MAAA,UAAA,GAAA,CAAA,GAAA,KAAA;AAEA,MAAM,KAAA,CAAA,mBAA2B,EAAA,GAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAC/B,MAAM,IAAA,KAAA,CAAA,MAAA,CAAA,MAAA,IAAqB,OAAK,KAAY,CAAA,MAAA,CAAA,MAAA,KAAA,UAAA,EAAA;AAC5C,QAAA,aAAiB,CAAA,KAAA,CAAA,aAAuB,EAAA,KAAA,CAAA;AACtC,OAAA;AAAiD,KACnD,CAAA;AAAA,IACF,KAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAEA,MACE,IAAA,WAAY,CAAA,KAAA,CAAA,QACN,CAAA,QAAA,CAAA,EAAA;AACJ,QAAA,WAAgB,CAAA,KAAA,EAAA,QAAe,CAAA,QAAA,EAAW,MAAA;AACxC,SAAY,CAAA,CAAA;AAAgC,OAAA;AAAE,KAChD,CAAA,CAAA;AAAA,IACF,OACF,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}