<template>
  <el-button class="base-button" v-bind="$attrs" @click="onClick">
    <slot>{{ $t('replenish.buttonText') }}</slot>
  </el-button>
</template>
<script lang="ts" setup>
import debounceFn from 'lodash-es/debounce'
const emits = defineEmits(['click'])
const props = defineProps({
  debounce: {
    type: Boolean,
    default: false
  }
})
function handleClick(e: MouseEvent) {
  emits('click', e)
}
const onClick = props.debounce ? debounceFn(handleClick, 300) : handleClick
</script>
<style lang="scss" scoped>
.el-button--primary,
.el-button--primary:focus {
  min-width: 60px;
  height: 32px;
  border: none;
  font-size: 14px;
  font-weight: 400;
  color: var(--ops-text-white-color);
  background-color: var(--ops-primary-color);
}
.el-button--primary:hover {
  background-color: var(--ops-primary-color-3) !important;
}
.el-button--primary:active {
  background-color: var(--ops-button-primary-active) !important;
}
.base-button.el-button--info {
  min-width: 60px;
  height: 32px;
  border: 1px solid var(--ops-primary-color);
  font-size: 14px;
  font-weight: 400;
  color: var(--ops-primary-color);
  background-color: var(--ops-bg-white-color);
}
.base-button.el-button--info:hover {
  background-color: var(--ops-button-info-active) !important;
}
.base-button.el-button--info:active {
  background-color: var(--ops-button-info-disabled) !important;
}
.el-button {
  &.is-link {
    height: 36px;
    border: none;
    border-radius: var(--radius-m);
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 14px;
    font-weight: 400;
    color: var(--ops-primary-color);
    background: transparent;
  }
  &.is-link.is-disabled {
    border: none;
    color: var(--ops-disabled-color);
    background-color: transparent;
    cursor: not-allowed;
  }
}
.el-button.is-disabled {
  background-color: var(--ops-button-primary-disabled) !important;
}
.el-button.el-button--info.is-disabled {
  border-color: #c3c7cb;
  color: var(--ops-button-primary-disabled);
  background-color: #ffffff !important;
}
// .el-button--primary:focus {
//   background-color: var(--ops-primary-color) !important;
// }
</style>
