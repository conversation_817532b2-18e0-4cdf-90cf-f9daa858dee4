{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/edit-table-column/index.ts"], "sourcesContent": ["import { withInstall } from \"@dc-components/utils\";\r\n\r\nimport EditTableColumn from \"./src/index.vue\";\r\n\r\nexport const DcEditTableColumn = withInstall(EditTableColumn);\r\n\r\nexport default DcEditTableColumn;\r\n\r\nexport * from \"./src/types\";\r\n"], "names": [], "mappings": ";;;;AAEY,MAAC,iBAAiB,GAAG,WAAW,CAAC,eAAe;;;;"}