{"version": 3, "file": "encryption.js", "sources": ["../../../../../../packages/components/login/src/encryption.ts"], "sourcesContent": ["import CryptoJS from \"crypto-js\";\n\nconst iv = CryptoJS.enc.Utf8.parse(\"70w5zbONA5xXAJ5C\"); // 偏移量\nconst KEY = \"A39DHJC4x6MAOaHr\";\n// 加密\nexport function encrypt(plaintText: string, key = KEY) {\n  if (!plaintText) {\n    return \"\";\n  }\n  // 秘钥\n  const newKey = CryptoJS.enc.Utf8.parse(key);\n  const iv = CryptoJS.enc.Utf8.parse(\"70w5zbONA5xXAJ5C\");\n  const newPlaintText = plaintText;\n  const options = {\n    iv,\n    mode: CryptoJS.mode.CBC,\n    padding: CryptoJS.pad.Pkcs7,\n  };\n  const encryptedData = CryptoJS.AES.encrypt(newPlaintText, newKey, options);\n  const encryptedBase64Str = encryptedData.toString();\n  // encryptedBase64Str = encryptedBase64Str.replace(/\\+/g,\"-\");\n  return encryptedBase64Str;\n}\n\n// 解密\nexport function decrypt(encryptedBase64Str: string, key = KEY) {\n  if (!encryptedBase64Str) {\n    return \"\";\n  }\n  // var vals = encryptedBase64Str.replace(/\\-/g, '+').replace(/_/g, '/');\n  const options = {\n    iv,\n    mode: CryptoJS.mode.CBC,\n    padding: CryptoJS.pad.Pkcs7,\n  };\n  const newKey = CryptoJS.enc.Utf8.parse(key);\n  const decryptedData = CryptoJS.AES.decrypt(\n    encryptedBase64Str,\n    newKey,\n    options\n  );\n  const decryptedStr = CryptoJS.enc.Utf8.stringify(decryptedData);\n  return decryptedStr;\n}\n\n// 解析路由url参数\nexport function getQueryParam(paramName) {\n  // 安全地解析查询字符串\n  const queryString = window.location.href.split(\"?\")?.[1] || \"\";\n  const params = parseQueryString(queryString);\n\n  // 确保 paramName 是安全的，避免参数注入\n  if (typeof paramName !== \"string\" || !paramName.match(/^[a-zA-Z0-9-_]+$/)) {\n    console.warn(\"Invalid parameter name.\");\n    return undefined;\n  }\n\n  // 返回指定的参数值，如果不存在则返回 undefined\n  return params[paramName] || undefined;\n}\n\n// 解析查询字符串的辅助函数\nexport const parseQueryString = (queryString) => {\n  const params = {};\n  queryString.split(\"&\").forEach((param) => {\n    const [key, value = \"\"] = param.split(\"=\");\n    params[decodeURIComponent(key)] = decodeURIComponent(value);\n  });\n  return params;\n};\n"], "names": ["CryptoJS"], "mappings": ";;;;;;;;;;AAEA,MAAM,EAAK,GAAAA,4BAAA,CAAS,GAAI,CAAA,IAAA,CAAK,MAAM,kBAAkB,CAAA,CAAA;AACrD,MAAM,GAAM,GAAA,kBAAA,CAAA;AAEY,SAAA,OAAA,CAAA,UAAA,EAAoB,MAAM,GAAK,EAAA;AACrD,EAAA,IAAI,CAAC,UAAY,EAAA;AACf,IAAO,OAAA,EAAA,CAAA;AAAA,GACT;AAEA,EAAA,MAAM,MAAS,GAAAA,4BAAA,CAAS,GAAI,CAAA,IAAA,CAAK,MAAM,GAAG,CAAA,CAAA;AAC1C,EAAA,MAAM,GAAK,GAAAA,4BAAA,CAAS,GAAI,CAAA,IAAA,CAAK,MAAM,kBAAkB,CAAA,CAAA;AACrD,EAAA,MAAM,aAAgB,GAAA,UAAA,CAAA;AACtB,EAAA,MAAM,OAAU,GAAA;AAAA,IACd,EAAA,EAAA,GAAA;AAAA,IACA,IAAA,EAAMA,6BAAS,IAAK,CAAA,GAAA;AAAA,IACpB,OAAA,EAASA,6BAAS,GAAI,CAAA,KAAA;AAAA,GACxB,CAAA;AACA,EAAA,MAAM,gBAAgBA,4BAAS,CAAA,GAAA,CAAI,OAAQ,CAAA,aAAA,EAAe,QAAQ,OAAO,CAAA,CAAA;AACzE,EAAM,MAAA,kBAAA,GAAqB,cAAc,QAAS,EAAA,CAAA;AAElD,EAAO,OAAA,kBAAA,CAAA;AACT,CAAA;AAGwB,SAAA,OAAA,CAAA,kBAAA,EAA4B,MAAM,GAAK,EAAA;AAC7D,EAAA,IAAI,CAAC,kBAAoB,EAAA;AACvB,IAAO,OAAA,EAAA,CAAA;AAAA,GACT;AAEA,EAAA,MAAM,OAAU,GAAA;AAAA,IACd,EAAA;AAAA,IACA,IAAA,EAAMA,6BAAS,IAAK,CAAA,GAAA;AAAA,IACpB,OAAA,EAASA,6BAAS,GAAI,CAAA,KAAA;AAAA,GACxB,CAAA;AACA,EAAA,MAAM,MAAS,GAAAA,4BAAA,CAAS,GAAI,CAAA,IAAA,CAAK,MAAM,GAAG,CAAA,CAAA;AAC1C,EAAA,MAAM,gBAAgBA,4BAAS,CAAA,GAAA,CAAI,OACjC,CAAA,kBAAA,EACA,QACA,OACF,CAAA,CAAA;AACA,EAAA,MAAM,YAAe,GAAAA,4BAAA,CAAS,GAAI,CAAA,IAAA,CAAK,UAAU,aAAa,CAAA,CAAA;AAC9D,EAAO,OAAA,YAAA,CAAA;AACT,CAAA;AAGO,SAAA,aAAA,CAAuB,SAAW,EAAA;AAEvC,EAAA,MAAM,cAAc,MAAO,CAAA,QAAA,CAAS,KAAK,KAAM,CAAA,GAAG,IAAI,CAAM,CAAA,IAAA,EAAA,CAAA;AAC5D,EAAM,MAAA,MAAA,GAAS,iBAAiB,WAAW,CAAA,CAAA;AAG3C,EAAA,IAAI,OAAO,SAAc,KAAA,QAAA,IAAY,CAAC,SAAU,CAAA,KAAA,CAAM,kBAAkB,CAAG,EAAA;AACzE,IAAA,OAAA,CAAQ,KAAK,yBAAyB,CAAA,CAAA;AACtC,IAAO,OAAA,KAAA,CAAA,CAAA;AAAA,GACT;AAGA,EAAA,OAAO,OAAO,SAAc,CAAA,IAAA,KAAA,CAAA,CAAA;AAC9B,CAAA;AAGa,MAAA,gBAAA,GAAmB,CAAC,WAAgB,KAAA;AAC/C,EAAA,MAAM,SAAS,EAAC,CAAA;AAChB,EAAA,WAAA,CAAY,KAAM,CAAA,GAAG,CAAE,CAAA,OAAA,CAAQ,CAAC,KAAU,KAAA;AACxC,IAAA,MAAM,CAAC,GAAK,EAAA,KAAA,GAAQ,EAAM,CAAA,GAAA,KAAA,CAAM,MAAM,GAAG,CAAA,CAAA;AACzC,IAAA,MAAA,CAAO,kBAAmB,CAAA,GAAG,CAAK,CAAA,GAAA,kBAAA,CAAmB,KAAK,CAAA,CAAA;AAAA,GAC3D,CAAA,CAAA;AACD,EAAO,OAAA,MAAA,CAAA;AACT;;;;;;;"}